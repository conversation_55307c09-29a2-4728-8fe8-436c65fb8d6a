package com.wanlianyida.bi.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.ctpcommon.model.query.InnerBaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/18 13:49
 */
@Data
public class CtpsPlatCommodityPriceInfoApiQuery extends InnerBaseQuery {

    @ApiModelProperty("品种编码")
    private String breedCode;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String scCode;

    @ApiModelProperty("周期类型 | 日、周、月")
    private String trType;

    @ApiModelProperty("材质编码")
    private String mqCode;

    @ApiModelProperty("数据同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date synTime;
}
