package com.wanlianyida.bi.api.model.dto;

import com.wanlianyida.framework.ctpcommon.model.dto.InnerBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AdsCtpsPlatCommodityCateBreedHfTreeApiDTO extends InnerBaseDTO implements Serializable {

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String cateName;

    /**
     * 品种编码
     */
    @ApiModelProperty("品种编码")
    private String breedCode;

    /**
     * 品种名称
     */
    @ApiModelProperty("品种名称")
    private String breedName;

    /**
     * 品种简称
     */
    @ApiModelProperty("品种简称")
    private String breedShortName;

    /**
     * 材质编码
     */
    @ApiModelProperty("材质编码")
    private String mqCode;

    /**
     * 材质名称
     */
    @ApiModelProperty("材质名称")
    private String mqName;

    /**
     * 材质简称
     */
    @ApiModelProperty("材质简称")
    private String mqShortName;

    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String scCode;

    /**
     * 规格名称
     */
    @ApiModelProperty("规格名称")
    private String scName;

    /**
     * 规格简称
     */
    @ApiModelProperty("规格简称")
    private String scShortName;


    private List<AdsCtpsPlatCommodityCateBreedHfTreeApiDTO> childrenList;

    public AdsCtpsPlatCommodityCateBreedHfTreeApiDTO() {
        this.childrenList = new ArrayList<>();
    }

    public void addChild(AdsCtpsPlatCommodityCateBreedHfTreeApiDTO children) {
        this.childrenList.add(children);
    }

}
