package com.wanlianyida.support.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/02/24/18:41
 */
@Data
public class PicLibraryListDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 图片库分类 10 商品图片库
     */
    private String picType;

    /**
     * 图片名称
     */
    private String picName;

    /**
     * 图片url
     */
    private String picUrl;

    /**
     * 图片尺寸长
     */
    private Integer pixelsLength;

    /**
     * 图片尺寸宽
     */
    private Integer pixelsWidth;

    /**
     * 图片大小
     */
    private String picSize;

    /**
     * 上传时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadDate;
}
