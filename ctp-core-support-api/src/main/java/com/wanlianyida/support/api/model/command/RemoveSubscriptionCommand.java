package com.wanlianyida.support.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 将消息从用户已订阅的列表移除
 */
@Data
public class RemoveSubscriptionCommand {
    /**
     * 用户id集合(注意这里的用户id不是userBaseId)
     */
    @NotNull(message = "用户id不能为空")
    @Size(min = 1, message = "用户id不能为空")
    private List<String> userIdList;

    /**
     * 模板id
     */
    @NotBlank(message = "模板id不能为空")
    private String templateId;

    /**
     * 消息接收方[10-买家,20-卖家]
     */
    @NotBlank(message = "消息接收方不能为空")
    private String messageReceiver;
}
