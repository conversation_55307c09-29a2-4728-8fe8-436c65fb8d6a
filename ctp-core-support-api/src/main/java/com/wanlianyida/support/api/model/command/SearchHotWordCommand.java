package com.wanlianyida.support.api.model.command;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2025/03/13
 * 搜索热词管理
 */
@Data
public class SearchHotWordCommand {
    /**
     * 主键
     */
    private Long id;

    /**
     * 热词名称
     */
    @Length(max = 10, message = "热词名称不能超过10个字符")
    private String hotWordName;

    /**
     * 启用状态 1启用 0禁用
     */
    private Boolean enabledStatus;

    /**
     * 排序序号
     */
    private Integer sortSeq;

}
