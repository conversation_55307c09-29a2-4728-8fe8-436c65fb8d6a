package com.wanlianyida.support.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class SearchHotWordDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 热词名称
     */
    private String hotWordName;

    /**
     * 启用状态 1启用 0禁用
     */
    private Integer enabledStatus;

    /**
     * 排序序号
     */
    private Integer sortSeq;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 更新人名字
     */
    private String updaterName;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;



}
