package com.wanlianyida.support.api.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum WxMsgTemplateEnum {
    ORDER_DELIVERY_NOTICE("202505281719", "订单送达提醒-通知买家","7dJYmY41PLXgdzD7p8ttOJCmo9j_WkHDmKj4HHHMX3w", "10"),
    SELLER_CONFIRM_SETTLEMENT("202505281724", "买家已确认结算单-通知卖家","8PpWzb-EbI80R6LD9LrbxPK_NlmlpNUPQPjY7AQuwCw","20"),
    ADVANCE_PAYMENT_SUCCESS("202505281727", "买家支付预付款成功-通知买家","nkXm0JqaYSJhuGIDhQMvOj1V7NK_-Two5ldFYeYSSzo","10"),
    ADVANCE_PAYMENT_FAIL("202505281728", "买家支付预付款失败-通知买家","dKXhAy1uzx0H3WO6f75MHVGz_l4OfIf3h2LjLHcuKiI","10"),
    ADVANCE_PAYMENT_COMPLETED("202505281729", "买家支付预付款成功-通知卖家","2w_Az5XAKw53_7hRDpSooiRZaLquFsy7OFvVtqNlX8A","20"),
    BALANCE_PAYMENT_SUCCESS("202505281733", "买家支付尾款成功-通知买家","nkXm0JqaYSJhuGIDhQMvOj1V7NK_-Two5ldFYeYSSzo","10"),
    BALANCE_PAYMENT_FAIL("202505281734", "买家支付尾款失败-通知买家","dKXhAy1uzx0H3WO6f75MHVGz_l4OfIf3h2LjLHcuKiI","10"),
    BALANCE_PAYMENT_COMPLETED("202505281735", "买家支付尾款成功-通知卖家","2w_Az5XAKw53_7hRDpSooiRZaLquFsy7OFvVtqNlX8A","20");

    /**
     * 站内信/短信模板id
     */
    private final String id;
    /**
     * 描述
     */
    private final String title;
    /**
     * 微信模板id
     */
    private final String wxTemplateId;

    /**
     * 消息接收方[10-买家,20-卖家]
     */
    private final String messageReceiver;

    WxMsgTemplateEnum(String id, String title, String wxTemplateId,String messageReceiver) {
        this.id = id;
        this.title = title;
        this.wxTemplateId = wxTemplateId;
        this.messageReceiver = messageReceiver;
    }

    /**
     * 通过id获取枚举的静态方法
     */
    private static final Map<String, WxMsgTemplateEnum> ID_MAP = new HashMap<>();

    static {
        // 初始化ID到枚举的映射
        for (WxMsgTemplateEnum value : values()) {
            ID_MAP.put(value.getId(), value);
        }
    }

     /**
     * 通过id获取对应的枚举实例
     * @param id 模板id
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static WxMsgTemplateEnum getById(String id) {
        return ID_MAP.get(id);
    }

    /**
     * 序列化时使用id作为JSON值
     */
    @JsonValue
    public String getId() {
        return id;
    }

    /**
     * 反序列化时通过id查找枚举
     */
    @JsonCreator
    public static WxMsgTemplateEnum fromId(String id) {
        return ID_MAP.get(id);
    }
}
