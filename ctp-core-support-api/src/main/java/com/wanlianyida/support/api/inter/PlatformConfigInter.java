package com.wanlianyida.support.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.model.dto.PlatformConfigDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "ctp-base-support", contextId = "PlatformConfigInter", path = "/ctp-base-support")
public interface PlatformConfigInter {

    /**
     * 分页查询
     */
    @GetMapping("/commonConfig/getCommonConfig")
    ResultMode<PlatformConfigDTO> getCommonConfig();
}
