<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wanlianyida</groupId>
        <artifactId>ctp</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <name>ctp-orch-platform</name>
    <groupId>com.wanlianyida</groupId>
    <artifactId>ctp-orch-platform</artifactId>
    <version>1.0-SNAPSHOT</version>
    <description>平台端端编排服务</description>

    <dependencies>
        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-cache</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.wanlianyida.framework</groupId>-->
        <!--            <artifactId>ctp-starters-mysql</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-auth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-sett-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-order-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-partner-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-product-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-transaction-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-content-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-user-auth-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-basic-data-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-file-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-log-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-base-support-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-search-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-cont-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-msg-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-process-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-base-process-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-sett-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-mdm-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-sett-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-base-rms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-core-bidding-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-starters-rocketmq</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-starters-rocketmq</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>ctp-base-bi-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-model</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
