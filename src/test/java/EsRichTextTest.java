import com.wanlianyida.ProductApplication;
import com.wanlianyida.ctpcore.product.domain.model.bo.RichTextBO;
import com.wanlianyida.ctpcore.product.infrastructure.exechange.EsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@SpringBootTest(classes = ProductApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class EsRichTextTest {

    @Resource
    EsExchangeService esExchangeService;

    @Test
    public void testEs() throws InterruptedException {
        RichTextBO textBO = new RichTextBO();
        String key = "code:" + System.currentTimeMillis();
        textBO.setSpuCode(key);
        textBO.setRichText(key + "的content内容");
        System.out.println("param: {}" + textBO.getRichText());
        log.info("保存的数据: {}", textBO.getRichText());
        esExchangeService.saveProductRichText(textBO);

        String productRichText = esExchangeService.getProductRichText(textBO);
        log.info("查询到的数据: {}", productRichText);

        log.info("开始休眠1秒");
        Thread.sleep(1000);

        log.info("休眠1秒后");
        productRichText = esExchangeService.getProductRichText(textBO);
        log.info("查询到的数据: {}", productRichText);

    }
}
