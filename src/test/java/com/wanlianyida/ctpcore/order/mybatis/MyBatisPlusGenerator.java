/*
package com.wanlianyida.ctpcore.order.mybatis;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

public class MyBatisPlusGenerator {

    public static void main(String[] args) {

        FastAutoGenerator.create("***************************************************************************************", "sit_user", "thoh1ayieZ")
                .globalConfig(builder -> {
                    builder.author("sunjin") // 设置作者
                            .outputDir("src/main/java"); // 输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.wanlianyida.ctpcore.order.infrastructure.repository") // 设置父包名
                            .entity("po");// 设置实体类包名
//                            .mapper("mapper") // 设置 Mapper 接口包名
//                            .service("persistence"); // 设置 Service 接口包名

                })
                .strategyConfig(builder -> {

                    builder.addInclude("oc_porder_product_sku","oc_porder_product_sku_attr","oc_porder_product_spu")
                            .entityBuilder()
                            .enableLombok()
                            .formatFileName("%sPO")
                            .enableLombok() // 启用 Lombok
                            .enableTableFieldAnnotation()
                            .mapperBuilder()
                            .enableBaseColumnList()
                            .enableBaseResultMap(); // 启;
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用 Freemarker 模板引擎
                .execute(); // 执行生成

    }
}
*/
