//import cn.hutool.extra.spring.SpringUtil;
//import com.wanlianyida.Application;
//import com.wanlianyida.baselog.domain.model.condition.LogOperationRecordCondition;
//import com.wanlianyida.baselog.domain.model.entity.LogOperationRecordEntity;
//import com.wanlianyida.baselog.domain.repository.LogOperationRecordRepo;
//import com.wanlianyida.baselog.infrastructure.repository.mapper.LogRecordMapper;
//import com.wanlianyida.baselog.infrastructure.repository.po.LogOperationRecordPO;
//import com.wanlianyida.baselog.infrastructure.repository.po.LogOperationRecordQueryPO;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.Import;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Date;
//import java.util.List;
//
//import javax.annotation.Resource;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@Import(SpringUtil.class)
//@SpringBootTest(classes = Application.class)
//public class LogOperationRecordTest {
//
//    @Resource
//    LogOperationRecordRepo logOperationRecordRepo;
//    @Resource
//    private LogRecordMapper logRecordMapper;
//
//    @Test
//    public void testInsert(){
//        LogOperationRecordEntity logOperationRecordEntity = new LogOperationRecordEntity();
//        logOperationRecordEntity.setBizId("123");
//        logOperationRecordEntity.setBizType("123");
//        logOperationRecordEntity.setUserBaseId("123");
//        logOperationRecordEntity.setUserName("123");
//        logOperationRecordEntity.setUserGroupId("123");
//        logOperationRecordEntity.setUserGroupName("123");
//        logOperationRecordEntity.setOperateType("123");
//        logOperationRecordEntity.setOperateContent("123");
//        logOperationRecordRepo.insert(logOperationRecordEntity);
//    }
//
//    @Test
//    public void testQuery(){
//        LogOperationRecordCondition logOperationRecordCondition = new LogOperationRecordCondition();
//        logOperationRecordCondition.setBizId("12");
////        List<LogOperationRecordPO> logOperationRecordPOS = logOperationRecordMapper.queryPage(logOperationRecordCondition, 1, 2);
////        log.info("logOperationRecordPOS:{}", JSONUtil.toJsonStr(logOperationRecordPOS));
//    }
//
//    @Test
//    public void testBuildQuery() {
////        LogOperationRecordCondition logOperationRecordCondition = new LogOperationRecordCondition();
////        logOperationRecordCondition.setBizId("1");
////        logOperationRecordCondition.setOperateTimeEnd(new Date());
////        logOperationRecordCondition.setOperateTimeStart(new Date());
////        logOperationRecordCondition.setBizType("2");
////        logOperationRecordCondition.setUserAccount("userAccount");
////        logOperationRecordCondition.setUserGroupId("3");
////
////        String where = new SqlQueryBuilder().buildWhereClause(logOperationRecordCondition);
////        System.out.println(where);
//        LogOperationRecordQueryPO recordQueryPO =new LogOperationRecordQueryPO();
//        recordQueryPO.setUserAccount("1");
//        recordQueryPO.setBizId("111111");
//        recordQueryPO.setOperateTimeStart(new Date());
//        List<LogOperationRecordPO> logOperationRecordPOS = logRecordMapper.queryPage(LogOperationRecordPO.class, recordQueryPO, 1, 2);
//
//
//
//    }
//}
