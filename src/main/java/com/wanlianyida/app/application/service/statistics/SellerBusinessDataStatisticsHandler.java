package com.wanlianyida.app.application.service.statistics;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsBusinessDataDTO;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.app.infrastructure.enums.SatisticsBizTypeEnum;
import com.wanlianyida.app.infrastructure.enums.WorkbenchesTypeEnum;
import com.wanlianyida.app.infrastructure.exchange.BiBigDataExchangeService;
import com.wanlianyida.app.infrastructure.exchange.StatisticsExchangeService;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.dto.StatisticsBusinessDTO;
import com.wanlianyida.order.api.model.query.StatisticsBusinessQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 经营数据卖家
 */
@Service
@Slf4j
public class SellerBusinessDataStatisticsHandler implements StatisticsHandler<StatisticsBusinessDataDTO> {

    @Resource
    StatisticsExchangeService statisticsExchangeService;

    @Resource
    private BiBigDataExchangeService biBigDataExchangeService;

    @Override
    public StatisticsBusinessDataDTO getBiData(StatisticsDataQuery query) {
        StatisticsBusinessQuery businessQuery = BeanUtil.toBean(query, StatisticsBusinessQuery.class);
        businessQuery.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        StatisticsBusinessDataDTO resultBusinessDataDTO = new StatisticsBusinessDataDTO();
        //经营数据
        StatisticsBusinessDTO businessDTO = biBigDataExchangeService.statisticsBusinessQuery(businessQuery);
        resultBusinessDataDTO = BeanUtil.toBean(businessDTO, StatisticsBusinessDataDTO.class);
        //商品浏览量总数据
        String productViewCount  = biBigDataExchangeService.productBrowserCount(businessQuery);
        resultBusinessDataDTO.setPageView(productViewCount);
        return resultBusinessDataDTO;

    }

    @Override
    public StatisticsBusinessDataDTO getServiceData(StatisticsDataQuery query) {
        StatisticsBusinessQuery businessQuery = BeanUtil.toBean(query, StatisticsBusinessQuery.class);
        businessQuery.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        businessQuery.setWorkbenchesType(WorkbenchesTypeEnum.SELLER.getCode());
        StatisticsBusinessDataDTO resultBusinessDataDTO = new StatisticsBusinessDataDTO();
        //经营数据
        StatisticsBusinessDTO businessDTO = statisticsExchangeService.statisticsBusinessQuery(businessQuery);
        resultBusinessDataDTO = BeanUtil.toBean(businessDTO, StatisticsBusinessDataDTO.class);
        resultBusinessDataDTO.setPageView("0");
        return resultBusinessDataDTO;
    }

    @Override
    public boolean handlerType(String type) {
        return SatisticsBizTypeEnum.BIZ_DATA_COUNT.getCode().equals(type);
    }
}
