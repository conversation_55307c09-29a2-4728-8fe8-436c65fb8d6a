package com.wanlianyida.app.application.service.stock;

import com.wanlianyida.app.infrastructure.enums.CtpPortalExceptionEnum;
import com.wanlianyida.app.infrastructure.exchange.ProductExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.ProductCheckCommand;
import com.wanlianyida.product.api.model.dto.*;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月28日 13:15
 */
@Service
public class ProductStockAppService {


    @Resource
    private ProductExchangeService productExchangeService;


    /**
     * 校验商品库存数和最小起订量
     * @param command
     * @return
     */
    public ResultMode<ProductCheckDTO> checkProductStock(ProductCheckCommand command) {
        List<String> skuCodeList = command.getProducts().stream().map(ProductCheckCommand.ProductCommand::getSkuCode).collect(Collectors.toList());
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productExchangeService.queryProductSkuDetail(skuCodeList);
        Map<String, ProductSkuDetailDTO> collect = productSkuDetailDTOS.stream().collect(Collectors.toMap(e -> e.getSkuCode(), Function.identity()));
        for (ProductCheckCommand.ProductCommand product : command.getProducts()) {
            ProductSkuDetailDTO productSkuDetailDTO = collect.get(product.getSkuCode());
            if (productSkuDetailDTO != null) {
                BigDecimal minQuantity = productSkuDetailDTO.getMinQuantity();
                BigDecimal quantity = productSkuDetailDTO.getQuantity();
                BigDecimal purchaseQuantity = product.getPurchaseQuantity();
                if (purchaseQuantity.compareTo(minQuantity) < 0 || purchaseQuantity.compareTo(quantity) > 0) {
                    return ResultMode.fail( CtpPortalExceptionEnum.PRODUCT_STOCK_ERROR.getCode(), CtpPortalExceptionEnum.PRODUCT_STOCK_ERROR.getMsg());
                }
            }
        }
        return ResultMode.success();
    }

}
