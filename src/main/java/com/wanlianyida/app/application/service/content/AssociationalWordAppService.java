package com.wanlianyida.app.application.service.content;

import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.search.api.IEsCtpAssociationalWordService;
import com.wanlianyida.search.dto.CtpAssociationalWordDTO;
import com.wanlianyida.search.query.CtpAssociationalWordQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class AssociationalWordAppService {

    @Resource
    private IEsCtpAssociationalWordService esCtpAssociationalWordService;

    /**
     * 招标公示列表
     */
    public ResponseMessage<List<CtpAssociationalWordDTO>> queryPage(PagingInfo<CtpAssociationalWordQuery> query) {
        return esCtpAssociationalWordService.queryPage(query);
    }

}
