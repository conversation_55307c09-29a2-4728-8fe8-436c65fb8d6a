package com.wanlianyida.app.application.service.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.application.assembler.ShopCardAssembler;
import com.wanlianyida.app.infrastructure.constant.ShopCartConstant;
import com.wanlianyida.app.infrastructure.exchange.ProductExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ProductPublishCompanyExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ShopExchangeService;
import com.wanlianyida.app.interfaces.dto.AvailableAndInvalidDTO;
import com.wanlianyida.app.interfaces.dto.ShopCartAvailableDTO;
import com.wanlianyida.app.interfaces.dto.ShopCartProductDTO;
import com.wanlianyida.app.interfaces.dto.ShopCartRedisDTO;
import com.wanlianyida.app.interfaces.dto.ShopSkusDTO;
import com.wanlianyida.app.interfaces.model.dto.ShopCartShowDTO;
import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.file.query.FileUrlsQuery;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.product.api.model.dto.PcProductPublishCompanyDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuAttrDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.dto.ProductSpuDTO;
import com.wanlianyida.product.api.model.query.PublishCompanyQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Component
public class ShopCartQueryV2AppService{

    @Resource
    private ProductExchangeService productExchangeService;

    @Resource
    private RedisService redisService;

    @Resource
    private IUploadService iUploadService;

    @Resource
    private ShopExchangeService shopExchangeService;

    @Resource
    private ProductPublishCompanyExchangeService productPublishCompanyExchangeService;

    public ResultMode<ShopCartShowDTO> queryShopCartList() {
        //购物车数据
        Map<String, List<ShopCartRedisDTO>> shopCartMap = getCarData();
        if (ObjUtil.isNull(shopCartMap)) {
            return ResultMode.success(new ShopCartShowDTO());
        }
        ShopSkusDTO shopSkusDTO = ShopCardAssembler.getShopAndSkuCode(shopCartMap);
        //店铺按更新时间降序排序
        List<String> shopIds = shopSkusDTO.getShopIds();
        //购物车中skuCode集合
        Set<String> skuCodes = shopSkusDTO.getSkus();
        //sku数据集合
        List<ProductSkuDetailDTO> productSkuDetail = getProductSkuDetail(skuCodes);
        if (CollUtil.isEmpty(productSkuDetail)) {
            return ResultMode.success(new ShopCartShowDTO());
        }
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        //发布范围
        validPublishCompany(productSkuDetail);
        //获取可用和失效数据
        AvailableAndInvalidDTO availableAndInvalidDTO = ShopCardAssembler.getAvailableInvalidPair(productSkuDetail, companyId);
        List<ProductSkuDetailDTO> availableList = availableAndInvalidDTO.getAvailableList();
        List<ProductSkuDetailDTO> invalidList = availableAndInvalidDTO.getInvalidList();

        // 统一获取图片映射
        List<String> picList = productSkuDetail.stream()
                .map(ProductSkuDetailDTO::getPicUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, String> picMap = getPicMap(picList);
        //可用的列表
        List<ShopCartAvailableDTO> availableDTOS = buildCarListAvailableData(availableList, shopCartMap, shopIds, picMap);
        //失效的列表
        List<ShopCartProductDTO> invalidDTOS = buildCarListInvalidData(invalidList, shopCartMap, picMap);
        //返回数据
        ShopCartShowDTO shopCartShowDTO = new ShopCartShowDTO();
        shopCartShowDTO.setAvailableList(availableDTOS);
        shopCartShowDTO.setInvalidList(invalidDTOS);

        return ResultMode.success(shopCartShowDTO);
    }

    private Map<String, List<ShopCartRedisDTO>> getCarData() {
        String userId = JwtUtil.getTokenInfo().getUserBaseId();
        String redisLockKey = ShopCartConstant.SHOP_CAR_PREFIX_V2 + userId;
        Map<String, Object> map = redisService.hGetAll(redisLockKey);

        if (CollUtil.isEmpty(map)) return null;

        Map<String, List<ShopCartRedisDTO>> shopCartMap = new HashMap<>();
        map.forEach((k, v) -> shopCartMap.put(k, JSONUtil.toList(v.toString(), ShopCartRedisDTO.class)));
        return shopCartMap;
    }

    private List<ProductSkuDetailDTO> getProductSkuDetail(Set<String> skuCodes) {
        ArrayList<String> skuCodesList = new ArrayList<>(skuCodes);
        List<ProductSkuDetailDTO> productSkuDetailDTOList = productExchangeService.queryProductSkuDetail(skuCodesList);
        log.info("查询购物车列表商品信息查询参数：{},返回结果：{}", JSONUtil.toJsonStr(skuCodesList), JSONUtil.toJsonStr(productSkuDetailDTOList));
        return productSkuDetailDTOList;
    }

    private Map<String, String> getPicMap(List<String> picList) {
        FileUrlsQuery fileUrlsQuery = new FileUrlsQuery();
        fileUrlsQuery.setUrls(picList);
        ResponseMessage<Map<String, String>> picResultMode = iUploadService.getUrls(fileUrlsQuery);
        return ObjUtil.isNotNull(picResultMode) && picResultMode.isSucceed()
                ? picResultMode.getModel()
                : new HashMap<>();
    }

    private List<ProductSkuDetailDTO> validPublishCompany(List<ProductSkuDetailDTO> productSkuDetail) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (ObjUtil.isEmpty(companyId)) {
            return productSkuDetail;
        }
        List<String> spuCodes = productSkuDetail.stream().filter(sku -> "20".equals(sku.getPublishType())).map(ProductSkuDetailDTO::getSpuCode).collect(Collectors.toList());
        if (CollUtil.isEmpty(spuCodes)) {
            return productSkuDetail;
        }
        ResultMode<List<PcProductPublishCompanyDTO>> listResultMode = productPublishCompanyExchangeService.publishCompanyList(PublishCompanyQuery.builder().spuCodeList(spuCodes).build());
        List<PcProductPublishCompanyDTO> model = listResultMode.getModel();
        if (ObjUtil.isEmpty(model)) {
            return productSkuDetail;
        }
        Map<String, List<String>> resultMap = model.stream()
                .collect(Collectors.groupingBy(
                        PcProductPublishCompanyDTO::getSpuCode,
                        Collectors.mapping(PcProductPublishCompanyDTO::getPublishCompanyId, Collectors.toList())
                ));
        log.info("validPublishCompany：{}",resultMap);
        for (ProductSkuDetailDTO productSkuDetailDTO : productSkuDetail) {
            List<String> publishCompanyIdList = resultMap.get(productSkuDetailDTO.getSpuCode());
            if (CollUtil.isNotEmpty(publishCompanyIdList)) {
                productSkuDetailDTO.setPublishCompanyIdList(publishCompanyIdList);
            }
        }
        return productSkuDetail;
    }

    private List<ShopCartAvailableDTO> buildCarListAvailableData(List<ProductSkuDetailDTO> model, Map<String, List<ShopCartRedisDTO>> shopCartMap,List<String> shopIds, Map<String, String> picMap) {
        if (CollUtil.isEmpty(model)) {
            return new ArrayList<>();
        }
        Map<String, ProductSkuDetailDTO> groupBySkuMap = model.stream().collect(Collectors.toMap(ProductSkuDetailDTO::getSkuCode, Function.identity()));
        String companyId = JwtUtil.getTokenInfo().getCompanyId();

        List<ShopCartAvailableDTO> availableDTOList = new ArrayList<>();
        shopIds.forEach(shopId -> {
            List<ShopCartRedisDTO> shopCartRedisDTOS = shopCartMap.get(shopId);
            //先排序
            shopCartRedisDTOS.sort(Comparator.comparing(ShopCartRedisDTO::getUpdateDate).reversed());

            ShopCartAvailableDTO dto = new ShopCartAvailableDTO();
            // 店铺信息
            com.wanlianyida.partner.api.model.dto.ShopDTO shopInfo = shopExchangeService.shopDetailByShopIdOrCompanyId(Long.valueOf(shopId), "");
            dto.setShopId(shopInfo.getId());
            dto.setShopName(shopInfo.getShopName());
            List<ShopCartProductDTO> products = new ArrayList<>();
            for (ShopCartRedisDTO shopCartDTO : shopCartRedisDTOS) {
                ProductSkuDetailDTO skuDetailDTO = groupBySkuMap.get(shopCartDTO.getSkuCode());
                if (!groupBySkuMap.containsKey(shopCartDTO.getSkuCode())) {
                    continue;
                }
                ShopCartProductDTO product = buildShopCartProductDTO(shopCartDTO, skuDetailDTO, picMap, companyId);
                product.setShopId(shopCartDTO.getShopId());
                product.setShopName(shopCartDTO.getShopName());
                products.add(product);
            }
            dto.setProducts(products);
            if (CollUtil.isNotEmpty(products)) {
                availableDTOList.add(dto);
            }
        });
        return availableDTOList;
    }

    private List<ShopCartProductDTO> buildCarListInvalidData(List<ProductSkuDetailDTO> model, Map<String, List<ShopCartRedisDTO>> shopCartMap, Map<String, String> picMap) {
        if (CollUtil.isEmpty(model)) {
            return new ArrayList<>();
        }
        Map<String, ProductSkuDetailDTO> groupBySkuMap = model.stream().collect(Collectors.toMap(ProductSkuDetailDTO::getSkuCode, Function.identity()));

        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        List<ShopCartProductDTO> invalidDtos = new ArrayList<>();

        List<ShopCartRedisDTO> cartDTOList = shopCartMap.values().stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        cartDTOList.sort(Comparator.comparing(ShopCartRedisDTO::getUpdateDate).reversed());

        for (ShopCartRedisDTO shopCartDTO : cartDTOList) {
            if (!groupBySkuMap.containsKey(shopCartDTO.getSkuCode())) {
                continue;
            }
            ProductSkuDetailDTO skuDetailDTO = groupBySkuMap.get(shopCartDTO.getSkuCode());
            ShopCartProductDTO product = buildShopCartProductDTO(shopCartDTO, skuDetailDTO, picMap, companyId);
            product.setShopId(shopCartDTO.getShopId());
            product.setShopName(shopCartDTO.getShopName());
            //product.setStatus("");
            invalidDtos.add(product);
        }
        return invalidDtos;
    }

    private ShopCartProductDTO buildShopCartProductDTO(ShopCartRedisDTO shopCartDTO, ProductSkuDetailDTO skuDetailDTO, Map<String, String> picMap, String companyId) {
        ShopCartProductDTO  product = new ShopCartProductDTO();
        product.setCompanyId(shopCartDTO.getCompanyId());
        product.setCompanyName(shopCartDTO.getCompanyName());
        product.setSkuCode(skuDetailDTO.getSkuCode());
        product.setSkuName(skuDetailDTO.getSkuName());
        product.setSpuCode(skuDetailDTO.getSpuCode());
        //查询图片
        ProductSpuDTO productSpu = skuDetailDTO.getProductSpu();
        //转图片
        product.setPicture(picMap.get(skuDetailDTO.getPicUrl()));
        List<ProductSkuAttrDTO> productSkuAttrList = skuDetailDTO.getProductSkuAttrList();
        if(CollUtil.isNotEmpty(productSkuAttrList)){
            Map<String, String> attrMap = productSkuAttrList.stream().collect(Collectors.toMap(ProductSkuAttrDTO::getAttributeName, ProductSkuAttrDTO::getValueText));
            product.setAttr(attrMap);
        }
        product.setDeliveryPeriod(skuDetailDTO.getDeliveryPeriod());
        product.setPrice(skuDetailDTO.getPriceFee());
        product.setPricingUnit(productSpu.getRelPricingUnitId());
        product.setPurchaseQuantity(shopCartDTO.getPurchaseQuantity());
        product.setPurchaseQuantityUnit(productSpu.getRelMeasurementUnitId());
        product.setUnitTransfer(skuDetailDTO.getUnitTransfer());
        product.setMinQuantity(skuDetailDTO.getMinQuantity());
        product.setQuantity(skuDetailDTO.getQuantity());
        product.setSubTotal(shopCartDTO.getPurchaseQuantity().multiply(skuDetailDTO.getPriceFee()));
        product.setStatus(skuDetailDTO.getOnSaleStatus()+"");
        if (CollUtil.isNotEmpty(skuDetailDTO.getPublishCompanyIdList()) && !skuDetailDTO.getPublishCompanyIdList().contains(companyId)) {
            product.setStatus("10");
        }
        product.setPublishCompanyIdList(ObjUtil.isEmpty(skuDetailDTO.getPublishCompanyIdList()) ? new ArrayList<>() : skuDetailDTO.getPublishCompanyIdList());
        return product;
    }
}
