package com.wanlianyida.app.application.service.statistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsDataDTO;
import com.wanlianyida.app.application.service.statistics.factory.BizHandlerFactory;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * 工作台统计信息
 **/
@Slf4j
@Service
@RefreshScope
public class StatisticsAppService {

    /**
     * 工作台环境配置 dev开发，prod生产
     */
    @Value("${environment-config:prod}")
    private String enviConfig;



    /**
     * 查询统计数据
     * @param query
     * @return
     */
    public ResultMode<StatisticsDataDTO> queryStatisticsData(StatisticsDataQuery query) {
        StatisticsHandler dataProcessor = BizHandlerFactory.getHandler(StatisticsHandler.class, query.getBizType(), false);
        if (ObjUtil.isNull(dataProcessor)) {
            return ResultMode.success();
        }
        // 根据配置走bi或者service TODO
        Object statisticsData = null;
        if(StrUtil.equals("prod",enviConfig)){
            statisticsData = dataProcessor.getBiData(query);
        }
        if(StrUtil.equals("dev",enviConfig)){
            statisticsData = dataProcessor.getServiceData(query);
        }
        StatisticsDataDTO dataDTO = new StatisticsDataDTO();
        dataDTO.setResult(BeanUtil.beanToMap(statisticsData));
        return ResultMode.success(dataDTO);
    }



}
