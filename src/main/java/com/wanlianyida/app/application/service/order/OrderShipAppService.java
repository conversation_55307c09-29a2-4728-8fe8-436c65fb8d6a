package com.wanlianyida.app.application.service.order;

import com.wanlianyida.app.infrastructure.exchange.LogisticsExchangeService;
import com.wanlianyida.exter.api.lgi.model.request.LogisticsTrackingNodesRequest;
import com.wanlianyida.exter.api.lgi.model.request.LogisticsTrailRequest;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.inter.OrderShipInter;
import com.wanlianyida.order.api.model.command.OrderShipCancelCommand;
import com.wanlianyida.order.api.model.command.OrderShipCheckCommand;
import com.wanlianyida.order.api.model.command.OrderShipCreateCommand;
import com.wanlianyida.order.api.model.command.PickUpCarListAddCmd;
import com.wanlianyida.order.api.model.dto.CreateOrderShipResDTO;
import com.wanlianyida.order.api.model.dto.OrderShipCheckDTO;
import com.wanlianyida.order.api.model.dto.OrderShipDetailResDTO;
import com.wanlianyida.order.api.model.dto.OrderShipListResDTO;
import com.wanlianyida.order.api.model.query.OrderShipDetailListQuery;
import com.wanlianyida.order.api.model.query.OrderShipDetailQuery;
import com.wanlianyida.order.api.model.query.OrderShipListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

import javax.annotation.Resource;

/**
 * 发-提货单相关接口
 *
 * <AUTHOR>
 * @since 2025/4/22
 */

@Service
@Slf4j
public class OrderShipAppService {

    @Resource
    private OrderShipInter orderShipDomainService;

    @Resource
    private LogisticsExchangeService logisticsExchangeService;

    /**
     * 创建订单发货
     *
     * @param command 创建订单发货
     * @return 订货单号
     */
    public ResultMode<CreateOrderShipResDTO> createOrderShip(OrderShipCreateCommand command) {
       return orderShipDomainService.createOrderShip(command);
    }

    /**
     * 订单发货分页列表
     *
     * @param pagingInfo 订单发货分页列表
     * @return 订单发货分页列表
     */
    public ResultMode<List<OrderShipListResDTO>> shipPageList(PagingInfo<OrderShipListQuery> pagingInfo) {
        return orderShipDomainService.shipPageList(pagingInfo);
    }

    /**
     * 订单发货详情
     *
     * @param query 订单发货详情
     * @return 订单发货详情
     */
    public ResultMode<OrderShipDetailResDTO> orderShipDetail(OrderShipDetailQuery query) {
        return orderShipDomainService.orderShipDetail(query);
    }

    /**
     * 订单-查询发货单、提货单
     */
    public ResultMode<List<OrderShipDetailResDTO>> orderShipDetailList(PagingInfo<OrderShipDetailListQuery> query) {
        return orderShipDomainService.orderShipDetailList(query);
    }

    /**
     * 取消订单发货
     *
     * @param command 取消订单发货
     * @return 订货单号
     */
    public ResultMode<OrderShipCheckDTO> cancelOrderShip(OrderShipCancelCommand command) {
        return orderShipDomainService.cancelOrderShip(command);
    }

    /**
     * 保存订单发货
     *
     * @param command 保存订单发货
     * @return 订货单号
     */
    public ResultMode<Void> savePickUpCarList(PickUpCarListAddCmd command) {
        return orderShipDomainService.savePickUpCarList(command);
    }

    /**
     * 在线发货校验
     */
    @PostMapping("/checkOnlineShip")
    public ResultMode<OrderShipCheckDTO> checkOnlineShip(OrderShipCheckCommand command) {
        return orderShipDomainService.checkOnlineShip(command);
    }

    /**
     * 在线托运校验
     */
    @PostMapping("/checkOnlineEntrust")
    public ResultMode<OrderShipCheckDTO> checkOnlineEntrust(OrderShipCheckCommand command) {
        return orderShipDomainService.checkOnlineEntrust(command);
    }

    /**
     * 查询物流轨迹
     *
     * @param query 查询物流轨迹
     * @return 发货单号
     */
    public ResultMode<Object> queryLogisticsTrail(LogisticsTrailRequest query) {
        return logisticsExchangeService.queryLogisticsTrail(query);
//        return orderShipDomainService.queryLogisticsTrail(query);
    }

    /**
     * 校验企业是否入驻
     */
    public ResultMode<Void> verifyCompany() {
        return orderShipDomainService.verifyCompany();
    }

    /**
     * 查询物流轨迹节点
     *
     * @param request 查询物流轨迹节点
     */
    public ResultMode<Object> logisticsTrackingNodesList(LogisticsTrackingNodesRequest request) {
        return logisticsExchangeService.logisticsTrackingNodesList(request);
    }
}
