package com.wanlianyida.app.application.service.shop;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.UmShopInter;
import com.wanlianyida.partner.api.model.dto.ShopDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class ShopAppService {
    @Resource
    private UmShopInter umShopInter;
    /**
     * 根据店铺id/公司id查询店铺信息
     *
     * @return {@code ResultMode<UmShopDTO> }
     */
    public ResultMode<ShopDTO> shopDetailByShopIdOrCompanyId(Long shopId, String companyId) {
        return umShopInter.shopDetailByShopIdOrCompanyId(shopId, companyId);
    }

}
