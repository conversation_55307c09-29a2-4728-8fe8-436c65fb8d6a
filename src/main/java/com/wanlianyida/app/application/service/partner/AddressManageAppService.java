package com.wanlianyida.app.application.service.partner;

import com.wanlianyida.app.infrastructure.exchange.BasicDataExchangeService;
import com.wanlianyida.basicdata.model.dto.PlatformCmCityDTO;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.partner.api.inter.AddressManageInter;
import com.wanlianyida.partner.api.model.command.AddressManageAddCommand;
import com.wanlianyida.partner.api.model.command.AddressManageDeleteCommand;
import com.wanlianyida.partner.api.model.command.AddressManageUpdateCommand;
import com.wanlianyida.partner.api.model.dto.AddressManageDTO;
import com.wanlianyida.partner.api.model.query.AddressManageListQuery;
import com.wanlianyida.partner.api.model.query.AddressManagePageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class AddressManageAppService {

    @Resource
    private AddressManageInter addressManageInter;

    @Resource
    private BasicDataExchangeService basicDataExchangeService;
    /**
     * 新增地址
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> add(AddressManageAddCommand command){
        return addressManageInter.add(command);
    }

    /**
     * 更新地址
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> update(AddressManageUpdateCommand command){
        return addressManageInter.update(command);
    }

    /**
     * 删除地址
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> delete(AddressManageDeleteCommand command){
        return addressManageInter.delete(command);
    }

    /**
     * 查询列表
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link List }<{@link AddressManageDTO }>>
     */
    public ResultMode<List<AddressManageDTO>> queryList(AddressManageListQuery query){
        if (JwtUtil.getTokenInfo().getCompanyId() == null) {
            return ResultMode.fail("尚未获取公司信息，请先维护公司信息");
        }
        return addressManageInter.queryList(query);
    }

    /**
     * 查询详情
     *
     * @param id 身份证
     * @return {@link ResultMode }<{@link AddressManageDTO }>
     */
    public ResultMode<AddressManageDTO> detail(Long id){
        return addressManageInter.detail(id);
    }

    /**
     * 根据父节点获取城市数据
     *
     * @param parentNode 父节点
     * @return {@code ResultMode<PlatformCmCity>}
     */
    public ResultMode<List<PlatformCmCityDTO>> getCityData(String parentNode){
        return basicDataExchangeService.getCityData(parentNode);
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    public ResultMode queryPage(PagingInfo<AddressManagePageQuery> pagingInfo){
        return addressManageInter.queryPage(pagingInfo);
    }
}
