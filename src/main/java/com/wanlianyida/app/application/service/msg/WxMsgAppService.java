package com.wanlianyida.app.application.service.msg;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.application.assembler.WxMsgAssembler;
import com.wanlianyida.app.application.model.bo.OrderInfoBO;
import com.wanlianyida.app.application.model.bo.WxUserAuthBO;
import com.wanlianyida.app.infrastructure.config.AppConfig;
import com.wanlianyida.app.infrastructure.exception.CtpOrchUserExceptionEnum;
import com.wanlianyida.app.infrastructure.exchange.*;
import com.wanlianyida.app.interfaces.model.command.AbleSubscribeMsgCommand;
import com.wanlianyida.app.interfaces.model.command.BindWxCommand;
import com.wanlianyida.app.interfaces.model.command.ReportSubscriptionCommand;
import com.wanlianyida.app.interfaces.model.dto.AbleSubscribeMsgDTO;
import com.wanlianyida.app.interfaces.model.dto.BindWxDTO;
import com.wanlianyida.basemdm.api.model.command.MiniProgramsAddCommand;
import com.wanlianyida.basemdm.api.model.dto.MdmOperatorDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmUserContactDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserContactQuery;
import com.wanlianyida.basemsg.api.model.command.WeixinSubscribeMsgCommand;
import com.wanlianyida.baseots.api.model.dto.WxUserAuthDTO;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.partner.api.model.dto.MemberConditionDTO;
import com.wanlianyida.support.api.model.command.RemoveSubscriptionCommand;
import com.wanlianyida.support.api.model.command.SubscribedMsgCommand;
import com.wanlianyida.support.api.model.command.WxMsgCommand;
import com.wanlianyida.support.api.model.dto.SubscribedMsgDTO;
import com.wanlianyida.support.api.model.enums.ExtendOrderReceiverEnum;
import com.wanlianyida.support.api.model.enums.MsgReceiverEnum;
import com.wanlianyida.support.api.model.enums.WxMsgTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 微信消息订阅
 * <AUTHOR>
 */
@Service
@Slf4j
public class WxMsgAppService {

    @Resource
    private FssWxMsgExchangeService fssWxMsgExchangeService;

    @Resource
    private MdmExchangeService mdmExchangeService;

    @Resource
    private SupportWxMsgExchangeService supportWxMsgExchangeService;

    @Resource
    private OtsWxExchangeService otsWxExchangeService;

    @Resource
    private AppConfig appConfig;

    @Resource
    private MdmOperatorExchangeService operatorExchangeService;

    @Resource
    private MemberExchangeService memberExchangeService;

    @Resource
    private OrderExchangeService orderExchangeService;

    /**
     * 用户可以订阅的消息模板列表
     * @param command command
     * @return 可以订阅的消息模板列表
     */
    public ResultMode<AbleSubscribeMsgDTO> ableSubscribeMsg(AbleSubscribeMsgCommand command){
        return supportWxMsgExchangeService.ableSubscribeMsg(command);
    }

    /**
     * 用户上报订阅的消息模板
     * @param command command
     * @return void
     */
    public ResultMode<Void> reportSubscription(ReportSubscriptionCommand command){
        return supportWxMsgExchangeService.reportSubscription(command);
    }

    /**
     * 用户绑定微信小程序
     */
    public ResultMode<BindWxDTO> bindWx(BindWxCommand command){
        // 根据appId、code获取用户openId
        WxUserAuthBO bo = getWxOpenId(command);
        if(bo == null){
            return ResultMode.fail(CtpOrchUserExceptionEnum.OTS_OPENID_EMPTY.getCode(),CtpOrchUserExceptionEnum.OTS_OPENID_EMPTY.getMsg());
        }

        // 绑定微信小程序
        MiniProgramsAddCommand miniProgramsAddCommand = WxMsgAssembler.buildMiniProgramsAddCommand(command.getAppId(), bo, command.getUserId());
        ResponseMessage<Void> responseMessage =  mdmExchangeService.bindMiniPrograms(miniProgramsAddCommand);
        if(responseMessage != null && responseMessage.isSucceed()){
            log.info("绑定用户==调用远程mdm==绑定用户成功,miniProgramsAddCommand={}",miniProgramsAddCommand);
            return ResultMode.success(WxMsgAssembler.buildBindWxDTO(command.getAppId(),bo));
        } else {
            log.info("绑定用户==调用远程mdm==绑定用户失败,miniProgramsAddCommand={}",miniProgramsAddCommand);
            return ResultMode.fail(CtpOrchUserExceptionEnum.MDM_BIND_USER_FAIL.getCode(),CtpOrchUserExceptionEnum.MDM_BIND_USER_FAIL.getMsg());
        }
    }

    /**
     * 获取用户openId
     * @param command command
     * @return 用户openId
     */
    private WxUserAuthBO getWxOpenId(BindWxCommand command){
        ResponseMessage<List<WxUserAuthDTO>> wxOpenId = otsWxExchangeService.getWxOpenId(WxMsgAssembler.getWxUserAuthQuery(command.getAppId(), command.getCode()));
        if(wxOpenId != null) {
            log.info("绑定用户==调用远程ots返回值={}，command={}",wxOpenId,command);
        } else{
            log.info("绑定用户==调用远程ots没有返回值，command={}",command);
            return null;
        }
        if(!CollectionUtil.isEmpty(wxOpenId.getModel()) && !StringUtils.isBlank(wxOpenId.getModel().get(0).getOpenpid())){
            log.info("绑定用户==调用远程ots==获取到openId={},,command={}",wxOpenId.getModel().get(0).getOpenpid(),command);
            return WxUserAuthBO.builder().openpid(wxOpenId.getModel().get(0).getOpenpid()).unionid(wxOpenId.getModel().get(0).getUnionid()).build();
        }
        log.info("绑定用户==调用远程ots==没有获取到openId,command={}",command);
        return null;
    }

    /**
     * 预处理消息
     */
    public void preProcessWxMessage(WxMsgCommand msgCommand){
        if(msgCommand.getExtendReceiver() == null){
            log.info("WxMsgListener===预处理消息===无需预处理消息");
            return;
        }
        WxMsgCommand.ExtendReceiver extendReceiver = msgCommand.getExtendReceiver();

        if(!StringUtils.isBlank(extendReceiver.getExtendOrderReceiver()) && !StringUtils.isBlank(extendReceiver.getOrderNo())){
            OrderInfoBO orderInfoBO = orderExchangeService.getOrderSellerInfo(extendReceiver.getOrderNo());
            // 消息接收者自动填充订单的卖家创建人
            preProcessFillCreator(extendReceiver,orderInfoBO,msgCommand);

            // 订单买家创建人
            preProcessOrderBuyerCreator(extendReceiver,orderInfoBO,msgCommand);
        } else{
            log.info("WxMsgListener===预处理消息===扩展订单接收者-无需扩展,msgCommand={}",msgCommand);
        }
    }

    /**
     * 消息接收者自动填充订单的卖家创建人
     */
    private void preProcessFillCreator(WxMsgCommand.ExtendReceiver extendReceiver,OrderInfoBO orderInfoBO,WxMsgCommand msgCommand){
        // 消息接收者自动填充订单的卖家创建人
        if(orderInfoBO != null && !StringUtils.isBlank(orderInfoBO.getCreatorId()) &&
                ExtendOrderReceiverEnum.TYPE_10.getCode().equals(extendReceiver.getExtendOrderReceiver())){
            List<String> targetUserBaseIdList = msgCommand.getTargetUserBaseIdList();
            if(targetUserBaseIdList == null){
                targetUserBaseIdList = new LinkedList<>();
                targetUserBaseIdList.add(orderInfoBO.getCreatorId());
                msgCommand.setTargetUserBaseIdList(targetUserBaseIdList);
            } else {
                msgCommand.getTargetUserBaseIdList().add(orderInfoBO.getCreatorId());
            }
            log.info("WxMsgListener===预处理消息===扩展订单卖家创建人成功==订单号={},creatorId={},msgCommand={}",extendReceiver.getOrderNo(),orderInfoBO.getCreatorId(),msgCommand);
        } else {
            log.info("WxMsgListener===预处理消息===扩展订单卖家创建人失败==根据订单获取订单信息失败，creatorId={},msgCommand={}",extendReceiver.getOrderNo(),msgCommand);
        }
    }

    /**
     * 订单买家创建人
     */
    private void preProcessOrderBuyerCreator(WxMsgCommand.ExtendReceiver extendReceiver,OrderInfoBO orderInfoBO,WxMsgCommand msgCommand){
        // 消息接收者自动填充订单买家创建人(创建意向单的买家或者创建订单选择的买家)
        if(orderInfoBO != null && !StringUtils.isBlank(orderInfoBO.getBuyerOperatorId()) &&
                ExtendOrderReceiverEnum.TYPE_20.getCode().equals(extendReceiver.getExtendOrderReceiver())){
            List<String> targetUserBaseIdList = msgCommand.getTargetUserBaseIdList();
            if(targetUserBaseIdList == null){
                targetUserBaseIdList = new LinkedList<>();
                targetUserBaseIdList.add(orderInfoBO.getBuyerOperatorId());
                msgCommand.setTargetUserBaseIdList(targetUserBaseIdList);
            } else {
                msgCommand.getTargetUserBaseIdList().add(orderInfoBO.getBuyerOperatorId());
            }
            log.info("WxMsgListener===预处理消息===扩展订单买家创建人成功==订单号={},买家创建人={},msgCommand={}",extendReceiver.getOrderNo(),orderInfoBO.getBuyerOperatorId(),msgCommand);
        } else {
            log.info("WxMsgListener===预处理消息===扩展订单买家创建人失败==根据订单获取订单信息失败，订单号={},msgCommand={}",extendReceiver.getOrderNo(),msgCommand);
        }
    }

    /**
     * 处理微信消息
     * @param msgCommand command
     */
    public void processWxMessage(WxMsgCommand msgCommand){
        // 参数校验
        if(!paramCheck(msgCommand)){
            return;
        }
        log.info("WxMsgListener===处理消息===参数校验通过，msgCommand={}",msgCommand);

        // 获取appId
        String appId = getAppId(msgCommand);

        // 获取userIdList
        List<String> userIdList = getUserIdList(msgCommand);
        log.info("WxMsgListener===处理消息===获取userIdList，用户id集合={}，msgCommand={}",userIdList,msgCommand);

        // 业务校验
        if(!bizCheck(appId,userIdList)){
            log.info("WxMsgListener===处理消息===业务校验===业务校验未通过===msgCommand={}",msgCommand);
            return;
        }

        // 获取订阅本模板的用户集合（传参用户集合中没订阅的不发送消息）
        List<String> userIdReceiver  = filterUserIdList(userIdList, msgCommand.getTemplateEnum());
        log.info("WxMsgListener===处理消息===获取userIdList，过滤后用户id集合={}，msgCommand={}",userIdReceiver,msgCommand);

        if(CollectionUtil.isEmpty(userIdReceiver)){
            log.info("WxMsgListener===处理消息===获取userIdList，过滤后用户id集合为空，无需发送消息，msgCommand={}",msgCommand);
            return;
        }

        // 获取openId集合
        List<String> openIdList = getOpenIdList(userIdReceiver, appId,msgCommand);
        log.info("WxMsgListener===处理消息===获取openIdList集合最终结果={}，msgCommand={}",openIdList,msgCommand);

        if(CollectionUtil.isEmpty(openIdList)){
            log.info("WxMsgListener===处理消息===获取openIdList为空，无需发送消息，msgCommand={}",msgCommand);
            return;
        }

        // 组装发送消息command
        List<WeixinSubscribeMsgCommand> commandList = WxMsgAssembler.buildSubscribeMsgCommand(appId, openIdList, msgCommand, getMiniProgramState());

        // 发送消息
        doSendMsg(commandList,msgCommand);

        // 后置处理
        postProcessing(userIdReceiver, msgCommand.getTemplateEnum(),msgCommand);
    }

    /**
     * 过滤消息发送用户（没有订阅模板的用户不发送消息）
     * @param userIdList 传递过来的用户id
     * @param templateEnum 模板
     * @return 应该发送的用户id
     */
    private List<String> filterUserIdList(List<String> userIdList,WxMsgTemplateEnum templateEnum){
        List<String> userIdListFilter = new ArrayList<>();
        SubscribedMsgCommand command = new SubscribedMsgCommand();
        command.setUserIdList(userIdList);
        command.setMessageReceiver(templateEnum.getMessageReceiver());
        ResultMode<SubscribedMsgDTO> resultMode = supportWxMsgExchangeService.subscribedMsg(command);
        if(resultMode != null && resultMode.getModel() != null && resultMode.getModel().getTemplateMap() != null){
            Map<String, List<String>> templateMap = resultMode.getModel().getTemplateMap();
            Set<String> userIdSet = templateMap.keySet();
            for (String userId : userIdSet) {
                if(templateMap.get(userId) != null && templateMap.get(userId).contains(templateEnum.getWxTemplateId())){
                    userIdListFilter.add(userId);
                }
            }
        }
        return userIdListFilter;
    }

    /**
     * 后置处理
     */
    private void postProcessing(List<String> userIdList, WxMsgTemplateEnum templateEnum, WxMsgCommand msgCommand){
        if(CollectionUtil.isEmpty(userIdList)){
            log.info("WxMsgListener===处理消息==后置处理===过滤后发送对象用户集合为空，无需后置处理，msgCommand={}",msgCommand);
            return;
        }
        RemoveSubscriptionCommand removeSubscriptionCommand = new RemoveSubscriptionCommand();
        removeSubscriptionCommand.setUserIdList(userIdList);
        removeSubscriptionCommand.setTemplateId(templateEnum.getWxTemplateId());
        removeSubscriptionCommand.setMessageReceiver(templateEnum.getMessageReceiver());
        supportWxMsgExchangeService.removeSubscription(removeSubscriptionCommand);
        log.info("WxMsgListener===处理消息==后置处理成功，removeSubscriptionCommand={}，msgCommand={}",removeSubscriptionCommand,msgCommand);
    }

    /**
     * 发送消息
     */
    private void doSendMsg(List<WeixinSubscribeMsgCommand> commandList,WxMsgCommand msgCommand){
        if(CollectionUtil.isEmpty(commandList)){
            return;
        }
        commandList.forEach(command -> {
            ResponseMessage<?> responseMessage = fssWxMsgExchangeService.sendMessage(command);
            if(responseMessage != null && !responseMessage.isSucceed()){
                log.info("WxMsgListener===处理消息===发送消息===返回失败,command={},msgCommand={}", command,msgCommand);
            } else{
                log.info("WxMsgListener===处理消息===发送消息===发送消息成功，command={},msgCommand={}", command,msgCommand);
            }
        });
    }

    /**
     * 获取openId集合
     */
    private List<String> getOpenIdList(List<String> userIdList,String appId,WxMsgCommand msgCommand){
        List<String> distinctUerIdList = userIdList.stream().distinct().collect(Collectors.toList());
        List<String> openIdList = new LinkedList<>();
        for (String userId : distinctUerIdList) {
            MdmUserContactQuery query = new MdmUserContactQuery();
            query.setUserId(Long.valueOf(userId));
            query.setCategory("40");
            log.info("WxMsgListener===处理消息===获取openIdList集合===调用远程接口传参={}，msgCommand={}",query,msgCommand);
            List<MdmUserContactDTO> mdmUserContactDTOList = mdmExchangeService.queryCmdUserContactList(query);
            if(CollectionUtil.isEmpty(mdmUserContactDTOList)){
                log.info("WxMsgListener===处理消息===获取openIdList集合===调用远程接口返回结果为空={}，msgCommand={},appId={}",query,msgCommand,appId);
                continue;
            }
            MdmUserContactDTO mdmUserContactDTO = mdmUserContactDTOList.get(0);
            if(!StringUtils.isBlank(mdmUserContactDTO.getContactInfo())){
                log.info("WxMsgListener===处理消息===获取openIdList集合===调用远程接口,返回结果contactInfo信息={}，query={},msgCommand={}",mdmUserContactDTO.getContactInfo(),query,msgCommand);
                String openId = getOpenId(mdmUserContactDTO.getContactInfo(), appId);
                if(!StringUtils.isBlank(openId)){
                    openIdList.add(openId);
                    log.info("WxMsgListener===处理消息===获取openIdList集合===调用远程接口获取到openId={}，query={}，msgCommand={},appId={}",openId,query,msgCommand,appId);
                } else {
                    log.info("WxMsgListener===处理消息===获取openIdList集合===调用远程接口没有获取到openId，query={},appId={}，msgCommand={}", query,appId,msgCommand);
                }
            }
        }
        return openIdList;
    }

    /**
     * 获取openId
     * @param contactInfo 中台给出的格式：category=40时为微信小程序， contactInfo格式为"appid:openid,appid:openid,appid:openid"
     * @param appId appId
     */
    private String getOpenId(String contactInfo, String appId) {
        if(StringUtils.isBlank(contactInfo) || StringUtils.isBlank(appId)){
            return "";
        }
        // contactInfo,分隔
        String[] contactInfoArr = contactInfo.split(",");
        for (String contactInfoStr : contactInfoArr) {
            // contactInfoStr,分隔
            String[] contactInfoStrArr = contactInfoStr.split(":");
            if(contactInfoStrArr.length != 2){
                continue;
            }
            if(appId.equals(contactInfoStrArr[0])){
                return contactInfoStrArr[1];
            }
        }
        return "";
    }

    /**
     * 业务校验
     */
    private Boolean bizCheck(String appId, List<String> userIdList) {
        // appId为空
        if(StringUtils.isBlank(appId)){
            log.info("WxMsgListener===业务校验===appId为空");
            return false;
        }
        // userIdList为空
        if(CollectionUtil.isEmpty(userIdList)){
            log.info("WxMsgListener===业务校验===userIdList为空");
            return false;
        }
        return true;
    }

    /**
     * 前置校验
     */
    private Boolean paramCheck(WxMsgCommand msgCommand) {
        // 模板枚举为空
        if(msgCommand.getTemplateEnum() == null){
            log.info("WxMsgListener===处理消息===参数校验未通过===模板为空");
            return false;
        }
        // 业务参数为空
        if(msgCommand.getParamMap() == null){
            log.info("WxMsgListener===处理消息===参数校验未通过===业务参数为空");
            return false;
        }
        // 发送对象为空
        if(StringUtils.isBlank(msgCommand.getCompanyId())
                && CollectionUtil.isEmpty(msgCommand.getTargetUserIdList())
                && CollectionUtil.isEmpty(msgCommand.getTargetUserBaseIdList())){
            log.info("WxMsgListener===处理消息===参数校验未通过===发送对象为空");
            return false;
        }
        return true;
    }

    /**
     * 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
     */
    private String getMiniProgramState(){
        // 从配置文件中取
        return appConfig.getMiniProgramState();
    }

    /**
     * 获取appId
     * @return appId
     */
    private String getAppId(WxMsgCommand msgCommand){
        String appId = "";
        // 买家
        if(MsgReceiverEnum.TYPE_10.getCode().equals(msgCommand.getTemplateEnum().getMessageReceiver())){
            appId = appConfig.getBuyerAppId();
            log.info("WxMsgListener===处理消息===获取appId，消息接收方是买家，appId={}，msgCommand={}",appId,msgCommand);
        } else {
            // 卖家
            appId = appConfig.getSellerAppId();
            log.info("WxMsgListener===处理消息===获取appId，消息接收方是卖家，appId={}，msgCommand={}",appId,msgCommand);
        }
        return appId;
    }

    /**
     * 获取userIdList
     * @return userIdList
     */
    private List<String> getUserIdList(WxMsgCommand msgCommand){
        List<String> userIdList = new LinkedList<>();
        // 传参过来的userId
        if(!CollectionUtil.isEmpty(msgCommand.getTargetUserIdList())){
            userIdList.addAll(msgCommand.getTargetUserIdList());
        }

        // 填充管理员的userId
        getUserBaseIdList(msgCommand,userIdList);

        if(!CollectionUtil.isEmpty(msgCommand.getTargetUserBaseIdList())){
            // 调用远程获取userId
            for (String userBaseId : msgCommand.getTargetUserBaseIdList()) {
                log.info("WxMsgListener===调用远程获取userId，传参userBaseId={}",userBaseId);
                ResponseMessage<MdmOperatorDTO> byOperatorCode = operatorExchangeService.getByOperatorCode(userBaseId);
                if(byOperatorCode != null){
                    log.info("WxMsgListener===调用远程获取userId返回={}，传参userBaseId={}",byOperatorCode,userBaseId);
                } else {
                    log.info("WxMsgListener===调用远程获取userId失败，传参userBaseId={}",userBaseId);
                    continue;
                }
                if(byOperatorCode.getModel() != null && !StringUtils.isBlank(byOperatorCode.getModel().getUserId())){
                    userIdList.add(byOperatorCode.getModel().getUserId());
                    log.info("WxMsgListener===调用远程获取userId成功，传参userBaseId={}，返回userId={}",userBaseId,byOperatorCode.getModel().getUserId());
                } else {
                    log.info("WxMsgListener===调用远程获取userId失败，传参userBaseId={}",userBaseId);
                }
            }
        }
        return userIdList;
    }

    /**
     * 填充管理员的userId
     */
    private void getUserBaseIdList(WxMsgCommand msgCommand,List<String> userIdList){
        // 公司id
        String companyId = msgCommand.getCompanyId();
        if(!StringUtils.isBlank(companyId)){
            log.info("WxMsgListener===处理消息===获取管理员userId，公司id={}，===msgCommand={}",companyId,msgCommand);
            // 获取公司id的管理员userBaseId
            ResultMode<List<MemberConditionDTO>> admins = memberExchangeService.queryByCondition(WxMsgAssembler.buildMemberQuery(companyId));

            if(admins != null && !CollectionUtil.isEmpty(admins.getModel())){
                MemberConditionDTO admin = IterUtil.getFirst(admins.getModel());
                userIdList.add(admin.getUserId());
                log.info("WxMsgListener===获取管理员userId===根据公司id查询到企业管理员信息，公司id: {},查询结果: {}，msgCommand={}", companyId, JSONUtil.toJsonStr(admin),msgCommand);
            } else {
                log.info("WxMsgListener===获取管理员userId===根据公司id没有查询到企业管理员信息，公司id: {}，msgCommand={}", companyId,msgCommand);
            }
        } else {
            log.info("WxMsgListener===处理消息===获取管理员userId===无需获取管理员userId，msgCommand={}",msgCommand);
        }
    }

}




