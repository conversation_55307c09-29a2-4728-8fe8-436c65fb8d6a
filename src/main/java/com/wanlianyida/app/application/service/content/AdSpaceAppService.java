package com.wanlianyida.app.application.service.content;

import com.wanlianyida.app.infrastructure.exchange.BaseContentExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.dto.AdSpaceDTO;
import com.wanlianyida.fssbasecontent.api.model.query.AdSpaceListQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class AdSpaceAppService {

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    /**
     * 查询列表
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link List }<{@link AdSpaceDTO }>>
     */
    public ResultMode<List<AdSpaceDTO>> queryList(AdSpaceListQuery query) {
        return baseContentExchangeService.addSpaceInterQueryList(query);
    }
}
