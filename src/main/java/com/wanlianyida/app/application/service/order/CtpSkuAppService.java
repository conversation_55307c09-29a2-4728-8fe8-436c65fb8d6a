package com.wanlianyida.app.application.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.infrastructure.enums.OrderTypeEnum;
import com.wanlianyida.app.infrastructure.exchange.BasicDataExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ProductExchangeService;
import com.wanlianyida.app.interfaces.dto.SkuAggregationDTO;
import com.wanlianyida.app.interfaces.model.dto.CtpSkuInfoDTO;
import com.wanlianyida.app.interfaces.model.query.SkuQuery;
import com.wanlianyida.basicdata.model.dto.PlatformCmCityDTO;
import com.wanlianyida.basicdata.model.query.CityNodesQuery;
import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.file.query.FileUrlsQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.product.api.inter.ProductBrandInter;
import com.wanlianyida.product.api.inter.ProductCategoryManagerInter;
import com.wanlianyida.product.api.model.dto.ProductBrandDTO;
import com.wanlianyida.product.api.model.dto.ProductCategoryDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.search.api.IEsStpSkuService;
import com.wanlianyida.search.dto.CtpSkuAggregationDTO;
import com.wanlianyida.search.dto.CtpSkuDTO;
import com.wanlianyida.search.query.CtpSkuQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CtpSkuAppService {
    @Resource
    private IEsStpSkuService esStpSkuService;
    @Resource
    private ProductBrandInter productBrandInter;
    @Resource
    private ProductCategoryManagerInter productCategoryManagerInter;
    @Resource
    private BasicDataExchangeService basicDataExchangeService;
    @Resource
    private IUploadService uploadService;
    @Resource
    private ProductExchangeService productExchangeService;

    // 定义所有排序类型的映射关系
    private static final Map<Integer, String> SORT_STRATEGY_MAP = new HashMap<>();

    static {
        SORT_STRATEGY_MAP.put(OrderTypeEnum.PRICE_ASC.getCode(), "priceFee asc");
        SORT_STRATEGY_MAP.put(OrderTypeEnum.PRICE_DESC.getCode(), "priceFee desc");
        SORT_STRATEGY_MAP.put(OrderTypeEnum.ON_SALE_DATE_ASC.getCode(), "onSaleDate asc");
        SORT_STRATEGY_MAP.put(OrderTypeEnum.ON_SALE_DATE_DESC.getCode(), "onSaleDate desc");
        SORT_STRATEGY_MAP.put(OrderTypeEnum.DEFAULT_SORT.getCode(), "productScore desc,onSaleDate desc");
    }

    public ResponseMessage<List<CtpSkuInfoDTO>> queryPage(PagingInfo<SkuQuery> query) {

        PagingInfo<CtpSkuQuery> ctpSkuQuery = new PagingInfo<>(
                BeanUtil.toBean(query.getFilterModel(), CtpSkuQuery.class),
                query.getCurrentPage(),
                query.getPageLength(),
                query.getCountTotal());

        //获取商品列表
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (StrUtil.isNotBlank(companyId)) {
            ctpSkuQuery.getFilterModel().setPublishCompanyId(companyId);
        }
        List<CtpSkuInfoDTO> ctpSkuInfoDTOS = new ArrayList<>();
        //设置排序策略
        Integer orderType = query.getFilterModel().getOrderType();
        String strategy = SORT_STRATEGY_MAP.getOrDefault(orderType, ("productScore desc,onSaleDate desc"));
        ctpSkuQuery.getFilterModel().setSortStrategy(strategy);

        ResponseMessage<List<CtpSkuDTO>> ctpskuResultMode = esStpSkuService.queryPage(ctpSkuQuery);
        List<CtpSkuDTO> ctpSkuDTOList = ctpskuResultMode.getModel();
        if (CollUtil.isEmpty(ctpSkuDTOList)) return ResponseMessage.successPageList(ctpSkuInfoDTOS, ctpskuResultMode.getTotal());

        List<ProductSkuDetailDTO> productSkuDetailDTOS = productExchangeService.queryProductSkuDetail(
                ctpSkuDTOList.stream().map(CtpSkuDTO::getSkuCode).collect(Collectors.toList()));

        log.info("商品列表查询参数：{},返回结果：{}", JSONUtil.toJsonStr(query), JSONUtil.toJsonStr(productSkuDetailDTOS));

        Map<String, ProductSkuDetailDTO> skuCodeMap = productSkuDetailDTOS.stream()
                .collect(Collectors.toMap(ProductSkuDetailDTO::getSkuCode,Function.identity(),  (old, now) -> old));
        ctpSkuInfoDTOS.addAll(BeanUtil.copyToList(ctpSkuDTOList, CtpSkuInfoDTO.class));
        for (CtpSkuInfoDTO ctpSkuInfoDTO : ctpSkuInfoDTOS) {
            ProductSkuDetailDTO productSkuDetailDTO = skuCodeMap.get(ctpSkuInfoDTO.getSkuCode());
            if (Objects.nonNull(productSkuDetailDTO)) {
                ctpSkuInfoDTO.setMinQuantity(productSkuDetailDTO.getMinQuantity());
            }
        }
        //获取压缩图
        List<String> skuUrlList = ctpSkuInfoDTOS.stream().map(CtpSkuDTO::getSkuUrl).collect(Collectors.toList());
        ResponseMessage<Map<String, String>> fileResultMode = uploadService.getCompressedUrls(new FileUrlsQuery(skuUrlList));
        Map<String, String> fileMap = fileResultMode.getModel();
        if (fileMap == null) return ResponseMessage.successPageList(ctpSkuInfoDTOS, ctpskuResultMode.getTotal());

        //组装带压缩图的商品列表
        List<CtpSkuInfoDTO> newList = ctpSkuInfoDTOS.stream().map(sku -> {
            sku.setSkuUrl(fileMap.get(sku.getSkuUrl()));
            return sku;
        }).collect(Collectors.toList());
        return ResponseMessage.successPageList(newList, ctpskuResultMode.getTotal());
    }
    /**
     * 自营商品聚合列表
     */
    public ResponseMessage<SkuAggregationDTO> queryAggregation(CtpSkuQuery query) {
        SkuAggregationDTO dto = new SkuAggregationDTO();
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (StrUtil.isNotBlank(companyId)) {
            if (query == null) {
                query = new CtpSkuQuery();
            }
            query.setPublishCompanyId(companyId);
        }




        ResponseMessage<CtpSkuAggregationDTO> resultMode = esStpSkuService.queryAggregation(query);
        CtpSkuAggregationDTO originalDTO = resultMode.getModel();
        if (originalDTO == null || originalDTO.getTotalCount() == 0) {
            return ResponseMessage.success(dto);
        }
        //总记录数
        dto.setTotalCount(originalDTO.getTotalCount());

        // 品牌列表
        ResultMode<List<ProductBrandDTO>> productBrandResultMode = productBrandInter.queryByIds(originalDTO.getBrandIdList());
        List<ProductBrandDTO> productBrandList = productBrandResultMode.getModel();
        Map<Long, String> brandMap = productBrandList.stream().collect(Collectors.toMap(ProductBrandDTO::getId, ProductBrandDTO::getBrandName));
        List<SkuAggregationDTO.Brand> brandList = originalDTO.getBrandIdList().stream().filter(x -> x != 1).map(brandId -> {
            SkuAggregationDTO.Brand brand = new SkuAggregationDTO.Brand();
            brand.setBrandId(brandId);
            brand.setBrandName(brandMap.get(brandId));
            return brand;
        }).collect(Collectors.toList());
        dto.setBrandList(brandList);

        //交货地城市列表
        CityNodesQuery cityNodesQuery = new CityNodesQuery();
        cityNodesQuery.setNodes(originalDTO.getDeliveryAddrCityCodeList());
        ResultMode<List<PlatformCmCityDTO>> cityResultMode = basicDataExchangeService.getCityDataByNodes(cityNodesQuery);
        List<PlatformCmCityDTO> cityList = cityResultMode.getModel();
        Map<String, String> cityMap = cityList.stream().collect(Collectors.toMap(PlatformCmCityDTO::getNode, PlatformCmCityDTO::getName));
        List<SkuAggregationDTO.City> deliveryAddrCityList = originalDTO.getDeliveryAddrCityCodeList().stream().map(cityCode -> {
            SkuAggregationDTO.City city = new SkuAggregationDTO.City();
            city.setCityCode(cityCode);
            city.setCityName(cityMap.get(cityCode));
            return city;
        }).collect(Collectors.toList());
        dto.setDeliveryAddrCityList(deliveryAddrCityList);

        //品类列表
        List<Long> categoryIdList = new ArrayList<>();
        originalDTO.getCategoryList().forEach(ca -> {
            categoryIdList.add(ca.getCategoryId());
            ca.getChildCategoryList().forEach(childCa -> {
                categoryIdList.add(childCa.getCategoryId());
                childCa.getChildCategoryList().forEach(grandsonCa -> {
                    categoryIdList.add(grandsonCa.getCategoryId());
                });
            });
        });
        ResultMode<List<ProductCategoryDTO>> productCategoryResultMode = productCategoryManagerInter.queryByIds(categoryIdList);
        List<ProductCategoryDTO> productCategoryList = productCategoryResultMode.getModel();
        Map<Long, ProductCategoryDTO> categoryMap = productCategoryList.stream().collect(Collectors.toMap(ProductCategoryDTO::getId, Function.identity()));

        //一级品类
        List<SkuAggregationDTO.Category> categoryList = originalDTO.getCategoryList().stream().map(ca -> {
            SkuAggregationDTO.Category category = new SkuAggregationDTO.Category();
            category.setCategoryId(ca.getCategoryId());
            ProductCategoryDTO categoryDTO = categoryMap.get(ca.getCategoryId());
            if (categoryDTO != null) {
                category.setCategoryLevel(categoryDTO.getLevel());
                category.setCategoryName(categoryDTO.getCategoryName());
            }
            //二级品类
            List<SkuAggregationDTO.Category> childCategoryList = ca.getChildCategoryList().stream().map(childCa -> {
                SkuAggregationDTO.Category childCategory = new SkuAggregationDTO.Category();
                childCategory.setCategoryId(childCa.getCategoryId());
                ProductCategoryDTO childCategoryDTO = categoryMap.get(childCa.getCategoryId());
                if (childCategoryDTO != null) {
                    childCategory.setCategoryLevel(childCategoryDTO.getLevel());
                    childCategory.setCategoryName(childCategoryDTO.getCategoryName());
                }
                //三级品类
                List<SkuAggregationDTO.Category> grandsonCategoryList = childCa.getChildCategoryList().stream().map(grandsonCa -> {
                    SkuAggregationDTO.Category grandsonCategory = new SkuAggregationDTO.Category();
                    grandsonCategory.setCategoryId(grandsonCa.getCategoryId());
                    ProductCategoryDTO grandsonCategoryDTO = categoryMap.get(grandsonCa.getCategoryId());
                    if (grandsonCategoryDTO != null) {
                        grandsonCategory.setCategoryLevel(grandsonCategoryDTO.getLevel());
                        grandsonCategory.setCategoryName(grandsonCategoryDTO.getCategoryName());
                    }
                    return grandsonCategory;
                }).collect(Collectors.toList());
                childCategory.setChildCategoryList(grandsonCategoryList);
                return childCategory;
            }).collect(Collectors.toList());
            category.setChildCategoryList(childCategoryList);
            return category;
        }).collect(Collectors.toList());
        dto.setCategoryList(categoryList);
        return ResponseMessage.success(dto);
    }
}
