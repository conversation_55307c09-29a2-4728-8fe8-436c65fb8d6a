package com.wanlianyida.app.application.service.partner;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.infrastructure.enums.RegisterResultEnum;
import com.wanlianyida.app.infrastructure.exchange.*;
import com.wanlianyida.app.interfaces.model.command.JsSignatureCommand;
import com.wanlianyida.app.interfaces.model.command.RegisterActivityUserCommand;
import com.wanlianyida.app.interfaces.model.dto.JsSignatureDTO;
import com.wanlianyida.app.interfaces.model.dto.RegisterActivityUserDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.baseots.api.model.dto.WxSignDTO;
import com.wanlianyida.basicdata.model.query.PlatformUmUserbaseinfoQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.partner.api.model.command.CmdUserInfoCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * @ClassName ActivityUserAppService
 * @Description
 * <AUTHOR>
 * @Veriosn 1.0
 **/

@Service
@Slf4j
public class ActivityUserAppService {

    @Resource
    private MdmExchangeService mdmExchangeService;

    @Resource
    private PersonalExchangeService personalExchangService;

    @Resource
    private BasicDataExchangeService basicDataExchangeService;

    @Resource
    private ActivityUserExchangeService activityUserExchangeService;

    @Resource
    private OtsWxExchangeService otsWxExchangeService;

    /**
     * 活动用户注册
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<RegisterActivityUserDTO> addUser(RegisterActivityUserCommand command) {
        RegisterActivityUserDTO userDTO = new RegisterActivityUserDTO();
        log.info("addUser#活动用户注册参数：{}", JSONUtil.toJsonStr(command));

        //1.校验验证码是否正确
        ResultMode<RegisterActivityUserDTO> resultMode = verifyCaptchaIsProper(command);
        if (!resultMode.isSucceed()){
            return resultMode;
        }

        //2.根据用户手机号查询
        queryWhetherRegistered(command,userDTO);
        //是否已经注册过
        if (userDTO.getRegisterResult() != null && userDTO.getRegisterResult().equals(RegisterResultEnum.EXIST.getCode())){
            return ResultMode.success(userDTO);
        }

        //3.注册用户
        registeredUsers(command,userDTO);
        //校验是否注册成功
        if (userDTO.getRegisterResult() != null && userDTO.getRegisterResult().equals(RegisterResultEnum.FAIL.getCode())){
            return ResultMode.success(userDTO);
        }

        //4.新增活动用户
        innerActivityUser(command);

        return ResultMode.success(userDTO);
    }


    /**
     * 校验验证码是否正确
     * @param command
     */
    public ResultMode<RegisterActivityUserDTO> verifyCaptchaIsProper(RegisterActivityUserCommand command) {
        PlatformUmUserbaseinfoQuery query = new PlatformUmUserbaseinfoQuery();
        query.setTelephone(command.getMobile());
        query.setMessageValidateCode(command.getCode());
        log.info("verifyCaptchaIsProper#校验验证码参数：{}", JSONUtil.toJsonStr(query));
        ResultMode<String> listResponseMessage = basicDataExchangeService.checkMessageValidateCode(query);
        log.info("verifyCaptchaIsProper#校验验证码结果：{}", JSONUtil.toJsonStr(listResponseMessage));
        if (!listResponseMessage.isSucceed()){
            return ResultMode.fail(listResponseMessage.getCode(), listResponseMessage.getMessage(),null);
        }
        return ResultMode.success();
    }


    /**
     * 新增活动用户
     * @param command
     */
    public void innerActivityUser(RegisterActivityUserCommand command) {
        log.info("innerActivityUser#新增活动用户参数：{}", JSONUtil.toJsonStr(command));
        ResultMode<Void> voidResultMode = activityUserExchangeService.addUser(command);
        log.info("innerActivityUser#新增活动用户结果：{}", JSONUtil.toJsonStr(voidResultMode));
    }


    /**
     * 注册用户
     * @param command
     * @param userDTO
     */
    public void registeredUsers(RegisterActivityUserCommand command, RegisterActivityUserDTO userDTO) {
        CmdUserInfoCommand cmdUserInfoCommand = new CmdUserInfoCommand();
        //手机号
        cmdUserInfoCommand.setPhone(command.getMobile());
        //状态10-注册
        cmdUserInfoCommand.setType("10");
        //密码
        cmdUserInfoCommand.setPassword(command.getPassword());
        //密码强度
        cmdUserInfoCommand.setPasswordStrength("20");
        log.info("registeredUsers#注册用户参数：{}", JSONUtil.toJsonStr(cmdUserInfoCommand));
        ResultMode<Map<String, String>> register = personalExchangService.register(cmdUserInfoCommand);
        log.info("registeredUsers#注册用户结果：{}", JSONUtil.toJsonStr(register));
        if (register.isSucceed()){
            //用户id
            if (StringUtils.isNotEmpty(register.getModel().get("userId"))){
                command.setUserId(register.getModel().get("userId"));
            }
            //登录账号
            if (StringUtils.isNotEmpty(register.getModel().get("loginName"))){
                command.setLoginAccount(register.getModel().get("loginName"));
            }
            userDTO.setRegisterResult(RegisterResultEnum.SUCCEED.getCode());
        }else {
            userDTO.setRegisterResult(RegisterResultEnum.FAIL.getCode());
        }
    }

    /**
     * 查询用户是否已经注册
     * @param command
     * @return
     */
    public void queryWhetherRegistered(RegisterActivityUserCommand command,RegisterActivityUserDTO userDTO) {
        log.info("queryWhetherRegistered#查询用户是否已经注册参数：{}", JSONUtil.toJsonStr(command));
        MdmUserInfoDTO userInfoDTO = mdmExchangeService.queryByUserPhone(command.getMobile());
        log.info("queryWhetherRegistered#查询用户是否已经注册结果：{}", JSONUtil.toJsonStr(userInfoDTO));
        if (userInfoDTO != null){
            if (userInfoDTO.getDelFlag().equals("0")){
                userDTO.setRegisterResult(RegisterResultEnum.EXIST.getCode());
            }
        }
    }


    /**
     * 获取js签名
     * @param command
     * @return
     */
    public ResultMode<JsSignatureDTO> jsSignature(JsSignatureCommand command) {
        JsSignatureDTO jsSignatureDTO  = new JsSignatureDTO();
        jsSignatureDTO.setAppId(command.getAppId());
        log.info("jsSignature#获取js签名参数：{}", JSONUtil.toJsonStr(command));
        ResponseMessage<List<WxSignDTO>> sign = otsWxExchangeService.getSign(command);
        log.info("jsSignature#获取js签名结果：{}", JSONUtil.toJsonStr(sign));
        if (sign.isSucceed()  && sign.getModel() != null){
            WxSignDTO signDTO = sign.getModel().get(0);
            //签名
            jsSignatureDTO.setSignature(signDTO.getSignature());
            //字符串
            jsSignatureDTO.setNoncestr(signDTO.getNoncestr());
            //时间戳
            jsSignatureDTO.setTimestamp(signDTO.getTimestamp());
        }

        return ResultMode.success(jsSignatureDTO);
    }
}

