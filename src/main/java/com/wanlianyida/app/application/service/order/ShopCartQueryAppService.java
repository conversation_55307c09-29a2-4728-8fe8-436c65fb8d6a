package com.wanlianyida.app.application.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.infrastructure.constant.Constant;
import com.wanlianyida.app.infrastructure.exchange.ProductPublishCompanyExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ShopExchangeService;
import com.wanlianyida.app.interfaces.model.dto.AppShopCartListDTO;
import com.wanlianyida.app.interfaces.model.dto.ShopDTO;
import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.file.query.FileUrlsQuery;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.order.api.model.dto.ShopCartDTO;
import com.wanlianyida.order.api.model.dto.ShopCartListDTO;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import com.wanlianyida.partner.api.model.dto.UmCompanyDTO;
import com.wanlianyida.product.api.inter.ProductInter;
import com.wanlianyida.product.api.model.dto.PcProductPublishCompanyDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuAttrDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.dto.ProductSpuDTO;
import com.wanlianyida.product.api.model.query.ProductSkuQuery;
import com.wanlianyida.product.api.model.query.PublishCompanyQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 购物车操作聚合成
 */
@Slf4j
@Component
public class ShopCartQueryAppService {

    @Resource
    private ProductInter productInter;

    @Resource
    private RedisService redisService;

    @Resource
    private UmCompanyInter umCompanyInter;

    @Resource
    private IUploadService iUploadService;

    @Resource
    private ShopExchangeService shopExchangeService;

    @Resource
    private ProductPublishCompanyExchangeService productPublishCompanyExchangeService;


    /**
     * 查询购物车列表
     *
     * @return
     */
    public ResultMode queryShopCartList() {
        //查询购物车信息
        Map<String, List<ShopCartDTO>> shopCartMap = getCarData();
        if (ObjUtil.isNull(shopCartMap)) {
            return ResultMode.success();
        }
        //查询商品信息
        Set<String> skuCodes = new HashSet<>();
        for (List<ShopCartDTO> value : shopCartMap.values()) {
            if (CollUtil.isEmpty(value)) {
                continue;
            }
            Set<String> skus = value.stream().map(d -> d.getSkuCode()).collect(Collectors.toSet());
            skuCodes.addAll(skus);
        }
        ProductSkuQuery query = new ProductSkuQuery();
        query.setSkuCodeList(new ArrayList<>(skuCodes));
        ResultMode<List<ProductSkuDetailDTO>> resultMode = productInter.queryProductSkuDetail(query);
        log.info("查询购物车列表商品信息查询参数：{},返回结果：{}", JSONUtil.toJsonStr(query), JSONUtil.toJsonStr(resultMode));
        List<ProductSkuDetailDTO> resultModeModel = resultMode.getModel();
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultModeModel)) {
            return ResultMode.success();
        }
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (ObjUtil.isNotEmpty(companyId)) {
            this.validPublishCompany(resultModeModel);
        }
        //构建购物车列表
        AppShopCartListDTO appShopCartListDTO = this.buildCarListData(resultModeModel, shopCartMap);
        ResultMode<Object> success = ResultMode.success();
        success.setModel(appShopCartListDTO);
        return success;
    }

    private void validPublishCompany(List<ProductSkuDetailDTO> resultMode) {
        List<String> spuCodes = resultMode.stream().filter(sku -> "20".equals(sku.getPublishType())).map(ProductSkuDetailDTO::getSpuCode).collect(Collectors.toList());
        if (CollUtil.isEmpty(spuCodes)) {
            return;
        }
        ResultMode<List<PcProductPublishCompanyDTO>> listResultMode = productPublishCompanyExchangeService.publishCompanyList(PublishCompanyQuery.builder().spuCodeList(spuCodes).build());
        List<PcProductPublishCompanyDTO> model = listResultMode.getModel();
        if (ObjUtil.isEmpty(model)) {
            return;
        }
        Map<String, List<String>> resultMap = model.stream()
                .collect(Collectors.groupingBy(
                        PcProductPublishCompanyDTO::getSpuCode,
                        Collectors.mapping(PcProductPublishCompanyDTO::getPublishCompanyId, Collectors.toList())
                ));
        log.info("validPublishCompany：{}", resultMap);
        for (ProductSkuDetailDTO productSkuDetailDTO : resultMode) {
            List<String> publishCompanyIdList = resultMap.get(productSkuDetailDTO.getSpuCode());
            if (CollUtil.isNotEmpty(publishCompanyIdList)) {
                productSkuDetailDTO.setPublishCompanyIdList(publishCompanyIdList);
            }
        }
    }

    /**
     * 构建购物车数据
     *
     * @param model
     * @param shopCartMap
     * @return
     */
    private AppShopCartListDTO buildCarListData(List<ProductSkuDetailDTO> model, Map<String, List<ShopCartDTO>> shopCartMap) {
        Map<String, ProductSkuDetailDTO> groupBySkuMap = model.stream().collect(Collectors.toMap(ProductSkuDetailDTO::getSkuCode, Function.identity()));

        //转图片链接
        List<String> picList = model.stream().map(sp -> sp.getPicUrl()).collect(Collectors.toList());
        FileUrlsQuery fileUrlsQuery = new FileUrlsQuery();
        fileUrlsQuery.setUrls(picList);
        ResponseMessage<Map<String, String>> picResultMode = iUploadService.getUrls(fileUrlsQuery);
        //图片信息
        Map<String, String> picMap = new HashMap<>();
        if (ObjUtil.isNotNull(picResultMode) && picResultMode.isSucceed()) {
            picMap = picResultMode.getModel();
        }
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        List<ShopCartListDTO> dtos = new ArrayList<>();
        for (Map.Entry<String, List<ShopCartDTO>> entry : shopCartMap.entrySet()) {
            List<ShopCartDTO> cartDTO = entry.getValue();
            ShopCartListDTO dto = new ShopCartListDTO();
            dto.setCompanyId(entry.getKey());
            ResultMode<UmCompanyDTO> resultMode = umCompanyInter.detail(Long.valueOf(entry.getKey()));
            String companyName = null;
            if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && ObjUtil.isNotNull(resultMode.getModel())) {
                companyName = resultMode.getModel().getCompanyName();
                dto.setCompanyName(companyName);
            }
            // 店铺信息
            ShopDTO shopInfo = shopExchangeService.getShopInfo(dto.getCompanyId());
            dto.setShopId(shopInfo.getShopId());
            dto.setShopName(shopInfo.getShopName());
            List<ShopCartListDTO.Product> products = new ArrayList<>();
            for (ShopCartDTO shopCartDTO : cartDTO) {
                ShopCartListDTO.Product product = new ShopCartListDTO.Product();
                product.setCompanyId(entry.getKey());
                product.setCompanyName(companyName);
                if (!groupBySkuMap.containsKey(shopCartDTO.getSkuCode())) {
                    continue;
                }
                ProductSkuDetailDTO skuDetailDTO = groupBySkuMap.get(shopCartDTO.getSkuCode());
                product.setSkuCode(skuDetailDTO.getSkuCode());
                product.setSkuName(skuDetailDTO.getSkuName());
                //查询图片
                ProductSpuDTO productSpu = skuDetailDTO.getProductSpu();
                //转图片
                product.setPicture(picMap.get(skuDetailDTO.getPicUrl()));
                List<ProductSkuAttrDTO> productSkuAttrList = skuDetailDTO.getProductSkuAttrList();
                if (CollUtil.isNotEmpty(productSkuAttrList)) {
                    Map<String, String> attrMap = productSkuAttrList.stream().collect(Collectors.toMap(ProductSkuAttrDTO::getAttributeName, ProductSkuAttrDTO::getValueText));
                    product.setAttr(attrMap);
                }
                product.setDeliveryPeriod(skuDetailDTO.getDeliveryPeriod());
                product.setPrice(skuDetailDTO.getPriceFee());
                product.setPricingUnit(productSpu.getRelPricingUnitId());
                product.setPurchaseQuantity(shopCartDTO.getPurchaseQuantity());
                product.setPurchaseQuantityUnit(productSpu.getRelMeasurementUnitId());
                product.setUnitTransfer(skuDetailDTO.getUnitTransfer());
                product.setMinQuantity(skuDetailDTO.getMinQuantity());
                product.setQuantity(skuDetailDTO.getQuantity());
                product.setSubTotal(shopCartDTO.getPurchaseQuantity().multiply(skuDetailDTO.getPriceFee()));
                product.setStatus(skuDetailDTO.getOnSaleStatus() + "");
                product.setShopId(dto.getShopId());
                product.setShopName(dto.getShopName());
                if (CollUtil.isNotEmpty(skuDetailDTO.getPublishCompanyIdList()) && !skuDetailDTO.getPublishCompanyIdList().contains(companyId)) {
                    product.setStatus("10");
                }
                product.setPublishCompanyIdList(ObjUtil.isEmpty(skuDetailDTO.getPublishCompanyIdList()) ? new ArrayList<>() : skuDetailDTO.getPublishCompanyIdList());
                products.add(product);
            }
            dto.setProducts(products);
            dtos.add(dto);
        }


        AppShopCartListDTO appShopCartListDTO = new AppShopCartListDTO();
        List<ShopCartListDTO> effectiveShoppingList = new ArrayList<>();
        List<AppShopCartListDTO.Product> InvalidShoppingList = new ArrayList<>();
        for (ShopCartListDTO dto : dtos) {
            List<ShopCartListDTO.Product> effectiveList = dto.getProducts().stream().filter(node -> !node.getStatus().equals("10")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(effectiveList))
                effectiveShoppingList.add(dto);
            List<ShopCartListDTO.Product> InvalidList = dto.getProducts().stream().filter(node -> node.getStatus().equals("10")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(InvalidList))
                InvalidShoppingList.addAll(BeanUtil.copyToList(InvalidList, AppShopCartListDTO.Product.class));
        }
        appShopCartListDTO.setEffectiveShoppingList(effectiveShoppingList);
        appShopCartListDTO.setInvalidShoppingList(InvalidShoppingList);
        return appShopCartListDTO;
    }

    /**
     * 查询购物车信息
     *
     * @return
     */
    private Map<String, List<ShopCartDTO>> getCarData() {
        String shopCarKey = Constant.RedisKey.SHOP_CAR_PREFIX;
        String userId = JwtUtil.getTokenInfo().getUserBaseId();
        String redisLockKey = shopCarKey + userId;
        Map<String, Object> map = redisService.hGetAll(redisLockKey);
        if (CollUtil.isEmpty(map)) {
            return null;
        }
        Map<String, List<ShopCartDTO>> shopCartMap = new HashMap<>();
        map.forEach((k, v) -> {
            shopCartMap.put(k, JSONUtil.toList(v.toString(), ShopCartDTO.class));
        });
        return shopCartMap;
    }
}
