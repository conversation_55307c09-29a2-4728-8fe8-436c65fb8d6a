package com.wanlianyida.app.application.service.dict;

import com.wanlianyida.app.infrastructure.exchange.BasicDataExchangeService;
import com.wanlianyida.basicdata.model.dto.PlatformCmCityDTO;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class CityAppService {

    @Resource
    private BasicDataExchangeService basicDataExchangeService;

    /**
     * 获取所有行政区划信息；无需token
     */
    public ResultMode<List<PlatformCmCityDTO>> getAllCmCity() {
        return basicDataExchangeService.getCityData("");
    }

    public ResultMode<List<PlatformCmCityDTO>> getCityData(String parentNode){
        return basicDataExchangeService.getCityData(parentNode);
    }
}
