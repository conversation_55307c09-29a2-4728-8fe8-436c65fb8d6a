package com.wanlianyida.app.application.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.app.infrastructure.exchange.EsExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ProductExchangeService;
import com.wanlianyida.app.interfaces.model.command.MoreInfoCommand;
import com.wanlianyida.app.interfaces.model.dto.ProductAggregationDTO;
import com.wanlianyida.app.interfaces.model.dto.RichTextDTO;
import com.wanlianyida.app.interfaces.model.query.ProductRichTextQuery;
import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.file.query.FileUrlsQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.product.api.inter.ProductInter;
import com.wanlianyida.product.api.model.dto.ProductMoreInfoDTO;
import com.wanlianyida.product.api.model.dto.ProductPortalPicDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuPortalDetailDTO;
import com.wanlianyida.product.api.model.query.ProductMoreInfoQuery;
import com.wanlianyida.product.api.model.query.ProductPortalPicQuery;
import com.wanlianyida.product.api.model.query.ProductPortalQuery;
import com.wanlianyida.search.api.IEsStpSkuService;
import com.wanlianyida.search.dto.CtpSkuDTO;
import com.wanlianyida.search.query.CtpSkuQuery;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月28日 13:15
 */
@Service
public class ProductAppService {

    @Resource
    private ProductInter productInter;

    @Resource
    private ProductExchangeService productExchangeService;

    @Resource
    private EsExchangeService esExchangeService;

    @Resource
    private IEsStpSkuService esStpSkuService;
    @Resource
    private IUploadService uploadService;

    /**
     * 自营商品列表
     */
    public ResponseMessage<List<CtpSkuDTO>> queryPage(PagingInfo<CtpSkuQuery> query) {
        //获取商品列表
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (StrUtil.isNotBlank(companyId)) {
            query.getFilterModel().setPublishCompanyId(companyId);
        }
        ResponseMessage<List<CtpSkuDTO>> ctpskuResultMode = esStpSkuService.queryPage(query);
        List<CtpSkuDTO> ctpSkuDTOList = ctpskuResultMode.getModel();
        if (CollUtil.isEmpty(ctpSkuDTOList)) return ctpskuResultMode;

        //获取缩略图
        List<String> skuUrlList = ctpSkuDTOList.stream().map(CtpSkuDTO::getSkuUrl).collect(Collectors.toList());
        ResponseMessage<Map<String, String>> fileResultMode = uploadService.getThumbnailUrls(new FileUrlsQuery(skuUrlList));
        Map<String, String> fileMap = fileResultMode.getModel();
        if (fileMap == null) return ctpskuResultMode;

        //组装带缩略图的商品列表
        List<CtpSkuDTO> newList = ctpSkuDTOList.stream().map(sku -> {
            sku.setSkuUrl(fileMap.get(sku.getSkuUrl()));
            return sku;
        }).collect(Collectors.toList());
        return ResponseMessage.successPageList(newList, ctpskuResultMode.getTotal());
    }

    public ResultMode<ProductSkuPortalDetailDTO> querySkuPortalDetail(ProductPortalQuery query) {
        return productExchangeService.querySkuPortalDetail(query);
    }

    public ResultMode<ProductMoreInfoDTO> queryProductMoreInfo(MoreInfoCommand infoQuery) {
        ProductMoreInfoQuery moreInfoQuery = new ProductMoreInfoQuery();
        moreInfoQuery.setPic(infoQuery.getPic());
        moreInfoQuery.setQualification(infoQuery.getQualification());
        moreInfoQuery.setSpuCode(infoQuery.getSkuCode().substring(0, 10));
        return productInter.queryProductMoreInfo(moreInfoQuery);
    }

    public ResultMode<ProductPortalPicDTO> queryProductPic(ProductPortalPicQuery query) {
        return productInter.queryPortalProductPic(query);
    }

    public ResultMode<RichTextDTO> queryProductRichText(ProductRichTextQuery query) {
        String productRichText = esExchangeService.getProductRichText(query.getSkuCode().substring(0, 10));
        RichTextDTO richTextDTO = new RichTextDTO();
        richTextDTO.setContent(productRichText);
        return ResultMode.success(richTextDTO);
    }

    public ResultMode<ProductAggregationDTO> queryProductAggregationDetail(ProductPortalQuery query) {
        ProductAggregationDTO productAggregationDTO = new ProductAggregationDTO();
        ResultMode<RichTextDTO> richTextDTOResultMode = this.queryProductRichText(new ProductRichTextQuery().setSkuCode(query.getSkuCode()));
        ProductPortalPicQuery productPortalPicQuery = new ProductPortalPicQuery();
        productPortalPicQuery.setSkuCode(query.getSkuCode());
        ResultMode<ProductPortalPicDTO> productPortalPicDTO = this.queryProductPic(productPortalPicQuery);
        ResultMode<ProductSkuPortalDetailDTO> productSkuPortalDetailDTO = this.querySkuPortalDetail(query);
        productAggregationDTO.setRichTextDTO(richTextDTOResultMode.getModel());
        productAggregationDTO.setProductPortalPicDTO(productPortalPicDTO.getModel());
        productAggregationDTO.setProductSkuPortalDetailDTO(productSkuPortalDetailDTO.getModel());
        return ResultMode.success(productAggregationDTO);
    }
}
