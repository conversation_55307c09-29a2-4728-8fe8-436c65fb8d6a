package com.wanlianyida.app.application.service.purchase;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.app.infrastructure.enums.UserErrorCode;
import com.wanlianyida.app.infrastructure.exchange.ProductExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ProductPublishCompanyExchangeService;
import com.wanlianyida.app.infrastructure.exchange.ShopExchangeService;
import com.wanlianyida.app.interfaces.model.dto.ShopDTO;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.product.api.model.dto.PcProductPublishCompanyDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.query.PublishCompanyQuery;
import com.wanlianyida.transaction.api.inter.PurchaseInter;
import com.wanlianyida.transaction.api.inter.QuotationInter;
import com.wanlianyida.transaction.api.model.command.QuotationProductSubmitCommand;
import com.wanlianyida.transaction.api.model.command.QuotationSubmitCommand;
import com.wanlianyida.transaction.api.model.dto.PurchaseEmptyDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 求购信息
 */
@Slf4j
@Service
public class QuotationCommandAppService {

    @Resource
    private ProductExchangeService productExchangeService;

    @Resource
    private ProductPublishCompanyExchangeService productPublishCompanyExchangeService;

    @Resource
    private ShopExchangeService shopExchangeService;

    @Resource
    private QuotationInter quotationInter;

    @Resource
    private PurchaseInter purchaseInter;

    /**
     * 报价提交校验
     * @param command
     * @return
     */
    public ResultMode<Map<Integer, QuotationProductSubmitCommand>> submitCheck(QuotationSubmitCommand command) {
        // 检查买家是否在当前商品定向客户范围内
        List<String> skuList = new ArrayList<>();
        validPublishCompany(command, skuList);
        if (ObjUtil.isNotEmpty(skuList)) {
            log.info(skuList.toString());
            return ResultMode.success(UserErrorCode.ERROR_10012.getCode(), this.buildRes(command, skuList));
        }
        return ResultMode.success();
    }

    /**
     * 保存报价
     * @param command
     * @return
     */
    public ResultMode<?> save(QuotationSubmitCommand command) {
        // 店铺信息
        appendShopInfo(command);
        return quotationInter.save(command);
    }

    /**
     * 提交报价
     * @param command
     * @return
     */
    public ResultMode<?> submit(QuotationSubmitCommand command) {
        // 店铺信息
        appendShopInfo(command);
        return quotationInter.submit(command);
    }

    private void validPublishCompany(QuotationSubmitCommand command, List<String> skuList) {
        Long relPurchaseId = command.getRelPurchaseId();
        if (ObjUtil.isEmpty(relPurchaseId)) {
            return;
        }
        IdQuery idQuery = new IdQuery();
        idQuery.setId(relPurchaseId);
        ResultMode<PurchaseEmptyDetailDTO> purchaseEmptyDetailDTOResultMode = purchaseInter.queryPurchaseDetail(idQuery);
        PurchaseEmptyDetailDTO getPublishCompanyId = purchaseEmptyDetailDTOResultMode.getModel();
        if (ObjUtil.isNull(getPublishCompanyId) && ObjUtil.isEmpty(getPublishCompanyId.getCompanyId())) {
            return;
        }
        String companyId = getPublishCompanyId.getCompanyId();
        List<String> skuCodes = command.getProductList().stream().map(QuotationProductSubmitCommand::getSkuCode).collect(Collectors.toList());
        List<ProductSkuDetailDTO> skuDetailDTOS = productExchangeService.queryProductSkuDetail(skuCodes);
        if (CollUtil.isEmpty(skuDetailDTOS)) {
            return;
        }
        List<String> spuCodes = skuDetailDTOS.stream().filter(sku -> "20".equals(sku.getPublishType())).map(ProductSkuDetailDTO::getSpuCode).collect(Collectors.toList());
        if (CollUtil.isEmpty(spuCodes)) {
            return;
        }
        ResultMode<List<PcProductPublishCompanyDTO>> listResultMode = productPublishCompanyExchangeService.publishCompanyList(PublishCompanyQuery.builder().spuCodeList(spuCodes).build());
        List<PcProductPublishCompanyDTO> model = listResultMode.getModel();
        if (ObjUtil.isEmpty(model)) {
            return;
        }
        Map<String, List<String>> resultMap = model.stream()
                .collect(Collectors.groupingBy(
                        PcProductPublishCompanyDTO::getSpuCode,
                        Collectors.mapping(PcProductPublishCompanyDTO::getPublishCompanyId, Collectors.toList())
                ));
        for (ProductSkuDetailDTO productSkuDetailDTO : skuDetailDTOS) {
            List<String> publishCompanyIdList = resultMap.get(productSkuDetailDTO.getSpuCode());
            if (CollUtil.isNotEmpty(publishCompanyIdList) && !publishCompanyIdList.contains(companyId)) {
                skuList.add(productSkuDetailDTO.getSkuCode());
            }
        }
    }

    private  Map<Integer, QuotationProductSubmitCommand> buildRes(QuotationSubmitCommand command,  List<String> skuList) {
        Map<Integer, QuotationProductSubmitCommand> map = new TreeMap<>();
        AtomicInteger index = new AtomicInteger(0);
        command.getProductList().forEach(product -> {
            int currentIndex = index.getAndIncrement();
            if (skuList.contains(product.getSkuCode())) {
                map.put(currentIndex, product);
            }
        });
        return map;
    }

    /**
     * 填充店铺信息
     */
    private void appendShopInfo(QuotationSubmitCommand command) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        ShopDTO shopInfo = shopExchangeService.getShopInfo(companyId);
        if (shopInfo == null) return;
        command.setShopId(shopInfo.getShopId());
        command.setShopName(shopInfo.getShopName());
    }
}
