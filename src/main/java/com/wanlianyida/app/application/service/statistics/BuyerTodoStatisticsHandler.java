package com.wanlianyida.app.application.service.statistics;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsBuyerToDoDTO;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.app.infrastructure.enums.SatisticsBizTypeEnum;
import com.wanlianyida.app.infrastructure.enums.WorkbenchesTypeEnum;
import com.wanlianyida.app.infrastructure.exchange.BiBigDataExchangeService;
import com.wanlianyida.app.infrastructure.exchange.StatisticsExchangeService;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.dto.StatisticsBuyerOrderDTO;
import com.wanlianyida.order.api.model.dto.StatisticsPurOrderDTO;
import com.wanlianyida.order.api.model.query.ResellMerchantQuery;
import com.wanlianyida.order.api.model.query.StatisticsPurchaseQuery;
import com.wanlianyida.transaction.api.model.dto.StatisticsBuyerPurchaseDTO;
import com.wanlianyida.transaction.api.model.query.PurchaseCompanyQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 买家待办数据处理
 */
@Service
@Slf4j
public class BuyerTodoStatisticsHandler implements StatisticsHandler<StatisticsBuyerToDoDTO> {

    @Resource
    StatisticsExchangeService statisticsExchangeService;

    @Resource
    private BiBigDataExchangeService biBigDataExchangeService;

    @Override
    public boolean handlerType(String type) {
        return SatisticsBizTypeEnum.BUYER_TODO_DATA_COUNT.getCode().equals(type);
    }

    @Override
    public StatisticsBuyerToDoDTO getBiData(StatisticsDataQuery query) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        //订单
        ResellMerchantQuery merchantQuery = new ResellMerchantQuery();
        merchantQuery.setCompanyId(companyId);
        StatisticsBuyerOrderDTO buyerOrderDTO = biBigDataExchangeService.statisticsBuyerOrderQuery(merchantQuery);
        StatisticsBuyerToDoDTO resultBuyerToDoDTO = BeanUtil.toBean(buyerOrderDTO, StatisticsBuyerToDoDTO.class);


        //求购统计
        PurchaseCompanyQuery purchaseCompanyQuery = new PurchaseCompanyQuery();
        purchaseCompanyQuery.setCompanyId(companyId);
        StatisticsBuyerPurchaseDTO statisticsBuyerPurchaseResult = biBigDataExchangeService.statisticsBuyerPurchaseQuery(purchaseCompanyQuery);
        resultBuyerToDoDTO.setReviewRejectedCount(statisticsBuyerPurchaseResult.getReviewRejectedCount());
        resultBuyerToDoDTO.setComparingPricesCount(statisticsBuyerPurchaseResult.getComparingPricesCount());

        //意向单
        StatisticsPurchaseQuery purchaseQuery = new StatisticsPurchaseQuery();
        purchaseQuery.setCompanyId(companyId);
        StatisticsPurOrderDTO statisticsPurOrderDTO = biBigDataExchangeService.statisticsBuyerPurOrderQuery(purchaseQuery);
        resultBuyerToDoDTO.setConfirmedCount(statisticsPurOrderDTO.getConfirmedCount());
        resultBuyerToDoDTO.setPendingConfirmationCount(statisticsPurOrderDTO.getPendingConfirmationCount());
        return resultBuyerToDoDTO;
    }

    @Override
    public StatisticsBuyerToDoDTO getServiceData(StatisticsDataQuery query) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        //订单
        ResellMerchantQuery merchantQuery = new ResellMerchantQuery();
        merchantQuery.setCompanyId(companyId);
        StatisticsBuyerOrderDTO buyerOrderDTO = statisticsExchangeService.statisticsBuyerOrderQuery(merchantQuery);
        StatisticsBuyerToDoDTO resultBuyerToDoDTO = BeanUtil.toBean(buyerOrderDTO, StatisticsBuyerToDoDTO.class);


        //求购统计
        PurchaseCompanyQuery purchaseCompanyQuery = new PurchaseCompanyQuery();
        purchaseCompanyQuery.setCompanyId(companyId);
        StatisticsBuyerPurchaseDTO statisticsBuyerPurchaseResult = statisticsExchangeService.statisticsBuyerPurchaseQuery(purchaseCompanyQuery);
        resultBuyerToDoDTO.setReviewRejectedCount(statisticsBuyerPurchaseResult.getReviewRejectedCount());
        resultBuyerToDoDTO.setComparingPricesCount(statisticsBuyerPurchaseResult.getComparingPricesCount());

        //意向单
        StatisticsPurchaseQuery purchaseQuery = new StatisticsPurchaseQuery();
        purchaseQuery.setCompanyId(companyId);
        purchaseQuery.setWorkbenchesType(WorkbenchesTypeEnum.BUYER.getCode());
        StatisticsPurOrderDTO statisticsPurOrderDTO = statisticsExchangeService.statisticsPurOrderQuery(purchaseQuery);
        resultBuyerToDoDTO.setConfirmedCount(statisticsPurOrderDTO.getConfirmedCount());
        resultBuyerToDoDTO.setPendingConfirmationCount(statisticsPurOrderDTO.getPendingConfirmationCount());
        return resultBuyerToDoDTO;
    }
}