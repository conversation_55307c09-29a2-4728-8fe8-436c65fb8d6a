package com.wanlianyida.app.application.service.content;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.app.infrastructure.exchange.BaseContentExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailUserDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/01/14/10:53
 */
@Service
public class ChInformationService {

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    public ResultMode<ChInformationDetailDTO> detailById(String infoId, String callAlias){
        if (StringUtils.isEmpty(infoId) && StringUtils.isEmpty(callAlias)) {
            return ResultMode.success();
        }
        if (StringUtils.isNotBlank(infoId)) {
            return baseContentExchangeService.chInformationInterDetailById(infoId);
        }
        ChInformationDetailUserDTO model = baseContentExchangeService.chInformationInterDetailByCallAlias(callAlias).getModel();
        ChInformationDetailDTO chInformationDetailDTO = BeanUtil.copyProperties(model, ChInformationDetailDTO.class);
        return ResultMode.success(chInformationDetailDTO);
    }
}
