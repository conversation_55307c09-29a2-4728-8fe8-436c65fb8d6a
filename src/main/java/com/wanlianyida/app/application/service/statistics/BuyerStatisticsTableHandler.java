package com.wanlianyida.app.application.service.statistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.app.application.assembler.StatisticsAssembler;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsDataTableDTO;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.app.infrastructure.enums.SatisticsBizTypeEnum;
import com.wanlianyida.app.infrastructure.enums.WorkbenchesTypeEnum;
import com.wanlianyida.app.infrastructure.exchange.BiBigDataExchangeService;
import com.wanlianyida.app.infrastructure.exchange.StatisticsExchangeService;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.dto.StatisticsTableDTO;
import com.wanlianyida.order.api.model.query.StatisticsBusinessQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

/**
 * 统计图买家
 **/
@Service
@Slf4j
public class BuyerStatisticsTableHandler implements StatisticsHandler<StatisticsDataTableDTO> {

    @Resource
    StatisticsExchangeService statisticsExchangeService;

    @Resource
    private BiBigDataExchangeService biBigDataExchangeService;

    @Override
    public boolean handlerType(String type) {
        Set<String> set = new HashSet<>();
        set.add(SatisticsBizTypeEnum.BUYER_TRANSACTION_AMOUNT.getCode());
        set.add(SatisticsBizTypeEnum.BUYER_TRANSACTION_NUMBER.getCode());
        set.add(SatisticsBizTypeEnum.BUYER_AVG_PRICE.getCode());
        return set.contains(type);
    }

    @Override
    public StatisticsDataTableDTO getBiData(StatisticsDataQuery query) {
        if (StrUtil.equals(SatisticsBizTypeEnum.BUYER_TRANSACTION_AMOUNT.getCode(), query.getBizType())) {
            //成交金额
            return this.totalAmountQuery(query,"bi");
        } else if (StrUtil.equals(SatisticsBizTypeEnum.BUYER_TRANSACTION_NUMBER.getCode(), query.getBizType())) {
            //成交单数
            return this.totalOrdersQuery(query,"bi");
        } else if (StrUtil.equals(SatisticsBizTypeEnum.BUYER_AVG_PRICE.getCode(), query.getBizType())) {
            //客单价
            return this.avgPriceQuery(query,"bi");
        }
        return null;
    }

    @Override
    public StatisticsDataTableDTO getServiceData(StatisticsDataQuery query) {
        if (StrUtil.equals(SatisticsBizTypeEnum.BUYER_TRANSACTION_AMOUNT.getCode(), query.getBizType())) {
            //成交金额
            return this.totalAmountQuery(query,"");
        } else if (StrUtil.equals(SatisticsBizTypeEnum.BUYER_TRANSACTION_NUMBER.getCode(), query.getBizType())) {
            //成交单数
            return this.totalOrdersQuery(query,"");
        } else if (StrUtil.equals(SatisticsBizTypeEnum.BUYER_AVG_PRICE.getCode(), query.getBizType())) {
            //客单价
            return this.avgPriceQuery(query,"");
        }
        return null;
    }

    /**
     * 成交金额
     */
    private StatisticsDataTableDTO totalAmountQuery(StatisticsDataQuery query,String dataType) {
        StatisticsBusinessQuery businessQuery = BeanUtil.toBean(query, StatisticsBusinessQuery.class);
        businessQuery.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        businessQuery.setWorkbenchesType(WorkbenchesTypeEnum.BUYER.getCode());
        List<StatisticsTableDTO> businessAmountDTO = null;
        if(StrUtil.equals("bi",dataType)){
            businessAmountDTO = biBigDataExchangeService.totalAmountBuyerQuery(businessQuery);
        }else{
            businessAmountDTO = statisticsExchangeService.totalAmountQuery(businessQuery);
        }
        List<StatisticsTableDTO> resultDTOS = StatisticsAssembler.buildAssembleDTO(businessAmountDTO, query.getTimePeriod());
        StatisticsDataTableDTO result = new StatisticsDataTableDTO();
        result.setBizType(query.getBizType());
        result.setReportList(StatisticsAssembler.convertToMapList(resultDTOS));
        return result;
    }

    /**
     * 按天客单价统计
     */
    private StatisticsDataTableDTO totalOrdersQuery(StatisticsDataQuery query,String dataType) {
        StatisticsBusinessQuery businessQuery = BeanUtil.toBean(query, StatisticsBusinessQuery.class);
        businessQuery.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        businessQuery.setWorkbenchesType(WorkbenchesTypeEnum.BUYER.getCode());
        List<StatisticsTableDTO> businessAmountDTO = null;
        if(StrUtil.equals("bi",dataType)){
            businessAmountDTO = biBigDataExchangeService.totalOrdersBuyerQuery(businessQuery);
        }else{
            businessAmountDTO = statisticsExchangeService.totalOrdersQuery(businessQuery);
        }
        List<StatisticsTableDTO> resultDTOS = StatisticsAssembler.buildAssembleDTO(businessAmountDTO, query.getTimePeriod());
        StatisticsDataTableDTO result = new StatisticsDataTableDTO();
        result.setBizType(query.getBizType());
        result.setReportList(StatisticsAssembler.convertToMapList(resultDTOS));
        return result;
    }

    /**
     * 按天客单价统计
     */
    private StatisticsDataTableDTO avgPriceQuery(StatisticsDataQuery query,String dataType) {
        StatisticsBusinessQuery businessQuery = BeanUtil.toBean(query, StatisticsBusinessQuery.class);
        businessQuery.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        businessQuery.setWorkbenchesType(WorkbenchesTypeEnum.BUYER.getCode());
        List<StatisticsTableDTO> businessAmountDTO = null;
        if(StrUtil.equals("bi",dataType)){
            businessAmountDTO = biBigDataExchangeService.avgPriceBuyerQuery(businessQuery);
        }else{
            businessAmountDTO = statisticsExchangeService.avgPriceQuery(businessQuery);
        }

        List<StatisticsTableDTO> resultDTOS = StatisticsAssembler.buildAssembleDTO(businessAmountDTO, query.getTimePeriod());
        StatisticsDataTableDTO result = new StatisticsDataTableDTO();
        result.setBizType(query.getBizType());
        result.setReportList(StatisticsAssembler.convertToMapList(resultDTOS));
        return result;
    }
}
