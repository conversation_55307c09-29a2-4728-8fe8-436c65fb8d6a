package com.wanlianyida.app.application.service.statistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StatisticsTodoDataDTO {

    //订单
    @ApiModelProperty("待签约")
    private Integer pendingSignCount;

    @ApiModelProperty("待结算")
    private Integer pendingSettlementCount;

    @ApiModelProperty("待发货")
    private Integer pendingShipCount;

    @ApiModelProperty("待确认收货")
    private Integer pendingConfirmPaymentCount;

    //商品
    @ApiModelProperty("审核驳回")
    private Integer rejectCount;

    //意向单
    @ApiModelProperty("待卖家确认")
    private Integer pendingConfirmationCount;

    @ApiModelProperty("待生成订单")
    private Integer confirmedCount;
}
