package com.wanlianyida.app.application.service.purchase;

import com.wanlianyida.app.infrastructure.enums.PurchaseScopeEnum;
import com.wanlianyida.app.interfaces.model.dto.PurchaseProductAggregationDTO;
import com.wanlianyida.basicdata.api.PlatformCmCityInter;
import com.wanlianyida.basicdata.model.dto.PlatformCmCityDTO;
import com.wanlianyida.basicdata.model.query.CityNodesQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.product.api.inter.ProductCategoryManagerInter;
import com.wanlianyida.product.api.model.dto.ProductCategoryDTO;
import com.wanlianyida.search.api.IEsStpPurchaseProductService;
import com.wanlianyida.search.dto.CtpPurchaseProductAggregationDTO;
import com.wanlianyida.search.query.CtpPurchaseProductQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CtpPurchaseProductAppService {
    @Resource
    private IEsStpPurchaseProductService esStpPurchaseProductService;
    @Resource
    private ProductCategoryManagerInter productCategoryManagerInter;
    @Resource
    private PlatformCmCityInter platformCmCityInter;

    private static final Map<String, String> SORT_STRATEGY_MAP = new HashMap<>();

    static {
        SORT_STRATEGY_MAP.put("6", "deadlineDate asc");
    }

    /**
     * 求购商品聚合列表
     */
    public ResultMode<PurchaseProductAggregationDTO> queryAggregation(PagingInfo<CtpPurchaseProductQuery> query) {
        PurchaseProductAggregationDTO dto = new PurchaseProductAggregationDTO();

        //设置排序策略
        String orderType = query.getSort();
        String strategy = SORT_STRATEGY_MAP.getOrDefault(orderType, ("releaseDate desc"));
        query.getFilterModel().setSortStrategy(strategy);
        // 只查询公开的
        query.getFilterModel().setPurchaseScope(PurchaseScopeEnum.SCOPE_10.getCode());
        ResponseMessage<CtpPurchaseProductAggregationDTO> resultMode = esStpPurchaseProductService.queryAggregation(query);
        CtpPurchaseProductAggregationDTO originalDTO = resultMode.getModel();
        if (originalDTO == null || originalDTO.getTotalCount() == 0) {
            return ResultMode.success(dto);
        }
        //总记录数
        dto.setTotalCount(originalDTO.getTotalCount());
        //求购单列表
        dto.setPurchaseDocumentDTOList(originalDTO.getPurchaseDocumentDTOList());
        //交货地城市列表
        CityNodesQuery cityNodesQuery = new CityNodesQuery();
        cityNodesQuery.setNodes(originalDTO.getCityCodeList());
        ResponseMessage<List<PlatformCmCityDTO>> cityResultMode = platformCmCityInter.getCityDataByNodes(cityNodesQuery);
        List<PlatformCmCityDTO> cityList = cityResultMode.getModel();
        Map<String, String> cityMap = cityList.stream().collect(Collectors.toMap(PlatformCmCityDTO::getNode, PlatformCmCityDTO::getName));
        List<PurchaseProductAggregationDTO.City> deliveryAddrCityList = originalDTO.getCityCodeList().stream().map(cityCode -> {
            PurchaseProductAggregationDTO.City city = new PurchaseProductAggregationDTO.City();
            city.setCityCode(cityCode);
            city.setCityName(cityMap.get(cityCode));
            return city;
        }).collect(Collectors.toList());
        dto.setCityList(deliveryAddrCityList);

        //品类列表
        List<Long> categoryIdList = new ArrayList<>();
        originalDTO.getCategoryList().forEach(ca -> {
            categoryIdList.add(ca.getCategoryId());
            ca.getChildCategoryList().forEach(childCa -> {
                categoryIdList.add(childCa.getCategoryId());
                childCa.getChildCategoryList().forEach(grandsonCa -> {
                    categoryIdList.add(grandsonCa.getCategoryId());
                });
            });
        });
        ResultMode<List<ProductCategoryDTO>> productCategoryResultMode = productCategoryManagerInter.queryByIds(categoryIdList);
        List<ProductCategoryDTO> productCategoryList = productCategoryResultMode.getModel();
        Map<Long, String> categoryMap = productCategoryList.stream().collect(Collectors.toMap(ProductCategoryDTO::getId, ProductCategoryDTO::getCategoryName));

        //一级品类
        List<PurchaseProductAggregationDTO.Category> categoryList = originalDTO.getCategoryList().stream().map(ca -> {
            PurchaseProductAggregationDTO.Category category = new PurchaseProductAggregationDTO.Category();
            category.setCategoryId(ca.getCategoryId());
            category.setCategoryName(categoryMap.get(ca.getCategoryId()));
            //二级品类
            List<PurchaseProductAggregationDTO.Category> childCategoryList = ca.getChildCategoryList().stream().map(childCa -> {
                PurchaseProductAggregationDTO.Category childCategory = new PurchaseProductAggregationDTO.Category();
                childCategory.setCategoryId(childCa.getCategoryId());
                childCategory.setCategoryName(categoryMap.get(childCa.getCategoryId()));
                //三级品类
                List<PurchaseProductAggregationDTO.Category> grandsonCategoryList = childCa.getChildCategoryList().stream().map(grandsonCa -> {
                    PurchaseProductAggregationDTO.Category grandsonCategory = new PurchaseProductAggregationDTO.Category();
                    grandsonCategory.setCategoryId(grandsonCa.getCategoryId());
                    grandsonCategory.setCategoryName(categoryMap.get(grandsonCa.getCategoryId()));
                    return grandsonCategory;
                }).collect(Collectors.toList());
                childCategory.setChildCategoryList(grandsonCategoryList);
                return childCategory;
            }).collect(Collectors.toList());
            category.setChildCategoryList(childCategoryList);
            return category;
        }).collect(Collectors.toList());
        dto.setCategoryList(categoryList);
        return ResultMode.success(dto);
    }
}
