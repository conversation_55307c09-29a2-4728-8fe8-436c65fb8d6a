package com.wanlianyida.app.application.service.statistics;

/**
 * TodoHandlerAppService
 *
 * <AUTHOR>
 * @since 2025/4/17
 */
import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsTodoDataDTO;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.app.infrastructure.enums.SatisticsBizTypeEnum;
import com.wanlianyida.app.infrastructure.enums.WorkbenchesTypeEnum;
import com.wanlianyida.app.infrastructure.exchange.BiBigDataExchangeService;
import com.wanlianyida.app.infrastructure.exchange.StatisticsExchangeService;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.dto.StatisticsOrderDTO;
import com.wanlianyida.order.api.model.dto.StatisticsPurOrderDTO;
import com.wanlianyida.order.api.model.query.ResellMerchantQuery;
import com.wanlianyida.order.api.model.query.StatisticsPurchaseQuery;
import com.wanlianyida.product.api.model.command.CompanyIdQuery;
import com.wanlianyida.product.api.model.dto.StatisticsProductDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 待办数据处理
 */
@Service
@Slf4j
public class SellerTodoStatisticsHandler implements StatisticsHandler<StatisticsTodoDataDTO> {

    @Resource
    private StatisticsExchangeService statisticsExchangeService;

    @Resource
    private BiBigDataExchangeService biBigDataExchangeService;

    @Override
    public boolean handlerType(String type) {
        return SatisticsBizTypeEnum.TODO_DATA_COUNT.getCode().equals(type);
    }

    @Override
    public StatisticsTodoDataDTO getBiData(StatisticsDataQuery query) {
        StatisticsTodoDataDTO result = new StatisticsTodoDataDTO();
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        ResellMerchantQuery resellMerchantQuery = new ResellMerchantQuery();
        resellMerchantQuery.setCompanyId(companyId);
        //商品驳回
        CompanyIdQuery companyIdQuery =new CompanyIdQuery() ;
        companyIdQuery.setCompanyId(companyId);
        StatisticsProductDTO productDTO = biBigDataExchangeService.statisticsProductQuery(companyIdQuery);
        result = BeanUtil.toBean(productDTO, StatisticsTodoDataDTO.class);
        //意向单
        StatisticsPurOrderDTO statisticsPurOrderDTO = biBigDataExchangeService.statisticsPurOrderQuery(resellMerchantQuery);
        result.setConfirmedCount(statisticsPurOrderDTO.getConfirmedCount());
        result.setPendingConfirmationCount(statisticsPurOrderDTO.getPendingConfirmationCount());
        //订单统计
        StatisticsOrderDTO statisticsOrderDTO = biBigDataExchangeService.statisticsOrderQuery(resellMerchantQuery);
        result.setPendingConfirmPaymentCount(statisticsOrderDTO.getPendingConfirmPaymentCount());
        result.setPendingSettlementCount(statisticsOrderDTO.getPendingSettlementCount());
        result.setPendingShipCount(statisticsOrderDTO.getPendingShipCount());
        result.setPendingSignCount(statisticsOrderDTO.getPendingSignCount());
        return result;
    }

    @Override
    public StatisticsTodoDataDTO getServiceData(StatisticsDataQuery query) {
        StatisticsTodoDataDTO result = new StatisticsTodoDataDTO();
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        ResellMerchantQuery resellMerchantQuery = new ResellMerchantQuery();
        resellMerchantQuery.setCompanyId(companyId);
        //商品驳回
        CompanyIdQuery companyIdQuery =new CompanyIdQuery() ;
        companyIdQuery.setCompanyId(companyId);
        StatisticsProductDTO productDTO = statisticsExchangeService.statisticsProductQuery(companyIdQuery);
        result = BeanUtil.toBean(productDTO, StatisticsTodoDataDTO.class);
        //意向单
        StatisticsPurchaseQuery purchaseQuery = new StatisticsPurchaseQuery();
        purchaseQuery.setCompanyId(companyId);
        purchaseQuery.setWorkbenchesType(WorkbenchesTypeEnum.SELLER.getCode());
        StatisticsPurOrderDTO statisticsPurOrderDTO = statisticsExchangeService.statisticsPurOrderQuery(purchaseQuery);
        result.setConfirmedCount(statisticsPurOrderDTO.getConfirmedCount());
        result.setPendingConfirmationCount(statisticsPurOrderDTO.getPendingConfirmationCount());
        //订单统计
        StatisticsOrderDTO statisticsOrderDTO = statisticsExchangeService.statisticsOrderQuery(resellMerchantQuery);
        result.setPendingConfirmPaymentCount(statisticsOrderDTO.getPendingConfirmPaymentCount());
        result.setPendingSettlementCount(statisticsOrderDTO.getPendingSettlementCount());
        result.setPendingShipCount(statisticsOrderDTO.getPendingShipCount());
        result.setPendingSignCount(statisticsOrderDTO.getPendingSignCount());
        return result;
    }
}
