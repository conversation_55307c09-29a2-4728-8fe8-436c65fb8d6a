package com.wanlianyida.app.application.service.product;

import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.app.infrastructure.exchange.ProductViewLogExchangeService;
import com.wanlianyida.app.infrastructure.exchange.UmShopExchangeService;
import com.wanlianyida.app.infrastructure.util.UserBaseInfoUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.dto.ShopDTO;
import com.wanlianyida.product.api.model.command.ProductViewLogAddCommand;
import com.wanlianyida.product.api.model.dto.ProductViewLogDTO;
import com.wanlianyida.product.api.model.query.ProductViewLogListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <p>
 * 用户浏览商品记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Service
public class ProductViewLogAppService {

    @Resource
    private ProductViewLogExchangeService productViewLogExchangeService;


    @Resource
    private UmShopExchangeService umShopExchangeService;


    /**
     * 新增商品收藏
     *
     * @param addCommand
     * @return {@link Boolean }
     */
    public ResultMode<?> addViewLog(ProductViewLogAddCommand addCommand) {
        UserBaseInfoUtil.setAddBaseInfo(addCommand);
        return productViewLogExchangeService.addViewLog((addCommand));
    }

    /**
     * 分页查询
     *
     * @param query 条件
     * @return {@code List<ProductCollectEntity> }
     */
    public ResultMode<List<ProductViewLogDTO>> queryViewLogListPage(PagingInfo<ProductViewLogListQuery> query) {
        ResultMode<List<ProductViewLogDTO>> listResultMode = productViewLogExchangeService.queryViewLogListPage(query);
        List<ProductViewLogDTO> productCollectDTOList = listResultMode.getModel();
        if (ObjectUtil.isEmpty(productCollectDTOList)) {
            return listResultMode;
        }
        List<Long> shopIds = new ArrayList<>();
        productCollectDTOList.forEach(e -> {
            e.getProductViewLists().stream().forEach(e1 -> {
                shopIds.add(e1.getShopId());
            });
        });
        //关联店铺信息
        ResultMode<List<ShopDTO>> listByShopIds = umShopExchangeService.batchQueryListByShopIds(shopIds);
        List<ShopDTO> shopDTOList = listByShopIds.getModel();
        if (ObjectUtil.isEmpty(shopDTOList)) {
            log.error("查询店铺信息失败");
            return listResultMode;
        }
        Map<Long, ShopDTO> shopDTOMap = shopDTOList.stream().collect(Collectors.toMap(ShopDTO::getId, Function.identity()));
        productCollectDTOList.forEach(e -> {
            e.getProductViewLists().stream().forEach(e1 -> {
                ShopDTO shopDTO = shopDTOMap.get(e1.getShopId());
                if (shopDTO != null) {
                    e1.setShopName(shopDTO.getShopName());
                    e1.setShopLogo(shopDTO.getShopLogoUrl());
                }
            });
        });
        return ResultMode.successPageList(productCollectDTOList, listResultMode.getTotal());
    }
}
