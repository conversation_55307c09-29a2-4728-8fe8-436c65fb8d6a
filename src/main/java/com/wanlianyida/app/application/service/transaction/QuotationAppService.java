package com.wanlianyida.app.application.service.transaction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.app.infrastructure.exchange.ProductExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.dto.ProductInventoryDTO;
import com.wanlianyida.transaction.api.inter.PurchaseInter;
import com.wanlianyida.transaction.api.inter.QuotationInter;
import com.wanlianyida.transaction.api.model.dto.*;
import com.wanlianyida.transaction.api.model.query.QuotationListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月09日 19:51
 */
@Slf4j
@Service
public class QuotationAppService {

    @Resource
    private QuotationInter quotationInter;


    @Resource
    private PurchaseInter purchaseInter;

    @Resource
    private ProductExchangeService productExchangeService;

    public ResultMode<List<QuotationListDTO>> pageCondition(PagingInfo<QuotationListQuery> pageQuery) {
        return quotationInter.pageCondition(pageQuery);
    }

    public ResultMode<QuotationDetailDTO> queryDetail(IdQuery query) {
        ResultMode<QuotationDetailDTO> result = quotationInter.queryDetail(query);
        buildInventory(result.getModel());
        appendShopInfoToProduct(result.getModel());
        return result;
    }


    private void appendShopInfoToProduct(QuotationDetailDTO dto) {
        if (CollUtil.isEmpty(dto.getProductList())) return;
        for (QuotationProductDetailDTO quotationProduct : dto.getProductList()) {
            if (quotationProduct.getOfferProductData() != null) {
                quotationProduct.getOfferProductData().setShopId(dto.getShopId());
                quotationProduct.getOfferProductData().setShopName(dto.getShopName());
            }
        }
    }



    public ResultMode<PurchaseEmptyDetailDTO> queryPurchaseDetail(IdQuery query) {
        return purchaseInter.queryPurchaseDetail(query);
    }


    /**
     * 封装库存
     */
    private void buildInventory(QuotationDetailDTO detail) {
        // 待报价展示库存
        if (detail != null && CollectionUtil.isNotEmpty(detail.getProductList()) && detail.getQuotationStatus().equals(10)) {
            List<String> skuCodeList = detail.getProductList().stream().filter(e -> e.getOfferProductData() != null).map(e -> e.getOfferProductData().getSkuCode()).collect(Collectors.toList());
            List<ProductInventoryDTO> inventoryList = productExchangeService.queryProductInventory(skuCodeList);
            if (CollectionUtil.isNotEmpty(inventoryList)) {
                Map<String, ProductInventoryDTO> mapping = inventoryList.stream().collect(Collectors.toMap(ProductInventoryDTO::getSkuCode, e -> e));
                for (QuotationProductDetailDTO quotation : detail.getProductList()) {
                    QuotationProductDTO product = quotation.getOfferProductData();
                    if (product != null && mapping.get(product.getSkuCode()) != null) {
                        product.setInventory(mapping.get(product.getSkuCode()).getAvailableQuantity());
                    }
                }
            }
        }
    }
}
