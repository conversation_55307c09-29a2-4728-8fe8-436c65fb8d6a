package com.wanlianyida.app.application.service.statistics.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * StatisticsQueryCommand
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@Data
public class StatisticsDataQuery {

    @ApiModelProperty("10卖家经营数据、20卖家待办数据、30卖家成交金额趋势图、40卖家成交单数趋势图、50卖家客单价趋势图、60卖家-浏览量趋势图、70买家-待办数据、80买家-采集数据、90买家-成交金额趋势图、100买家-成交单数趋势图、110买家-客单价趋势图")
    String bizType;

    @ApiModelProperty("时间范围天数：7, 15, 30")
    Integer timePeriod;

    @ApiModelProperty("时间维度：year month day")
    String timeDimension;



}
