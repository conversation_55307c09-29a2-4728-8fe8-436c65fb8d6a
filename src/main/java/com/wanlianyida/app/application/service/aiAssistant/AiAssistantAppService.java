package com.wanlianyida.app.application.service.aiAssistant;

import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.app.infrastructure.exchange.AiAssistantExchangeService;
import com.wanlianyida.app.interfaces.model.command.AppConversationCommand;
import com.wanlianyida.app.interfaces.model.command.AppMessageFeedbackCommand;
import com.wanlianyida.app.interfaces.model.command.AppServiceConvMessageCommand;
import com.wanlianyida.app.interfaces.model.dto.AppConversationDTO;
import com.wanlianyida.app.interfaces.model.dto.AppServiceConvMessageDTO;
import com.wanlianyida.app.interfaces.model.query.AppConversationQuery;
import com.wanlianyida.app.interfaces.model.query.AppServiceConvMessagePageQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 小程序端-智能客服
 */
@Service
public class AiAssistantAppService {

    @Resource
    private AiAssistantExchangeService aiAssistantExchangeService;

    /**
     * 创建会话
     * @param command
     * @return
     */
    public ResultMode<AppConversationDTO> createConvos(AppConversationCommand command){
        // 构建查询会话的参数对象
        AppConversationQuery convosQuery = new AppConversationQuery();
        convosQuery.setUserId(command.getUserId());       // 设置用户ID
        convosQuery.setConvType(command.getConvType());   // 设置会话类型
        convosQuery.setConvSource(command.getConvSource()); // 设置对话来源
        convosQuery.setPlatformType(command.getPlatformType()); // 设置平台类型

        // 查询是否存在已有会话
        ResultMode<AppConversationDTO> resultMode = aiAssistantExchangeService.queryConvos(convosQuery);
        
        // 如果查询成功且存在会话记录，直接返回已有会话信息
        if (resultMode.isSucceed() && ObjectUtil.isNotNull(resultMode.getModel())){
            return resultMode;
        }
        
        // 否则创建新的会话并返回
        return aiAssistantExchangeService.createConvos(command);
    }

    /**
     * 查询会话
     * @param convosQuery
     * @return
     */
    public ResultMode<AppConversationDTO> queryConvos(AppConversationQuery convosQuery){
        return aiAssistantExchangeService.queryConvos(convosQuery);
    }

    /**
     * 保存会话消息
     * @param command
     * @return
     */
    public ResultMode<AppServiceConvMessageDTO> saveMessage(AppServiceConvMessageCommand command){
        return aiAssistantExchangeService.saveMessage(command);
    }

    /**
     * 会话消息反馈
     * @param command
     * @return
     */
    public ResultMode<?> messageFeedback(AppMessageFeedbackCommand command){
        return aiAssistantExchangeService.messageFeedback(command);
    }

    /**
     * 分页查询会话消息
     * @param pageQuery
     * @return
     */
    public ResultMode<List<AppServiceConvMessageDTO>> queryMessage(PagingInfo<AppServiceConvMessagePageQuery> pageQuery){
        return aiAssistantExchangeService.queryMessage(pageQuery);
    }
}
