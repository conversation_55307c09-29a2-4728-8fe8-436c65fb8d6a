package com.wanlianyida.app.application.service.statistics;

import com.wanlianyida.app.application.assembler.StatisticsAssembler;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsDataTableDTO;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.app.infrastructure.enums.SatisticsBizTypeEnum;
import com.wanlianyida.app.infrastructure.exchange.BiBigDataExchangeService;
import com.wanlianyida.app.infrastructure.exchange.StatisticsExchangeService;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.dto.StatisticsShopOrderDTO;
import com.wanlianyida.order.api.model.query.ResellMerchantQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 店铺采购情况
 */
@Service
@Slf4j
public class BuyerShopOrderTotalAmountHandler implements StatisticsHandler<StatisticsDataTableDTO> {

    @Resource
    StatisticsExchangeService statisticsExchangeService;

    @Resource
    private BiBigDataExchangeService biBigDataExchangeService;

    @Override
    public StatisticsDataTableDTO getBiData(StatisticsDataQuery query) {
        ResellMerchantQuery merchantQuery = new ResellMerchantQuery();
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        merchantQuery.setCompanyId(companyId);
        List<StatisticsShopOrderDTO> orderDTOList = biBigDataExchangeService.statisticsShopOrderAmountQuery(merchantQuery);
        StatisticsDataTableDTO result = new StatisticsDataTableDTO();
        result.setBizType(query.getBizType());
        result.setReportList(StatisticsAssembler.convertToMapList(orderDTOList));
        return result;
    }

    @Override
    public StatisticsDataTableDTO getServiceData(StatisticsDataQuery query) {
        ResellMerchantQuery merchantQuery = new ResellMerchantQuery();
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        merchantQuery.setCompanyId(companyId);
        List<StatisticsShopOrderDTO> orderDTOList =  statisticsExchangeService.statisticsShopOrderAmountQuery(merchantQuery).getModel();
        StatisticsDataTableDTO result = new StatisticsDataTableDTO();
        result.setBizType(query.getBizType());
        result.setReportList(StatisticsAssembler.convertToMapList(orderDTOList));
        return result;
    }

    @Override
    public boolean handlerType(String type) {
        return SatisticsBizTypeEnum.BUYER_ORDER_TOTAL_AMOUNT.getCode().equals(type);
    }
}
