package com.wanlianyida.app.application.service.content;

import com.wanlianyida.app.infrastructure.exchange.BaseContentExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.dto.BannerDTO;
import com.wanlianyida.fssbasecontent.api.model.query.BannerInfoListQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class BannerInfoAppService {

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    /**
     * 根据广告位id查询轮播图
     *
     * @param relAdId rel 广告 ID
     * @return {@link ResultMode }<{@link List }<{@link BannerDTO }>>
     */
    public ResultMode<List<BannerDTO>> queryList(Long relAdId){
        return baseContentExchangeService.bannerInfoInterQueryList(relAdId);

    }

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    public ResultMode queryPage(PagingInfo<BannerInfoListQuery> pagingInfo){
        return baseContentExchangeService.bannerInfoInterQueryPage(pagingInfo);
    }


}
