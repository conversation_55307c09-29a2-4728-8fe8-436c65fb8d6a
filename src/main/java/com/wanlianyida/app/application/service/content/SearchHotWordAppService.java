package com.wanlianyida.app.application.service.content;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.SearchHotWordInter;
import com.wanlianyida.support.api.model.dto.SearchHotWordDTO;
import com.wanlianyida.support.api.model.query.SearchHotWordPageQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class SearchHotWordAppService {

    @Resource
    private SearchHotWordInter searchHotWordInter;

    /**
     * 查询指定条数的热词数据
     * @param query
     * @return
     */
    public ResultMode<List<SearchHotWordDTO>> queryLimitedData(SearchHotWordPageQuery query) {
        return searchHotWordInter.queryLimitedData(query);
    }

}
