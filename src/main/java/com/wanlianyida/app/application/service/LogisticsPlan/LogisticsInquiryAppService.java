package com.wanlianyida.app.application.service.LogisticsPlan;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.app.application.assembler.LogisticsInquiryAssembler;
import com.wanlianyida.app.application.service.order.OrderShipAppService;
import com.wanlianyida.app.infrastructure.constant.CommonConstants;
import com.wanlianyida.app.infrastructure.exchange.LogisticsInquiryExchangService;
import com.wanlianyida.app.interfaces.model.dto.CompanyInfoDTO;
import com.wanlianyida.app.interfaces.model.dto.LogisticsInquiryDetailDTO;
import com.wanlianyida.app.interfaces.model.dto.LogisticsInquiryListDTO;
import com.wanlianyida.exter.api.lgi.inter.LogisticExchangeInter;
import com.wanlianyida.exter.api.lgi.model.request.ContractSignFlowRequest;
import com.wanlianyida.exter.api.lgi.model.request.LogisticsTemplateIdRequest;
import com.wanlianyida.exter.api.lgi.model.request.SocialCreditRequest;
import com.wanlianyida.exter.api.lgi.model.request.WaybillListRequest;
import com.wanlianyida.exter.api.lgi.model.response.CompanyInfo;
import com.wanlianyida.exter.api.lgi.model.response.DictionaryResponse;
import com.wanlianyida.exter.api.lgi.model.response.LoqisticsExchangeResponse;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.inter.LogisticsInquiryInter;
import com.wanlianyida.order.api.model.command.*;
import com.wanlianyida.order.api.model.dto.LogisticsInquiryAndAddressDTO;
import com.wanlianyida.order.api.model.dto.LogisticsInquiryDTO;
import com.wanlianyida.order.api.model.dto.OrderShipDetailResDTO;
import com.wanlianyida.order.api.model.query.LogisticsInquiryQuery;
import com.wanlianyida.order.api.model.query.OrderShipDetailQuery;
import com.wanlianyida.order.api.model.query.WaybillListQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @ClassName: OrderCommandAppService
 * @description:
 * @author: zhangZhen
 * @date: 2025年04月24日
 * @version: 1.0
 */
@Slf4j
@Service
public class LogisticsInquiryAppService {

    @Autowired
    private LogisticsInquiryInter logisticsInquiryInter;

    @Resource
    private LogisticsInquiryExchangService logisticsInquiryExchangService;

    @Resource
    private LogisticExchangeInter logisticExchangeInter;

    @Resource
    private OrderShipAppService orderShipAppService;


    public ResultMode createLogisticsInquiry(@RequestBody LogisticsInquiryListAddCmd command) {
        return logisticsInquiryInter.createLogisticsInquiry(command);
    }


    public ResultMode updateLogisticsInquiry(@RequestBody LogisticsInquiryListUpdateCmd command) {
        return logisticsInquiryInter.updateLogisticsInquiry(command);
    }


    public ResultMode restartLogisticsInquiry(@RequestBody LogisticsInquiryListAddCmd command) {
        return logisticsInquiryInter.restartLogisticsInquiry(command);
    }


    public ResultMode<Object> createLogisticsInquiryVerify(@RequestBody String shipNo) {
        return logisticsInquiryInter.createLogisticsInquiryVerify(shipNo);
    }


    public ResultMode<Object> restartLogisticsInquiryVerify(@RequestBody LogisticsInquiryVerifyCmd command) {
        return logisticsInquiryInter.restartLogisticsInquiryVerify(command);
    }

    /**
     * 询价单列表
     */
    public ResultMode<List<LogisticsInquiryListDTO>> pageList(PagingInfo<LogisticsInquiryQuery> pagingInfo) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if(StringUtils.isNotBlank(companyId)){
            pagingInfo.getFilterModel().setCompanyId(companyId);
        }

        ResultMode<List<LogisticsInquiryDTO>> resultMode = logisticsInquiryExchangService.pageList(pagingInfo);
        List<LogisticsInquiryDTO> modelInquiryDTO = resultMode.getModel();
        List<LogisticsInquiryListDTO> logisticsInquiryListDTOS = BeanUtil.copyToList(modelInquiryDTO, LogisticsInquiryListDTO.class);
        return ResultMode.successPageList(logisticsInquiryListDTOS, (int)  resultMode.getTotal());
    }
    public ResultMode<List<LogisticsInquiryDTO>> queryLogisticsInquiryByShipNo(String shipNo) {
        return logisticsInquiryInter.queryLogisticsInquiryByShipNo(shipNo);
    }

    /**
     * 审批
     */
    public ResultMode approval(@Valid LogisticsInquiryApprovalCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return logisticsInquiryExchangService.approval(command);
    }

    /**
     * 撤销
     */
    public ResultMode revoke(@Valid LogisticsInquiryIdCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return logisticsInquiryExchangService.revoke(command);
    }

    /**
     * 详情
     */
    public ResultMode<LogisticsInquiryDetailDTO> getDetail(@Valid LogisticsInquiryIdCommand command) {
        ResultMode<LogisticsInquiryAndAddressDTO> detail = logisticsInquiryExchangService.getDetail(command);
        LogisticsInquiryDetailDTO logisticsInquiryDetailDTO = LogisticsInquiryAssembler.assembleLogisticsInquiryDetailDTO(detail.getModel());
        //合同数据
        try {
            ContractSignFlowRequest contractSignFlowRequest = new ContractSignFlowRequest();
            contractSignFlowRequest.setBizId(logisticsInquiryDetailDTO.getInquiryNo());
            contractSignFlowRequest.setType("10");
            contractSignFlowRequest.setFlowStatus("200");
            log.info("queryContractSignFlowByCondition参数:{}", contractSignFlowRequest);
            LoqisticsExchangeResponse responseContract = logisticExchangeInter.queryContractSignFlowByCondition(contractSignFlowRequest);
            log.info("queryContractSignFlowByCondition返回值:{}", responseContract);
            // 获取意愿签合同
            List contractList = getContractList();
            List ansList = Lists.newArrayList();
            if (CollUtil.isNotEmpty(contractList)) {
                ansList.addAll(contractList);
            }
            if (CollUtil.isNotEmpty(responseContract.getModel())) {
                ansList.addAll(responseContract.getModel());
            }
            logisticsInquiryDetailDTO.setContractList(ansList);
        }catch (Exception e){
            log.info("queryContractSignFlowByCondition异常:{}", e.getMessage());
        }
        logisticsInquiryDetailDTO.setOrderShipType(getOrderShipType(logisticsInquiryDetailDTO.getShipNo()));
        return ResultMode.success(logisticsInquiryDetailDTO);
    }

    /**
     * 获取意愿签合同
     */
    private List getContractList(){
        ContractSignFlowRequest contractSignFlowRequest = new ContractSignFlowRequest();
        contractSignFlowRequest.setBizId(JwtUtil.getTokenInfo().getLicenseNo());
        contractSignFlowRequest.setType("40");
        contractSignFlowRequest.setFlowStatus("200");
        log.info("queryContractSignFlowByCondition参数-2:{}", contractSignFlowRequest);
        LoqisticsExchangeResponse responseContract = logisticExchangeInter.queryContractSignFlowByCondition(contractSignFlowRequest);
        log.info("queryContractSignFlowByCondition返回值-2:{}", responseContract);
        return responseContract.getModel();
    }

    public DictionaryResponse platformCmDictionaryByIds(@RequestBody List<String> request){
        return logisticExchangeInter.platformCmDictionaryByIds(request);
    }

    public LoqisticsExchangeResponse queryWaybillList(WaybillListQuery waybillListQuery) {
        WaybillListRequest request = BeanUtil.toBean(waybillListQuery, WaybillListRequest.class);
        log.info("LogisticsInquiryAppServicequeryWaybillList参数:{}", request);
        LoqisticsExchangeResponse exchangeResponse = logisticExchangeInter.queryWaybillList(request);
        log.info("LogisticsInquiryAppServicequeryWaybillList返回值:{}", exchangeResponse);
        return exchangeResponse;
    }

    public ResultMode queryDetailUrlByTemplateId(LogisticsTemplateIdRequest request) {
        log.info("[LogisticsInquiryAppService.queryDetailUrlByTemplateId] request:{}", request);
        LoqisticsExchangeResponse response = logisticExchangeInter.queryDetailUrlByTemplateId(request);
        if (response.getSucceed()){
            return ResultMode.success(response.getModel());
        }
        return ResultMode.fail(response.getErrCode(),response.getErrMsg());
    }

    public ResultMode<CompanyInfoDTO> getCompanyByModel(String socialCreditCode) {
        log.info("ctp-orch-user[LogisticsInquiryAppService.getCompanyByModel] request:{}", socialCreditCode);
        LoqisticsExchangeResponse<CompanyInfo> response = logisticExchangeInter.getCompanyByModel(new SocialCreditRequest().setSocialCreditCode(socialCreditCode));
        log.info("ctp-orch-user[LogisticsInquiryAppService.getCompanyByModel] response:{}", response);
        CompanyInfoDTO companyInfoDTO = new CompanyInfoDTO();
        companyInfoDTO.setRegister(CommonConstants.INTEGER_0);
        if (response.getSucceed() && CollUtil.isNotEmpty(response.getModel())){
            CompanyInfo first = CollUtil.getFirst(response.getModel());
            companyInfoDTO.setRegister(CommonConstants.INTEGER_1);
            companyInfoDTO.setContacts(first.getContacts());
            companyInfoDTO.setPhone(first.getPhone());
            return ResultMode.success(companyInfoDTO);
        }
        return ResultMode.success(companyInfoDTO);
    }

    private Integer getOrderShipType(String shipNo) {
        Integer orderShipType = null;
        if (StrUtil.isBlank(shipNo)) {
            return orderShipType;
        }
        OrderShipDetailQuery query = new OrderShipDetailQuery();
        query.setShipNo(shipNo);
        ResultMode<OrderShipDetailResDTO> orderShipDetailResDTOResultMode = orderShipAppService.orderShipDetail(query);
        OrderShipDetailResDTO orderShipDetailResDTO = orderShipDetailResDTOResultMode.getModel();
        if (orderShipDetailResDTO == null) {
            return orderShipType;
        }
        if (orderShipDetailResDTO.getOrderShipDTO() != null) {
            orderShipType = orderShipDetailResDTO.getOrderShipDTO().getType();
        }
        return orderShipType;
    }
}
