package com.wanlianyida.app.application.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.infrastructure.exchange.UserAuthExchangeService;
import com.wanlianyida.app.interfaces.model.command.UserInfoCommand;
import com.wanlianyida.app.interfaces.model.dto.UserFunctionsDTO;
import com.wanlianyida.app.interfaces.model.dto.UserInfoDTO;
import com.wanlianyida.app.interfaces.model.dto.UserPermissionDTO;
import com.wanlianyida.app.interfaces.model.query.UserInfoQuery;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcommon.enums.CommonCodeEnum;
import com.wanlianyida.framework.ctpcommon.enums.PlatfromCodeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssuserauth.api.model.command.UmLogininfoCommand;
import com.wanlianyida.fssuserauth.api.model.command.UserInfoPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.command.UserSystemPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.dto.UmFunctionDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UserSystemRelDTO;
import com.wanlianyida.fssuserauth.api.model.query.UserPermissionPlatformQuery;
import com.wanlianyida.partner.api.inter.MemberInter;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import com.wanlianyida.partner.api.model.command.MemberAddCommand;
import com.wanlianyida.partner.api.model.command.MemberDeleteCommand;
import com.wanlianyida.partner.api.model.command.MemberUpdateCommand;
import com.wanlianyida.partner.api.model.dto.MemberConditionDTO;
import com.wanlianyida.partner.api.model.dto.MemberDTO;
import com.wanlianyida.partner.api.model.dto.UmUserCacheDTO;
import com.wanlianyida.partner.api.model.query.MemberListQuery;
import com.wanlianyida.partner.api.model.query.MemberQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * @Author: Qin
 * @Date: 2024/11/23 12:57
 * @Description:
 **/
@Service
@Slf4j
public class UserInfoAppService {

    @Resource
    private RedisService redisService;

    @Resource
    private UmCompanyInter umCompanyInter;

    @Resource
    private UserAuthExchangeService userAuthExchangeService;

    @Resource
    private MemberInter memberInter;

    public ResultMode<List<UserFunctionsDTO>> getUserFunction() {
        ResultMode<List<UmFunctionDTO>> resultMode = userAuthExchangeService.getUserFunction(PlatfromCodeEnum.CTP_USER_SYS.getCode());
        List<UserFunctionsDTO> dtoList = BeanUtil.copyToList(resultMode.getModel(), UserFunctionsDTO.class);
        ResultMode<List<UserFunctionsDTO>> result = new ResultMode<>();
        result.setSucceed(true);
        result.setModel(dtoList);
        return result;
    }

    /**
     * 登录时取token缓存用户企业信息
     */
    public ResultMode<?> redisUserCompany() {
        // 缓存userBaseId关联的companyId和companyName到TokenInfo
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (StrUtil.isNotBlank(tokenInfo.getUserBaseId())) {
            umCompanyInter.redisUserCompany(tokenInfo.getUserBaseId());
        }
        return ResultMode.success();
    }

    public Map<String, String> getMapByUserIds(List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        ResultMode<List<UserInfoDTO>> listResultMode = userAuthExchangeService.queryListByUserIds(userIds);
        if (!listResultMode.isSucceed()) {
            return Collections.emptyMap();
        }
        List<UserInfoDTO> dtoList = listResultMode.getModel();
        return dtoList.stream().collect(Collectors.toMap(
                UserInfoDTO::getUserBaseId,
                UserInfoDTO::getUsername,
                (existingValue, newValue) -> existingValue));

    }

    public ResultMode addUserInfo(UserInfoCommand command) {
        UserInfoPlatformCommand info = new UserInfoPlatformCommand();
        BeanUtil.copyProperties(command, info);
        info.setSysType(PlatfromCodeEnum.CTP_MANAGE_SYS.getCode());
        ResultMode<String> addUserInfo = userAuthExchangeService.addUserInfo(info);
        if (!addUserInfo.isSucceed()) {
            return ResultMode.fail(CommonCodeEnum.BUSS_ERROR_DB_CREATE_ERR.getCode(), addUserInfo.getMessage());
        }
        // 获取用户id
        String userBaseId = addUserInfo.getModel();
        MemberAddCommand memberAddCommand = BeanUtil.copyProperties(command, MemberAddCommand.class);
        memberAddCommand.setUserBaseId(userBaseId);
        memberAddCommand.setUserName(command.getUsername());
        memberAddCommand.setStatus(command.getUserStatus());
        ResultMode<Boolean> resultMode = memberInter.add(memberAddCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("调用partner新增用户信息异常");
        }


        UserSystemPlatformCommand systemCommand = new UserSystemPlatformCommand();
        systemCommand.setUserBaseId(userBaseId);
        systemCommand.setSystemCode(PlatfromCodeEnum.CTP_MANAGE_SYS.getCode());
        userAuthExchangeService.updatePlatformUserSystemRel(systemCommand);
        return ResultMode.success();
    }

    public ResultMode updateUserInfo(UserInfoCommand command) {
        MemberUpdateCommand updateCommand = BeanUtil.copyProperties(command, MemberUpdateCommand.class);
        updateCommand.setStatus(command.getUserStatus());
        updateCommand.setUserName(command.getUsername());
        ResultMode<Boolean> resultMode = memberInter.update(updateCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("调用partner更新用户信息异常：" + resultMode.getMessage());
        }

        UserInfoPlatformCommand info = new UserInfoPlatformCommand();
        BeanUtil.copyProperties(command, info);
        return userAuthExchangeService.updateUserInfo(info);
    }

    public ResultMode deleteUserInfo(UserInfoCommand command) {
        MemberDeleteCommand deleteCommand = BeanUtil.copyProperties(command, MemberDeleteCommand.class);
        ResultMode<Boolean> resultMode = memberInter.delete(deleteCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("调用partner删除用户信息异常：" + resultMode.getMessage());
        }

        return userAuthExchangeService.delUserInfo(command.getUserBaseId());
    }

    public ResultMode<List<UserInfoDTO>> queryUserList(PagingInfo<UserInfoQuery> pagingInfo) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String companyId = tokenInfo.getCompanyId();
        if (StrUtil.isEmpty(companyId)) {
            return ResultMode.fail("用户未登录");
        }
        UserInfoQuery filterModel = pagingInfo.getFilterModel();

        MemberListQuery memberListQuery = BeanUtil.copyProperties(pagingInfo.filterModel, MemberListQuery.class);
        String username = filterModel.getUsername();
        if (StrUtil.isNotEmpty(username)) {
            memberListQuery.setUserName(username);
        }
        PagingInfo<MemberListQuery> queryPagingInfo = new PagingInfo<>();
        queryPagingInfo.setFilterModel(memberListQuery);
        queryPagingInfo.setPageLength(pagingInfo.getPageLength());
        queryPagingInfo. setCurrentPage(pagingInfo.getCurrentPage());
        ResultMode<List<MemberDTO>> resultMode = memberInter.queryList(queryPagingInfo);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("调用partner用户查询接口异常:" + resultMode.getMessage());
        }

        List<MemberDTO> memberDTOList = resultMode.getModel();

        if (CollUtil.isEmpty(memberDTOList)) {
            return ResultMode.success();
        }
        // 转map
        Map<String, MemberDTO> memberMap = memberDTOList.stream()
                .collect(Collectors.toMap(MemberDTO::getUserBaseId, Function.identity()));

        // 获取所有的userBaseId
        List<String> userBaseIds = memberDTOList.stream().map(MemberDTO::getUserBaseId).collect(Collectors.toList());

        // 查询用户基础信息
        ResultMode<List<UserInfoDTO>> listResultMode = userAuthExchangeService.queryListByUserIds(userBaseIds);
        if (!listResultMode.isSucceed()) {
            return ResultMode.fail(CommonCodeEnum.BUSS_ERROR_DB_QUERY_ERR.getCode(), listResultMode.getMessage());
        }

        List<UserInfoDTO> infoDTOList = listResultMode.getModel();
        log.info("<<<<<<infoDTOList:{}<<<<<<<<", infoDTOList);
        infoDTOList.stream()
                .filter(userInfoDTO -> userBaseIds.contains(userInfoDTO.getUserBaseId()))
                .forEach(userInfoDTO -> {
                    String userBaseId = userInfoDTO.getUserBaseId();
                    Optional.ofNullable(memberMap.get(userBaseId))
                            .ifPresent(memberDTO -> setUserInfoFromMember(userInfoDTO, memberDTO));
                });


        //补充创建人逻辑
        List<String> userIds = infoDTOList.stream().map(UserInfoDTO::getCreateBy).collect(Collectors.toList());
        Map<String, String> mapByUserIds = getMapByUserIds(userIds);
        if (!mapByUserIds.isEmpty()) {
            infoDTOList.forEach(item -> {
                item.setCreateBy(mapByUserIds.get(item.getCreateBy()));
            });
        }
        ResultMode<List<UserInfoDTO>> result = new ResultMode<>();
        result.setTotal(resultMode.getTotal());
        result.setSucceed(true);
        result.setModel(infoDTOList);
        return result;
    }

    public ResultMode<UserInfoDTO> queryByUserBaseId(UserInfoQuery query) {
        MemberListQuery listQuery = BeanUtil.copyProperties(query, MemberListQuery.class);
        if (StrUtil.isEmpty(listQuery.getUserBaseId())) {
            return ResultMode.fail("参数异常");
        }
        ResultMode<MemberDTO> resultMode = memberInter.queryDetail(listQuery);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("调用partner查询用户关联信息异常:" + resultMode.getMessage());
        }

        MemberDTO model = resultMode.getModel();
        if (ObjectUtil.isEmpty(model)) {
            return ResultMode.fail("未查询到用户信息");
        }

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        List<String> userBaseIds = new ArrayList<>();
        userBaseIds.add(query.getUserBaseId());

        // 查询用户基础信息
        ResultMode<List<UserInfoDTO>> userInfoDTOResultMode = userAuthExchangeService.queryListByUserIds(userBaseIds);
        if (!userInfoDTOResultMode.isSucceed()) {
            return ResultMode.fail(CommonCodeEnum.BUSS_ERROR_DB_QUERY_ERR.getCode(), userInfoDTOResultMode.getMessage());
        }
        List<UserInfoDTO> infoDTOList = userInfoDTOResultMode.getModel();
        if (CollUtil.isNotEmpty(infoDTOList)) {
            userInfoDTO = infoDTOList.get(0);
            if (ObjectUtil.isNotEmpty(userInfoDTO)) {
                setUserInfoFromMember(userInfoDTO, model);
            }
        }
        UserPermissionPlatformQuery permissionQuery = new UserPermissionPlatformQuery();
        permissionQuery.setUserBaseId(query.getUserBaseId());
        permissionQuery.setSysType(PlatfromCodeEnum.CTP_USER_SYS.getCode());
        ResultMode<List<UserPermissionDTO>> listResultMode = userAuthExchangeService.queryByUserBaseId(permissionQuery);
        if (!listResultMode.isSucceed()) {
            log.error("根据userBaseId获取角色信息失败，userBaseId:{}", query.getUserBaseId());
            return ResultMode.fail(CommonCodeEnum.BUSS_ERROR_DB_QUERY_ERR.getCode(), listResultMode.getMessage());
        }
        List<UserPermissionDTO> permissionList = listResultMode.getModel();
        userInfoDTO.setPermissionInfoList(permissionList);
        return ResultMode.success(userInfoDTO);
    }

    /**
     * 员工条件查询
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link MemberDTO }>
     */
    public ResultMode<List<MemberConditionDTO>> queryByCondition(@RequestBody MemberQuery query) {
        ResultMode<List<MemberConditionDTO>> resultMode = memberInter.queryByCondition(query);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("调用partner查询用户关联信息异常:" + resultMode.getMessage());
        }
        List<MemberConditionDTO> model = resultMode.getModel();
        if (CollUtil.isEmpty(model)) {
            return ResultMode.success();
        }

        return ResultMode.success(model);
    }

    public ResultMode<MemberConditionDTO> queryCompanyInfoByUserId(String userBaseId) {
        ResultMode<MemberConditionDTO> memberDTOResultMode = memberInter.queryCompanyInfoByUserId(userBaseId);
        log.info("查询用户关联信息:" + JSONUtil.toJsonStr(memberDTOResultMode));
        return memberDTOResultMode;
    }

    public ResultMode<List<UserSystemRelDTO>> queryUserSystemByUserBaseId() {
        return userAuthExchangeService.queryUserSystemByUserBaseId();
    }

    /**
     * 设置成员用户信息
     *
     * @param userInfoDTO 用户信息 DTO
     * @param memberDTO   成员 DTO
     */
    private void setUserInfoFromMember(UserInfoDTO userInfoDTO, MemberDTO memberDTO) {
        userInfoDTO.setCompanyName(Optional.ofNullable(memberDTO.getCompanyName()).orElse(""));
        userInfoDTO.setDeptList(Optional.ofNullable(memberDTO.getDeptList()).orElse(new ArrayList<>()));
        userInfoDTO.setPostName(Optional.ofNullable(memberDTO.getPostName()).orElse(""));
        userInfoDTO.setPost(Optional.ofNullable(memberDTO.getPostId()).orElse(""));
        userInfoDTO.setId(Optional.ofNullable(memberDTO.getId()).orElse(""));
        userInfoDTO.setLoginName(Optional.ofNullable(memberDTO.getLoginName()).orElse(""));
        userInfoDTO.setLevelType(Optional.ofNullable(memberDTO.getLevelType()).orElse(""));
        userInfoDTO.setRemark(Optional.ofNullable(memberDTO.getRemark()).orElse(""));
    }

    public ResultMode<List<UmUserCacheDTO>> updateCache() {
        return memberInter.updateCache();
    }

    public ResultMode updatePW(UmLogininfoCommand command) {
        return userAuthExchangeService.updatePw(command);
    }


    public ResultMode resetPW(UmLogininfoCommand command) {
        return userAuthExchangeService.resetPW(command);
    }

    /**
     * 退出登录
     *
     * @return
     */
    public ResultMode<String> webLogout() {
        return userAuthExchangeService.webLogout();
    }
}
