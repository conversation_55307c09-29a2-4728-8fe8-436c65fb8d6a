package com.wanlianyida.app.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.app.interfaces.model.command.PurchasePurposeOrderCommand;
import com.wanlianyida.framework.ctpcommon.entity.BaseException;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.command.*;
import com.wanlianyida.order.api.model.dto.PurOrderMappingDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.transaction.api.model.command.PurposeOrderCommand;
import com.wanlianyida.transaction.api.model.command.PurposeProductOrderCommand;
import com.wanlianyida.transaction.api.model.dto.PurchaseEmptyDetailDTO;
import com.wanlianyida.transaction.api.model.dto.QuotationDetailDTO;
import com.wanlianyida.transaction.api.model.dto.QuotationProductDTO;
import com.wanlianyida.transaction.api.model.dto.QuotationProductDetailDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月10日 16:39
 */
public class PurchaseAssembler {

    /**
     * 生成意向单
     */
    public static PurOrderCreateCommand purposeOrder(QuotationDetailDTO quotationDetail, PurchaseEmptyDetailDTO purchaseDetail, List<ProductSkuDetailDTO> productSkuDetailList) {
        PurOrderCreateCommand result = new PurOrderCreateCommand();
        result.setCreateMethod("30");
        result.setPurOrderSource(10);
        // 商品数据
        List<SellerProductCommand> productCommandList = buildSellerProduct(quotationDetail, productSkuDetailList);
        result.setPorderList(productCommandList);

        List<CtpProductSkuDetailCommand> skuDetailCommandList = BeanUtil.copyToList(productSkuDetailList, CtpProductSkuDetailCommand.class);
        result.setProductSkuDetails(skuDetailCommandList);
        // 地址信息
        OcPurOrderAddressCommand addressCommand = new OcPurOrderAddressCommand();
        addressCommand.setLinkName(purchaseDetail.getLinkmanName());
        addressCommand.setLinkTelephone(purchaseDetail.getLinkmanMobile());
        addressCommand.setProvince(purchaseDetail.getProvinceCode());
        addressCommand.setProvince(purchaseDetail.getProvinceName());
        addressCommand.setCityCode(purchaseDetail.getCityCode());
        addressCommand.setCity(purchaseDetail.getCityName());
        result.setAddress(addressCommand);
        // 买方信息
        CompanyCommand companyCommand = new CompanyCommand();
        companyCommand.setCompanyId(purchaseDetail.getCompanyId());
        companyCommand.setCompanyName(purchaseDetail.getCompanyName());
        companyCommand.setLinkName(purchaseDetail.getLinkmanName());
        companyCommand.setLinkTelephone(purchaseDetail.getLinkmanMobile());
        companyCommand.setAccount(JwtUtil.getTokenInfo().getLoginName());
        result.setBuyerInfo(companyCommand);
        return result;
    }

    private static List<SellerProductCommand> buildSellerProduct(QuotationDetailDTO quotationDetail, List<ProductSkuDetailDTO> productSkuDetailList) {
        SellerProductCommand result = new SellerProductCommand();
        result.setCompanyId(quotationDetail.getCompanyId());
        List<PurchaseGoodsCommand> productList = new ArrayList<>();
        Map<String, ProductSkuDetailDTO> mapping = productSkuDetailList.stream().collect(Collectors.toMap(ProductSkuDetailDTO::getSkuCode, e -> e));
        for (QuotationProductDetailDTO productDetail : quotationDetail.getProductList()) {
            QuotationProductDTO product = productDetail.getOfferProductData();
            if (StrUtil.isBlank(product.getSkuCode())) {
                continue;
            }
            if (productDetail.getOfferProductData() != null && StrUtil.isNotBlank(productDetail.getOfferProductData().getOrderNo())) {
                throw new BaseException("报价商品【" + product.getSkuCode() + "】已生成意向单！");
            }
            ProductSkuDetailDTO sku = mapping.get(product.getSkuCode());
            if (sku == null) {
                throw new BaseException("商品【" + product.getSkuCode() + "】未查询到相关信息");
            }
            if (!sku.getOnSaleStatus().equals(20)) {
                throw new BaseException("商品【" + product.getSkuCode() + "】已下架，不允许下单！");
            }
            PurchaseGoodsCommand productCommand = new PurchaseGoodsCommand();
            productCommand.setSkuCode(sku.getSkuCode());
            productCommand.setPrice(product.getPriceFee());
            productCommand.setPurchaseQuantity(product.getSupplyCount());
            productCommand.setSubtotal(product.getSubtotal());
            productCommand.setUnitCon(product.getUnitTransfer());
            productList.add(productCommand);
        }
        if (CollectionUtil.isEmpty(productList)) {
            throw new BaseException("所选商品已生成意向单！");
        }
        result.setProducts(productList);
        CompanyCommand companyCommand = new CompanyCommand();
        companyCommand.setCompanyName(quotationDetail.getCompanyName());
        companyCommand.setAccount(quotationDetail.getAccount());
        companyCommand.setCompanyId(quotationDetail.getCompanyId());
        companyCommand.setLinkName(quotationDetail.getLinkmanName());
        companyCommand.setLinkTelephone(quotationDetail.getLinkmanMobile());
        result.setSellerInfo(companyCommand);
        return Collections.singletonList(result);
    }

    public static PurposeOrderCommand getPurposeOrderCommand(PurchasePurposeOrderCommand command, QuotationDetailDTO quotationDetail, List<PurOrderMappingDTO> mappingList) {
        // 查询意向单号
        Map<String, PurOrderMappingDTO> mapping = mappingList.stream().collect(Collectors.toMap(PurOrderMappingDTO::getSkuCode, e -> e));
        PurposeOrderCommand purposeOrderCommand = new PurposeOrderCommand();
        purposeOrderCommand.setId(quotationDetail.getRelPurchaseId());
        List<PurposeProductOrderCommand> productOrderCommandList = new ArrayList<>();
        for (QuotationProductDetailDTO product : quotationDetail.getProductList()) {
            PurOrderMappingDTO purOrderMappingDTO = mapping.get(product.getOfferProductData().getSkuCode());
            if (purOrderMappingDTO != null) {
                PurposeProductOrderCommand productOrderCommand = new PurposeProductOrderCommand();
                productOrderCommand.setId(product.getOfferProductData().getId());
                productOrderCommand.setOrderNo(purOrderMappingDTO.getPorderNo());
                productOrderCommandList.add(productOrderCommand);
            }
        }
        purposeOrderCommand.setProductList(productOrderCommandList);
        return purposeOrderCommand;
    }
}
