package com.wanlianyida.app.application.assembler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.app.infrastructure.constant.ShopCartConstant;
import com.wanlianyida.app.interfaces.dto.AvailableAndInvalidDTO;
import com.wanlianyida.app.interfaces.dto.ShopCartRedisDTO;
import com.wanlianyida.app.interfaces.dto.ShopSkusDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;

import java.util.*;
import java.util.stream.Collectors;

public class ShopCardAssembler {


    public static ShopSkusDTO getShopAndSkuCode(Map<String, List<ShopCartRedisDTO>> shopCartMap) {
        Set<String> skuCodes = new HashSet<>();
        Iterator<Map.Entry<String, List<ShopCartRedisDTO>>> iterator = shopCartMap.entrySet().iterator();
        Map<String, Date> shopDateMapUseSort = new HashMap<>();
        while (iterator.hasNext()) {
            Map.Entry<String, List<ShopCartRedisDTO>> entry = iterator.next();
            String shopId = entry.getKey();
            List<ShopCartRedisDTO> value = entry.getValue();
            if (CollUtil.isEmpty(value)) {
                continue;
            }
            Set<String> skus = value.stream().map(d -> d.getSkuCode()).collect(Collectors.toSet());
            skuCodes.addAll(skus);
            Optional<ShopCartRedisDTO> latestCartItem = value.stream()
                    .max(Comparator.comparing(ShopCartRedisDTO::getUpdateDate));
            latestCartItem.ifPresent(item -> {
                shopDateMapUseSort.put(shopId, item.getUpdateDate());
            });
        }
        List<String> shopIds = Lists.newArrayList();
        LinkedHashMap<String, Date> sortedByValue = (LinkedHashMap<String, Date>) MapUtil.sortByValue(shopDateMapUseSort, true);
        sortedByValue.forEach((k, v) -> {
            shopIds.add(k);
        });
        ShopSkusDTO shopSkusDTO = new ShopSkusDTO();
        shopSkusDTO.setShopIds(shopIds);
        shopSkusDTO.setSkus(skuCodes);
        return shopSkusDTO;
    }

    public static AvailableAndInvalidDTO getAvailableInvalidPair(List<ProductSkuDetailDTO> skuDetailModel, String companyId) {
        List<ProductSkuDetailDTO> availableList = Lists.newArrayList();
        List<ProductSkuDetailDTO> invalidList = Lists.newArrayList();
        skuDetailModel.forEach(e -> {
            List<String> publishCompanyIdList = e.getPublishCompanyIdList() == null ? Lists.newArrayList() : e.getPublishCompanyIdList();
            boolean publishCompanyFlg = true;
            if(StrUtil.equals(ShopCartConstant.SKU_PUBLISH_TYPE_20,e.getPublishType())){
                publishCompanyFlg = publishCompanyIdList.contains(companyId);
            }
            boolean saleStatusFlg = ObjUtil.equals(e.getOnSaleStatus(), ShopCartConstant.SKU_OK);
            if (publishCompanyFlg && saleStatusFlg) {
                availableList.add(e);
            } else {
                invalidList.add(e);
            }
        });
        AvailableAndInvalidDTO availableAndInvalidDTO = new AvailableAndInvalidDTO();
        availableAndInvalidDTO.setAvailableList(availableList);
        availableAndInvalidDTO.setInvalidList(invalidList);
        return availableAndInvalidDTO;
    }

}
