package com.wanlianyida.app.application.assembler;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.app.application.model.bo.WxUserAuthBO;
import com.wanlianyida.app.interfaces.model.dto.BindWxDTO;
import com.wanlianyida.basemdm.api.model.command.MiniProgramsAddCommand;
import com.wanlianyida.basemsg.api.model.command.WeixinSubscribeMsgCommand;
import com.wanlianyida.baseots.api.model.query.WxUserAuthQuery;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.partner.api.model.query.MemberQuery;
import com.wanlianyida.support.api.model.command.WxMsgCommand;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class WxMsgAssembler {

    /**
     * 构建订阅消息command
     */
    public static List<WeixinSubscribeMsgCommand> buildSubscribeMsgCommand(String appId, List<String> openIdList, WxMsgCommand msgCommand,String miniProgramState) {
        if(CollectionUtil.isEmpty(openIdList)){
            return new LinkedList<>();
        }
        List<String> distinctOpenIdList = openIdList.stream().distinct().collect(Collectors.toList());
        List<WeixinSubscribeMsgCommand> commandList = new LinkedList<>();
        distinctOpenIdList.forEach(openId -> {
            WeixinSubscribeMsgCommand command = new WeixinSubscribeMsgCommand();
            command.setAppid(appId);
            command.setTouser(openId);
            command.setTemplateId(msgCommand.getTemplateEnum().getWxTemplateId());
            command.setData(convertMap(msgCommand.getParamMap()));
            // 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
            command.setMiniprogramState(miniProgramState);
            // msgId
            command.setMsgId(UUID.randomUUID().toString());
            commandList.add(command);
        });
        return commandList;
    }

    private static Map<String, WeixinSubscribeMsgCommand.Filed> convertMap(Map<String, WxMsgCommand.TemplateParam> paramMap){
        if(paramMap == null){
            return null;
        }
        Map<String, WeixinSubscribeMsgCommand.Filed> map = new HashMap<>();
        paramMap.forEach((k, v) -> {
            WeixinSubscribeMsgCommand.Filed filed = new WeixinSubscribeMsgCommand.Filed();
            filed.setValue(v.getValue());
            map.put(k, filed);
        });
        return map;
    }

    /**、
     * 构建获取用户id实体
     * @param appId appId
     * @param code 微信授权码（前端传输）
     */
    public static WxUserAuthQuery getWxUserAuthQuery(String appId, String code){
        WxUserAuthQuery query = new WxUserAuthQuery();
        query.setAppid(appId);
        query.setCode(code);
        return query;
    }

    /**
     * 构建绑定用户请求实体
     * @param appId appId
     * @param bo bo
     * @param userId 用户id，不是userBaseId
     */
    public static MiniProgramsAddCommand buildMiniProgramsAddCommand(String appId, WxUserAuthBO bo, String userId){
        MiniProgramsAddCommand miniProgramsAddCommand = new MiniProgramsAddCommand();
        miniProgramsAddCommand.setAppId(appId);
        miniProgramsAddCommand.setOpenId(bo.getOpenpid());
        miniProgramsAddCommand.setUnionId(bo.getUnionid());
        if(!StringUtils.isBlank(userId)){
            miniProgramsAddCommand.setUserId(Long.valueOf(userId));
        } else{
            miniProgramsAddCommand.setUserId(Long.valueOf(JwtUtil.getTokenInfo().getUserId()));
        }
        return miniProgramsAddCommand;
    }

    /**
     * 构建绑定用户返回DTP
     * @param appId aooId
     * @param bo bo
     */
    public static BindWxDTO buildBindWxDTO(String appId, WxUserAuthBO bo){
        BindWxDTO dto = new BindWxDTO();
        dto.setAppId(appId);
        dto.setOpenId(bo.getOpenpid());
        return dto;
    }

    /**
     * 构建查询管理员MemberQuery
     */
    public static MemberQuery buildMemberQuery(String companyId){
        MemberQuery memberQuery = new MemberQuery();
        memberQuery.setCompanyId(companyId);
        memberQuery.setLevelType("1");
        return memberQuery;
    }

}
