package com.wanlianyida.app.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "commerce.assistant")
public class SmartCustomerServiceProperties {

    private String key;

    private String smartUrl;

    private String speechUrl;

    private List<String> commonProblemList;
}
