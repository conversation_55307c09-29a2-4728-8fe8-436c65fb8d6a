package com.wanlianyida.app.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * appId配置
 */
@Data
@Configuration
@ConfigurationProperties("micro-app")
public class AppConfig {
    /**
     * 买家小程序appId
     */
    private String buyerAppId ;

    /**
     * 买家小程序secret
     */
    private String buyerSecret ;

    /**
     * 卖家小程序appId
     */
    private String sellerAppId ;

    /**
     * 卖家小程序secret
     */
    private String sellerSecret ;

    /**
     * 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
     */
    private String miniProgramState;
}
