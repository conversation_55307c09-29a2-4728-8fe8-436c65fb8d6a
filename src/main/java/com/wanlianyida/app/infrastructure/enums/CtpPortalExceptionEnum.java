package com.wanlianyida.app.infrastructure.enums;

import lombok.Getter;

@Getter
public enum CtpPortalExceptionEnum {
    USER_ERROR_PARAM_EMPTY("P10001", "参数不允许为空"),
    QUANTITY_ERROR("P10002","购买数量需在%s和%s之间"),
    ID_NOT_EMPTY("P10003", "id不能为空"),
    PHONE_NOT_EMPTY("P10004", "手机号不能为空"),
    PHONE_LENGTH_ERROR("P10005", "手机号长度为11位"),
    PHONE_FORMAT_ERROR("P10006", "手机号格式错误"),
    // isNeedQueryPhone不能为空
    IS_NEED_QUERY_PHONE_NOT_EMPTY("P10007", "isNeedQueryPhone不能为空"),
    // 验证码不能为空
    CODE_NOT_EMPTY("P10008", "验证码不能为空"),
    // 验证码类型不能为空
    TYPE_NOT_EMPTY("P10009", "验证码类型不能为空"),
    // 当前用户未登录
    USER_NOT_LOGIN("P100010", "当前用户未登录"),
    // 验证码类型错误
    TYPE_ERROR("P100011", "验证码类型错误"),
    // 验证码错误
    CODE_ERROR("P100012", "验证码错误"),
    // 验证码已过期失效
    CODE_EXPIRE("P100013", "验证码已过期失效"),
    // 密码不能为空
    PASSWORD_NOT_EMPTY("P100014", "密码不能为空"),
    // 密码长度为6-16位
    PASSWORD_LENGTH_ERROR("P100015", "密码长度为6-16位"),
    // 密码中必须包含数字和英文
    PASSWORD_FORMAT_ERROR("P100016", "密码中必须包含数字和英文"),
    // 密码中不能包含中文
    PASSWORD_CHINESE_ERROR("P100017", "密码中不能包含中文"),
    NEW_PHONE_NOT_EMPTY("P100018","新手机号不能为空" ),
    NEW_PHONE_CODE_NOT_EMPTY("P100019", "新手机号验证码不能为空"),
    EMAIL_ADDRESS_NOT_EMPTY("P100020","请输入邮箱地址" ),
    // 参数不全
    PARAM_NOT_EMPTY("P100021", "请完整填写后提交"),
    // 当前用户未登录
    USER_NOT_LOGIN_ERROR("P100022", "当前用户未登录"),
    // 调用mdm服务查询用户信息失败
    MDM_SERVICE_ERROR("P100023", "调用mdm服务查询用户信息失败"),

    PRODUCT_STOCK_ERROR("P100024", "部分商品库存数量不足,请返回购物车修改"),
    ;

    private final String code;

    private final String msg;

    CtpPortalExceptionEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
