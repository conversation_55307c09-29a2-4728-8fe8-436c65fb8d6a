package com.wanlianyida.app.infrastructure.enums;

import lombok.Getter;

/**
 * 工作台统计枚举
 */
@Getter
public enum SatisticsBizTypeEnum {
    TODO_DATA_COUNT("10", "卖家-待办数据"),
    BIZ_DATA_COUNT("20", "卖家-经营数据"),

    TRANSACTION_AMOUNT("30", "卖家-成交金额趋势图"),
    TRANSACTION_NUMBER("40", "卖家-成交单数趋势图"),
    AVG_PRICE("50", "卖家-客单价趋势图"),
    PAGE_VIEW("60", "卖家-浏览量趋势图"),

    BUYER_TODO_DATA_COUNT("70", "买家-待办数据"),
    BUYER_BIZ_DATA_COUNT("80", "买家-采集数据"),
    BUYER_TRANSACTION_AMOUNT("90", "买家-成交金额趋势图"),
    BUYER_TRANSACTION_NUMBER("100", "买家-成交单数趋势图"),
    BUYER_AVG_PRICE("110", "买家-客单价趋势图"),
    BUYER_ORDER_TOTAL_AMOUNT("120", "买家_店铺采购情况_按订单总金额统计"),
    BUYER_ORDER_TOTAL_COUNT("130", "买家_店铺采购情况_按订单数量统计"),
    ;
    private final String code;
    private final String desc;

    SatisticsBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
