package com.wanlianyida.app.infrastructure.enums;

import com.wanlianyida.app.infrastructure.constant.Constant;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum PhoneCodeTypeEnum {

    REGISTER("10", "注册", Constant.REGISTER_MSG_TEMPLATE_ID),
    LOGIN("20", "登录",""),
    FIND_PASSWORD("30", "找回密码",""),
    UPDATE_PHONE("40", "修改手机号",Constant.UPDATE_PHONE_MSG_TEMPLATE_ID),
    APPLY_ADMIN("50", "申请管理员",Constant.APPLY_ADMIN_MSG_TEMPLATE_ID),
    ADMIN_TRANSFER("60", "管理员过户",Constant.ADMIN_HANDOVER_MSG_TEMPLATE_ID),
    // 员工解绑
    EMPLOYEE_UNBIND("70", "员工解绑",Constant.EMPLOYEE_UNBIND_MSG_TEMPLATE_ID);

    private final String code;
    private final String desc;
    private final String templateId;

    PhoneCodeTypeEnum(String code, String desc, String templateId) {
        this.code = code;
        this.desc = desc;
        this.templateId = templateId;
    }

    public static PhoneCodeTypeEnum getByCode(String code) {
        for (PhoneCodeTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
