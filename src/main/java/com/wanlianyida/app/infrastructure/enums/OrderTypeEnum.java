package com.wanlianyida.app.infrastructure.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum OrderTypeEnum {
    DEFAULT_SORT(0, "默认排序"),
    PRICE_ASC(1, "价格升序"),
    PRICE_DESC(2, "价格降序"),
    ON_SALE_DATE_ASC(3, "上架时间升序"),
    ON_SALE_DATE_DESC(4, "上架时间降序"),
    ;

    Integer code;
    String desc;

    OrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
