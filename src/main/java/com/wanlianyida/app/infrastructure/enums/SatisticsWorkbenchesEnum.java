package com.wanlianyida.app.infrastructure.enums;

import lombok.Getter;

@Getter
public enum SatisticsWorkbenchesEnum {

    /**
     * 卖家
     */
    SELLER_PRODUCT_REJECTED_COUNT("seller_product_rejected_count", "卖家中心_商品审核驳回数量"),
    SELLER_NEED_CONFIRMED_PORDERCOUNT("seller_porder_need_confirmed_pordercount", "卖家中心_意向单_待卖家确认的意向单数量和待生成订单的意向单数量"),
    SELLER_NEEDDEALORDERS( "seller_order_needDealOrders", "卖家中心_订单_待签约订单数_待结算订单数_待发货订单数_待确认收款订单数"),
    SELLER_NDAYTOTALDATA( "seller_order_ndayTotalData", "卖家中心_经营数据_最近n天的成交总金额_总成交单数_总客单价"),
    SELLER_NDAYTRENDDATA("seller_order_ndayTrendData", "卖家中心_经营数据_最近n天的成交金额_成交单数_客单价"),

    PRODUCT_BROWSER_NDAYPERDAYCOUNT("product_browser_ndayPerDayCount", "卖家中心_商品浏览量统计_最近n天每天的商品总浏览量统计"),
    PRODUCT_BROWSER_NDAYCOUNT("product_browser_ndayCount", "卖家中心_商品浏览量统计_最近n天的商品总浏览量统计"),

    /**
     * 买家
     */
    BUYER_PURCHASE_REJECTEDANDCOMPARINCOUNT("buyer_purchase_rejectedAndComparinCount", "买家中心_求购_审核驳回数量和比价中的数量"),
    BUYER_PORDER_CONFIRMCOUNT("buyer_porder_confirmCount", "买家中心_意向单_待卖家确认和待生成订单数量"),
    BUYER_ORDER_NEEDDEALORDERCOUNT("buyer_order_needDealOrderCount", "买家中心_订单_5个待办数量统计"),
    BUYER_SHOPPURCHASE_ORDERTOTALAMOUNTLIST("buyer_shoppurchase_orderTotalAmountList", "买家中心_店铺采购情况_按订单总金额统计"),
    BUYER_SHOPPURCHASE_ORDERTOTALCOUNTLIST("buyer_shoppurchase_orderTotalCountList", "买家中心_店铺采购情况_按订单数量统计"),
    BUYER_ORDER_NDAYTOTALDATA("buyer_order_ndayTotalData", "买家中心_采购数据_最近n天的订单总金额_订单总数量_总的单均价"),
    BUYER_ORDER_NDAYTRENDDATA("buyer_order_ndayTrendData", "买家中心_采购数据_最近n天的订单金额_订单数量_单均价")


    ;

    private String code;


    private String desc;

    SatisticsWorkbenchesEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
