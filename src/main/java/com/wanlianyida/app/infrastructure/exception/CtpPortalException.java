package com.wanlianyida.app.infrastructure.exception;

import com.wanlianyida.app.infrastructure.enums.CtpPortalExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.BaseException;
import com.wanlianyida.framework.ctpcommon.enums.CommonCodeEnum;

/**
 * partner 异常类
 *
 * <AUTHOR>
 * @date 2025/01/09
 */
public class CtpPortalException extends BaseException {
    private String msg;

    private String code = CommonCodeEnum.BUSS_ERROR_BCOM0500.getCode();

    @Override
    public String getMessage() {
        return super.getMessage();
    }

    public CtpPortalException() {
        super();
    }

    public CtpPortalException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public CtpPortalException(String msg, String code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }


    public CtpPortalException(String msg, String code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

    public CtpPortalException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public CtpPortalException(CtpPortalExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMsg());
        this.code = exceptionEnum.getCode();
        this.msg = exceptionEnum.getMsg();
    }

    public CtpPortalException(CtpPortalExceptionEnum exceptionEnum, String msg) {
        super(msg);
        this.code = exceptionEnum.getCode();
        this.msg = msg;
    }
}
