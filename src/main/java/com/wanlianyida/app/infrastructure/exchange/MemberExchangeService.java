package com.wanlianyida.app.infrastructure.exchange;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.MemberInter;
import com.wanlianyida.partner.api.model.dto.MemberConditionDTO;
import com.wanlianyida.partner.api.model.query.MemberQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class MemberExchangeService {

    @Resource
    private MemberInter memberInter;

    /**
     * 员工信息条件查询查询
     * @param query
     * @return
     */
    public ResultMode<List<MemberConditionDTO>> queryByCondition(MemberQuery query){
        return memberInter.queryByCondition(query);
    }

}
