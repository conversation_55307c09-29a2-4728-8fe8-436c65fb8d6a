package com.wanlianyida.app.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.app.interfaces.model.dto.ChBannerInfoListDTO;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.inter.*;
import com.wanlianyida.fssbasecontent.api.model.command.*;
import com.wanlianyida.fssbasecontent.api.model.dto.*;
import com.wanlianyida.fssbasecontent.api.model.query.*;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/02/08/09:18
 */
@Slf4j
@Service
public class BaseContentExchangeService {

    @Resource
    private AdSpaceInter adSpaceInter;
    @Resource
    private ChSiteInter chSiteInter;

    @Resource
    private BannerInfoInter bannerInfoInter;

    @Resource
    private ChCategoryCenterInter chCategoryCenterInter;


    @Resource
    private ChInformationInter chInformationInter;


    @Resource
    private SiteResourceInter siteResourceInter;

    @Resource
    private ChTagsInter chTagsInter;


    public ResultMode<Boolean> addSpaceInterAdd(AdSpaceAddCommand command) {
        ResponseMessage<Boolean> responseMessage = adSpaceInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> addSpaceInterUpdate(AdSpaceUpdateCommand command) {
        ResponseMessage<Boolean> responseMessage = adSpaceInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> addSpaceInterDelete(AdSpaceDeleteCommand command) {
        ResponseMessage<Boolean> responseMessage = adSpaceInter.batchDelete(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<AdSpaceDTO>> addSpaceInterQueryList(AdSpaceListQuery query) {
        ResponseMessage<List<AdSpaceDTO>> responseMessage = adSpaceInter.queryList(query);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(BeanUtil.copyToList(responseMessage.getModel(), AdSpaceDTO.class), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<SiteDTO> chSiteInterQueryByPlatformCode(String platformCode) {
        ResponseMessage<SiteDTO> responseMessage = chSiteInter.queryByPlatformCode(platformCode);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterAdd(BannerInfoAddCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterUpdate(BannerInfoUpdateCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterDelete(BannerInfoDeleteCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.batchDelete(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> bannerInfoInterQueryPage(PagingInfo<BannerInfoListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<BannerInfoListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<?> responseMessage = bannerInfoInter.queryPage(pagingInfo);
        if (responseMessage.isSucceed()) {
            Object model = responseMessage.getModel();
            if (Objects.nonNull(model)) {
                List<ChBannerInfoListDTO> list = JSON.parseArray(JSON.toJSONString(model), ChBannerInfoListDTO.class);
                List<ChBannerInfoListDTO> collect = list.stream().filter(node -> node.getEndTime().compareTo(new Date()) > 0
                        && node.getStartTime().compareTo(new Date()) <= 0
                        && node.getAuditStatus().equals(20)).collect(Collectors.toList());
                return ResultMode.successPageList(collect, collect.size());
            }
            return ResultMode.successPageList(model, responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<BannerDTO>> bannerInfoInterQueryList(Long relAdId) {
        ResponseMessage<List<BannerDTO>> responseMessage = bannerInfoInter.queryList(relAdId);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(BeanUtil.copyToList(responseMessage.getModel(), BannerDTO.class), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterBatchAudit(BannerAuditCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.batchAudit(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chInformationInterAdd(ChinformationAddICommand addCommand) {
        ResponseMessage<?> responseMessage = chInformationInter.add(addCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chInformationInterUpdate(ChinformationUpdateICommand updateCommand) {
        ResponseMessage<?> responseMessage = chInformationInter.update(updateCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> siteResourceInterAdd(SiteResourceAddCommand command) {
        ResponseMessage<Boolean> responseMessage = siteResourceInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> siteResourceInterUpdate(SiteResourceUpdateCommand command) {
        ResponseMessage<Boolean> responseMessage = siteResourceInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<SiteResourceDTO>> siteResourceInterQueryList() {
        ResponseMessage<List<SiteResourceDTO>> responseMessage = siteResourceInter.queryList();
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<ChInformationDetailDTO> chInformationInterDetailById(String infoId) {
        ResponseMessage<ChInformationDetailDTO> responseMessage = chInformationInter.detailById(infoId);
        if (responseMessage.isSucceed()) {
            return ResultMode.success();
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<ChInformationDetailUserDTO> chInformationInterDetailByCallAlias(String callAlias) {
        ResponseMessage<ChInformationDetailUserDTO> responseMessage = chInformationInter.detailByCallAlias(callAlias, null);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<ChInformationListDTO>> chInformationInterList(PagingInfo<ChInformationListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<ChInformationListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<List<ChInformationListDTO>> responseMessage = chInformationInter.list(pagingInfo);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<ChInformationListUserDTO>> chInformationInterListUser(PagingInfo<ChInformationListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<ChInformationListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<List<ChInformationListUserDTO>> responseMessage = chInformationInter.listPageUser(pagingInfo);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<ChInformationDetailUserDTO> chInformationInterDetailByIdUser(String infoId) {
        ResponseMessage<ChInformationDetailUserDTO> responseMessage = chInformationInter.detailByIdUser(infoId, null);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> chInformationInterBatchDelete(ChInformationBatchDeleteICommand deleteCommand) {
        ResponseMessage<Boolean> responseMessage = chInformationInter.batchDelete(deleteCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> chInformationInterBatchAudit(ChInformationAuditDeleteICommand auditCommand) {
        ResponseMessage<Boolean> responseMessage = chInformationInter.batchAudit(auditCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Map<String, List<ChInformationListDTO>>> chInformationInterHomepage(Map<String, Map<String, String>> map) {
        ResponseMessage<Map<String, List<ChInformationListDTO>>> responseMessage = chInformationInter.homepage(map);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chTagsInterlist(ChTagsListQuery query) {
        ResponseMessage<?> responseMessage = chTagsInter.list(query);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chTagsInterlist(PagingInfo<ChTagsListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<ChTagsListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<?> responseMessage = chTagsInter.list(pagingInfo);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chTagsInterAdd(ChTagsAddICommand addCommand) {
        ResponseMessage<?> responseMessage = chTagsInter.add(addCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chTagsInterUpdate(ChTagsUpdateICommand updateCommand) {
        ResponseMessage<?> responseMessage = chTagsInter.update(updateCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> chTagsInterBatchDelete(ChTagsBatchDeleteICommand deleteCommand) {
        ResponseMessage<Boolean> responseMessage = chTagsInter.batchDelete(deleteCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chCategoryCenterInterList(ChCategoryListQuery query) {
        ResponseMessage<?> responseMessage = chCategoryCenterInter.list(query);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chCategoryCenterInterAdd(ChCategoryAddICommand command) {
        ResponseMessage<?> responseMessage = chCategoryCenterInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> chCategoryCenterInterUpdate(ChCategoryUpdateICommand command) {
        ResponseMessage<Boolean> responseMessage = chCategoryCenterInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chCategoryCenterInterDelete(ChCategoryDeleteICommand command) {
        ResponseMessage<?> responseMessage = chCategoryCenterInter.deleteById(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }


}
