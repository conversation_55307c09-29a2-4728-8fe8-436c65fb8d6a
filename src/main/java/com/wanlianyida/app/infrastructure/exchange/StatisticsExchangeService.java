package com.wanlianyida.app.infrastructure.exchange;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.inter.StatisticsQueryInter;
import com.wanlianyida.order.api.model.dto.*;
import com.wanlianyida.order.api.model.query.ResellMerchantQuery;
import com.wanlianyida.order.api.model.query.StatisticsBusinessQuery;
import com.wanlianyida.order.api.model.query.StatisticsPurchaseQuery;
import com.wanlianyida.product.api.inter.StatisticsProductInter;
import com.wanlianyida.product.api.model.command.CompanyIdQuery;
import com.wanlianyida.product.api.model.dto.StatisticsProductDTO;
import com.wanlianyida.transaction.api.inter.PurchaseStatisticsInter;
import com.wanlianyida.transaction.api.model.dto.StatisticsBuyerPurchaseDTO;
import com.wanlianyida.transaction.api.model.query.PurchaseCompanyQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * 工作台统计信息
 */
@Slf4j
@Component
public class StatisticsExchangeService {

    @Resource
    private StatisticsQueryInter statisticsQueryInter;

    @Resource
    private StatisticsProductInter statisticsProductInter;

    @Resource
    private PurchaseStatisticsInter purchaseStatisticsInter;

    /**
     * 商品驳回
     */
    public StatisticsProductDTO statisticsProductQuery(CompanyIdQuery query){
        StatisticsProductDTO productDTO = statisticsProductInter.statisticsProductQuery(query).getModel();
        return productDTO;
    }

    /**
     * 意向单统计
     */
    public StatisticsPurOrderDTO statisticsPurOrderQuery(StatisticsPurchaseQuery purchaseQuery) {
        StatisticsPurOrderDTO statisticsPurOrderDTO = statisticsQueryInter.statisticsPurOrderQuery(purchaseQuery).getModel();
        return statisticsPurOrderDTO;
    }

    /**
     * 订单统计
     */
    public StatisticsOrderDTO statisticsOrderQuery(ResellMerchantQuery query) {
        StatisticsOrderDTO statisticsOrderDTO = statisticsQueryInter.statisticsOrderQuery(query).getModel();
        return statisticsOrderDTO;
    }

    /**
     * 经营数据
     */
    public StatisticsBusinessDTO statisticsBusinessQuery(StatisticsBusinessQuery query) {
        StatisticsBusinessDTO businessDTO = statisticsQueryInter.statisticsBusinessQuery(query).getModel();
        return businessDTO;
    }

    /**
     * 趋势图sql查询 按天成交金额统计（带企业ID过滤）
     */
    public List<StatisticsTableDTO> totalAmountQuery(StatisticsBusinessQuery query) {
        List<StatisticsTableDTO> statisticsBusinessDTO = statisticsQueryInter.totalAmountQuery(query).getModel();
        return statisticsBusinessDTO;
    }

    /**
     * 趋势图sql查询 按天成交单数统计（带企业ID过滤）
     */
    public List<StatisticsTableDTO> totalOrdersQuery(StatisticsBusinessQuery query) {
        List<StatisticsTableDTO> businessDTO = statisticsQueryInter.totalOrdersQuery(query).getModel();
        return businessDTO;
    }

    /**
     * 趋势图sql查询 按天客单价统计（带企业ID过滤）
     */
    public List<StatisticsTableDTO> avgPriceQuery(StatisticsBusinessQuery query) {
        List<StatisticsTableDTO> businessDTO = statisticsQueryInter.avgPriceQuery(query).getModel();
        return businessDTO;
    }

    /**
     * 求购统计
     */
    public StatisticsBuyerPurchaseDTO statisticsBuyerPurchaseQuery(PurchaseCompanyQuery purchaseCompanyQuery) {
        StatisticsBuyerPurchaseDTO statisticsBuyerPurchaseDTO = purchaseStatisticsInter.statisticsBuyerPurchaseQuery(purchaseCompanyQuery).getModel();
        return statisticsBuyerPurchaseDTO;

    }

    /**
     * 买家工作台订单
     */
    public StatisticsBuyerOrderDTO statisticsBuyerOrderQuery(ResellMerchantQuery merchantQuery) {
        StatisticsBuyerOrderDTO buyerOrderDTO = statisticsQueryInter.statisticsBuyerOrderQuery(merchantQuery).getModel();
        return buyerOrderDTO;
    }

    /**
     * 店铺采购情况-金额
     */
    public ResultMode<List<StatisticsShopOrderDTO>> statisticsShopOrderAmountQuery(ResellMerchantQuery merchantQuery){
        ResultMode<List<StatisticsShopOrderDTO>> statisticsShopOrderDTOResultMode = statisticsQueryInter.statisticsShopOrderAmountQuery(merchantQuery);
        return statisticsShopOrderDTOResultMode;
    }

    /**
     * 店铺采购情况-数量
     */
    public ResultMode<List<StatisticsShopOrderDTO>> statisticsShopOrderCountQuery(ResellMerchantQuery merchantQuery){
        ResultMode<List<StatisticsShopOrderDTO>> statisticsShopOrderDTOResultMode = statisticsQueryInter.statisticsShopOrderCountQuery(merchantQuery);
        return statisticsShopOrderDTOResultMode;
    }
}
