package com.wanlianyida.app.infrastructure.exchange;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.search.api.IEsCtpProductContentService;
import com.wanlianyida.search.dto.CtpProductContentDTO;
import com.wanlianyida.search.query.CtpProductContentQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class EsExchangeService {

    public static final String PRODUCT_RICH_TEXT_TYPE = "pc_product_spu_rich_text";

    @Resource
    private IEsCtpProductContentService iEsCtpProductContentService;

    /**
     * 查询商品富文本数据
     */
    public String getProductRichText(String spuCode) {
        CtpProductContentQuery ctpProductContentQuery = new CtpProductContentQuery();
        ctpProductContentQuery.setContentType(PRODUCT_RICH_TEXT_TYPE);
        ctpProductContentQuery.setBizId(spuCode);
        ResponseMessage<CtpProductContentDTO> res = iEsCtpProductContentService.getDetail(ctpProductContentQuery);
        if (res.isSucceed() && res.getModel() != null) {
            return res.getModel().getContentText();
        }
        return "";
    }
}
