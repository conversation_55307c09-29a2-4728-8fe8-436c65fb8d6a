package com.wanlianyida.app.interfaces.dto;

import lombok.Data;

import java.util.List;

/**
 * 大宗商品sku分组 DTO
 *
 * <AUTHOR>
 */
@Data
public class SkuAggregationDTO {
    /**
     * 总记录数
     */
    private Long totalCount;
    /**
     * 品牌列表
     */
    private List<Brand> brandList;
    /**
     * 交货地城市列表
     */
    private List<City> deliveryAddrCityList;
    /**
     * 品类列表
     */
    private List<Category> categoryList;

    @Data
    public static class Brand {
        /**
         * 品牌ID
         */
        private Long brandId;
        /**
         * 品牌名称
         */
        private String brandName;
    }

    @Data
    public static class Category {
        /**
         * 品类ID
         */
        private Long categoryId;
        /**
         * 品类级别
         */
        private Integer categoryLevel;
        /**
         * 品类名称
         */
        private String categoryName;
        /**
         * 子品类列表
         */
        private List<Category> childCategoryList;
    }

    @Data
    public static class City {
        /**
         * 市编码
         */
        private String cityCode;
        /**
         * 市名称
         */
        private String cityName;
    }
}
