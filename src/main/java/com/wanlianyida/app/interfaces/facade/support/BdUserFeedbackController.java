package com.wanlianyida.app.interfaces.facade.support;

import com.wanlianyida.app.application.service.support.BdUserFeedbackService;
import com.wanlianyida.app.interfaces.model.command.FeedbackCommand;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackAndAttachDTO;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackListDTO;
import com.wanlianyida.support.api.model.query.BdUserFeedbackQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/support/bdUserFeedback")
public class BdUserFeedbackController {

    @Resource
    BdUserFeedbackService bdUserFeedbackService;

    /**
     * 添加用户反馈
     */
    @PostMapping("/add")
    public ResultMode add(@Validated @RequestBody FeedbackCommand command) {
        return bdUserFeedbackService.add(command);
    }


    /**
     * 用户反馈列表
     */
    @PostMapping("/pageList")
    public ResultMode<List<BdUserFeedbackListDTO>> pageList(@RequestBody PagingInfo<BdUserFeedbackQuery> pagingInfo){
        return bdUserFeedbackService.pageList(pagingInfo);
    }


    /**
     * 用户反馈详情
     */
    @PostMapping("/getDetail")
    public ResultMode<BdUserFeedbackAndAttachDTO> getDetail(@RequestBody @Valid BdUserFeedbackQuery query){
        return bdUserFeedbackService.getDetail(query);
    }


}
