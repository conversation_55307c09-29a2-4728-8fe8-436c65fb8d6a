package com.wanlianyida.app.interfaces.facade.statistics;

import com.wanlianyida.app.application.service.statistics.StatisticsAppService;
import com.wanlianyida.app.application.service.statistics.dto.StatisticsDataDTO;
import com.wanlianyida.app.application.service.statistics.query.StatisticsDataQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api("工作台统计信息接口")
@RestController
@RequestMapping("/statistics")
public class StatisticsAppController {
    @Resource
    StatisticsAppService statisticsAppService;

    /**
     * 查询统计相关数据
     */
    @ApiOperation("查询统计相关数据")
    @PostMapping("/queryStatisticsData")
    public ResultMode<StatisticsDataDTO> queryStatisticsData(@RequestBody @Validated StatisticsDataQuery query){
        return statisticsAppService.queryStatisticsData(query);
    }


}

