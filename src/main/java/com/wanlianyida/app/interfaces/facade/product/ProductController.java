package com.wanlianyida.app.interfaces.facade.product;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.app.application.service.product.ProductAppService;
import com.wanlianyida.app.interfaces.model.command.MoreInfoCommand;
import com.wanlianyida.app.interfaces.model.dto.ProductAggregationDTO;
import com.wanlianyida.app.interfaces.model.dto.RichTextDTO;
import com.wanlianyida.app.interfaces.model.query.ProductRichTextQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.product.api.model.dto.ProductMoreInfoDTO;
import com.wanlianyida.product.api.model.dto.ProductPortalPicDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuPortalDetailDTO;
import com.wanlianyida.product.api.model.query.ProductPortalPicQuery;
import com.wanlianyida.product.api.model.query.ProductPortalQuery;
import com.wanlianyida.search.dto.CtpSkuDTO;
import com.wanlianyida.search.query.CtpSkuQuery;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/product/appNoAuth")
public class ProductController {

    @Resource
    private ProductAppService productAppService;

    @ApiOperation(("查询商品列表接口"))
    @PostMapping("queryPage")
    public ResponseMessage<List<CtpSkuDTO>> queryPage(@RequestBody PagingInfo<CtpSkuQuery> query) {
        log.info("ctp商品列表请求参数:{}", JSONObject.toJSONString(query));
        return productAppService.queryPage(query);
    }

    @ApiOperation(("门户查询sku详情"))
    @PostMapping("/querySkuPortalDetail")
    public ResultMode<ProductSkuPortalDetailDTO> querySkuPortalDetail(@RequestBody @Validated ProductPortalQuery query) {
        return productAppService.querySkuPortalDetail(query);
    }


    @ApiOperation("查询商品更多信息:资质")
    @PostMapping("/queryProductMoreInfo")
    public ResultMode<ProductMoreInfoDTO> queryProductMoreInfo(@RequestBody @Validated MoreInfoCommand query) {
        return productAppService.queryProductMoreInfo(query);
    }

    @ApiOperation("查询商品富文本详情")
    @PostMapping("/queryRichText")
    public ResultMode<RichTextDTO> queryRichText(@RequestBody @Validated ProductRichTextQuery query) {
        return productAppService.queryProductRichText(query);
    }

    @ApiOperation("查询商品详情图")
    @PostMapping("/queryPortalProductPic")
    public ResultMode<ProductPortalPicDTO> queryProductPic(@RequestBody @Validated ProductPortalPicQuery query) {
        return productAppService.queryProductPic(query);
    }

    @ApiOperation("查询商品信息聚合接口")
    @PostMapping("/queryProductAggregationDetail")
    public ResultMode<ProductAggregationDTO> queryProductAggregationDetail(@RequestBody @Validated ProductPortalQuery query) {
        return productAppService.queryProductAggregationDetail(query);
    }

}
