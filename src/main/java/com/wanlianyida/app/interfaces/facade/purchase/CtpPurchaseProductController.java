package com.wanlianyida.app.interfaces.facade.purchase;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.app.application.service.purchase.CtpPurchaseProductAppService;
import com.wanlianyida.app.interfaces.model.dto.PurchaseProductAggregationDTO;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.search.query.CtpPurchaseProductQuery;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api(tags = "求购商品 Controller")
@RequestMapping({"/ctp/purchaseProduct/appNoAuth/"})
public class CtpPurchaseProductController {
    @Resource
    private CtpPurchaseProductAppService ctpPurchaseProductAppService;

    /**
     * ctp求购商品聚合列表
     */
    @PostMapping("queryAggregation")
    public ResultMode<PurchaseProductAggregationDTO> queryAggregation(@RequestBody PagingInfo<CtpPurchaseProductQuery> query) {
        log.info("ctp求购商品聚合列表请求参数:{}", JSONObject.toJSONString(query));
        return ctpPurchaseProductAppService.queryAggregation(query);
    }
}
