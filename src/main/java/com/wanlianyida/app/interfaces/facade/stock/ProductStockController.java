package com.wanlianyida.app.interfaces.facade.stock;

import com.wanlianyida.app.application.service.stock.ProductStockAppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.ProductCheckCommand;
import com.wanlianyida.product.api.model.dto.ProductCheckDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/product/appNoAuth")
public class ProductStockController {

    @Resource
    private ProductStockAppService productStockAppService;

    @ApiOperation(("校验商品库存、最小起订量等信息"))
    @PostMapping("/checkProductStock")
    public ResultMode<ProductCheckDTO> checkProductStock(@RequestBody @Validated ProductCheckCommand command) {
        return productStockAppService.checkProductStock(command);
    }
}
