package com.wanlianyida.app.interfaces.facade.user;

import com.wanlianyida.app.application.service.user.UserInfoAppService;
import com.wanlianyida.app.interfaces.model.command.UserInfoCommand;
import com.wanlianyida.app.interfaces.model.dto.UserFunctionsDTO;
import com.wanlianyida.app.interfaces.model.dto.UserInfoDTO;
import com.wanlianyida.app.interfaces.model.query.UserInfoQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssuserauth.api.model.dto.UserSystemRelDTO;
import com.wanlianyida.partner.api.model.dto.MemberConditionDTO;
import com.wanlianyida.partner.api.model.dto.MemberDTO;
import com.wanlianyida.partner.api.model.dto.UmUserCacheDTO;
import com.wanlianyida.partner.api.model.query.MemberQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user/userInfo")
public class UserInfoController {

    @Resource
    private UserInfoAppService userInfoAppService;

    /**
     * 获取用户拥有的菜单权限
     */
    @PostMapping("/getUserFunction")
    public ResultMode<List<UserFunctionsDTO>> getUserFunction() {
        return userInfoAppService.getUserFunction();
    }

    /**
     * 登录时取token缓存用户企业信息
     */
    @PostMapping("/redisUserCompany")
    public ResultMode<?> redisUserCompany() {
        return ResultMode.success();
        //return userInfoAppService.redisUserCompany();
    }


    /**
     * 修改用户
     */
    @PostMapping("/updateUserInfo")
    public ResultMode updateUserInfo(@RequestBody @Validated UserInfoCommand userInfoCommand) {
        return userInfoAppService.updateUserInfo(userInfoCommand);
    }

    /**
     * 用户-列表查询
     */
    @PostMapping("/queryList")
    public ResultMode<List<UserInfoDTO>> queryList(@RequestBody @Validated PagingInfo<UserInfoQuery> pagingInfo) {
        return userInfoAppService.queryUserList(pagingInfo);
    }

    /**
     * 用户-明细查询
     */
    @PostMapping("/queryByUserBaseId")
    public ResultMode<UserInfoDTO> queryByUserBaseId(@RequestBody @Validated UserInfoQuery query) {
        return userInfoAppService.queryByUserBaseId(query);
    }

    /**
     * 用户条件查询（查询条件为空，则查询当前用户所属企业下的所有用户）
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link MemberDTO }>
     */
    @PostMapping("/queryByCondition")
    public ResultMode<List<MemberConditionDTO>> queryByCondition(@RequestBody MemberQuery query) {
        return userInfoAppService.queryByCondition(query);
    }

    /**
     * 根据用户id查询公司信息
     *
     * @param userBaseId 用户 ID
     * @return {@link ResultMode }<{@link MemberDTO }>
     */
    @PostMapping("/queryCompanyInfoByUserId")
    public ResultMode<MemberConditionDTO> queryCompanyInfoByUserId(@RequestParam("userBaseId") String userBaseId) {
        return userInfoAppService.queryCompanyInfoByUserId(userBaseId);
    }

    @PostMapping("/queryUserSystemByUserBaseId")
    public ResultMode<List<UserSystemRelDTO>> queryUserSystemByUserBaseId() {
        return userInfoAppService.queryUserSystemByUserBaseId();
    }

    /**
     * queryUserSystemByUserBaseId
     * 更新缓存
     *
     * @return {@code ResultMode<List<UmUserCacheDTO>> }
     */
    @Deprecated
    @PostMapping("/updateCache")
    public ResultMode<List<UmUserCacheDTO>> updateCache() {
        return userInfoAppService.updateCache();
    }


    /**
     * 用户登出
     */
    @PostMapping("/webLogout")
    public ResultMode<String> webLogout() {
        return userInfoAppService.webLogout();
    }

}
