package com.wanlianyida.app.interfaces.facade.product;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.app.application.service.product.StatisticsCountAppService;
import com.wanlianyida.app.interfaces.model.command.StatisticsCollectCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/03/13
 *  pv uv统计
 */
@RestController
@RequestMapping("/statistics/appNoAuth")
public class StatisticsController {
    @Resource
    private StatisticsCountAppService appService;

    /**
     * 统计
     */
    @PostMapping("/collect")
    public ResultMode<Void> addStatisticsCount(@RequestBody StatisticsCollectCommand command){
        if(StrUtil.isBlank(command.getUserId())){
            command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        }
        if(StrUtil.isBlank(command.getCompanyId())){
            command.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        }
        if (StrUtil.isBlank(command.getDeviceType())){
            command.setDeviceType("20");
        }
        appService.statisticCollect(command);
        return ResultMode.success();
    }

}
