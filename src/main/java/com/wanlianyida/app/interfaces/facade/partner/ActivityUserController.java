package com.wanlianyida.app.interfaces.facade.partner;

import com.wanlianyida.app.application.service.partner.ActivityUserAppService;
import com.wanlianyida.app.interfaces.model.command.JsSignatureCommand;
import com.wanlianyida.app.interfaces.model.command.RegisterActivityUserCommand;
import com.wanlianyida.app.interfaces.model.dto.JsSignatureDTO;
import com.wanlianyida.app.interfaces.model.dto.RegisterActivityUserDTO;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 活动用户控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity/appNoAuth")
public class ActivityUserController {

    @Resource
    private ActivityUserAppService activityUserAppService;

    /**
     * 注册活动用户
     *
     */
    @PostMapping("/register-user")
    public ResultMode<RegisterActivityUserDTO> addUser(@RequestBody @Validated RegisterActivityUserCommand command){
        return activityUserAppService.addUser(command);
    }

    /**
     * 获取JS-SDK使用权限签名接口
     *
     */
    @PostMapping("/js-signature")
    public ResultMode<JsSignatureDTO> jsSignature(@RequestBody @Validated JsSignatureCommand command){
        return activityUserAppService.jsSignature(command);
    }

}
