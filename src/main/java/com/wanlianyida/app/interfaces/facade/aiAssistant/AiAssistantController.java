package com.wanlianyida.app.interfaces.facade.aiAssistant;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.app.application.service.aiAssistant.AiAssistantAppService;
import com.wanlianyida.app.infrastructure.config.SmartCustomerServiceProperties;
import com.wanlianyida.app.interfaces.model.command.AppConversationCommand;
import com.wanlianyida.app.interfaces.model.command.AppMessageFeedbackCommand;
import com.wanlianyida.app.interfaces.model.command.AppServiceConvMessageCommand;
import com.wanlianyida.app.interfaces.model.dto.AppConversationDTO;
import com.wanlianyida.app.interfaces.model.dto.AppServiceConvMessageDTO;
import com.wanlianyida.app.interfaces.model.dto.SpeechRecognitionDTO;
import com.wanlianyida.app.interfaces.model.query.AppConversationQuery;
import com.wanlianyida.app.interfaces.model.query.AppServiceConvMessagePageQuery;
import com.wanlianyida.app.interfaces.model.query.SpeechRecognitionQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.List;

import javax.annotation.Resource;

/**
 * 小程序端-智能客服
 */
@Slf4j
@RestController
@RequestMapping("/commerce/assistant")
public class AiAssistantController {

    @Resource
    private AiAssistantAppService aiAssistantAppService;

    @Resource
    private SmartCustomerServiceProperties smartCustomerServiceProperties;

    @ApiOperation("创建会话")
    @PostMapping("/create-convos")
    public ResultMode<AppConversationDTO> createConvos(@RequestBody @Validated AppConversationCommand command){
        return aiAssistantAppService.createConvos(command);
    }

    @ApiOperation("查询会话")
    @PostMapping("/query-convos")
    public ResultMode<AppConversationDTO> queryConvos(@RequestBody @Validated AppConversationQuery convosQuery){
        return aiAssistantAppService.queryConvos(convosQuery);
    }

    @ApiOperation("保存会话消息")
    @PostMapping("/save-message")
    public ResultMode<AppServiceConvMessageDTO> saveMessage(@RequestBody @Validated AppServiceConvMessageCommand command){
        return aiAssistantAppService.saveMessage(command);
    }

    @ApiOperation("会话消息反馈")
    @PostMapping("/message-feedback")
    public ResultMode<?> messageFeedback(@RequestBody @Validated AppMessageFeedbackCommand command){
        return aiAssistantAppService.messageFeedback(command);
    }

    @ApiOperation("分页查询会话消息")
    @PostMapping("/query-message")
    public ResultMode<List<AppServiceConvMessageDTO>> queryMessage(@RequestBody @Validated PagingInfo<AppServiceConvMessagePageQuery> pageQuery){
        return aiAssistantAppService.queryMessage(pageQuery);
    }

    @ApiOperation("数智客服-商贸端")
    @PostMapping(value = "/smart-customer-service",  produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> smartCustomerService(@RequestBody String queryParam){
        log.info("================>调用AI数智客服路径: {}，appKey: {}", smartCustomerServiceProperties.getSmartUrl(), smartCustomerServiceProperties.getKey());
        WebClient webClient = WebClient.create();
        return webClient.post()
                .uri(smartCustomerServiceProperties.getSmartUrl())
                .header("Authorization", smartCustomerServiceProperties.getKey())
                .header("Content-Type", "application/json")
                .header("Accept", "text/event-stream")
                .bodyValue(queryParam)
                .retrieve()
                .bodyToFlux(String.class)
                .doOnNext(data -> log.info("SSE推送内容: {}", data))
                .doOnError(e -> log.error("推送异常", e));
    }

    @ApiOperation("语音识别")
    @PostMapping("/speech-recognition")
    public ResultMode<SpeechRecognitionDTO> speechRecognition(@RequestBody SpeechRecognitionQuery queryParam){
        String response = HttpUtil.post(smartCustomerServiceProperties.getSpeechUrl(), JSONUtil.toJsonStr(queryParam));
        return ResultMode.success(JSONObject.parseObject(response, SpeechRecognitionDTO.class));
    }

    @ApiOperation("常见问题")
    @PostMapping("/common-problem")
    public ResultMode<List<String>> commonProblem(){
        return ResultMode.success(smartCustomerServiceProperties.getCommonProblemList());
    }

}
