package com.wanlianyida.app.interfaces.facade.account;

import com.wanlianyida.app.application.service.account.PlatformUserBasrInfoAppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssuserauth.api.model.dto.PlatformUmUserbaseinfoDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UserAccountInfoDTO;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 企业用户信息 Controller
 *
 * <AUTHOR>
 * @date 2024-12-2
 */
@Api(value = "企业用户信息api")
@RequestMapping("/Platformumuserbaseinfo")
@RestController
public class PlatformUserBaseInfoController {

	@Resource
	private PlatformUserBasrInfoAppService platformUserBasrInfoAppService;

	/**
	 * 用户端登录后获取企业账号信息
	 *
	 * @return {@link PlatformUmUserbaseinfoDTO}
	 */
	@PostMapping("/getUserInfo")
	ResultMode<UserAccountInfoDTO> getUserInfo() {
		return platformUserBasrInfoAppService.getUserInfo();
	}
}

