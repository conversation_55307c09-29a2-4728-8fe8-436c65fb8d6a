package com.wanlianyida.app.interfaces.facade.order;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.app.application.service.order.ShopCartCommandV2AppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.model.command.shopcart.AddShopProductV2Command;
import com.wanlianyida.order.api.model.command.shopcart.DelShopCartNumV2Command;
import com.wanlianyida.order.api.model.command.shopcart.DelShopProductV2Command;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 购物车V2
 */
@Slf4j
@RequestMapping("/v2/order/shopCartCommand")
@RestController
public class ShopCartCommandV2Controller {

    @Autowired
    private ShopCartCommandV2AppService shopCartCommandV2AppService;

    /**
     * 添加购物车V2
     */
    @PostMapping("/addShopCart")
    public ResultMode addShopCart(@RequestBody @Validated AddShopProductV2Command command){
        log.info("addShopCartV2#添加购物车参数：{}", JSONUtil.toJsonStr(command));
        return shopCartCommandV2AppService.addShopCart(command);
    }

    /**
     * 删除购物车V2
     */
    @PostMapping("/delShopCart")
    public ResultMode delShopCart(@RequestBody @Validated List<DelShopProductV2Command> command){
        log.info("delShopCartV2#删除购物车参数：{}", JSONUtil.toJsonStr(command));
        return shopCartCommandV2AppService.delShopCart(command);
    }

    /**
     * 删除当前购物车数量V2
     */
    @PostMapping("/delShopCartNum")
    public ResultMode delShopCartNum(@RequestBody @Validated DelShopCartNumV2Command command){
        log.info("delShopCartNumV2#删除购物车数量参数：{}", JSONUtil.toJsonStr(command));
        return shopCartCommandV2AppService.delShopCartNum(command);
    }

}
