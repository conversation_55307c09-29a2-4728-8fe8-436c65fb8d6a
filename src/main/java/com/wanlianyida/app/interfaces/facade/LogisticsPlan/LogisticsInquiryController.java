package com.wanlianyida.app.interfaces.facade.LogisticsPlan;

import com.wanlianyida.app.application.service.LogisticsPlan.LogisticsInquiryAppService;
import com.wanlianyida.app.interfaces.model.dto.LogisticsInquiryDetailDTO;
import com.wanlianyida.app.interfaces.model.dto.LogisticsInquiryListDTO;
import com.wanlianyida.app.interfaces.model.dto.WaybillListDTO;
import com.wanlianyida.exter.api.lgi.model.request.LogisticsTemplateIdRequest;
import com.wanlianyida.exter.api.lgi.model.response.DictionaryResponse;
import com.wanlianyida.exter.api.lgi.model.response.LoqisticsExchangeResponse;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.model.command.*;
import com.wanlianyida.order.api.model.dto.LogisticsInquiryDTO;
import com.wanlianyida.order.api.model.query.LogisticsInquiryQuery;
import com.wanlianyida.order.api.model.query.WaybillListQuery;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 物流计划相关接口
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@Slf4j
@RestController
@RequestMapping("/logisticsInquiry")
public class LogisticsInquiryController {

    @Resource
    private LogisticsInquiryAppService logisticsInquiryAppService;
    @ApiOperation(value = "发起物流询价")
    @PostMapping("/createLogisticsInquiry")
    public ResultMode createLogisticsInquiry(@RequestBody LogisticsInquiryListAddCmd command) {
        return logisticsInquiryAppService.createLogisticsInquiry(command);
    }

    @ApiOperation(value = "修改发起物流询价")
    @PostMapping("/updateLogisticsInquiry")
    public ResultMode updateLogisticsInquiry(@RequestBody LogisticsInquiryListUpdateCmd command) {
        return logisticsInquiryAppService.updateLogisticsInquiry(command);
    }

    @ApiOperation(value = "重新发起物流询价")
    @PostMapping("/restartLogisticsInquiry")
    public ResultMode restartLogisticsInquiry(@RequestBody LogisticsInquiryListAddCmd command) {
        return logisticsInquiryAppService.restartLogisticsInquiry(command);
    }

    @ApiOperation(value = "发起物流询价校验")
    @PostMapping("/createLogisticsInquiryVerify")
    public ResultMode<Object> createLogisticsInquiryVerify(@RequestBody String shipNo) {
        return logisticsInquiryAppService.createLogisticsInquiryVerify(shipNo);
    }

    @ApiOperation(value = "重新发起物流询价校验")
    @PostMapping("/restartLogisticsInquiryVerify")
    public ResultMode<Object> restartLogisticsInquiryVerify(@RequestBody LogisticsInquiryVerifyCmd command) {
        return logisticsInquiryAppService.restartLogisticsInquiryVerify(command);
    }

    /**
     * 询价单列表
     */
    @PostMapping("/pageList")
    public ResultMode<List<LogisticsInquiryListDTO>> pageList(@RequestBody PagingInfo<LogisticsInquiryQuery> pagingInfo){
        return logisticsInquiryAppService.pageList(pagingInfo);
    }

    @ApiOperation(value = "根据单号查询询价单")
    @GetMapping("/queryLogisticsInquiryByShipNo")
    public ResultMode<List<LogisticsInquiryDTO>> queryLogisticsInquiryByShipNo(@RequestParam(value = "shipNo") String shipNo) {
        return logisticsInquiryAppService.queryLogisticsInquiryByShipNo(shipNo);
    }

    /**
     * 审批
     */
    @PostMapping("/approval")
    public ResultMode approval(@RequestBody @Valid LogisticsInquiryApprovalCommand command){
        return logisticsInquiryAppService.approval(command);
    }

    /**
     * 撤销
     */
    @PostMapping("/revoke")
    public ResultMode revoke(@RequestBody @Valid LogisticsInquiryIdCommand command){
        return logisticsInquiryAppService.revoke(command);
    }

    /**
     * 详情
     */
    @PostMapping("/getDetail")
    public ResultMode<LogisticsInquiryDetailDTO> getDetail(@RequestBody @Valid LogisticsInquiryIdCommand command){
        return logisticsInquiryAppService.getDetail(command);
    }
    @ApiOperation(value = "字典值查询接口")
    @PostMapping("/platformCmDictionaryByIds")
    public ResultMode<DictionaryResponse> platformCmDictionaryByIds(@RequestBody List<String> request){
        return ResultMode.success(logisticsInquiryAppService.platformCmDictionaryByIds(request));
    };

    @ApiOperation(value = "运单详情列表")
    @PostMapping("/queryWaybillList")
    public ResultMode<List<WaybillListDTO>> queryWaybillList(@RequestBody PagingInfo<WaybillListQuery> waybillListQuery){
        LoqisticsExchangeResponse response = logisticsInquiryAppService.queryWaybillList(waybillListQuery.filterModel);
        ResultMode<List<WaybillListDTO>> resultMode = ResultMode.success(response.getModel());
        return resultMode;
    };

    @ApiOperation(value = "查询合同模板")
    @PostMapping("/queryDetailUrlByTemplateId")
    public ResultMode queryDetailUrlByTemplateId(@RequestBody LogisticsTemplateIdRequest request){
        return logisticsInquiryAppService.queryDetailUrlByTemplateId(request);
    };

    @ApiOperation(value = "获取物流平台管理员信息")
    @GetMapping("/getCompanyByModel")
    public ResultMode getCompanyByModel(){
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        return logisticsInquiryAppService.getCompanyByModel(tokenInfo.getLicenseNo());
    };
}
