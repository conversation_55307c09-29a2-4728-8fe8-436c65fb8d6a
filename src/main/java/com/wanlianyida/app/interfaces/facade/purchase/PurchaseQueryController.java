package com.wanlianyida.app.interfaces.facade.purchase;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.app.application.service.purchase.PurchaseQueryAppService;
import com.wanlianyida.app.interfaces.model.dto.DeliveryAddrCityDTO;
import com.wanlianyida.app.interfaces.model.dto.PurchaseDocumentDTO;
import com.wanlianyida.app.interfaces.model.dto.UmCompanyDTO;
import com.wanlianyida.app.interfaces.model.query.UmCompanyQuery;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.search.query.CtpPurchaseProductQuery;
import com.wanlianyida.transaction.api.model.dto.PurchaseEmptyDetailDTO;
import com.wanlianyida.transaction.api.model.dto.PurchaseProductDTO;
import com.wanlianyida.transaction.api.model.dto.PurchaseSimpleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/purchase")
public class PurchaseQueryController {

    @Resource
    private PurchaseQueryAppService purchaseQueryAppService;

    /**
     * 查询交货地城市列表
     * @param pagingInfo
     * @return
     */
    @PostMapping("/appNoAuth/queryDeliveryAddrCityList")
    public ResultMode<DeliveryAddrCityDTO> queryDeliveryAddrCityList(@RequestBody com.wanlianyida.fssmodel.PagingInfo<CtpPurchaseProductQuery> pagingInfo) {
        log.info("小程序查询交货地城市列表请求参数：{}", JSONObject.toJSONString(pagingInfo));
        return purchaseQueryAppService.queryDeliveryAddrCityList(pagingInfo);
    }

    /**
     * 查询求购列表
     * @param pagingInfo
     * @return
     */
    @PostMapping("/appNoAuth/queryPurchaseDocumentList")
    public ResultMode<PurchaseDocumentDTO> queryPurchaseDocumentList(@RequestBody com.wanlianyida.fssmodel.PagingInfo<CtpPurchaseProductQuery> pagingInfo) {
        log.info("小程序查询求购商品列表请求参数：{}", JSONObject.toJSONString(pagingInfo));
        return purchaseQueryAppService.queryPurchaseDocumentList(pagingInfo);
    }

    /**
     * 查询求购详情
     * @param idQuery
     * @return
     */
    @PostMapping("/appNoAuth/queryInfo")
    public ResultMode<PurchaseSimpleDTO> queryInfo(@RequestBody IdQuery idQuery) {
        log.info("小程序查询求购详情请求参数：{}", JSONObject.toJSONString(idQuery));
        return purchaseQueryAppService.queryInfo(idQuery);
    }

    /**
     * 查询求购详情商品列表
     * @param pagingInfo
     * @return
     */
    @PostMapping("/appNoAuth/pageProductList")
    public ResultMode<List<PurchaseProductDTO>> pageProductList(@RequestBody PagingInfo<IdQuery> pagingInfo) {
        log.info("小程序查询求购详情商品列表请求参数：{}", JSONObject.toJSONString(pagingInfo));
        return purchaseQueryAppService.pageProductList(pagingInfo);
    }

    /**
     * 用户所属企业信息
     * @param umCompanyQuery
     * @return
     */
    @PostMapping("/umCompanyByUserBaseId")
    ResultMode<UmCompanyDTO> umCompanyByUserBaseId(@RequestBody UmCompanyQuery umCompanyQuery) {
        log.info("小程序查询用户所属企业信息请求参数：{}", JSONObject.toJSONString(umCompanyQuery));
        return purchaseQueryAppService.umCompanyByUserBaseId(umCompanyQuery);
    }

    /**
     * 查询立即报价初始化信息
     * @param idQuery
     * @return
     */
    @PostMapping("/queryPurchaseDetail")
    ResultMode<PurchaseEmptyDetailDTO> queryPurchaseDetail(@RequestBody IdQuery idQuery) {
        log.info("小程序查询立即报价初始化信息请求参数：{}", JSONObject.toJSONString(idQuery));
        return purchaseQueryAppService.queryPurchaseDetail(idQuery);
    }
}
