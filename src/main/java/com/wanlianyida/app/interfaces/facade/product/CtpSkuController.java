package com.wanlianyida.app.interfaces.facade.product;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.app.application.service.order.CtpSkuAppService;
import com.wanlianyida.app.interfaces.dto.SkuAggregationDTO;
import com.wanlianyida.app.interfaces.model.dto.CtpSkuInfoDTO;
import com.wanlianyida.app.interfaces.model.query.SkuQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.search.query.CtpSkuQuery;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api(tags = "sku Controller")
@RequestMapping({"/product/appNoAuth/sku/"})
public class CtpSkuController {
    @Resource
    CtpSkuAppService ctpSkuApplicationService;
    /**
     * 自营商品列表
     */
    @PostMapping("queryPage")
    public ResponseMessage<List<CtpSkuInfoDTO>> queryPage(@RequestBody PagingInfo<SkuQuery> query) {
        log.info("ctp商品列表请求参数:{}", JSONObject.toJSONString(query));
        return ctpSkuApplicationService.queryPage(query);
    }
    /**
     * 自营商品聚合列表
     */
    @PostMapping("queryAggregation")
    public ResponseMessage<SkuAggregationDTO> queryAggregation(@RequestBody CtpSkuQuery query) {
        log.info("ctp商品聚合列表请求参数:{}", JSONObject.toJSONString(query));
        return ctpSkuApplicationService.queryAggregation(query);
    }
}
