package com.wanlianyida.app.interfaces.facade.msg;

import com.wanlianyida.app.application.service.msg.WxMsgAppService;
import com.wanlianyida.app.interfaces.model.command.AbleSubscribeMsgCommand;
import com.wanlianyida.app.interfaces.model.command.BindWxCommand;
import com.wanlianyida.app.interfaces.model.command.ReportSubscriptionCommand;
import com.wanlianyida.app.interfaces.model.dto.AbleSubscribeMsgDTO;
import com.wanlianyida.app.interfaces.model.dto.BindWxDTO;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 微信消息订阅
 */
@RestController
@RequestMapping("/wx/msg")
@Validated
public class WxMsgController {

    @Resource
    private WxMsgAppService wxMsgAppService;

    /**
     * 用户可以订阅的消息模板列表
     */
    @PostMapping("/able-subscribe")
    public ResultMode<AbleSubscribeMsgDTO> ableSubscribeMsg(@RequestBody @Valid AbleSubscribeMsgCommand command) {
        return wxMsgAppService.ableSubscribeMsg(command);
    }

    /**
     * 用户上报订阅的消息模板
     */
    @PostMapping("/report-subscription")
    public ResultMode<Void> reportSubscription(@RequestBody @Valid ReportSubscriptionCommand command) {
        return wxMsgAppService.reportSubscription(command);
    }

    /**
     * 用户绑定微信小程序
     */
    @PostMapping("/bind-wx")
    public ResultMode<BindWxDTO> bindWx(@RequestBody @Valid BindWxCommand command) {
        return wxMsgAppService.bindWx(command);
    }

}
