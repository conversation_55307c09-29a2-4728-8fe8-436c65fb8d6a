package com.wanlianyida.app.interfaces.facade.content;

import com.wanlianyida.app.application.service.content.ChInformationService;
import com.wanlianyida.app.infrastructure.exchange.BaseContentExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailUserDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationListDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationListUserDTO;
import com.wanlianyida.fssbasecontent.api.model.query.ChInformationListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 *  内容管理
 * <AUTHOR>
 * @since 2024/11/21/19:19
 */
@RestController
@RequestMapping("/content/information/appNoAuth")
public class ChInformationController {

    @Resource
    private ChInformationService chInformationService;

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping("/listPage")
    public ResultMode<List<ChInformationListDTO>> list(@Validated @RequestBody PagingInfo<ChInformationListQuery> query) {
        return baseContentExchangeService.chInformationInterList(query);
    }

    @PostMapping("/listPageUser")
    public ResultMode<List<ChInformationListUserDTO>> listPageUser(@Validated @RequestBody PagingInfo<ChInformationListQuery> query) {
        return baseContentExchangeService.chInformationInterListUser(query);
    }

    /**
     * 内容详情 后台
     * @param infoId
     * @return
     */
    @GetMapping("/detailById")
    public ResultMode<ChInformationDetailDTO> detailById(@RequestParam(name = "infoId", required = false) String infoId,
                                                         @RequestParam(name = "callAlias",required = false)  String callAlias){
        return chInformationService.detailById(infoId, callAlias);
    }

    /**
     * 内容详情 门户
     * @param infoId
     * @return
     */
    @GetMapping("/detailByIdUser")
    ResultMode<ChInformationDetailUserDTO> detailByIdUser(@RequestParam(name = "infoId") String infoId){
        return baseContentExchangeService.chInformationInterDetailByIdUser(infoId);
    }

    /**
     * 关于我们
     * @param callAlias
     * @return
     */
    @GetMapping("/detailByCallAlias")
    public ResultMode<ChInformationDetailUserDTO> detailByCallAlias(@RequestParam(name = "callAlias")  String callAlias) {
        return baseContentExchangeService.chInformationInterDetailByCallAlias(callAlias);
    }

    @PostMapping("/homepage")
    ResultMode<Map<String, List<ChInformationListDTO>>> homepage(@RequestBody  Map<String, Map<String, String>> map) {
        return baseContentExchangeService.chInformationInterHomepage(map);
    }

}
