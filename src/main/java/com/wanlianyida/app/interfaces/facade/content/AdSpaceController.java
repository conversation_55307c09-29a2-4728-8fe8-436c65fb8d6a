package com.wanlianyida.app.interfaces.facade.content;

import com.wanlianyida.app.application.service.content.AdSpaceAppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.dto.AdSpaceDTO;
import com.wanlianyida.fssbasecontent.api.model.query.AdSpaceListQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 广告位管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/content/ad/space/appNoAuth/")
public class AdSpaceController {

    @Resource
    private AdSpaceAppService appService;

    /**
     * 查询列表
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link List }<{@link AdSpaceDTO }>>
     */
    @PostMapping("/queryList")
    public ResultMode<List<AdSpaceDTO>> queryList(@RequestBody AdSpaceListQuery query) {
        return appService.queryList(query);
    }

}
