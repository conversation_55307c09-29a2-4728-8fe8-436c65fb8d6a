package com.wanlianyida.app.interfaces.facade.order;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.enums.CommonCodeEnum;
import com.wanlianyida.framework.ctpcore.utils.IdempotentUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/token")
public class TokenController {

    @Resource
    private IdempotentUtil idempotentUtil;


    /**
     * 获取业务token
     * @return
     */
    @PostMapping("/createToken")
    public ResultMode createToken() {
        String token =  idempotentUtil.createToken();
        return ResultMode.success(CommonCodeEnum.BUSS_SUCCESS_BCOM0200.getMsg(),token);
    }

}
