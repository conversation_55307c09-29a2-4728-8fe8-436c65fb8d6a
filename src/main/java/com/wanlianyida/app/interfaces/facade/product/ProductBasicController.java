package com.wanlianyida.app.interfaces.facade.product;

import com.wanlianyida.app.application.service.product.ProductBasicAppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.dto.ProductCategoryTreeDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product/basic")
@Validated
public class ProductBasicController {

    @Resource
    private ProductBasicAppService productBasicAppService;

    @PostMapping("/category/appNoAuth/navbarCategoryTree")
    @ApiOperation("查询品类列表接口")
    public ResultMode<List<ProductCategoryTreeDTO>> navbarCategoryTree() {
        return productBasicAppService.navbarCategoryTree();
    }

}
