package com.wanlianyida.app.interfaces.facade.purchase;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.app.application.service.purchase.ProductOperateAppService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.ChangeSaleStatusCommand;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuListDTO;
import com.wanlianyida.product.api.model.query.ProductSkuListQuery;
import com.wanlianyida.product.api.model.query.ProductSkuQuery;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/product")
public class ProductQueryController {

    @Resource
    private ProductOperateAppService productOperateAppService;

    /**
     * 查询报价商品列表
     * @param pagingInfo
     * @return
     */
    @PostMapping("/skuPageCondition")
    public ResultMode<List<ProductSkuListDTO>> skuPageCondition(@RequestBody PagingInfo<ProductSkuListQuery> pagingInfo) {
        log.info("小程序查询报价商品列表请求参数：{}", JSONObject.toJSONString(pagingInfo));
        return productOperateAppService.skuPageCondition(pagingInfo);
    }

    /**
     * 查询商品详情(sku纬度)
     * @param skuQuery
     * @return
     */
    @PostMapping("/queryProductSkuDetail")
    public ResultMode<List<ProductSkuDetailDTO>> queryProductSkuDetail(@RequestBody ProductSkuQuery skuQuery) {
        log.info("小程序查询商品详情(sku纬度)请求参数：{}", JSONObject.toJSONString(skuQuery));
        return productOperateAppService.queryProductSkuDetail(skuQuery);
    }

    @ApiOperation(value = "商品上/下架")
    @PostMapping("/changeSaleStatus")
    public ResultMode<?> onSaleProduct(@RequestBody @Valid ChangeSaleStatusCommand command) {
        return productOperateAppService.changeSaleStatus(command);
    }
}
