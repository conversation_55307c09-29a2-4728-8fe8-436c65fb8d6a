package com.wanlianyida.app.interfaces.facade.content;

import com.wanlianyida.app.application.service.content.AssociationalWordAppService;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.search.dto.CtpAssociationalWordDTO;
import com.wanlianyida.search.query.CtpAssociationalWordQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 联想词
 */
@RestController
@RequestMapping("/associationalWord/appNoAuth")
public class AssociationalWordController {

    @Resource
    private AssociationalWordAppService associationalWordAppService;

    /**
     * 联想词列表
     */
    @PostMapping("/queryPage")
    ResponseMessage<List<CtpAssociationalWordDTO>> queryPage(@RequestBody @Validated PagingInfo<CtpAssociationalWordQuery> query) {
        return associationalWordAppService.queryPage(query);
    }

}
