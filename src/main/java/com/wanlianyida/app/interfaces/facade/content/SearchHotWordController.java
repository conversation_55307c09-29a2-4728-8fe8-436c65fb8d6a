package com.wanlianyida.app.interfaces.facade.content;

import com.wanlianyida.app.application.service.content.SearchHotWordAppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.model.dto.SearchHotWordDTO;
import com.wanlianyida.support.api.model.query.SearchHotWordPageQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/searchHotWord/appNoAuth")
public class SearchHotWordController {

    @Resource
    private SearchHotWordAppService appService;

    /**
     * 查询指定条数的热词数据
     * @param query
     * @return
     */
    @PostMapping("/queryByNum")
    public ResultMode<List<SearchHotWordDTO>> queryLimitedData(@RequestBody SearchHotWordPageQuery query) {
        return appService.queryLimitedData(query);
    }
}
