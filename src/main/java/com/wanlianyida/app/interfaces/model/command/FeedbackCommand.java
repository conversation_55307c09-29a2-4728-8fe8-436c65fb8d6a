package com.wanlianyida.app.interfaces.model.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

@Data
public class FeedbackCommand implements Serializable {


    /**
     * 反馈类型[字典suggestType]
     */
    @NotNull(message = "feedbackType不能为空")
    private Integer feedbackType;

    /**
     * 反馈内容
     */
    @NotNull(message = "feedbackContent不能为空")
    private String feedbackContent;

    private List<AttachmentCommand> attachmentList;

    @Data
    public static class AttachmentCommand implements Serializable{

        /**
         * 业务单号
         */
        private String bizNo;

        /**
         * 附件类型
         */
        private Integer attachmentType;

        /**
         * 附件url
         */
        private String attachmentUrl;

        /**
         * 文件名
         */
        private String attachmentName;
    }
}
