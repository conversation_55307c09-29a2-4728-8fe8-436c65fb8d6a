package com.wanlianyida.app.interfaces.model.dto;

import com.wanlianyida.app.interfaces.dto.ShopCartAvailableDTO;
import com.wanlianyida.app.interfaces.dto.ShopCartProductDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 购物车列表
 */
@Data
public class ShopCartShowDTO implements Serializable {
    private static final long serialVersionUID = 7534602948970050008L;

    /**
     * 可用商品
     */
    private List<ShopCartAvailableDTO> availableList;

    /**
     * 失效商品
     */
    private List<ShopCartProductDTO> invalidList;


}
