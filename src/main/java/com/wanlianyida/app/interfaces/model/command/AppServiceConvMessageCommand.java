package com.wanlianyida.app.interfaces.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description 数智客服对话消息，es索引：fss_service_conv_message
 * @Date 2025年04月14日 16:46
 */
@Data
public class AppServiceConvMessageCommand {

    @ApiModelProperty("会话id")
    @NotNull(message = "会话id不能为空")
    private Long convId;

    @ApiModelProperty("用户id")
    @NotBlank(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户账号")
    private String userAccount;

    @ApiModelProperty("问题类型，10：文本，20：语音，30：图片，40：文件")
    @NotNull(message = "问题类型不能为空")
    private Integer questionType;

    @ApiModelProperty("问题内容，文本")
    private String questionContent;

    @ApiModelProperty("问题url")
    private String questionUrl;

    @ApiModelProperty("答案类型，10：文本，20：语音，30：图片，40：文件")
    @NotNull(message = "答案类型不能为空")
    private Integer answerType;

    @ApiModelProperty("答案内容，文本")
    private String answerContent;

    @ApiModelProperty("答案url")
    private String answerUrl;

    @ApiModelProperty("上一条记录id")
    private String preRecordId;

    @ApiModelProperty("平台码[10-物流，20-商贸，60-AI大模型]")
    private Integer platformType;

    @ApiModelProperty("客户端类型[物流：111-司机APP,112-司机小程序,121-货主APP,122-3pl,123-货主小程序,131-4pl;商贸：211-用户端APP,212-用户端小程序,213-用户端web,221-门户web]")
    private Integer clientType;

    @ApiModelProperty("角色类型[物流：101-司机,111-货主,121-运营，商贸：201-买家,211-卖家]")
    private Integer roleType;
}
