package com.wanlianyida.app.interfaces.model.dto;

import com.wanlianyida.order.api.model.dto.OrderBaseDetailDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @ClassName: OrderBaseDetailInfoDTO
 * @description:
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2025年05月20日
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderBaseDetailInfoDTO extends OrderBaseDetailDTO {

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;

    /**
     * 结算金额
     */
    private BigDecimal smentAmount;
}
