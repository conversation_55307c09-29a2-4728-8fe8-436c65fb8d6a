package com.wanlianyida.app.interfaces.model.command;

import com.wanlianyida.framework.ctpcommon.model.command.FacadeBaseCommand;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 用户上报订阅的消息模板command
 */
@Data
public class ReportSubscriptionCommand extends FacadeBaseCommand {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 模板id
     */
    @NotNull(message = "模板id不能为空")
    @Size(min = 1, message = "模板id不能为空")
    private List<String> templateIdList;

    /**
     * 消息接收方[10-买家,20-卖家]
     */
    @NotBlank(message = "消息接收方不能为空")
    private String messageReceiver;
}
