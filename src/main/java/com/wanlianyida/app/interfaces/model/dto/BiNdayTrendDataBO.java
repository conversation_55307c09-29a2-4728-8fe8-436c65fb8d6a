package com.wanlianyida.app.interfaces.model.dto;

import com.wanlianyida.app.application.assembler.StatisticsAssembler;
import lombok.Data;

@Data
public class BiNdayTrendDataBO {

    /**
     * 企业ID
     */
    private String sellerCompanyId;

    /**
     * 天
     */
    private String dayId;

    /**
     * 金额
     */
    private String totalAmount;

    /**
     * 天数
     */
    private String totalOrders;

    /**
     * 单价
     */
    private String amountPerOrder;

    private void setTotalAmount(String totalAmount) {
        this.totalAmount = StatisticsAssembler.convertNumberToStringForce(totalAmount);
    }

    private void setAmountPerOrder(String amountPerOrder) {
        this.amountPerOrder = StatisticsAssembler.convertNumberToStringForce(amountPerOrder);
    }

}
