package com.wanlianyida.app.interfaces.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月14日 16:31
 */
@Data
@ApiModel("会话表")
public class AppConversationDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long convId;

    @ApiModelProperty("会话标题")
    private String title;

    @ApiModelProperty("会话类型[10-普通，20-内容创作，30-对比]")
    private Integer convType;

    @ApiModelProperty("对话来源[10-数智客服，20-智能助手-问答，30-智能助手-内容创作，40-数智客服演示]")
    private Integer convSource;

    @ApiModelProperty("会话开始时间")
    private String startTime;

    @ApiModelProperty("会话结束时间")
    private String endTime;
}
