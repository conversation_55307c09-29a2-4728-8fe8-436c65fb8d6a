package com.wanlianyida.app.interfaces.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 企业信息表 DTO
 *
 * <AUTHOR>
 */
@Data
public class UmCompanyDTO {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 企业名称(全称)
	 */
	private String companyName;

	/**
	 * 联系人
	 */
	private String contacts;

	/**
	 * 电话
	 */
	private String phone;
}
