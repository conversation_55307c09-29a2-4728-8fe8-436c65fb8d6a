package com.wanlianyida.app.interfaces.model.dto;

import com.wanlianyida.search.dto.CtpPurchaseDocumentDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 大宗商品求购商品分组 DTO
 *
 * <AUTHOR>
 */
@Data
public class PurchaseProductAggregationDTO {
    /**
     * 交货地城市列表
     */
    private List<City> cityList;
    /**
     * 品类列表
     */
    private List<Category> categoryList;
    /**
     * 总记录数
     */
    private Integer totalCount;
    /**
     * 求购单列表
     */
    private List<CtpPurchaseDocumentDTO> purchaseDocumentDTOList = new ArrayList<>();

    @Data
    public static class Brand {
        /**
         * 品牌ID
         */
        private Long brandId;
        /**
         * 品牌名称
         */
        private String brandName;
    }

    @Data
    public static class Category {
        /**
         * 品类ID
         */
        private Long categoryId;
        /**
         * 品类名称
         */
        private String categoryName;
        /**
         * 子品类列表
         */
        private List<Category> childCategoryList;
    }

    @Data
    public static class City {
        /**
         * 市编码
         */
        private String cityCode;
        /**
         * 市名称
         */
        private String cityName;
    }
}
