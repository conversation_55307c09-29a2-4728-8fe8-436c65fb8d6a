package com.wanlianyida.app.interfaces.model.command;

import com.wanlianyida.framework.ctpcommon.model.command.FacadeBaseCommand;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户可订阅的消息列表
 */
@Data
public class AbleSubscribeMsgCommand extends FacadeBaseCommand {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 事件编码
     */
    @NotBlank(message = "事件编码不能为空")
    private String eventCode;

    /**
     * 消息接收方[10-买家,20-卖家]
     */
    @NotBlank(message = "消息接收方不能为空")
    private String messageReceiver;
}
