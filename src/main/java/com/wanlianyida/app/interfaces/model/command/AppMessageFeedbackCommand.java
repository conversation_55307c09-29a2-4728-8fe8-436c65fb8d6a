package com.wanlianyida.app.interfaces.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月16日 19:59
 */
@ApiModel("消息反馈")
@Data
public class AppMessageFeedbackCommand {

    private String userId;

    @NotNull(message = "消息id不能为空")
    @ApiModelProperty("消息id")
    private String messageId;

    @NotBlank(message = "点赞状态不能为空")
    @ApiModelProperty("点赞状态 0取消点赞/取消点踩 10点赞 20点踩")
    private String likeStatus;

    @ApiModelProperty("反馈类型，10内容不准确，20与问题不相关，30逻辑不通顺")
    private String feedbackType;

    @NotNull(message = "反馈内容不能为空")
    @ApiModelProperty("反馈内容")
    @Length(max = 1000, message = "反馈内容不能超过1000个字符")
    private String feedback;
}
