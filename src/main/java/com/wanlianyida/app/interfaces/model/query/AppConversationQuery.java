package com.wanlianyida.app.interfaces.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月16日 19:13
 */
@Data
public class AppConversationQuery {

    @NotBlank(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty("会话类型[10普通，20内容创作，30对比]")
    private Integer convType;

    @ApiModelProperty("对话来源[10-数智客服，20-智能助手-问答，30-智能助手-内容创作，40-数智客服演示]")
    private Integer convSource;

    @ApiModelProperty("平台类型[10-物流，20-商贸，60-AI大模型]")
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;
}
