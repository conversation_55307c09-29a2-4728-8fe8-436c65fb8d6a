package com.wanlianyida.app.interfaces.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description 报价生成意向单
 * @Date 2024年12月10日 14:35
 */
@Data
public class PurchasePurposeOrderCommand {

    @ApiModelProperty("报价单id")
    @NotNull(message = "报价单id不能为空")
    private Long id;

    @ApiModelProperty("报价商品id集合")
    @NotEmpty(message = "请选择商品")
    private List<Long> productIdList;

}
