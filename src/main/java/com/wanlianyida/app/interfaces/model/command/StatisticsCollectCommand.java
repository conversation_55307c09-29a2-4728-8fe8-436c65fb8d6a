package com.wanlianyida.app.interfaces.model.command;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class StatisticsCollectCommand {
    /**
     * 业务id（比如商品id等）
     */
    @NotEmpty(message = "bizId不能为空")
    private String bizId;

    /**
     * 10-商品浏览
     */
    @NotEmpty(message = "bizType不能为空")
    private String bizType;

    /**
     * 10-web端 20-app
     */
    private String deviceType;

    /**
     * 企业ID
     */
    private String companyId;
    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 登录用户ID
     */
    private String userId;
}
