package com.wanlianyida.app.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.partner.api.model.dto.DeptDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserInfoDTO {

    private String id;

    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 登录账号
     */
    private String loginName;
    /**
     * 用户姓名
     */
    private String username;
    /**
     * 用户登录ID
     */
    private String loginId;
    /**
     * 用户基本信息表Id
     */
    private String userBaseId;
    /**
     * 初始密码
     */
    private String password;

    /**
     * 用户等级:1管理员,2普通员工
     */
    private String levelType;
    /**
     * 角色集合
     */
    private List<UserPermissionDTO> permissionInfoList;
    /**
     * 角色名称集合
     */
    private String exPermissionName;

    /**
     * 用户信息状态(1:有效, 2:无效)
     */
    private String userStatus;

    /**
     * 手机号
     */
    private String telephone;
    /**
     * 姓名
     */
    private String remark;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    /**
     * 修改人
     */
    private String modifyBy;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;

    /**
     * 部门集合
     */
    private List<DeptDTO> deptList;

    /**
     * 岗位id
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 公司名称
     */
    private String companyName;
}
