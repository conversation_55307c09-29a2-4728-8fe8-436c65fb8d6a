package com.wanlianyida.app.interfaces.model.command;

import com.wanlianyida.framework.ctpcommon.model.command.FacadeBaseCommand;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 注册活动用户command
 */
@Data
public class RegisterActivityUserCommand extends FacadeBaseCommand {

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 录入公司名称
     */
    private String recordCompanyName;

    /**
     * 业务方向dict=biz_direction
     */
    private String bizDirection;

    /**
     * 参会目的[10-采购,20-销售]
     */
    private String attendancePurpose;

}
