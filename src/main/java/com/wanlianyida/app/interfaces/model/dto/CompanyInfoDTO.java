package com.wanlianyida.app.interfaces.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: CompanyInfo
 * @description:
 * @author: zhang<PERSON>hen
 * @date: 2025年05月14日
 * @version: 1.0
 */
@Data
public class CompanyInfoDTO implements Serializable {
    /**
     * 是否在物流平台注册标记
     * 0-未注册 1-已注册
     */
    @ApiModelProperty(value = "是否在物流平台注册标记(0-未注册 1-已注册)", required = true, example = "1")
    private Integer register;

    /**
     * 管理员名称
     */
    @ApiModelProperty(value = "管理员名称", example = "张三")
    private String contacts;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13800138000")
    private String phone;

}
