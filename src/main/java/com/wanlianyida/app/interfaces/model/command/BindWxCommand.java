package com.wanlianyida.app.interfaces.model.command;

import com.wanlianyida.framework.ctpcommon.model.command.FacadeBaseCommand;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 绑定微信小程序command
 */
@Data
public class BindWxCommand extends FacadeBaseCommand {

    /**
     * 用户id(注意这里的用户id不是userBaseId)
     */
    private String userId;

    /**
     * appId
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 微信code
     */
    @NotBlank(message = "code不能为空")
    private String code;
}
