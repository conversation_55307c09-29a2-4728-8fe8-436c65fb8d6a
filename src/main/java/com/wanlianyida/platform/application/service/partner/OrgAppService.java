package com.wanlianyida.platform.application.service.partner;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.ctpcommon.entity.BaseException;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcommon.enums.PlatfromCodeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssuserauth.api.model.command.UserInfoPlatformCommand;
import com.wanlianyida.partner.api.inter.OrgManageInter;
import com.wanlianyida.partner.api.model.command.OrgCommand;
import com.wanlianyida.partner.api.model.command.OrgDeleteCommand;
import com.wanlianyida.partner.api.model.dto.OrgDTO;
import com.wanlianyida.partner.api.model.query.OrgTreeQuery;
import com.wanlianyida.platform.infrastructure.constant.Constant;
import com.wanlianyida.platform.infrastructure.exchange.MsgExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.UserAuthExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import javax.annotation.Resource;

@Service
@Slf4j
public class OrgAppService {
    @Resource
    private OrgManageInter orgManageInter;

    @Resource
    private UserAuthExchangeService userAuthExchangeService;

    @Resource
    private MsgExchangeService msgExchangeService;

    /**
     * 组织新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> add(OrgCommand command) {
        command.setPlatformType("21");
        ResultMode<OrgDTO> validationResponse = validatedParams(command);
        if (!validationResponse.isSucceed()) {
            return ResultMode.fail(validationResponse.getMessage());
        }
        OrgDTO model = validationResponse.getModel();
        log.info("<<<<<<<<<<<<<参数认证结果:<<<<<<<<<<<<<<<<" + model);
        command.setId(Long.valueOf(model.getId()));

        // 验证用户密码
        ResultMode<Boolean> passwordValidationResponse = checkLoginPassword(command);
        if (!passwordValidationResponse.isSucceed()) {
            return ResultMode.fail(passwordValidationResponse.getMessage());
        }
        log.info("<<<<<<<<<<<<<登录认证结果:<<<<<<<<<<<<<<<<" + passwordValidationResponse.getMessage());
        // 新增组织关系
        ResultMode<Boolean> addResponse = orgManageInter.add(command);
        if (!addResponse.isSucceed()) {
            return ResultMode.fail(addResponse.getMessage());
        }
        log.info("<<<<<<<<<<<<<组织新增结果:<<<<<<<<<<<<<<<<" + addResponse);
        model.setParentOrgName(command.getParentOrgName());
        // 发送站内信
//        sendNotificationIfNecessary(model, Constant.ORG_MSG_ADD);

        return addResponse;
    }

    /**
     * 组织更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> update(OrgCommand command) {
        command.setPlatformType("21");
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String licenseNo = tokenInfo.getLicenseNo();
        if (ObjectUtil.isEmpty(licenseNo)) {
            return ResultMode.fail("未能获取有效的公司信息,请重新登录");
        }
        if (ObjectUtil.notEqual(licenseNo, command.getSocialCreditCode()) && ObjectUtil.isNotEmpty(command.getParentOrgId())){
            // 参数校验
            ResultMode<OrgDTO> validationResponse = validatedParams(command);
            if (!validationResponse.isSucceed()) {
                return ResultMode.fail(validationResponse.getMessage());
            }

            OrgDTO model = validationResponse.getModel();
            // 更新下级组织关系
            ResultMode<Boolean> updateResponse = orgManageInter.update(command);
            if (!updateResponse.isSucceed()) {
                return ResultMode.fail(updateResponse.getMessage());
            }
            // 发送站内信
//            sendNotificationIfNecessary(model, Constant.ORG_MSG_ADD);
            return updateResponse;
        }

        // 更新登录人的组织信息
        ResultMode<Boolean> updateResponse = orgManageInter.update(command);
        if (!updateResponse.isSucceed()) {
            return ResultMode.fail(updateResponse.getMessage());
        }

        return updateResponse;
    }

    /**
     * 组织删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> delete(OrgDeleteCommand command) {
        command.setPlatformType("21");
        ResultMode<OrgDTO> deleteResponse = orgManageInter.delete(command);
        if (!deleteResponse.isSucceed()) {
            return ResultMode.fail(deleteResponse.getMessage());
        }
        OrgDTO model = deleteResponse.getModel();

        // 发送站内信
//        sendNotificationIfNecessary(model, Constant.ORG_MSG_CANCEL);

        return ResultMode.success();
    }

    /**
     * 构建组织树
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link OrgDTO }>
     */
    public ResultMode<OrgDTO> buildOrganizationTree(OrgTreeQuery query) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)){
            return ResultMode.fail("当前用户未登录");
        }
        String licenseNo = tokenInfo.getLicenseNo();
        if (StrUtil.isEmpty(licenseNo)){
            // 运营端 只有一家企业，先写死。后期组织架构会重构
            query.setLicenseNo(Constant.OPERATOR_COMPANY_SOCIAL_CREDIT_CODE);
        }


        query.setPlatformType("21");
        return orgManageInter.buildOrganizationTree(query);
    }


    /**
     * 验证参数
     *
     * @param command 命令
     * @return {@code ResultMode<OrgDTO> }
     */
    /**
     * 验证参数
     *
     * @param command 命令
     * @return {@code ResultMode<OrgDTO> }
     */
    private ResultMode<OrgDTO> validatedParams(OrgCommand command) {
        // 校验参数
        ResultMode<OrgDTO> orgDTOResultMode = orgManageInter.validatedParams(command);
        log.info("======================参数校验结果：{}===================", orgDTOResultMode.getMessage());
        log.info("======================返回对象结果：{}===================", orgDTOResultMode.getModel());
        if (!orgDTOResultMode.isSucceed()) {
            return ResultMode.fail(orgDTOResultMode.getMessage());
        }

        return orgDTOResultMode;
    }

    /**
     * 验证密码
     *
     * @param command 命令
     * @return {@code ResultMode<OrgDTO> }
     */
    private ResultMode<Boolean> checkLoginPassword(OrgCommand command) {
        // 验证用户密码
        UserInfoPlatformCommand userInfoPlatformCommand = new UserInfoPlatformCommand();
        userInfoPlatformCommand.setSysType(PlatfromCodeEnum.CTP_MANAGE_SYS.getCode());
        userInfoPlatformCommand.setLoginName(command.getAdminAccount());
        userInfoPlatformCommand.setPassword(command.getAdminPassword());
        log.info("用户登录参数:" + JSON.toJSONString(userInfoPlatformCommand));
        ResultMode<Boolean> listResultMode = userAuthExchangeService.checkLoginPassword(userInfoPlatformCommand);
        if (!listResultMode.isSucceed()) {
            return ResultMode.fail(listResultMode.getMessage());
        }
        if (!BooleanUtil.isTrue(listResultMode.getModel())) {
            throw new BaseException("密码错误，请确认后重新输入");
        }
        return listResultMode;
    }

    /**
     * 发送通知（站内信或短信）
     *
     * @param model      组织模型
     * @param templateId 模板ID
     */
    private void sendNotificationIfNecessary(OrgDTO model, String templateId) {
        if (ObjectUtil.isNotEmpty(model.getUserBaseId())) {
            Set<String> receiverList = new HashSet<>();
            receiverList.add(model.getUserBaseId());
            HashMap<String, String> paramsMap = new HashMap<>();
            paramsMap.put("companyName", model.getParentOrgName());
            msgExchangeService.sendMsg(templateId, "2", receiverList, paramsMap);
        }
    }
}
