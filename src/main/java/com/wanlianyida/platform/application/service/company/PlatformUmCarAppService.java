package com.wanlianyida.platform.application.service.company;

import com.wanlianyida.basicdata.model.command.PlatformUmImageCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.BasicDataExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/02/25/14:04
 */
@Slf4j
@Service
public class PlatformUmCarAppService {

    @Resource
    private BasicDataExchangeService basicDataExchangeService;

    public ResultMode<?> imageRotate(PlatformUmImageCommand platformUmImageCommand) {
        return basicDataExchangeService.imageRotate(platformUmImageCommand);
    }
}
