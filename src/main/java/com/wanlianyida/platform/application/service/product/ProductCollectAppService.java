package com.wanlianyida.platform.application.service.product;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.dto.ShopDTO;
import com.wanlianyida.platform.infrastructure.enums.CollectActionEnum;
import com.wanlianyida.platform.infrastructure.exchange.ProductCollectExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.UmShopExchangeService;
import com.wanlianyida.product.api.model.command.ProductCollectActionCommand;
import com.wanlianyida.product.api.model.command.ProductCollectAddCommand;
import com.wanlianyida.product.api.model.command.ProductCollectCancelCommand;
import com.wanlianyida.product.api.model.dto.ProductCollectDTO;
import com.wanlianyida.product.api.model.query.ProductCollectListQuery;
import com.wanlianyida.product.api.model.query.ProductCollectQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 用户收藏店铺
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Service
@Slf4j
public class ProductCollectAppService {

    @Resource
    private ProductCollectExchangeService productCollectExchangeService;

    @Resource
    private UmShopExchangeService umShopExchangeService;


    /**
     * 店铺收藏操作
     *
     * @param addCommand
     * @return {@link Boolean }
     */
    public ResultMode<?> productCollectAction(ProductCollectActionCommand addCommand) {
        if (CollectActionEnum.MUST_INPUT.getCode().equals(addCommand.getCollectActionType())) {
            return productCollectExchangeService.addProductCollect(BeanUtil.copyProperties(addCommand, ProductCollectAddCommand.class));
        } else if (CollectActionEnum.NOT_MUST_INPUT.getCode().equals(addCommand.getCollectActionType())) {
            return productCollectExchangeService.cancelCollection(BeanUtil.copyProperties(addCommand, ProductCollectCancelCommand.class));
        }
        return ResultMode.fail("操作类型错误");
    }

    public ResultMode<Boolean> queryCollectCondition(ProductCollectQuery query) {
        return productCollectExchangeService.queryCollectCondition(query);
    }

    /**
     * 查询收藏列表
     *
     * @param query
     * @return {@link List}<{@link ProductCollectDTO}>
     */
    public ResultMode<List<ProductCollectDTO>> queryCollectListPage(PagingInfo<ProductCollectListQuery> query) {
        ResultMode<List<ProductCollectDTO>> listResultMode = productCollectExchangeService.queryCollectListPage(query);
        List<ProductCollectDTO> productCollectDTOList = listResultMode.getModel();
        if (ObjectUtil.isEmpty(productCollectDTOList)) {
            return listResultMode;
        }
        List<Long> shopIds = productCollectDTOList.stream().map(ProductCollectDTO::getShopId).collect(Collectors.toList());
        //关联店铺信息
        ResultMode<List<ShopDTO>> listByShopIds = umShopExchangeService.batchQueryListByShopIds(shopIds);
        List<ShopDTO> shopDTOList = listByShopIds.getModel();
        if (ObjectUtil.isEmpty(shopDTOList)) {
            log.error("查询店铺信息失败");
            return listResultMode;
        }
        Map<Long, ShopDTO> shopDTOMap = shopDTOList.stream().collect(Collectors.toMap(ShopDTO::getId, Function.identity()));
        for (ProductCollectDTO productCollectDTO : productCollectDTOList) {
            ShopDTO shopDTO = shopDTOMap.get(productCollectDTO.getShopId());
            if (shopDTO != null) {
                productCollectDTO.setShopName(shopDTO.getShopName());
                productCollectDTO.setShopLogoUrl(shopDTO.getShopLogoUrl());
            }
        }
        return ResultMode.successPageList(productCollectDTOList, listResultMode.getTotal());
    }


}
