package com.wanlianyida.platform.application.service.support;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.assembler.content.UserBaseInfossemble;
import com.wanlianyida.platform.infrastructure.exchange.SupportExchangeService;
import com.wanlianyida.platform.infrastructure.util.BizLogUtilService;
import com.wanlianyida.platform.interfaces.model.command.SendAuditLogCommand;
import com.wanlianyida.support.api.model.command.AutoAuditConfigAddCommand;
import com.wanlianyida.support.api.model.command.AutoAuditConfigUpdateCommand;
import com.wanlianyida.support.api.model.dto.AutoAuditConfigListDTO;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;
import com.wanlianyida.support.api.model.query.AutoAuditConfigListQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 自动审核-配置表
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Service
public class BdAutoAuditConfigService {


    @Resource
    private SupportExchangeService supportExchangeService;


    /**
     * 新增
     * @param addCommand
     * @return
     */
    public ResultMode add(AutoAuditConfigAddCommand addCommand) {
        UserBaseInfossemble.setAddBaseInfo(addCommand);
        return supportExchangeService.add(addCommand);
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    public ResultMode update(AutoAuditConfigUpdateCommand updateCommand) {
        UserBaseInfossemble.setUpdateBaseInfo(updateCommand);
        ResultMode resultMode = supportExchangeService.update(updateCommand);
        // 修改成功后发送MQ消息添加日志记录
        if (resultMode.isSucceed()) {
            String optTypeAndContent = updateCommand.
                    getAutoAudit() == 1
                    ? LogOperationRecordEnum.OperateEnum.OPERATE_TYPE_OPEN.getOperateType()
                    : LogOperationRecordEnum.OperateEnum.OPERATE_TYPE_CLOSE.getOperateType();

            // 业务id
            SendAuditLogCommand command = new SendAuditLogCommand();
            command.setBizId(updateCommand.getAutoSceneType()); // 业务id
            command.setOperateType(optTypeAndContent); // 操作类型
            command.setOperateContent(optTypeAndContent); // 操作内容
            BizLogUtilService.sendAuditLogBizMsg(command);
        }
        return resultMode;
    }


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    public ResultMode<List<AutoAuditConfigListDTO>> list(PagingInfo<AutoAuditConfigListQuery> query) {
        return supportExchangeService.list(query);
    }

}
