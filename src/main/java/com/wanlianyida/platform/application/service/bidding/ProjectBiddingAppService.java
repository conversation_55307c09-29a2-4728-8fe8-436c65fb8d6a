package com.wanlianyida.platform.application.service.bidding;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.bidding.api.model.dto.ConsultDetailDTO;
import com.wanlianyida.bidding.api.model.dto.TenderPackageProductDTO;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.TenderPackageTimeDTO;
import com.wanlianyida.bidding.api.model.query.ClarifyReplyQuery;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.enums.ClarifyAuditStatusEnum;
import com.wanlianyida.platform.infrastructure.exchange.bidding.ClarifyQueryExchange;
import com.wanlianyida.platform.infrastructure.exchange.bidding.ConsultInfoExchange;
import com.wanlianyida.platform.infrastructure.exchange.bidding.TenderPackageExchange;
import com.wanlianyida.platform.interfaces.model.dto.bidding.ApiPageClarifyListDTO;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageClarifyQuery;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageConsultInfoQuery;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageTenderPackageProductQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

@Slf4j
@Service
public class ProjectBiddingAppService {

    @Autowired
    private TenderPackageExchange tenderPackageExchange;

    @Autowired
    private ClarifyQueryExchange clarifyQueryExchange;

    @Autowired
    private ConsultInfoExchange consultInfoExchange;

    public ResultMode<List<TenderPackageProductDTO>> queryTenderPackageProductPage(PagingInfo<ApiPageTenderPackageProductQuery> pagingInfoQuery) {
        return tenderPackageExchange.queryTenderPackageProductPage(pagingInfoQuery);
    }

    public ResultMode<TenderPackageTimeDTO> getTimeDetail(@Valid IdQuery query) {
        return tenderPackageExchange.getTimeDetail(query);
    }

    public ResultMode queryClarifyPage(PagingInfo<ApiPageClarifyQuery> pagingInfo) {
        ResultMode resultMode = clarifyQueryExchange.queryClarifyPage(pagingInfo);
        if(resultMode.getModel() == null ){
            return resultMode;
        }
        List<ApiPageClarifyListDTO> clarifyListDTOS = JSONObject.parseArray(JSON.toJSONString(resultMode.getModel()), ApiPageClarifyListDTO.class);
        clarifyListDTOS  =clarifyListDTOS.stream().map(dto -> {
            if (ObjectUtil.notEqual(dto.getAuditStatus(), ClarifyAuditStatusEnum.Audit_Status_20.getCode())){
                dto.setAuditStatus(ClarifyAuditStatusEnum.Audit_Status_10.getCode());
            }
            return dto;
        }).collect(Collectors.toList());
        return ResultMode.successPageList(clarifyListDTOS,resultMode.getTotal());
    }

    public ResultMode<List<ConsultDetailDTO>> queryPageConsult(PagingInfo<ApiPageConsultInfoQuery> pageQuery) {
        return consultInfoExchange.queryPageConsult(pageQuery);
    }

    public ResultMode queryPageBiddingSupplier(PagingInfo<ClarifyReplyQuery> pagingInfo) {
        return clarifyQueryExchange.queryPageBiddingSupplier(pagingInfo);
    }

    public ResultMode queryConsultFileUrl(ApiPageConsultInfoQuery query) {
        return consultInfoExchange.queryConsultFileUrl(query);
    }
}
