package com.wanlianyida.platform.application.service.product;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.inter.ProductCategoryConfigInter;
import com.wanlianyida.product.api.model.command.*;
import com.wanlianyida.product.api.model.dto.*;
import com.wanlianyida.product.api.model.query.ProductCategoryConfigPageQuery;
import com.wanlianyida.product.api.model.query.RelatedAttributeQuery;
import com.wanlianyida.product.api.model.query.RelatedBrandQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class ProductCategoryConfigAppService {

    @Resource
    private ProductCategoryConfigInter configInter;

    /**
     * 获取已经关联的规格列表信息
     */
    public ResultMode<List<CategoryRelatedAttributeDTO>> queryRelatedAttributes(RelatedAttributeQuery query) {
        return configInter.queryRelatedAttributes(query);
    }

    /**
     * 获取已经关联的品牌列表信息
     */
    public ResultMode<List<CategoryRelatedBrandDTO>> queryRelatedBrands(RelatedBrandQuery query) {
        return configInter.queryRelatedBrands(query);
    }

    /**
     * 关联规格
     * @param commandList 参数
     */
    public ResultMode<?> relateAttributes(List<RelateAttributesCommand> commandList) {
        return configInter.relateAttributes(commandList);
    }

    /**
     * 关联品牌
     * @param command 参数
     */
    public ResultMode<?> relateBrands(RelateBrandCommand command) {
        return configInter.relateBrands(command);
    }

    /**
     * 分页查询商品品类配置
     * @param query 分页参数
     */
    public ResultMode<List<ProductCategoryConfigPageDTO>> pageQuery(PagingInfo<ProductCategoryConfigPageQuery> query) {
        return configInter.pageQuery(query);
    }


}
