package com.wanlianyida.platform.application.service.bidding;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.bidding.TenderPackageExchange;
import com.wanlianyida.platform.interfaces.model.dto.bidding.ApiPageQueryPackageDTO;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageQueryPackage;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class TenderAppService {

    @Resource
    private TenderPackageExchange tenderPackageExchange;

    /**
     * 标包列表分页查询
     */
    public ResultMode<List<ApiPageQueryPackageDTO>> pageQueryPackage(PagingInfo<ApiPageQueryPackage> pageQueryPackage) {
        return tenderPackageExchange.pageQueryPackage(pageQueryPackage);
    }
}
