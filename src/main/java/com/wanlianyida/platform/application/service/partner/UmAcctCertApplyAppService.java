package com.wanlianyida.platform.application.service.partner;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.UmAcctCertApplyInter;
import com.wanlianyida.partner.api.model.command.UmAcctCertApplyCreateCommand;
import com.wanlianyida.partner.api.model.command.UmAcctCertAuditCommand;
import com.wanlianyida.partner.api.model.dto.AcctCertStatusStatisticsDTO;
import com.wanlianyida.partner.api.model.dto.UmAcctCertApplyDTO;
import com.wanlianyida.partner.api.model.dto.UmAcctCertApplyDetailDTO;
import com.wanlianyida.partner.api.model.dto.UpdateAcctCertDTO;
import com.wanlianyida.partner.api.model.query.AcctCertApplyDetailQuery;
import com.wanlianyida.partner.api.model.query.AcctCertApplyQuery;
import com.wanlianyida.platform.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.sett.api.inter.SettAcctCertInter;
import com.wanlianyida.sett.api.model.dto.CallbackAcctCertDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 账户证书审核
 * <AUTHOR>
 */
@Slf4j
@Service
public class UmAcctCertApplyAppService {

    @Resource
    private UmAcctCertApplyInter umAcctCertApplyInter;

    @Resource
    private SettAcctCertInter settAcctCertInter;

    @Resource
    private UploadExchangeService uploadExchangeService;

    public ResultMode<List<UmAcctCertApplyDTO>> pageList(PagingInfo<AcctCertApplyQuery> info) {
        return umAcctCertApplyInter.queryAcctCertApplyList(info);
    }

    public ResultMode<String> createAcctCertApply(UmAcctCertApplyCreateCommand command) {
        return umAcctCertApplyInter.createAcctCertApply(command);
    }

    public ResultMode<UmAcctCertApplyDetailDTO> queryAcctCertApplyDetail(AcctCertApplyDetailQuery query) {
        return umAcctCertApplyInter.queryAcctCertApplyDetail(query);
    }

    public ResultMode<?> auditAcctCert(UmAcctCertAuditCommand command) {
        // 调用核心服务执行审核操作
        ResultMode<UpdateAcctCertDTO> resultMode = umAcctCertApplyInter.auditAcctCert(command);
        if (resultMode.isSucceed()) {
            log.info("用户证书审核操作成功后同步审核状态开始，请求参数{}", JSON.toJSONString(resultMode.getModel()));
            // 审核操作成功调用用户证书接口同步审核状态
            UpdateAcctCertDTO updateAcctCertDTO = resultMode.getModel();
            ResultMode<Void> result = settAcctCertInter.callbackUpdateAcctCertStatus(BeanUtil.toBean(updateAcctCertDTO, CallbackAcctCertDTO.class));
            log.info("用户证书审核操作成功后同步审核状态结束，用户证书ID{}，审核状态{}，同步结果{}", updateAcctCertDTO.getCertId(), updateAcctCertDTO.getAuditStatus(), result.isSucceed());
        }
        return resultMode;
    }

    public ResultMode<AcctCertStatusStatisticsDTO> queryStatusStatistics(AcctCertApplyQuery query) {
        return umAcctCertApplyInter.queryStatusStatistics(query);
    }
}
