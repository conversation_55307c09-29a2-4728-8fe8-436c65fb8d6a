package com.wanlianyida.platform.application.service.support;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exception.CtpOrchPlatformException;
import com.wanlianyida.platform.infrastructure.exchange.MsgMallExchangeService;
import com.wanlianyida.support.api.model.command.MsgTypeCommand;
import com.wanlianyida.support.api.model.dto.BdMsgTemplateTypeDTO;
import com.wanlianyida.support.api.model.dto.MsgTypeDTO;
import com.wanlianyida.support.api.model.query.BdMsgTemplateTypeQuery;
import com.wanlianyida.support.api.model.query.MsgTypeQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
@Slf4j
public class MsgTypeAppService {
    @Resource
    private MsgMallExchangeService msgMallExchangeService;
    public ResultMode<String> addMsgType(MsgTypeCommand command) {
        return msgMallExchangeService.addMsgType(command);
    }

    public ResultMode<Void> updateMsgType(MsgTypeCommand command) {
        return msgMallExchangeService.updateMsgType(command);
    }

    public ResultMode<MsgTypeDTO> queryDetail(MsgTypeQuery query) {
        return msgMallExchangeService.msgTypeQueryDetail(query);
    }

    public ResultMode queryPage(PagingInfo<MsgTypeQuery> pagingInfo) {
        return msgMallExchangeService.queryPage(pagingInfo);
    }

    public ResultMode<Void> delMsgType(MsgTypeCommand command) {
        String id = command.getId();
        if (StrUtil.isEmpty(id)){
            throw new CtpOrchPlatformException("id不能为空");
        }
        BdMsgTemplateTypeQuery msgTemplateTypeQuery = new BdMsgTemplateTypeQuery();
        msgTemplateTypeQuery.setMsgTypeId(id);
        ResultMode<List<BdMsgTemplateTypeDTO>> listResultMode = msgMallExchangeService.queryByCondition(msgTemplateTypeQuery);
        if (!listResultMode.isSucceed()){
            throw new CtpOrchPlatformException("查询模版类型失败：" + listResultMode.getMessage());
        }
        List<BdMsgTemplateTypeDTO> model = listResultMode.getModel();
        if (ObjectUtil.isNotEmpty(model)){
            // 获取消息分类下的所有模版id
            List<String> templateIdList = model.stream().map(BdMsgTemplateTypeDTO::getTemplateId).collect(Collectors.toList());
            return ResultMode.fail("该消息分类下存在系统消息板,无法删除");
        }
        return msgMallExchangeService.delMsgType(command);
    }
}




