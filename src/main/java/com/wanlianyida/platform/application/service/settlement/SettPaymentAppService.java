package com.wanlianyida.platform.application.service.settlement;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.inter.OcOrderReceiveMethodInter;
import com.wanlianyida.order.api.model.dto.OrderReceiveMethodDTO;
import com.wanlianyida.order.api.model.query.OrderReceiveMethodQuery;
import com.wanlianyida.sett.api.inter.SettPaymentQueryInter;
import com.wanlianyida.sett.api.model.dto.SettPaymentDTO;
import com.wanlianyida.sett.api.model.dto.SettPaymentDetailDTO;
import com.wanlianyida.sett.api.model.query.SettPaymentDetailQuery;
import com.wanlianyida.sett.api.model.query.SettPaymentListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 付款单
 */
@Slf4j
@Service
public class SettPaymentAppService {

    @Resource
    private SettPaymentQueryInter settPaymentQueryInter;

    @Resource
    private OcOrderReceiveMethodInter ocOrderReceiveMethodInter;


    /**
     * 查询付款单分页列表
     *
     * @param param
     * @return
     */
    public ResultMode<List<SettPaymentDTO>> querySettPaymentPage(PagingInfo<SettPaymentListQuery> param) {
        return settPaymentQueryInter.querySettPaymentPage(param);
    }
    /**
     * 查询付款单分页导出列表
     *
     * @param param
     * @return
     */
    public ResultMode<List<SettPaymentDTO>> querySettPaymentexportPage(PagingInfo<SettPaymentListQuery> param) {
        ResultMode<List<SettPaymentDTO>> listResultMode = settPaymentQueryInter.querySettPaymentPage(param);
        List<SettPaymentDTO> model = listResultMode.getModel();
        if (CollectionUtil.isNotEmpty(model)) {
            model.parallelStream().forEach(f -> {
                f.setPayableAmount(BigDecimal.ZERO.compareTo(f.getPayableAmount()) == 0 ? null : f.getPayableAmount());
                f.setPaidAmount(BigDecimal.ZERO.compareTo(f.getPaidAmount()) == 0 ? null : f.getPaidAmount());
            });
        }
        return listResultMode;
    }

    /**
     * 付款单详情页查询
     */
    public ResultMode<SettPaymentDetailDTO> queryPaymentDetailsAfterSubmit(SettPaymentDetailQuery query) {
        SettPaymentDetailDTO model = settPaymentQueryInter.querySettPaymentDetail(query).getModel();
        if (Objects.nonNull(model)) {
            OrderReceiveMethodQuery orderReceiveMethodQuery = new OrderReceiveMethodQuery();
            orderReceiveMethodQuery.setOrderNo(model.getOrderNo());
            // 封装付款方式信息
            List<OrderReceiveMethodDTO> methodDTOList = ocOrderReceiveMethodInter.queryList(orderReceiveMethodQuery).getModel();
            String collect = methodDTOList.stream().map(OrderReceiveMethodDTO::getReceiveMethod).collect(Collectors.joining(StrPool.COMMA));
            model.setReceiveMethod(collect);
        }
        return ResultMode.success(model);
    }

}
