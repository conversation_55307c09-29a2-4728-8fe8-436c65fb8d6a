package com.wanlianyida.platform.application.service.rms;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.enums.RcResultEnum;
import com.wanlianyida.platform.infrastructure.exchange.rms.QualExpiryExchange;
import com.wanlianyida.rms.model.dto.RcQualExpiryDTO;
import com.wanlianyida.rms.model.dto.RcQualExpiryRecordDTO;
import com.wanlianyida.rms.model.query.RcQualExpiryPageQuery;
import com.wanlianyida.rms.model.query.RcQualExpiryRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class QualExpiryAppService {
    @Resource
    private QualExpiryExchange qualExpiryExchange;


    /**
     * 资质到期分页查询
     * @param pageQuery
     * @return
     */
    public ResultMode<List<RcQualExpiryDTO>> pageQueryQualExpiry(PagingInfo<RcQualExpiryPageQuery> pageQuery){
        ResultMode<List<RcQualExpiryDTO>> resultMode = qualExpiryExchange.pageQueryQualExpiry(pageQuery);
        List<RcQualExpiryDTO> rcQualExpiryDTOList = resultMode.getModel();
        if (resultMode.isSucceed() && rcQualExpiryDTOList != null){
            for (RcQualExpiryDTO rcQualExpiryDTO : rcQualExpiryDTOList) {
                String rcResult = rcQualExpiryDTO.getRcResult();
                if (StrUtil.isNotBlank(rcResult)){
                    rcQualExpiryDTO.setRcResult(RcResultEnum.getMsgByCode(rcResult));
                }
            }
        }
        return resultMode;
    }


    /**
     * 资质到期变更记录查询
     * @param query
     * @return
     */
    public ResultMode<List<RcQualExpiryRecordDTO>> queryQualExpiryRecord(RcQualExpiryRecordQuery query){
        return qualExpiryExchange.queryQualExpiryRecord(query);
    }
}
