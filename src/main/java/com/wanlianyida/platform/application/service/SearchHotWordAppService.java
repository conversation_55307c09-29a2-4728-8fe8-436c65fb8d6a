package com.wanlianyida.platform.application.service;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.SearchHotWordInter;
import com.wanlianyida.support.api.model.command.SearchHotWordCommand;
import com.wanlianyida.support.api.model.dto.SearchHotWordDTO;
import com.wanlianyida.support.api.model.query.SearchHotWordPageQuery;
import com.wanlianyida.support.api.model.query.SearchHotWordQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class SearchHotWordAppService {
    @Resource
    private SearchHotWordInter searchHotWordInter;
    /**
     * 新增搜索热词
     * @param command
     * @return
     */
    public ResultMode<Void> saveOrUpdate(SearchHotWordCommand command) {
        return  searchHotWordInter.saveOrUpdate(command);
    }

    /**
     * 查询详情
     * @param query
     * @return
     */
    public ResultMode<SearchHotWordDTO> queryDetail(SearchHotWordQuery query) {
        return searchHotWordInter.queryDetail(query);
    }

    /**
     * 删除搜索热词
     * @param command
     * @return
     */
    public ResultMode<Void> delete(SearchHotWordCommand command) {
        return searchHotWordInter.delete(command);
    }

    /**
     * 分页查询搜索热词
     * @param pagingInfo
     * @return
     */
    public ResultMode queryPage(PagingInfo<SearchHotWordPageQuery> pagingInfo) {
        return searchHotWordInter.queryPage(pagingInfo);
    }

    /**
     * 查询指定条数的数据
     * @param query
     * @return
     */
    public ResultMode<List<SearchHotWordDTO>> queryLimitedData(SearchHotWordPageQuery query) {
        return searchHotWordInter.queryLimitedData(query);
    }

}
