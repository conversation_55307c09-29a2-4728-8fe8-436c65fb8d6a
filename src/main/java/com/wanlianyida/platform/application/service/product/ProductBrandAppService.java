package com.wanlianyida.platform.application.service.product;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.inter.ProductBrandInter;
import com.wanlianyida.product.api.model.command.*;
import com.wanlianyida.product.api.model.dto.BrandRelatedCategoryDTO;
import com.wanlianyida.product.api.model.dto.ProductBrandDTO;
import com.wanlianyida.product.api.model.dto.ProductBrandPageDTO;
import com.wanlianyida.product.api.model.dto.ProductBrandPicDTO;
import com.wanlianyida.product.api.model.query.ProductBrandPageQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class ProductBrandAppService {

    @Resource
    private ProductBrandInter productBrandInter;

    /**
     * 新增商品品牌
     * @param command 参数
     */
    public ResultMode<?> add(ProductBrandAddCommand command) {
        return productBrandInter.add(command);
    }

    /**
     * 编辑商品品牌
     * @param command 参数
     */
    public ResultMode<?> edit(ProductBrandEditCommand command) {
        return productBrandInter.edit(command);
    }

    /**
     * 删除商品品牌
     * @param command 参数
     */
    public ResultMode<?> delete(ProductBrandDeleteCommand command) {
        return productBrandInter.delete(command);
    }

    /**
     * 分页查询商品品牌
     * @param query 分页参数
     */
    public ResultMode<List<ProductBrandPageDTO>> pageQuery(PagingInfo<ProductBrandPageQuery> query) {
        return productBrandInter.pageQuery(query);
    }

    /**
     * 根据主键列表查询
     * @param idList 参数
     */
    public ResultMode<List<ProductBrandDTO>> queryByIds(List<Long> idList) {
        return productBrandInter.queryByIds(idList);
    }

    /**
     * 根据主键查询品牌图片
     * @param query 参数
     */
    public ResultMode<ProductBrandPicDTO> queryBrandLogoById(IdQuery query) {
        return productBrandInter.queryBrandLogoById(query);
    }

    /**
     * 品牌已经关联的品类列表
     */
    public ResultMode<List<BrandRelatedCategoryDTO>> queryRelatedCategory(IdQuery query){
        return productBrandInter.queryRelatedCategory(query);
    }

    /**
     * 品牌批量关联品类
     */
    public ResultMode<?> relateCategory(RelateCategoryCommand command){
        return productBrandInter.relateCategory(command);
    }
}
