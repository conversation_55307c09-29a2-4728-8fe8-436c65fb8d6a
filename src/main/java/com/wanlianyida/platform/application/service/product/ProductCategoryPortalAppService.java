package com.wanlianyida.platform.application.service.product;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.inter.ProductCategoryPortalInter;
import com.wanlianyida.product.api.inter.ProductCategoryUserInter;
import com.wanlianyida.product.api.model.command.ProductCategoryUpdateNavConfigCommand;
import com.wanlianyida.product.api.model.command.SetCategoryIconCommand;
import com.wanlianyida.product.api.model.command.SetRecommendBrandCommand;
import com.wanlianyida.product.api.model.dto.ProductBrandSimpleDTO;
import com.wanlianyida.product.api.model.dto.ProductCategoryDTO;
import com.wanlianyida.product.api.model.dto.ProductCategoryPortalPageDTO;
import com.wanlianyida.product.api.model.query.CategoryBrandQuery;
import com.wanlianyida.product.api.model.query.ProductCategoryPortalDirectSubQuery;
import com.wanlianyida.product.api.model.query.ProductCategoryPortalPageQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class ProductCategoryPortalAppService {

    @Resource
    private ProductCategoryPortalInter portalInter;

    @Resource
    private ProductCategoryUserInter userInter;

    /**
     * 更新品类导航配置
     * @param command 参数
     */
    public ResultMode<?> updateNavConfig(ProductCategoryUpdateNavConfigCommand command) {
        return portalInter.updateNavConfig(command);
    }

    /**
     * 品类导航商品品类分页查询
     * @param query 分页参数
     */
    public ResultMode<List<ProductCategoryPortalPageDTO>> pageQueryProductCategoryPortal(PagingInfo<ProductCategoryPortalPageQuery> query) {
        return portalInter.pageQueryProductCategoryPortal(query);
    }

    /**
     * 品类导航获取分类下所有未删除的直接子级
     * @param query 分页参数
     */
    public ResultMode<List<ProductCategoryPortalPageDTO>> queryDirectSubLevelPortal(ProductCategoryPortalDirectSubQuery query) {
        return portalInter.queryDirectSubLevelPortal(query);
    }

    /**
     * 设置品类图标
     */
    public ResultMode<?> setCategoryIcon(SetCategoryIconCommand command){
        return portalInter.setCategoryIcon(command);
    }

    /**
     * 品类详情
     */
    public ResultMode<ProductCategoryDTO> categoryDetail(IdQuery idQuery){
        return portalInter.categoryDetail(idQuery);
    }

    /**
     * 获取品类关联的品牌列表
     */
    public ResultMode<List<ProductBrandSimpleDTO>> queryProductBrandByCategoryId(CategoryBrandQuery query){
        return userInter.queryProductBrandByCategoryId(query);
    }

    /**
     * 设置推荐品牌
     */
    public ResultMode<?> setRecommendBrand(SetRecommendBrandCommand command){
        return portalInter.setRecommendBrand(command);
    }

}
