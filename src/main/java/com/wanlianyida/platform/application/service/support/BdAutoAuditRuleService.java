package com.wanlianyida.platform.application.service.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.IdUtil;
import com.wanlianyida.fssbasecontent.api.inter.ChCategoryCenterInter;
import com.wanlianyida.fssbasecontent.api.model.query.ChCategoryListQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.partner.api.model.dto.UmCompanyMiniDTO;
import com.wanlianyida.partner.api.model.query.UmCompanyPageMiniQuery;
import com.wanlianyida.platform.application.assembler.content.UserBaseInfossemble;
import com.wanlianyida.platform.application.assembler.support.BdAutoAuditRuleAssemble;
import com.wanlianyida.platform.infrastructure.enums.AutoAuditRuleValueEnum;
import com.wanlianyida.platform.infrastructure.exchange.EsExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.SupportExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.UmCompanyExchangeService;
import com.wanlianyida.platform.infrastructure.util.BizLogUtilService;
import com.wanlianyida.platform.interfaces.model.command.SendAuditLogCommand;
import com.wanlianyida.search.command.CtpContentCommand;
import com.wanlianyida.support.api.model.command.BdAutoAuditRuleAddCommand;
import com.wanlianyida.support.api.model.dto.BdAutoAuditRuleListDTO;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;
import com.wanlianyida.support.api.model.query.BdAutoAuditRuleListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 自动审核-规则表
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Slf4j
@Service
public class BdAutoAuditRuleService {

    @Resource
    private SupportExchangeService supportExchangeService;

    @Resource
    private UmCompanyExchangeService umCompanyExchangeService;

    @Resource
    private EsExchangeService esExchangeService;

    @Resource
    private ChCategoryCenterInter chCategoryCenterInter;

    @Resource
    private IdUtil idUti;

    // 定义场景值数组常量[10007,10008,10009]
    private static final String[] SCENE_VALUE_ARRAY = new String[]{"10007", "10008", "10009"};

    /**
     * 新增与更新
     * @param addCommand
     * @return
     */
    public ResultMode addOrUpdateRule(List<BdAutoAuditRuleAddCommand> addCommand) {
        // 遍历添加命令，为每个命令设置创建人信息
        addCommand.forEach(UserBaseInfossemble::setAddBaseInfo);

        // 获取修改前的第一条数据用于对比
        BdAutoAuditRuleAddCommand auditRuleAddCommand = IterUtil.getFirst(addCommand);
        // 根据配置ID查询当前审核规则的详情数据,获取修改之前的数据
        BdAutoAuditRuleListDTO bdAutoAuditRule = getBdAutoAuditRuleListDTO(auditRuleAddCommand.getConfigId());

        // 调用服务进行新增或更新操作
        ResultMode resultMode = supportExchangeService.addOrUpdateRule(addCommand);

        if (resultMode.isSucceed()) {

            // 修改成功后发送MQ消息添加日志记录
            String operateContent = "";
            List<String> sceneValueArray = Arrays.asList(SCENE_VALUE_ARRAY);
            // 判断是否为发布文章类型的操作
            if (sceneValueArray.contains(auditRuleAddCommand.getAutoSceneType())) {

                // 拼接发布文章类型的详细操作内容
                operateContent = getSceneOperateContent(auditRuleAddCommand, bdAutoAuditRule, auditRuleAddCommand.getBussCategory());
            } else {
                // 拼接非发布文章类型的简单操作内容
                operateContent = getNotSceneOperateContent(addCommand);
            }

            // 构建并封装审计日志参数
            SendAuditLogCommand command = new SendAuditLogCommand();
            command.setOperateType(LogOperationRecordEnum.OperateEnum.OPERATE_TYPE_MODIFY.getOperateType()); // 设置操作类型为"修改配置"
            command.setOperateContent(operateContent); // 设置具体操作内容
            command.setAutoSceneType(auditRuleAddCommand.getAutoSceneType());

            // 发送MQ消息，记录审计日志
            String detailId = BizLogUtilService.sendAuditLogBizMsg(command);

            // 构建并封装ES保存的日志内容参数
            CtpContentCommand contentCommand = BdAutoAuditRuleAssemble.buildAutoAuditLogAssemble(detailId, addCommand, operateContent);
            // 保存日志详情到ES
            ResultMode esResultMode = esExchangeService.saveContent(contentCommand);
            if (!esResultMode.isSucceed()) {
                log.info("保存日志详情到ES失败参数：{}", contentCommand);
            }
        }

        return resultMode;
    }
    /**
     * 发布文章类型拼接操作内容
     *
     * @return
     */
    private String getSceneOperateContent(BdAutoAuditRuleAddCommand auditRuleAddCommand, BdAutoAuditRuleListDTO auditRuleListDTO, String bussCategory) {

        // 初始化规则名称为“全部”
        String ruleValueName = AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL.getDesc();
        String newRuleValueName = AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL.getDesc();
        // 构建操作内容字符串
        StringBuilder operateContentStr = new StringBuilder();

        // 如果旧规则不存在，则记录日志为初始化
        if (ObjUtil.isEmpty(auditRuleListDTO)){
            Map<String, Object> auditRuleMap = getAuditRuleMap(auditRuleAddCommand.getBusiIdList(), new ArrayList<>(), bussCategory);
            List<String> newRuleValueNameList = convertObjectToList(auditRuleMap.get("newRuleValueNameList"), String.class);
            // 如果新规则值不是“全部”，则使用中文顿号拼接新分类名称
            if (!AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL.getCode().equals(auditRuleAddCommand.getRuleValue())){
                newRuleValueName = String.join("、", newRuleValueNameList);
            }
            operateContentStr.append("首次操作记录：【");
            operateContentStr.append(auditRuleAddCommand.getRuleName()); // 新的规则名称
            operateContentStr.append("：");
            operateContentStr.append(newRuleValueName); // 新的规则值名称
            operateContentStr.append("】");

            return String.valueOf(operateContentStr);
        }

        // 获取新旧业务ID对应的分类名称列表
        Map<String, Object> auditRuleMap = getAuditRuleMap(auditRuleAddCommand.getBusiIdList(), auditRuleListDTO.getBusiIdList(), bussCategory);
        List<String> newRuleValueNameList = convertObjectToList(auditRuleMap.get("newRuleValueNameList"), String.class);
        List<String> ruleValueNameList = convertObjectToList(auditRuleMap.get("ruleValueNameList"), String.class);

        // 如果新规则值不是“全部”，则使用中文顿号拼接新分类名称
        if (!AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL.getCode().equals(auditRuleAddCommand.getRuleValue())){
            newRuleValueName = String.join("、", newRuleValueNameList);
        }

        // 如果旧规则值不是“全部”，则使用中文顿号拼接旧分类名称
        if (!AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL.getCode().equals(auditRuleListDTO.getRuleValue())){
            ruleValueName = String.join("、", ruleValueNameList);
        }

        // 示例：【资讯类别：全部】改为【资讯类别：类别1、类别2】
        operateContentStr.append("【");
        operateContentStr.append(auditRuleListDTO.getRuleName()); // 旧的规则名称
        operateContentStr.append("：");
        operateContentStr.append(ruleValueName); // 旧的规则值名称
        operateContentStr.append("】改为【");
        operateContentStr.append(auditRuleAddCommand.getRuleName()); // 新的规则名称
        operateContentStr.append("：");
        operateContentStr.append(newRuleValueName); // 新的规则值名称
        operateContentStr.append("】");

        return String.valueOf(operateContentStr);
    }

    /**
     * 根据新旧业务ID列表获取对应的分类名称列表（仅顶级分类）
     *
     * @param newBusiIdList 新的业务ID列表
     * @param busiIdList    旧的业务ID列表
     * @param bussCategory  业务分类标识
     * @return 包含新旧分类名称列表的Map，key为"ruleValueNameList"和"newRuleValueNameList"
     */
    private Map<String, Object> getAuditRuleMap(List<String> newBusiIdList, List<String> busiIdList, String bussCategory) {
        // 构建查询参数并获取分类列表
        ChCategoryListQuery query = new ChCategoryListQuery();
        query.setBussCategory(bussCategory);
        ResponseMessage<?> categoryListMode = chCategoryCenterInter.list(query);

        // 如果查询失败，记录日志并返回空map
        if (!categoryListMode.isSucceed()) {
            log.info("获取分类列表失败参数：{}", query);
            return new HashMap<>();
        }

        // 转换响应数据为JSONObject列表
        List<Map> mapList = convertObjectToList(categoryListMode.getModel(), Map.class);

        // 存储匹配到的新旧分类名称
        List<String> ruleValueNameList = new ArrayList<>();
        List<String> newRuleValueNameList = new ArrayList<>();

        // 遍历所有分类数据，筛选顶级分类并匹配业务ID
        mapList.forEach(item -> {
            // 父级分类为null则为顶级分类
            if (ObjUtil.isEmpty(item.get("parentEnableStatus"))) {
                String categoryId = String.valueOf(item.get("categoryId"));
                String categoryName = String.valueOf(item.get("categoryName"));

                // 匹配新的业务ID
                if (newBusiIdList.contains(categoryId)) {
                    newRuleValueNameList.add(categoryName);
                }

                // 匹配旧的业务ID
                if (busiIdList.contains(categoryId)) {
                    ruleValueNameList.add(categoryName);
                }
            }
        });

        // 构建结果map并返回
        Map<String, Object> categoryMap = new HashMap<>();
        categoryMap.put("ruleValueNameList", ruleValueNameList);
        categoryMap.put("newRuleValueNameList", newRuleValueNameList);
        return categoryMap;
    }

    /**
     * 转换对象为列表
     * @param obj
     * @param elementClass
     * @return
     * @param <T>
     */
    public static <T> List<T> convertObjectToList(Object obj, Class<T> elementClass) {
        if (ObjUtil.isEmpty(obj)) {
            // 返回空列表，防止报空指针错误
            return new ArrayList<>();
        }
        if (!(obj instanceof List)) {
            // 返回空列表，防止报空指针错误
            return new ArrayList<>();
        }

        List<?> list = (List<?>) obj;
        int index = 0;
        for (Object element : list) {
            if (ObjUtil.isNotEmpty(element) && !elementClass.isInstance(element)) {
                log.info("元素 {} 类型不匹配: 期望 {}, 实际 {}",
                        index, elementClass.getName(), element.getClass().getName());
                // 返回空列表，防止报空指针错误
                return new ArrayList<>();
            }
            index++;
        }
        @SuppressWarnings("unchecked")
        List<T> result = (List<T>) list;
        return result;
    }

    /**
     * 发布文章类型拼接操作内容
     *
     * @return
     */
    private String getNotSceneOperateContent(List<BdAutoAuditRuleAddCommand> addCommand) {
        // 初始化商家类型和商品类型的默认值为“全部”
        String businessContent = AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL_BUSINESS.getDesc();
        String productTypeContent = AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL_CATEGORY.getDesc();

        // 遍历所有规则命令，筛选并处理商家类型和商品类型
        for (BdAutoAuditRuleAddCommand command : addCommand) {
            // 判断是否为商家类型规则，并且不为“全部”时进行处理
            if (AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_TYPE_BUSINESS.getStrCode().equals(command.getRuleType())
                && !AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL_BUSINESS.getCode().equals(command.getRuleValue())) {
                
                // 如果是“内部商家”类型，则查询对应的商家信息
                if (AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_INTERNAL_BUSINESS.getCode().equals(command.getRuleValue())
                        && ObjUtil.isNotEmpty(command.getSelectNum())
                        && command.getSelectNum() > 0
                ) {
                    PagingInfo<UmCompanyPageMiniQuery> pagingInfo = new PagingInfo<>();
                    UmCompanyPageMiniQuery query = new UmCompanyPageMiniQuery();
                    
                    // 查询条件设置为自营（10）
                    query.setBizType(AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_SELF_OPERATION.getStrCode());
                    pagingInfo.setFilterModel(query);
                    pagingInfo.setPageLength(command.getSelectNum());  // 设置分页长度
                    
                    // 调用接口查询商家数据
                    ResultMode<List<UmCompanyMiniDTO>> resultMode = umCompanyExchangeService.queryPageMini(pagingInfo);
                    List<UmCompanyMiniDTO> miniDTOS = resultMode.getModel();
                    
                    // 如果查询结果非空，提取商家ID并设置到command中
                    if (ObjUtil.isNotEmpty(miniDTOS)) {
                        List<String> busiIdList = miniDTOS.stream()
                                .map(UmCompanyMiniDTO::getId)
                                .collect(Collectors.toList());
                        command.setBusiIdList(busiIdList);  // 设置业务ID列表
                    }
                }

                // 更新商家类型的内容描述（带数量）
                businessContent = getStrContentValue(command.getRuleValueName(), command.getSelectNum());
            } 
            // 判断是否为商品类型规则，并且不为“全部”时进行处理
            else if (AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_TYPE_PRODUCT.getStrCode().equals(command.getRuleType())
                && !AutoAuditRuleValueEnum.AUTO_AUDIT_RULE_VALUE_ALL_CATEGORY.getCode().equals(command.getRuleValue())) {
                
                // 更新商品类型的内容描述（带数量）
                productTypeContent = getStrContentValue(command.getRuleValueName(), command.getSelectNum());
            }
        }

        // 构建最终的操作内容字符串
        StringBuilder operateContentStr = new StringBuilder();
        operateContentStr.append("商家类型=");
        operateContentStr.append(businessContent);  // 添加商家类型内容
        operateContentStr.append("，商品品类=");
        operateContentStr.append(productTypeContent);  // 添加商品类型内容

        return String.valueOf(operateContentStr);  // 返回拼接好的操作内容
    }

    /**
     * 获取字符串内容
     * @return
     */
    private String getStrContentValue(String ruleValueName, Integer selectNum){
        StringBuilder operateContentStr = new StringBuilder();
        operateContentStr.append(ruleValueName);
        operateContentStr.append("（");
        operateContentStr.append(ObjUtil.isEmpty(selectNum) ? 0 : selectNum);
        operateContentStr.append("）");
        log.info("更新商家类型的内容描述（带数量）operateContentStr:{}", operateContentStr);
        return String.valueOf(operateContentStr);
    }

    /**
     * 根据配置ID获取自动审核规则详情数据
     * 
     * @param configId 配置ID
     * @return 返回转换后的BdAutoAuditRuleListDTO对象
     */
    private BdAutoAuditRuleListDTO getBdAutoAuditRuleListDTO(String configId) {
        // 构建查询参数对象并设置配置ID
        BdAutoAuditRuleListQuery query = new BdAutoAuditRuleListQuery();
        query.setConfigId(configId);
        
        // 调用supportExchangeService的detail方法获取结果
        ResultMode<List<BdAutoAuditRuleListDTO>> resultMode = supportExchangeService.detail(query);
        
        // 从结果中获取第一条数据并转换为BdAutoAuditRuleListDTO对象返回
        return BeanUtil.toBean(IterUtil.getFirst(resultMode.getModel()), BdAutoAuditRuleListDTO.class);
    }

    /**
     * 查询详情
     *
     * @param query
     * @return
     */
    public ResultMode<List<BdAutoAuditRuleListDTO>> detail(BdAutoAuditRuleListQuery query) {
        return supportExchangeService.detail(query);
    }

}
