package com.wanlianyida.platform.application.service.partner;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.basemdm.api.inter.MdmUserInfoInter;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserInfoQuery;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.partner.api.inter.MemberInter;
import com.wanlianyida.partner.api.inter.UmBusinessLicenseAuditInter;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import com.wanlianyida.partner.api.model.command.UmBusinessLicenseAuditUpdateCommand;
import com.wanlianyida.partner.api.model.dto.*;
import com.wanlianyida.partner.api.model.query.MemberQuery;
import com.wanlianyida.partner.api.model.query.UmBusinessLicenseAuditQuery;
import com.wanlianyida.platform.infrastructure.exchange.MsgExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月31日 10:40
 */
@Slf4j
@Service
public class UmBusinessLicenseAuditAppService {

    @Resource
    private UmBusinessLicenseAuditInter businessLicenseAuditInter;
    @Resource
    private MsgExchangeService msgExchangeService;
    @Resource
    private UmCompanyInter companyInter;
    @Resource
    private MdmUserInfoInter userInfoInter;

    public ResultMode<List<UmBusinessLicenseAuditListDTO>> pageCondition(PagingInfo<UmBusinessLicenseAuditQuery> pageQuery) {
        return businessLicenseAuditInter.pageCondition(pageQuery);
    }

    public ResultMode<UmBusinessLicenseAuditDetailDTO> queryDetail(IdQuery query) {
        return businessLicenseAuditInter.queryDetail(query);
    }

    public ResultMode<?> audit(UmBusinessLicenseAuditUpdateCommand command) {
        /*IdQuery query = new IdQuery();
        query.setId(command.getId());
        ResultMode<UmBusinessLicenseAuditDetailDTO> beforeResultMode = businessLicenseAuditInter.queryDetail(query);
        if (!beforeResultMode.isSucceed() || beforeResultMode.getModel() == null) {
            return ResultMode.fail("审核记录不存在");
        }
        UmBusinessLicenseAuditDetailDTO licenseAuditDetailDTO = beforeResultMode.getModel();
        ResultMode<UmCompanyDTO> companyResultMode = companyInter.queryBySocialCreditCode(beforeResultMode.getModel().getSocialCreditCode());
        boolean isCreate = companyResultMode.isSucceed() && companyResultMode.getModel() == null;*/
        ResultMode<?> result = businessLicenseAuditInter.audit(command);
        /*if (result.isSucceed() && isCreate) {
            ResultMode<UmBusinessLicenseAuditDetailDTO> resultMode = businessLicenseAuditInter.queryDetail(query);
            if (resultMode.isSucceed() && resultMode.getModel() != null) {
                String auditResult = command.getAuditStatus() == 20 ? "审核通过" : "审核驳回";
                Set<String> receiveList = new HashSet<>();
                MdmUserInfoQuery userQuery = new MdmUserInfoQuery();
                userQuery.setLoginName(licenseAuditDetailDTO.getApplyLoginName());
                ResponseMessage<MdmUserInfoDTO> userInfoResultMode = userInfoInter.queryByLoginName(userQuery);
                if (userInfoResultMode.isSucceed() && userInfoResultMode.getModel() != null) {
                    receiveList.add(userInfoResultMode.getModel().getMobile());
                    HashMap<String, String> param = new HashMap<>();
                    param.put("result", auditResult);
                    msgExchangeService.sendMsg("SMS_486475175", "1", receiveList, param);
                }
            }
        }*/
        return result;
    }

    public ResultMode<AuditStatisticsDTO> statistics() {
        return businessLicenseAuditInter.statistics();
    }
}
