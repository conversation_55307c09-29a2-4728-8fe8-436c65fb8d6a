package com.wanlianyida.platform.application.service.bidding;

import com.wanlianyida.bidding.api.model.dto.BiddingSupplierListDTO;
import com.wanlianyida.bidding.api.model.dto.TenderPackagePurchaseListDTO;
import com.wanlianyida.bidding.api.model.query.*;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.bidding.SupplierResponseExchange;
import com.wanlianyida.platform.interfaces.model.dto.SupplierDetailsDTO;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/22 13:12
 */
@Service
public class SupplierResponseAppService {


    @Resource
    SupplierResponseExchange supplierResponseExchange;

    public ResultMode querySupplierInvitePage(PagingInfo<InviteQuery> pagingInfo) {
        return supplierResponseExchange.querySupplierInvitePage(pagingInfo);
    }

    public ResultMode querySupplierRegisterPage(PagingInfo<TenderConfirmListQuery> pagingInfo) {

        return supplierResponseExchange.querySupplierRegisterPage(pagingInfo);
    }

    public ResultMode<SupplierDetailsDTO> querySupplierDetails(Long id) {

        return supplierResponseExchange.querySupplierDetails(id);
    }

    public ResultMode<List<TenderPackagePurchaseListDTO>> queryBidpaymentPage(PagingInfo<TenderPackagePurchasePageQuery> pagingInfo) {

        return supplierResponseExchange.queryBidpaymentPage(pagingInfo);
    }

    public ResultMode<List<BiddingSupplierListDTO>> queryBidInfoPage(PagingInfo<BiddingListQuery> pagingInfo) {

        return supplierResponseExchange.queryBidInfoPage(pagingInfo);
    }

    public ResultMode<List<String>> queryBidpaymentUrlList(TenderPackagePurchasePageQuery query) {

        return supplierResponseExchange.queryBidpaymentUrlList(query);
    }
}
