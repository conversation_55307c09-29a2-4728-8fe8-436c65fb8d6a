package com.wanlianyida.platform.application.service.settlement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.enums.PlatfromCodeEnum;
import com.wanlianyida.order.api.inter.OrderQueryInter;
import com.wanlianyida.order.api.inter.SettlementOrderInter;
import com.wanlianyida.order.api.model.dto.SettlementOrderDTO;
import com.wanlianyida.order.api.model.dto.SettlementOrderShipProductDTO;
import com.wanlianyida.order.api.model.query.SettlementOrderQuery;
import com.wanlianyida.order.api.model.query.SettlementOrderShipProductQuery;
import com.wanlianyida.partner.api.inter.BdPricingTypeConfigInter;
import com.wanlianyida.partner.api.inter.UmShopInter;
import com.wanlianyida.partner.api.model.dto.BdPricingTypeConfigDTO;
import com.wanlianyida.partner.api.model.dto.UmShopCompanyDTO;
import com.wanlianyida.partner.api.model.query.BdPricingTypeConfigQuery;
import com.wanlianyida.platform.application.assembler.settlement.ExportAssembler;
import com.wanlianyida.platform.interfaces.model.query.ExportSettlementQuery;
import com.wanlianyida.sett.api.inter.SettlementQueryInter;
import com.wanlianyida.sett.api.model.dto.ExportSettlementListDTO;
import com.wanlianyida.sett.api.model.dto.SettlementApplyDetailDTO;
import com.wanlianyida.sett.api.model.dto.SettlementDetailListDTO;
import com.wanlianyida.sett.api.model.dto.SettlementListDTO;
import com.wanlianyida.sett.api.model.query.ExportSettlementListQuery;
import com.wanlianyida.sett.api.model.query.SettlementApplyDetailQuery;
import com.wanlianyida.sett.api.model.query.SettlementPageListQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 结算单
 */
@Slf4j
@Service
public class SettSettlementAppService {

    @Resource
    private SettlementQueryInter settlementQueryInter;

    @Resource
    private SettlementOrderInter settlementOrderInter;

    @Resource
    private BdPricingTypeConfigInter pricingTypeConfigInter;

    @Resource
    private UmShopInter umShopInter;

    @Resource
    private OrderQueryInter orderQueryInter;

    // 接口请求来源 [10-用户端,20-平台端]
    private static final Integer source = 20;

    /**
     * 查询结算申请单详情信息
     */
    public ResultMode<SettlementApplyDetailDTO> querySettlementDetails(SettlementApplyDetailQuery param) {
        ResultMode<SettlementApplyDetailDTO> resultMode = settlementQueryInter.querySettlementDetails(param);
        SettlementOrderQuery filter = new SettlementOrderQuery();
        if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && ObjUtil.isNotNull(resultMode.getModel())) {
            filter.setOrderNo(resultMode.getModel().getOrderNo());
            // 获取计价单位和数量单位
            getUnit(resultMode.getModel().getSettlementDetail().getDetailList(), resultMode.getModel().getOrderNo());
        }
        ResultMode<List<SettlementOrderDTO>> orderResult = settlementOrderInter.settlementOrderDetailList(filter);
        List<SettlementOrderDTO> list = orderResult.getModel();
        if (CollectionUtil.isNotEmpty(list)) {
            resultMode.getModel().setPickupList(BeanUtil.copyToList(list, com.wanlianyida.sett.api.model.dto.SettlementOrderDTO.class));
        }
        return resultMode;
    }

    /**
     * 查询结算申请单列表
     */
    public ResultMode<List<SettlementListDTO>> pageList(PagingInfo<SettlementPageListQuery> pagingInfo) {
        return settlementQueryInter.pageList(pagingInfo);
    }

    private void getUnit(List<SettlementDetailListDTO> list, String orderNo) {
        List<String> shipNoList = list.stream().map(SettlementDetailListDTO::getPickupNo).distinct().collect(Collectors.toList());
        SettlementOrderShipProductQuery queryInfo = new SettlementOrderShipProductQuery();
        queryInfo.setOrderNo(orderNo);
        queryInfo.setShipNoList(shipNoList);
        ResultMode<List<SettlementOrderShipProductDTO>> shipResultMode = settlementOrderInter.queryOrderSendOrPickUpProductList(queryInfo);
        List<SettlementOrderShipProductDTO> shipList = shipResultMode.getModel();
        Map<String, List<SettlementOrderShipProductDTO>> shipMap = shipList.stream().collect(Collectors.groupingBy(SettlementOrderShipProductDTO::getShipNo));
        for (SettlementDetailListDTO detail : list) {
            List<SettlementOrderShipProductDTO> ships = shipMap.get(detail.getPickupNo());
            if (CollectionUtil.isNotEmpty(ships)) {
                detail.setPriceUnit(ships.get(0).getPriceUnit());
                detail.setPurchaseQuantityUnit(ships.get(0).getPurchaseQuantityUnit());
            }
        }
    }

    /**
     * 查询结算申请单导出列表
     */
    public ResultMode<List<ExportSettlementListDTO>> exportPageList(PagingInfo<ExportSettlementQuery> pagingInfo) {
        PagingInfo<ExportSettlementListQuery> info = new PagingInfo<>();
        info.setFilterModel(BeanUtil.toBean(pagingInfo.getFilterModel(), ExportSettlementListQuery.class));
        info.getFilterModel().setSource(source);
        ResultMode<List<ExportSettlementListDTO>> resultMode = settlementQueryInter.getExportSettlementDetails(info);
        List<ExportSettlementListDTO> model = resultMode.getModel();
        if (ObjectUtils.isNotEmpty(model)) {
            resultMode.setTotal(model.size());
            // 获取计价单位和数量单位
            getExportUnit(resultMode.getModel(), pagingInfo.getFilterModel().getDetailField());
            // 查询店铺
            List<String> companyIdList = model.stream().map(ExportSettlementListDTO::getSellerCompanyId).distinct().collect(Collectors.toList());
            ResultMode<List<UmShopCompanyDTO>> shopResult = umShopInter.queryShopCompanyIdList(companyIdList);
            Map<String, String> shopMap = new HashMap<>();
            if (shopResult.isSucceed()) {
                shopMap = shopResult.getModel().stream().collect(Collectors.toMap(UmShopCompanyDTO::getCompanyId, UmShopCompanyDTO::getShopName));
            }
            for (ExportSettlementListDTO sett : resultMode.getModel()) {
                sett.setShopName(StrUtil.isNotBlank(shopMap.get(sett.getSellerCompanyId())) ? shopMap.get(sett.getSellerCompanyId()) : "");
            }
        }
        return resultMode;
    }

    // 获取计价单位和数量单位
    private void getExportUnit(List<ExportSettlementListDTO> list, Integer detailField) {
        if (detailField == 0) {
            for (ExportSettlementListDTO detail : list) {
                // 设置千分位和两位小数
                DecimalFormat decimalFormat = new DecimalFormat("0.00");
                detail.setSettTotalAmount(decimalFormat.format(new BigDecimal(detail.getSettTotalAmount())));
                detail.setPayTotalAmount(decimalFormat.format(new BigDecimal(detail.getPayTotalAmount())));
                detail.setPrepaidTotalAmount(decimalFormat.format(new BigDecimal(detail.getPrepaidTotalAmount())));
            }
        } else {
            // 查询计量单位
            BdPricingTypeConfigQuery queryMeasure = new BdPricingTypeConfigQuery();
            queryMeasure.setPlatformCode(PlatfromCodeEnum.CTP.getCode());
            ResultMode<List<BdPricingTypeConfigDTO>> measureUnitList = pricingTypeConfigInter.query(queryMeasure);
            Map<Integer, List<BdPricingTypeConfigDTO>> measureUnitMap = measureUnitList.getModel().stream().collect(Collectors.groupingBy(BdPricingTypeConfigDTO::getPricingType));
            // 查询商品
            List<String> shipNoList = list.stream().map(ExportSettlementListDTO::getPickupNo).distinct().collect(Collectors.toList());
            SettlementOrderShipProductQuery queryInfo = new SettlementOrderShipProductQuery();
            queryInfo.setShipNoList(shipNoList);
            for (ExportSettlementListDTO detail : list) {
                queryInfo.setOrderNo(detail.getOrderNo());
                ResultMode<List<SettlementOrderShipProductDTO>> shipResultMode = settlementOrderInter.queryOrderSendOrPickUpProductList(queryInfo);
                if (shipResultMode.isSucceed() && ObjectUtils.isNotEmpty(shipResultMode.getModel())) {
                    Map<String, List<SettlementOrderShipProductDTO>> shipMap = shipResultMode.getModel().stream().collect(Collectors.groupingBy(SettlementOrderShipProductDTO::getShipNo));
                    List<SettlementOrderShipProductDTO> ships = shipMap.get(detail.getPickupNo());
                    if (CollectionUtil.isNotEmpty(ships)) {
                        ExportAssembler.buildExportSettlementDTO(detail, measureUnitMap, IterUtil.getFirst(ships));
                    }
                }
            }
        }
    }
}
