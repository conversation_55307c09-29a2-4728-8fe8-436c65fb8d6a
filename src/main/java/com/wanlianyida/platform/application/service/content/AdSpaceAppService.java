package com.wanlianyida.platform.application.service.content;

import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.enums.PlatfromCodeEnum;
import com.wanlianyida.fssbasecontent.api.model.command.AdSpaceAddCommand;
import com.wanlianyida.fssbasecontent.api.model.command.AdSpaceDeleteCommand;
import com.wanlianyida.fssbasecontent.api.model.command.AdSpaceUpdateCommand;
import com.wanlianyida.fssbasecontent.api.model.dto.AdSpaceDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.SiteDTO;
import com.wanlianyida.fssbasecontent.api.model.query.AdSpaceListQuery;
import com.wanlianyida.platform.application.assembler.content.UserBaseInfossemble;
import com.wanlianyida.platform.infrastructure.exchange.BaseContentExchangeService;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class AdSpaceAppService {

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    /**
     * 新增广告位
     *
     * @param command
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> add(AdSpaceAddCommand command) {
        SiteDTO site = getSite(PlatfromCodeEnum.CTP.getCode());
        if (ObjectUtil.isNotEmpty(site)){
            command.setRelSiteId(site.getSiteId());
            command.setPlatformCode(site.getPlatformCode());
        }
        UserBaseInfossemble.setAddBaseInfo(command);
        return baseContentExchangeService.addSpaceInterAdd(command);
    }

    /**
     * 更新广告位
     *
     * @param command
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> update(AdSpaceUpdateCommand command) {
        UserBaseInfossemble.setUpdateBaseInfo(command);
        return baseContentExchangeService.addSpaceInterUpdate(command);
    }

    /**
     * 删除广告位(批量)
     *
     * @param command
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> batchDelete(AdSpaceDeleteCommand command) {
        UserBaseInfossemble.setUpdateBaseInfo(command);
        return baseContentExchangeService.addSpaceInterDelete(command);
    }

    /**
     * 查询列表
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link List }<{@link AdSpaceDTO }>>
     */
    public ResultMode<List<AdSpaceDTO>> queryList(AdSpaceListQuery query) {
        return baseContentExchangeService.addSpaceInterQueryList(query);
    }

    private SiteDTO getSite(String platformCode) {
        // 查询站点信息
        ResultMode<SiteDTO> siteDTOResultMode = baseContentExchangeService.chSiteInterQueryByPlatformCode(platformCode);
        SiteDTO model = siteDTOResultMode.getModel();
        return model;
    }

}
