package com.wanlianyida.platform.application.service.support;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.BdThemePlanInter;
import com.wanlianyida.support.api.model.command.BdThemePlanCreateCommand;
import com.wanlianyida.support.api.model.command.BdThemePlanDeleteCommand;
import com.wanlianyida.support.api.model.command.BdThemeUpdateCommand;
import com.wanlianyida.support.api.model.dto.BdThemeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class BdThemePlanCommandAppService {

    @Resource
    private BdThemePlanInter bdThemePlanInter;

    public ResultMode<BdThemeDTO> updateTheme(BdThemeUpdateCommand command) {
        return bdThemePlanInter.updateTheme(command);
    }

    public ResultMode<Void> createThemePlan(BdThemePlanCreateCommand command) {
        return bdThemePlanInter.createThemePlan(command);
    }

    public ResultMode<Void> themePlanCancel(BdThemePlanDeleteCommand command) {
        return bdThemePlanInter.themePlanCancel(command);
    }
}
