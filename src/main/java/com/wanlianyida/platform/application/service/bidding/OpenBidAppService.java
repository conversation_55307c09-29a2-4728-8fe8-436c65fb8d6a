package com.wanlianyida.platform.application.service.bidding;

import com.wanlianyida.bidding.api.model.dto.OpenSupplierListDTO;
import com.wanlianyida.bidding.api.model.dto.OpeningSingPriceDTO;
import com.wanlianyida.bidding.api.model.dto.SupplierConfDTO;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.AttachmentDTO;
import com.wanlianyida.bidding.api.model.query.*;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.bidding.OpenBidQueryExchange;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/22 13:20
 */
@Service
public class OpenBidAppService {

    @Resource
    private OpenBidQueryExchange openBidQueryExchange;

    public ResultMode<List<SupplierConfDTO>> pageQueryNobidSet(PagingInfo<SupplierConfQuery> query) {
        
        return openBidQueryExchange.pageQueryNobidSet(query);
    }

    public ResultMode<List<OpeningSingPriceDTO>> pageQuerySingPrice(PagingInfo<OpeningSingPriceQuery> query) {
        
        return openBidQueryExchange.pageQuerySingPrice(query);
    }

    public ResultMode<List<AttachmentDTO>> queryList(AttachmentQuery query) {
        
        return openBidQueryExchange.queryList(query);
    }

    public ResultMode<List<OpenSupplierListDTO>> pageQueryOpenBidRecord(PagingInfo<SupplierConfQuery> query) {
        
        return openBidQueryExchange.pageQueryOpenBidRecord(query);
    }

    public ResultMode<List<AttachmentDTO>> querySupplierSign(PagingInfo<ReviewAttachmentQuery> pageQuery) {
        
        return openBidQueryExchange.querySupplierSign(pageQuery);
    }

    public ResultMode<List<AttachmentDTO>> queryOpenbidEnd(PagingInfo<ReviewAttachmentQuery> pageQuery) {
        
        return openBidQueryExchange.queryOpenbidEnd(pageQuery);
    }

    public ResultMode<List<AttachmentDTO>> pageQueryAttachment(PagingInfo<AttachmentPageQuery> pageQuery) {
        
        return openBidQueryExchange.pageQueryAttachment(pageQuery);
    }

    public ResultMode<List<String>> querysupplierSignUrlList(ReviewAttachmentQuery query) {
        
        return openBidQueryExchange.querysupplierSignUrlList(query);
    }

    public ResultMode<List<String>> queryOpenbidEndUrlList(ReviewAttachmentQuery query) {
        
        return openBidQueryExchange.queryOpenbidEndUrlList(query);
    }
}
