package com.wanlianyida.platform.application.service.content;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.inter.SiteResourceInter;
import com.wanlianyida.fssbasecontent.api.model.command.SiteResourceAddCommand;
import com.wanlianyida.fssbasecontent.api.model.command.SiteResourceUpdateCommand;
import com.wanlianyida.fssbasecontent.api.model.dto.SiteResourceDTO;
import com.wanlianyida.platform.application.assembler.content.UserBaseInfossemble;
import com.wanlianyida.platform.infrastructure.exchange.BaseContentExchangeService;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class SiteResourceAppService {
    @Resource
    private SiteResourceInter siteResourceInter;


    @Resource
    private BaseContentExchangeService baseContentExchangeService;


    /**
     * 新增站点资源
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    public ResultMode<Boolean> add(SiteResourceAddCommand command) {
        UserBaseInfossemble.setAddBaseInfo(command);
        return baseContentExchangeService.siteResourceInterAdd(command);
    }

    /**
     * 更新站点资源
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    public ResultMode<Boolean> update(SiteResourceUpdateCommand command){
        UserBaseInfossemble.setUpdateBaseInfo(command);
        return baseContentExchangeService.siteResourceInterUpdate(command);
    }

    /**
     * 查询列表
     *
     * @return {@link ResultMode }<{@link List }<{@link SiteResourceDTO }>>
     */
    public ResultMode<List<SiteResourceDTO>> queryList(){
        return baseContentExchangeService.siteResourceInterQueryList();
    }
}
