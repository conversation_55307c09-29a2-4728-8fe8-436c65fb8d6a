package com.wanlianyida.platform.application.service.partner;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.UmAccreditAuditInter;
import com.wanlianyida.partner.api.model.command.UmAccreditAuditUpdateCommand;
import com.wanlianyida.partner.api.model.dto.AuditStatisticsDTO;
import com.wanlianyida.partner.api.model.dto.UmAccreditAuditDetailDTO;
import com.wanlianyida.partner.api.model.dto.UmAccreditAuditListDTO;
import com.wanlianyida.partner.api.model.query.UmAccreditAuditQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月31日 10:29
 */
@Slf4j
@Service
public class UmAccreditAuditAppService {

    @Resource
    private UmAccreditAuditInter accreditAuditInter;

    public ResultMode<List<UmAccreditAuditListDTO>> pageCondition(PagingInfo<UmAccreditAuditQuery> pageQuery) {
        return accreditAuditInter.pageCondition(pageQuery);
    }

    public ResultMode<UmAccreditAuditDetailDTO> queryDetail(IdQuery query) {
        return accreditAuditInter.queryDetail(query);
    }

    public ResultMode<?> audit(UmAccreditAuditUpdateCommand command) {
        return accreditAuditInter.audit(command);
    }

    public ResultMode<AuditStatisticsDTO> statistics() {
        return accreditAuditInter.statistics();
    }
}
