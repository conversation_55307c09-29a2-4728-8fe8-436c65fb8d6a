package com.wanlianyida.platform.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcommon.enums.PlatfromCodeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssuserauth.api.enums.UserAuthEnums;
import com.wanlianyida.fssuserauth.api.model.command.UmPermissionfunctionsCommand;
import com.wanlianyida.fssuserauth.api.model.command.UserPermissionFunctionsPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.command.UserUmPermissionPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.query.UserPermissionFunctionsPlatformQuery;
import com.wanlianyida.fssuserauth.api.model.query.UserPermissionPlatformQuery;
import com.wanlianyida.platform.infrastructure.exchange.UserAuthExchangeService;
import com.wanlianyida.platform.interfaces.model.command.UserPermissionFunctionsCommand;
import com.wanlianyida.platform.interfaces.model.command.UserUmPermissionCommand;
import com.wanlianyida.platform.interfaces.model.dto.UserFunctionsDTO;
import com.wanlianyida.platform.interfaces.model.query.UserPermissionFunctionsQuery;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * @Author: Qin
 * @Date: 2024/11/23 12:57
 * @Description:
 **/
@Service
public class UserPermissionFunctionsAppService {

    @Resource
    private UserAuthExchangeService userAuthExchangeService;

    public ResultMode assignUserPermissions(UserPermissionFunctionsCommand command) {
        UserPermissionFunctionsPlatformCommand model = new UserPermissionFunctionsPlatformCommand();
        BeanUtil.copyProperties(command, model);
        List<UmPermissionfunctionsCommand> commandList = new ArrayList<>();
        if (CollUtil.isEmpty(model.getFuncIdList())) {
            return ResultMode.fail("功能不能为空");
        }
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        model.getFuncIdList().forEach(funcId -> {
            UmPermissionfunctionsCommand m = new UmPermissionfunctionsCommand();
            m.setPermissionId(model.getPermissionId());
            m.setFuncId(funcId);
            m.setStatus(UserAuthEnums.ValidEnum.YES.getCode());
            m.setCreateBy(tokenInfo.getUserBaseId());
            m.setModifyBy(tokenInfo.getUserBaseId());
            commandList.add(m);
        });
        return userAuthExchangeService.addUserPermissionFunctions(commandList);
    }

    public ResultMode<List<UserFunctionsDTO>> queryAssignedList(UserPermissionFunctionsQuery query) {
        UserPermissionFunctionsPlatformQuery model = new UserPermissionFunctionsPlatformQuery();
        BeanUtil.copyProperties(query, model);
        return userAuthExchangeService.queryUserFunctionsList(model);
    }

    public ResultMode<List<UserFunctionsDTO>> queryNotBindMenusList(UserPermissionFunctionsQuery query) {
        UserPermissionFunctionsPlatformQuery model = new UserPermissionFunctionsPlatformQuery();
        BeanUtil.copyProperties(query, model);
        model.setSysType(PlatfromCodeEnum.CTP_USER_SYS.getCode());
        return userAuthExchangeService.queryNotBindMenusList(model);
    }

    public ResultMode<List<UserFunctionsDTO>> queryUmUserFunctionsList(UserPermissionFunctionsQuery query) {
        UserPermissionPlatformQuery model = new UserPermissionPlatformQuery();
        BeanUtil.copyProperties(query, model);
        model.setSysType(PlatfromCodeEnum.CTP_USER_SYS.getCode());
        return userAuthExchangeService.queryListBySysType(model);
    }

    public ResultMode<String> saveUserFunctions(UserUmPermissionCommand command) {
        UserUmPermissionPlatformCommand model = new UserUmPermissionPlatformCommand();
        BeanUtil.copyProperties(command, model);
        return userAuthExchangeService.saveUserFunctions(model);
    }

    public ResultMode<String> deleteUmUserFunctions(UserUmPermissionCommand command) {
        UserUmPermissionPlatformCommand model = new UserUmPermissionPlatformCommand();
        BeanUtil.copyProperties(command, model);
        return userAuthExchangeService.deleteUmUserFunctions(model);
    }
}
