package com.wanlianyida.platform.application.service.product;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.enums.PlatformCategoryTreeEnum;
import com.wanlianyida.platform.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.product.api.inter.ProductCategoryManagerInter;
import com.wanlianyida.product.api.inter.ProductCategoryUserInter;
import com.wanlianyida.product.api.model.command.*;
import com.wanlianyida.product.api.model.dto.*;
import com.wanlianyida.product.api.model.query.CategoryTreeQuery;
import com.wanlianyida.product.api.model.query.ProductCategoryDirectSubQuery;
import com.wanlianyida.product.api.model.query.ProductCategoryPageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductCategoryManagerAppService {

    @Resource
    private ProductCategoryManagerInter managerInter;

    @Resource
    private ProductCategoryUserInter categoryUserInter;

    @Resource
    private UploadExchangeService uploadExchangeService;

    /**
     * 新增品类
     * @param command 参数
     */
    public ResultMode<AddCategoryDTO> add(ProductCategoryAddCommand command) {
        return managerInter.add(command);
    }

    /**
     * 编辑品类
     * @param command 参数
     */
    public ResultMode<?> edit(ProductCategoryEditCommand command) {
        return managerInter.edit(command);
    }

    /**
     * 删除品类
     * @param command 参数
     */
    public ResultMode<?> delete(ProductCategoryDeleteCommand command) {
        return managerInter.delete(command);
    }

    /**
     * 更新品类状态
     * @param command 参数
     */
    public ResultMode<?> updateEnableStatus(ProductCategoryUpdateEnableStatusCommand command) {
        return managerInter.updateEnableStatus(command);
    }

    /**
     * 商品品类分页查询
     * @param query 分页参数
     */
    public ResultMode<List<ProductCategoryPageDTO>> pageQuery(PagingInfo<ProductCategoryPageQuery> query) {
        return managerInter.pageQueryProductCategory(query);
    }

    /**
     * 获取分类下所有未删除的直接子级
     * @param query 参数
     */
    public ResultMode<List<ProductCategoryDirectSubDTO>> queryDirectSubLevel(ProductCategoryDirectSubQuery query) {
        return managerInter.queryDirectSubLevel(query);
    }

    /**
     * 根据主键列表查询
     * @param idList 参数
     */
    public ResultMode<List<ProductCategoryDTO>> queryByIds(List<Long> idList) {
        return managerInter.queryByIds(idList);
    }

    /**
     * 根据主键查询品类图片
     * @param query 参数
     */
    public ResultMode<ProductCategoryPicDTO> queryCategoryPicById(IdQuery query) {
        return managerInter.queryCategoryPicById(query);
    }

    public ResultMode<ProductCategoryDTO> queryCategoryById(IdQuery query){
        Long id = query.getId();
        if(id != null){
            List<Long> idList = new LinkedList<>();
            idList.add(id);
            ResultMode<List<ProductCategoryDTO>> listResultMode = managerInter.queryByIds(idList);
            if(listResultMode != null && listResultMode.isSucceed() && listResultMode.getModel() != null){
                if(!CollectionUtil.isEmpty(listResultMode.getModel())){
                    ProductCategoryDTO returnDto = listResultMode.getModel().get(0);
                    this.convertUrl(returnDto);
                    return ResultMode.success(returnDto);
                }
            }
        }
        return ResultMode.fail("未获取到品类");
    }

    private void convertUrl(ProductCategoryDTO returnDto){
        log.info("queryCategoryById===》转换url");
        if(!StringUtils.isEmpty(returnDto.getCategoryPic())){
            log.info("queryCategoryById===》转换url，原url={}",returnDto.getCategoryPic());
            String url = uploadExchangeService.convertUrl(returnDto.getCategoryPic());
            log.info("queryCategoryById===》转换url，转换后url={}",url);
            returnDto.setCategoryPic(url);
        }
    }

    public ResultMode<List<ProductCategoryTreeDTO>> queryCategoryTree(){
        CategoryTreeQuery query = new CategoryTreeQuery();
        query.setQueryType(PlatformCategoryTreeEnum.PLATFORM_CATEGORY_MANAGE.getQueryType());
        return categoryUserInter.queryCategoryTree(query);
    }


    public ResultMode<List<ProductCategoryTreeDTO>> autoAuditCategoryTree(){
        CategoryTreeQuery query = new CategoryTreeQuery();
        query.setQueryType("60");
        return categoryUserInter.queryCategoryTree(query);
    }

}
