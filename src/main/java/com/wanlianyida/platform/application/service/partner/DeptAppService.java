package com.wanlianyida.platform.application.service.partner;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.partner.api.inter.DeptManageInter;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import com.wanlianyida.partner.api.model.command.DeptAddCommand;
import com.wanlianyida.partner.api.model.command.DeptDeleteCommand;
import com.wanlianyida.partner.api.model.command.DeptUpdateCommand;
import com.wanlianyida.partner.api.model.dto.OrgAndDeptTreeDTO;
import com.wanlianyida.partner.api.model.dto.UmCompanyDTO;
import com.wanlianyida.partner.api.model.query.DeptTreeQuery;
import com.wanlianyida.platform.infrastructure.constant.Constant;
import com.wanlianyida.platform.infrastructure.exception.CtpOrchPlatformException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DeptAppService {
    @Resource
    private DeptManageInter deptManageInter;
    @Resource
    private UmCompanyInter umCompanyInter;

    /**
     * 部门新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> add(DeptAddCommand command) {
        // 根据企业信用编码查询企业
        UmCompanyDTO companyInfo = getCompanyInfo();
        command.setCompanyId(String.valueOf(companyInfo.getId()));

        return deptManageInter.add(command);
    }

    /**
     * 部门更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> update(DeptUpdateCommand command) {
        // 根据企业信用编码查询企业
        UmCompanyDTO companyInfo = getCompanyInfo();
        command.setCompanyId(String.valueOf(companyInfo.getId()));
        return deptManageInter.update(command);
    }

    /**
     * 部门删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> delete(DeptDeleteCommand command) {
        // 根据企业信用编码查询企业
        UmCompanyDTO companyInfo = getCompanyInfo();
        command.setCompanyId(String.valueOf(companyInfo.getId()));
        return deptManageInter.delete(command);
    }

    /**
     * 构建部门树
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link OrgAndDeptTreeDTO }>
     */
    public ResultMode<OrgAndDeptTreeDTO> buildDeptTree(DeptTreeQuery query) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            return ResultMode.fail("用户未登录");
        }

        String licenseNo = tokenInfo.getLicenseNo();
        if (StrUtil.isEmpty(licenseNo)) {
            // 运营端 只有一家企业，先写死。后期组织架构会重构
            query.setLicenseNo(Constant.OPERATOR_COMPANY_SOCIAL_CREDIT_CODE);
        }
        ResultMode<OrgAndDeptTreeDTO> orgAndDeptTreeDTOResultMode = deptManageInter.buildDeptTree(query);
        log.info("构建部门树结果：{}", orgAndDeptTreeDTOResultMode);
        return orgAndDeptTreeDTOResultMode;
    }

    /**
     * 获取企业信息
     * @return 企业信息表DTO
     */
    private UmCompanyDTO getCompanyInfo() {
        // 根据企业信用编码查询企业
        ResultMode<UmCompanyDTO> resultMode = umCompanyInter.queryBySocialCreditCode(Constant.OPERATOR_COMPANY_SOCIAL_CREDIT_CODE);
        if (!resultMode.isSucceed()) {
            throw new CtpOrchPlatformException("查询企业信息失败");
        }
        UmCompanyDTO umCompanyDTO = resultMode.getModel();
        return umCompanyDTO;
    }

}
