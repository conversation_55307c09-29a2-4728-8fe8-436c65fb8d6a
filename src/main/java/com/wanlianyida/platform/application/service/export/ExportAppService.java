package com.wanlianyida.platform.application.service.export;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.file.api.IExportService;
import com.wanlianyida.file.command.ExportCommand;
import com.wanlianyida.file.dto.ExportTaskDTO;
import com.wanlianyida.file.query.ExportTaskQuery;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.platform.infrastructure.exchange.ExportExchangeService;
import com.wanlianyida.platform.interfaces.model.command.APIFileCompressCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class ExportAppService {

    @Resource
    IExportService exportService;

    @Resource
    ExportExchangeService exportExchangeService;

    public ResponseMessage exportData(@RequestBody ExportCommand command) {
        if (command.getFilterModel() == null) {
            command.setFilterModel(new HashMap<>());
        }

        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (StrUtil.isNotBlank(tokenInfo.getCompanyId())) {
            command.getFilterModel().put("tokenCompanyId", tokenInfo.getCompanyId());
        }
        if (StrUtil.isNotBlank(tokenInfo.getUserBaseId())) {
            command.getFilterModel().put("tokenUserBaseId", tokenInfo.getUserBaseId());
        }
        if (StrUtil.isNotBlank(tokenInfo.getLicenseNo())) {
            command.getFilterModel().put("tokenSocialCreditCode", tokenInfo.getLicenseNo());
        }
        //平台类型[10-物流，20-商贸，60-AI大模型]
        command.getFilterModel().put("productCode", 20);
        log.info("tokenCompanyId:{}",command.getFilterModel().get("tokenCompanyId"));
        return exportService.exportData(command);
    }

    public ResponseMessage<List<ExportTaskDTO>> queryExportTask(PagingInfo<ExportTaskQuery> pagingInfo) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        ExportTaskQuery query=new ExportTaskQuery();
        query.setUserBaseId(tokenInfo.getUserBaseId());
        pagingInfo.setFilterModel(query);
        return exportService.queryExportTask(pagingInfo);
    }

    public ResponseMessage fileCompress(APIFileCompressCommand command) {
        return exportExchangeService.fileCompress(command);
    }
}
