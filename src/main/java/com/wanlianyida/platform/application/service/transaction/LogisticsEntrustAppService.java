package com.wanlianyida.platform.application.service.transaction;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssuserauth.api.model.dto.UserAccountInfoDTO;
import com.wanlianyida.platform.infrastructure.exchange.UserAuthExchangeService;
import com.wanlianyida.transaction.api.inter.LogisticsEntrustInter;
import com.wanlianyida.transaction.api.model.command.LogisticsEntrustFollowedUpCommand;
import com.wanlianyida.transaction.api.model.dto.LogisticsEntrustDetailDTO;
import com.wanlianyida.transaction.api.model.dto.LogisticsEntrustListDTO;
import com.wanlianyida.transaction.api.model.query.LogisticsEntrustQuery;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 物流委托 App Service
 *
 * <AUTHOR>
 */
@Service
public class LogisticsEntrustAppService {

    @Resource
    private LogisticsEntrustInter logisticsEntrustInter;
    @Resource
    private UserAuthExchangeService userAuthExchangeService;

    /**
     * 查询物流委托
     */
    public ResultMode<List<LogisticsEntrustListDTO>> queryPage(PagingInfo<LogisticsEntrustQuery> pageQuery) {
        return logisticsEntrustInter.queryPage(pageQuery);
    }

    /**
     * 查看物流委托详情
     */
    public ResultMode<LogisticsEntrustDetailDTO> detail(IdQuery query) {
        return logisticsEntrustInter.detail(query);
    }

    /**
     * 跟进物流委托
     */
    public ResultMode followedUp(LogisticsEntrustFollowedUpCommand command) {
        ResultMode<UserAccountInfoDTO> userInfo = userAuthExchangeService.getUserInfo();
        UserAccountInfoDTO userAccountInfoDTO = userInfo.getModel();
        if (userAccountInfoDTO != null) {
            command.setFollowPhone(userAccountInfoDTO.getTelephone());
        }
        return logisticsEntrustInter.followedUp(command);
    }
}
