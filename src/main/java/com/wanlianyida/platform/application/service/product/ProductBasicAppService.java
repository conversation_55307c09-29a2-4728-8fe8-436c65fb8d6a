package com.wanlianyida.platform.application.service.product;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.assembler.product.ImportAssembler;
import com.wanlianyida.platform.application.handler.CommonImportVerifyHandler;
import com.wanlianyida.platform.infrastructure.constant.ImportConstant;
import com.wanlianyida.platform.infrastructure.enums.*;
import com.wanlianyida.product.api.inter.ProductBasicInter;
import com.wanlianyida.product.api.model.command.*;
import com.wanlianyida.product.api.model.dto.ImportResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductBasicAppService {

    @Resource
    private ProductBasicInter basicInter;

    @Resource
    private CommonImportVerifyHandler commonImportVerifyHandler;

    /**
     * 导入品类
     */
    public ResultMode<ImportResultDto> importCategory(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = getExcelDataList(file);

        if(CollectionUtil.isEmpty(dataList)){
            log.info("导入品类===解析出excel中具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品类===解析出excel中具有0条原始数据"));
        } else {
            log.info("导入品类===解析出excel中具有{}条原始数据",dataList.size());
        }

        // 转换excel数据结构
        List<Map<String, Object>> newMapList = generateNewMap(dataList,ImportEnum.CATEGORY);

        if(CollectionUtil.isEmpty(newMapList)){
            log.info("导入品类===转换excel数据结构后===具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品类===转换excel数据结构后===具有0条原始数据"));
        } else {
            log.info("导入品类===转换excel数据结构后===具有{}条原始数据",newMapList.size());
        }

        // 转换核心服务需要的数据结构
        List<CategoryImportCommand> categoryImportCommands = BeanUtil.copyToList(newMapList, CategoryImportCommand.class);

        return basicInter.importCategory(categoryImportCommands);
    }

    /**
     * 导入品牌
     */
    public ResultMode<ImportResultDto> importBrand(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = getExcelDataList(file);

        if(CollectionUtil.isEmpty(dataList)){
            log.info("导入品牌===解析出excel中具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品牌===解析出excel中具有0条原始数据"));
        } else {
            log.info("导入品牌===解析出excel中具有{}条原始数据",dataList.size());
        }

        // 转换excel数据结构
        List<Map<String, Object>> newMapList = generateNewMap(dataList,ImportEnum.BRAND);

        if(CollectionUtil.isEmpty(newMapList)){
            log.info("导入品牌===转换excel数据结构后===具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品牌===转换excel数据结构后===具有0条原始数据"));
        } else {
            log.info("导入品牌===转换excel数据结构后===具有{}条原始数据",newMapList.size());
        }

        // 转换核心服务需要的数据结构
        List<BrandImportCommand> brandImportCommands = BeanUtil.copyToList(newMapList, BrandImportCommand.class);

        return basicInter.importBrand(brandImportCommands);
    }

    /**
     * 导入规格
     */
    public ResultMode<ImportResultDto> importAttribute(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = getExcelDataList(file);

        if(CollectionUtil.isEmpty(dataList)){
            log.info("导入规格===解析出excel中具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入规格===解析出excel中具有0条原始数据"));
        } else {
            log.info("导入规格===解析出excel中具有{}条原始数据",dataList.size());
        }

        // 转换excel数据结构
        List<Map<String, Object>> newMapList = generateNewMap(dataList,ImportEnum.ATTRIBUTE);

        if(CollectionUtil.isEmpty(newMapList)){
            log.info("导入规格===转换excel数据结构后===具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入规格===转换excel数据结构后===具有0条原始数据"));
        } else {
            log.info("导入规格===转换excel数据结构后===具有{}条原始数据",newMapList.size());
        }

        // 转换核心服务需要的数据结构
        List<AttributeImportCommand> brandImportCommands = BeanUtil.copyToList(newMapList, AttributeImportCommand.class);

        return basicInter.importAttribute(brandImportCommands);
    }


    /**
     * 导入品类品牌关联
     */
    public ResultMode<ImportResultDto> importCategoryBrand(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = getExcelDataList(file);

        if(CollectionUtil.isEmpty(dataList)){
            log.info("导入品类品牌关联===解析出excel中具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品类品牌关联===解析出excel中具有0条原始数据"));
        } else {
            log.info("导入品类品牌关联===解析出excel中具有{}条原始数据",dataList.size());
        }

        // 转换excel数据结构
        List<Map<String, Object>> newMapList = generateNewMap(dataList,ImportEnum.CATEGORY_BRAND);

        if(CollectionUtil.isEmpty(newMapList)){
            log.info("导入品类品牌关联===转换excel数据结构后===具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品类品牌关联===转换excel数据结构后===具有0条原始数据"));
        } else {
            log.info("导入品类品牌关联===转换excel数据结构后===具有{}条原始数据",newMapList.size());
        }

        // 转换核心服务需要的数据结构
        List<CategoryBrandImportCommand> brandImportCommands = BeanUtil.copyToList(newMapList, CategoryBrandImportCommand.class);

        return basicInter.importCategoryBrand(brandImportCommands);
    }

    /**
     * 导入品类规格
     */
    public ResultMode<ImportResultDto> importCategoryAttribute(MultipartFile file) throws Exception {
        List<Map<String, Object>> dataList = getExcelDataList(file);

        if(CollectionUtil.isEmpty(dataList)){
            log.info("导入品类规格===解析出excel中具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品类规格===解析出excel中具有0条原始数据"));
        } else {
            log.info("导入品类规格===解析出excel中具有{}条原始数据",dataList.size());
        }

        // 转换excel数据结构
        List<Map<String, Object>> newMapList = generateNewMap(dataList,ImportEnum.CATEGORY_ATTRIBUTE);

        if(CollectionUtil.isEmpty(newMapList)){
            log.info("导入品类规格===转换excel数据结构后===具有0条原始数据");
            return ResultMode.success(ImportAssembler.buildImportResultDto(0,0,"导入品类规格===转换excel数据结构后===具有0条原始数据"));
        } else {
            log.info("导入品类规格===转换excel数据结构后===具有{}条原始数据",newMapList.size());
        }

        // 转换核心服务需要的数据结构
        List<CategoryAttributeImportCommand> attributeImportCommands = BeanUtil.copyToList(newMapList, CategoryAttributeImportCommand.class);

        return basicInter.importCategoryAttribute(attributeImportCommands);
    }

    /**
     * 新map的key：远程接口请求实体的字段名称
     * 新map的value：excel中的用户输入的值
     * @param dataList 通过框架获取的原始数据，原始map key：excel中的列名，value：excel中的列值
     * @param importEnum 导入枚举
     */
    private List<Map<String, Object>> generateNewMap(List<Map<String, Object>> dataList, ImportEnum importEnum){
        List<Map<String, Object>> newDataList = new LinkedList<>();
        for(Map<String,Object> map : dataList){
            // 创建新map，key：远程接口请求实体的字段名称，value：excel中的用户输入的值
            Map<String,Object> newMap = new HashMap<>(map.size());
            // 处理原始map每一个元素（每一列）
            for(String columnName : map.keySet()){
                if(ImportConstant.EXCEL_ROW_NUM.equals(columnName)){
                    newMap.put(columnName,map.get(columnName));
                    continue;
                }
                String propertyName = getPropertyName(importEnum, columnName);
                if(StringUtils.isNotBlank(propertyName)){
                    Object valueBeforeDeal = map.get(columnName);
                    // excel值处理
                    String valueAfterDeal = getValueAfterDeal(valueBeforeDeal,importEnum);
                    newMap.put(propertyName,valueAfterDeal);
                } else {
                    log.error(importEnum.getDesc()+"generateNewMap失败"+"columnName={}"+"未获取到对应的propertyName",columnName);
                    return new LinkedList<>();
                }
            }
            newDataList.add(newMap);
        }
        return newDataList;
    }

    /**
     * excel值处理
     */
    private String getValueAfterDeal(Object valueBeforeDeal,ImportEnum importEnum){
        String valueAfterDeal = "";
        if(valueBeforeDeal != null){
            // 去掉value的前后两端空格
            valueAfterDeal = String.valueOf(valueBeforeDeal).trim();
            // 中文逗号处理
            if(valueAfterDeal.contains("，")){
                valueAfterDeal = valueAfterDeal.replace("，",",");
            }
        }
        if(ImportEnum.CATEGORY_ATTRIBUTE.equals(importEnum)){
            // 是否必填(0否，1是)
            if(InputFlagEnum.getCode(valueAfterDeal) != null){
                valueAfterDeal = InputFlagEnum.getCode(valueAfterDeal) + "";
            }
            // 输入方式(10选项，20文本)
            if(InputTypeEnum.getInputTypeCode(valueAfterDeal) != null){
                valueAfterDeal = InputTypeEnum.getInputTypeCode(valueAfterDeal);
            }
            // 是否是销售规格(0否，1是)
            if(TypeFlagEnum.getCode(valueAfterDeal) != null){
                valueAfterDeal = TypeFlagEnum.getCode(valueAfterDeal) + "";
            }
        }
        return valueAfterDeal;
    }

    /**
     * 获取属性名
     */
    private String getPropertyName(ImportEnum importEnum,String columnName){
        String propertyName = "";
        if(ImportEnum.CATEGORY.equals(importEnum)){
            propertyName = CategoryImportEnum.getPropertyName(columnName);
        }
        if(ImportEnum.BRAND.equals(importEnum)){
            propertyName = BrandImportEnum.getPropertyName(columnName);
        }
        if(ImportEnum.ATTRIBUTE.equals(importEnum)){
            propertyName = AttributeImportEnum.getPropertyName(columnName);
        }
        if(ImportEnum.CATEGORY_BRAND.equals(importEnum)){
            propertyName = CategoryBrandImportEnum.getPropertyName(columnName);
        }
        if(ImportEnum.CATEGORY_ATTRIBUTE.equals(importEnum)){
            propertyName = CategoryAttributeImportEnum.getPropertyName(columnName);
        }
        return propertyName;
    }

    /**
     * 获取excel数据
     */
    private List<Map<String, Object>>  getExcelDataList(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
        return ExcelImportUtil.importExcel(
                inputStream, Map.class, ImportAssembler.buildProductImportParams(commonImportVerifyHandler));
    }

}
