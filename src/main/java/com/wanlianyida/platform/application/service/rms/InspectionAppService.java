package com.wanlianyida.platform.application.service.rms;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.rms.InspectionExchange;
import com.wanlianyida.rms.model.dto.RcInspResultDTO;
import com.wanlianyida.rms.model.query.RcInspResultQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class InspectionAppService {

    @Autowired
    private InspectionExchange inspectionExchange;


    public ResultMode<List<RcInspResultDTO>> pageQueryInspection(PagingInfo<RcInspResultQuery> pageQuery) {
        return inspectionExchange.pageQueryInspection(pageQuery);
    }

}
