package com.wanlianyida.platform.application.service.partner;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.UmShopInter;
import com.wanlianyida.partner.api.model.dto.ShopContactsDTO;
import com.wanlianyida.partner.api.model.dto.ShopDTO;
import com.wanlianyida.partner.api.model.query.ShopPageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class ShopAppService {
    @Resource
    private UmShopInter umShopInter;
    /**
     * 根据公司id查询店铺信息
     *
     * @return {@code ResultMode<UmShopDTO> }
     */
    public ResultMode<ShopDTO> shopDetailByShopIdOrCompanyId(Long shopId, String companyId) {
        return umShopInter.shopDetailByShopIdOrCompanyId(shopId, companyId);
    }

    /**
     * 批量获取店铺联系方式
     *
     * @param shopIds 店铺id列表
     * @return {@code ResultMode<String> }
     */
    public ResultMode<List<ShopContactsDTO>> getShopContactsList(List<Long> shopIds) {
        return umShopInter.getShopContactsList(shopIds);
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 寻呼信息
     * @return {@code ResultMode }
     */
    public ResultMode queryPage(PagingInfo<ShopPageQuery> pagingInfo){
        return umShopInter.queryPage(pagingInfo);
    }
}
