package com.wanlianyida.platform.application.service.partner;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.PostManageInter;
import com.wanlianyida.partner.api.model.command.PostAddCommand;
import com.wanlianyida.partner.api.model.command.PostDeleteCommand;
import com.wanlianyida.partner.api.model.command.PostUpdateCommand;
import com.wanlianyida.partner.api.model.query.PostPageQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PostAppService {
    @Resource
    private PostManageInter postManageInter;

    /**
     * 岗位新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> add(PostAddCommand command) {
        return postManageInter.add(command);
    }

    /**
     * 岗位更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> update(PostUpdateCommand command) {
        return postManageInter.update(command);
    }

    /**
     * 岗位删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> delete(PostDeleteCommand command) {
        return postManageInter.delete(command);
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    public ResultMode queryPage(PagingInfo<PostPageQuery> pagingInfo) {
        return postManageInter.queryPage(pagingInfo);
    }
}
