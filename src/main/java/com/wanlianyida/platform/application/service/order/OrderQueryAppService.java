package com.wanlianyida.platform.application.service.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.basecont.api.model.dto.ContractSignArchiveDTO;
import com.wanlianyida.basecont.api.model.query.ContractSignArchiveQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.inter.OrderQueryInter;
import com.wanlianyida.order.api.model.dto.*;
import com.wanlianyida.order.api.model.query.OrderDetailQuery;
import com.wanlianyida.order.api.model.query.OrderInfoListQuery;
import com.wanlianyida.order.api.model.query.OrderShipQuery;
import com.wanlianyida.platform.infrastructure.exchange.ContractExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.sett.api.inter.AggregateInter;
import com.wanlianyida.sett.api.model.dto.OrderRelatedDTO;
import com.wanlianyida.sett.api.model.query.OrderRelatedQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 订单查询
 */
@Slf4j
@Service
public class OrderQueryAppService {

    @Resource
    private OrderQueryInter orderQueryInter;

    @Resource
    private UploadExchangeService uploadExchangeService;

    @Resource
    private ContractExchangeService contractExchangeService;

    @Resource
    private AggregateInter aggregateInter;

    /**
     * 订单列表查询（平台端）
     */
    public ResultMode<List<OrderInfoListDTO>> pageInfoList(PagingInfo<OrderInfoListQuery> pagingInfo) {
        return orderQueryInter.pageInfoList(pagingInfo);
    }

    /**
     * 订单详情查询-基本信息
     */
    public ResultMode<OrderBaseDetailDTO> baseDetail(OrderDetailQuery query) {
        ResultMode<OrderBaseDetailDTO> resultMode = orderQueryInter.baseDetail(query);
        if (ObjUtil.isNull(resultMode.getModel())) {
            return ResultMode.success();
        }

        OrderBaseDetailDTO dto = resultMode.getModel();
        List<OrderProductDTO> productList = dto.getProductList();
        convertProductUrls(productList);

        dto.setContractOssKeyList(getContractOssKey(dto));

        return resultMode;
    }

    /**
     * 订单详情查询-付款与结算信息
     */
    public ResultMode<OrderPaymentInfoDetailDTO> paymentDetail(OrderDetailQuery query) {
        ResultMode<OrderPaymentInfoDetailDTO> resultMode = orderQueryInter.paymentDetail(query);
        if (ObjUtil.isNull(resultMode.getModel())) {
            return ResultMode.success();
        }

        OrderPaymentInfoDetailDTO dto = resultMode.getModel();
        List<OrderProductDTO> productList = dto.getProductList();
        convertProductUrls(productList);

        if (IterUtil.isNotEmpty(dto.getPayDetailList())) {
            List<OrderAttachmentDTO> payDetailAttList = dto.getPayDetailList().stream()
                    .filter(paymentDetail -> IterUtil.isNotEmpty(paymentDetail.getAttachmentList()))
                    .flatMap(paymentDetail -> paymentDetail.getAttachmentList().stream()).collect(Collectors.toList());
            convertAttachmentUrls(payDetailAttList);
        }

        List<OrderAttachmentDTO> attachmentList = dto.getAttachmentList();
        convertAttachmentUrls(attachmentList);

        return resultMode;
    }

    /**
     * 订单详情查询-提货记录
     */
    public ResultMode<List<OrderPickUpDetailDTO>> pickUpDetail(OrderDetailQuery query) {
        ResultMode<List<OrderPickUpDetailDTO>> resultMode = orderQueryInter.pickUpDetail(query);
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }

        List<OrderPickUpDetailDTO> list = resultMode.getModel();
        List<OrderShipProductDTO> productList = list.stream().filter(dto -> IterUtil.isNotEmpty(dto.getProductList()))
                .flatMap(dto -> dto.getProductList().stream()).collect(Collectors.toList());
        convertShipProductUrls(productList);

        List<OrderAttachmentDTO> attachmentList = list.stream().filter(dto -> IterUtil.isNotEmpty(dto.getAttachmentList()))
                .flatMap(dto -> dto.getAttachmentList().stream()).collect(Collectors.toList());
        convertAttachmentUrls(attachmentList);

        return resultMode;
    }

    /**
     * 订单详情查询-发货记录
     */
    public ResultMode<List<OrderShipmentsDetailDTO>> shipmentsDetail(OrderDetailQuery query) {
        ResultMode<List<OrderShipmentsDetailDTO>> resultMode = orderQueryInter.shipmentsDetail(query);
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }

        List<OrderShipmentsDetailDTO> list = resultMode.getModel();
        List<OrderShipProductDTO> productList = list.stream().filter(dto -> IterUtil.isNotEmpty(dto.getProductList()))
                .flatMap(dto -> dto.getProductList().stream()).collect(Collectors.toList());
        convertShipProductUrls(productList);

        List<OrderAttachmentDTO> attachmentList = list.stream().filter(dto -> IterUtil.isNotEmpty(dto.getAttachmentList()))
                .flatMap(dto -> dto.getAttachmentList().stream()).collect(Collectors.toList());
        convertAttachmentUrls(attachmentList);

        return resultMode;
    }

    /**
     * 订单-查询提货单、发货单
     */
    public ResultMode<List<OrderShipInfoDTO>> queryShipInfo(OrderShipQuery query) {
        ResultMode<List<OrderShipInfoDTO>> resultMode = orderQueryInter.queryShipInfo(query);
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }

        List<OrderShipInfoDTO> list = resultMode.getModel();

        List<OrderAttachmentDTO> attachmentList = list.stream().filter(dto -> IterUtil.isNotEmpty(dto.getAttachmentList()))
                .flatMap(dto -> dto.getAttachmentList().stream()).collect(Collectors.toList());
        convertAttachmentUrls(attachmentList);

        return resultMode;
    }

    /**
     * 转换商品缩略图
     */
    private void convertProductUrls(List<OrderProductDTO> productList) {
        if (IterUtil.isEmpty(productList)) {
            return;
        }

        List<String> urls = productList.stream().filter(product -> StrUtil.isNotBlank(product.getPicture()))
                .map(OrderProductDTO::getPicture).distinct().collect(Collectors.toList());
        Map<String, String> urlMap = uploadExchangeService.convertThumbnailUrls(urls);
        if (MapUtil.isEmpty(urlMap)) {
            return;
        }

        productList.stream().filter(product -> StrUtil.isNotBlank(product.getPicture()))
                .forEach(product -> product.setPicture(urlMap.get(product.getPicture())));
    }

    /**
     * 转换发货、提货商品缩略图
     */
    private void convertShipProductUrls(List<OrderShipProductDTO> productList) {
        if (IterUtil.isEmpty(productList)) {
            return;
        }

        List<String> urls = productList.stream().filter(product -> StrUtil.isNotBlank(product.getPicture()))
                .map(OrderShipProductDTO::getPicture).distinct().collect(Collectors.toList());
        Map<String, String> urlMap = uploadExchangeService.convertThumbnailUrls(urls);
        if (MapUtil.isEmpty(urlMap)) {
            return;
        }

        productList.stream().filter(product -> StrUtil.isNotBlank(product.getPicture()))
                .forEach(product -> product.setPicture(urlMap.get(product.getPicture())));
    }

    /**
     * 转换附件图片url
     */
    private void convertAttachmentUrls(List<OrderAttachmentDTO> attachmentList) {
        if (IterUtil.isEmpty(attachmentList)) {
            return;
        }

        List<String> urls = attachmentList.stream().filter(att -> StrUtil.isNotBlank(att.getAttachmentUrl()))
                .map(OrderAttachmentDTO::getAttachmentUrl).distinct().collect(Collectors.toList());
        Map<String, String> urlMap = uploadExchangeService.convertUrls(urls);
        if (MapUtil.isEmpty(urlMap)) {
            return;
        }

        attachmentList.stream().filter(att -> StrUtil.isNotBlank(att.getAttachmentUrl()))
                .forEach(att -> att.setAttachmentUrl(urlMap.get(att.getAttachmentUrl())));
    }

    /**
     * 查询合同ossKey
     */
    private List<String> getContractOssKey(OrderBaseDetailDTO dto) {
        if (ObjUtil.isNull(dto) || StrUtil.isBlank(dto.getContractNo())) {
            return null;
        }

        try {
            ContractSignArchiveQuery query = new ContractSignArchiveQuery();
            query.setBusinessOrderId(dto.getOrderNo());
            ContractSignArchiveDTO contractBaseDTO = contractExchangeService.queryContractBaseDTO(query);
            if (ObjUtil.isNull(contractBaseDTO) || CollUtil.isEmpty(contractBaseDTO.getOssKeyList())) {
                return null;
            }
            return contractBaseDTO.getOssKeyList();
        } catch (Exception e) {
            log.error("查询合同ossKey失败:", e);
        }

        return null;
    }

    /**
     * 付款与结算信息
     */
    public ResultMode<OrderRelatedDTO> orderRelated(OrderRelatedQuery query){
        return aggregateInter.orderRelated(query);
    }
}
