package com.wanlianyida.platform.application.service.bi;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.BiBigDataExchangeService;
import com.wanlianyida.platform.interfaces.model.query.BatchBigDataQuery;
import com.wanlianyida.platform.interfaces.model.query.BigDataQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import javax.annotation.Resource;

/**
 * @ClassName OperationalBigDataAppService
 * @Description
 * <AUTHOR>
 * @Veriosn 1.0
 **/

@Slf4j
@Service
public class BigDataAppService {


    @Resource
    private BiBigDataExchangeService biBigDataExchangeService;


    public ResultMode<Map<String, Object>> biStatistics(BigDataQuery query) {
        return biBigDataExchangeService.biStatistics(query);
    }

    public ResultMode<Map<String, Object>> biBatchStatistics(BatchBigDataQuery query) {
        return biBigDataExchangeService.biBatchStatistics(query);
    }
}
