package com.wanlianyida.platform.application.service.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.exchange.BdUserFeedbackExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.SensitiveExchangeService;
import com.wanlianyida.platform.interfaces.model.command.ReplyFeedbackCommand;
import com.wanlianyida.support.api.model.command.BdAttachmentAddCommand;
import com.wanlianyida.support.api.model.command.BdUserFeedbackAndAttachReplyUpdateCommand;
import com.wanlianyida.support.api.model.command.BdUserFeedbackReplyUpdateCommand;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackAndAttachDTO;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackDTO;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackListDTO;
import com.wanlianyida.support.api.model.query.BdUserFeedbackQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Service
@Slf4j
public class BdUserFeedbackService {

    @Resource
    private BdUserFeedbackExchangeService bdUserFeedbackExchangeService;

    @Resource
    private SensitiveExchangeService sensitiveExchangeService;

    public ResultMode updateReplyFeedback(ReplyFeedbackCommand command) {
        BdUserFeedbackAndAttachReplyUpdateCommand updateCommand = new BdUserFeedbackAndAttachReplyUpdateCommand();
        BdUserFeedbackReplyUpdateCommand feedbackReplyUpdateCommand = new BdUserFeedbackReplyUpdateCommand();
        feedbackReplyUpdateCommand.setId(command.getId());
        feedbackReplyUpdateCommand.setReplyContent(command.getReplyContent());

        String ans = sensitiveExchangeService.checkWords(command.getReplyContent());
        if (!StrUtil.isBlank(ans)) {
            return ResultMode.fail("您当前提交的内容包含敏感词["+ ans+"]，请修改后再提交" );
        }
        List<BdAttachmentAddCommand> attachmentAddCommandList = BeanUtil.copyToList(command.getAttachmentList(), BdAttachmentAddCommand.class);
        updateCommand.setFeedbackReplyUpdateCommand(feedbackReplyUpdateCommand);
        updateCommand.setAttachmentAddCommandList(attachmentAddCommandList);
        return bdUserFeedbackExchangeService.updateReplyFeedback(updateCommand);
    }

    public ResultMode<List<BdUserFeedbackListDTO>> pageList(PagingInfo<BdUserFeedbackQuery> pagingInfo) {
        ResultMode<List<BdUserFeedbackDTO>> listResultMode = bdUserFeedbackExchangeService.pageList(pagingInfo);
        List<BdUserFeedbackDTO> list = listResultMode.getModel();
        if(CollUtil.isEmpty(list)){
            ResultMode.success();
        }
        List<BdUserFeedbackListDTO> bdUserFeedbackListDTOS = BeanUtil.copyToList(list, BdUserFeedbackListDTO.class);
        return ResultMode.successPageList(bdUserFeedbackListDTOS,listResultMode.getTotal());
    }

    public ResultMode<BdUserFeedbackAndAttachDTO> getDetail(@Valid BdUserFeedbackQuery query) {
        return bdUserFeedbackExchangeService.getDetail(query);
    }
}
