package com.wanlianyida.platform.application.service.bidding;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.bidding.api.inter.*;
import com.wanlianyida.bidding.api.model.command.EvalExpertDTO;
import com.wanlianyida.bidding.api.model.dto.*;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.ProjectTenderPackageDTO;
import com.wanlianyida.bidding.api.model.query.*;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.infrastructure.enums.QuotationContentEnum;
import com.wanlianyida.platform.infrastructure.exchange.bidding.EvalBidQueryExchange;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
public class EvalBidQueryAppService {

    @Resource
    private EvalExpertInter evalExpertInter;

    @Resource
    private ExpertCriticiseInter expertCriticiseInter;

    @Resource
    private BiddingRoundInter biddingRoundInter;

    @Resource
    private WinningResultManagerInter winningResultManagerInter;

    @Resource
    private TenderPackageInter tenderPackageInter;

    @Resource
    private EvalBidQueryExchange evalBidQueryExchange;

    /**
     * 评委会成员信息列表查询
     * @param query
     * @return
     */
    public ResultMode<List<EvalExpertDTO>> queryEvalExpert(EvalExpertListQuery query) {
        return evalExpertInter.list(query);
    }

    /**
     * 评标结果信息列表查询
     * @param query
     * @return
     */
    public ResultMode<List<TenderPackageScoreListDTO>> queryEvalResult(PackageWinQuery query) {
        IdQuery idQuery = new IdQuery();
        idQuery.setId(query.getRelTenderPackageId());
        ResultMode<ProjectTenderPackageDTO> resultMode = tenderPackageInter.queryDetailTenderPackageProject(idQuery);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getCode(), resultMode.getMessage());
        }
        // 评标方式 10线上,20线下
        String tenderEvaluationMethod = resultMode.getModel().getTenderPackage().getTenderEvaluationMethod();
        // 轮次
        Integer bidRound = resultMode.getModel().getTenderPackage().getBidRound();
        if (ObjUtil.equals("10", tenderEvaluationMethod)) {
            EvalScoreResultQuery scoreResultQuery = BeanUtil.toBean(query, EvalScoreResultQuery.class);
            scoreResultQuery.setPackageId(query.getRelTenderPackageId());
            ResultMode<List<EvalScoreResultListDTO>> scoreResultMode = expertCriticiseInter.queryEvalScoreResultPage(scoreResultQuery);
            if (resultMode.isSucceed() && ObjUtil.isNotEmpty(scoreResultMode.getModel())) {
                List<TenderPackageScoreListDTO> list = BeanUtil.copyToList(scoreResultMode.getModel(), TenderPackageScoreListDTO.class);
                list.stream().peek(e -> e.setTechnologyScore(e.getTechScore())).collect(Collectors.toList());
                return ResultMode.success(list);
            }
        } else {
            if (!query.getBidRoundList().contains(bidRound)) {
                return ResultMode.success(Lists.newArrayList());
            }
        }
        return winningResultManagerInter.packageWinList(query);
    }

    /**
     * 评标轮次信息列表查询
     * @param pageQuery
     * @return
     */
    public ResultMode<List<BiddingRoundDTO>> pageQueryRoundList(PagingInfo<BiddingRoundQuery> pageQuery) {
        ResultMode<List<BiddingRoundDTO>> resultMode = biddingRoundInter.pageQueryRoundList(pageQuery);
        if (resultMode.isSucceed() && ObjUtil.isNotEmpty(resultMode.getModel())) {
            resultMode.getModel().stream().peek(e -> {
                String quotationContent = joinQuotationContent(e.getQuotationContent());
                e.setQuotationContentValue(quotationContent);
            }).collect(Collectors.toList());
        }
        return resultMode;
    }

    /**
     * 中标候选人列表查询（按候选人排序升序）
     * @param query
     * @return
     */
    public ResultMode<List<EvalScoreResultListDTO>> queryCandidate(EvalScoreResultQuery query) {
        query.setCandidateFlag(1);
        ResultMode<List<EvalScoreResultListDTO>> resultMode = expertCriticiseInter.queryEvalScoreResultPage(query);
        if (resultMode.isSucceed() && ObjUtil.isNotEmpty(resultMode.getModel())) {
            resultMode.setModel(resultMode.getModel().stream().sorted(Comparator.comparing(EvalScoreResultListDTO::getBidRound)).collect(Collectors.toList()));
        }
        return resultMode;
    }

    private static String joinQuotationContent(String quotationContent) {
        if (StrUtil.isBlank(quotationContent)) {
            return "";
        }
        List<String> list = new ArrayList<>();
        String[] arr = quotationContent.split(",");
        Arrays.stream(arr).forEach(e -> {
            list.add(QuotationContentEnum.getDescByCode(e));
        });
        return String.join(",", list);
    }

    public ResultMode<List<ClarifyFyDTO>> pageQueryClarifyPublish(PagingInfo<ClarifyQuery> pagingInfo) {
        return evalBidQueryExchange.pageQueryClarifyPublish(pagingInfo);
    }

    public ResultMode<List<ClarifyReplyFyDTO>> pageQueryClarifyReply(PagingInfo<ClarifyQuery> pagingInfo) {
        return evalBidQueryExchange.pageQueryClarifyReply(pagingInfo);
    }

    public ResultMode<List<BidChangeLogDTO>> pageQueryEvalResultChange(PagingInfo<BidChangeLogQuery> pagingInfo) {
        return evalBidQueryExchange.pageQueryEvalResultChange(pagingInfo);
    }

    public ResultMode<List<AttachmentEvaDTO>> queryReviewAttachment(ReviewAttachmentQuery query) {
        return evalBidQueryExchange.queryReviewAttachment(query);
    }
}
