package com.wanlianyida.platform.application.service.company;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.basemdm.api.model.dto.MdmCompanyInfoDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmOperatorDTO;
import com.wanlianyida.basemdm.api.model.query.MdmCompanyInfoQuery;
import com.wanlianyida.basemdm.api.model.query.MdmOperatorQuery;
import com.wanlianyida.basicdata.model.query.PlatformUmCompanyQurey;
import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssuserauth.api.enums.UserAuthEnums;
import com.wanlianyida.fssuserauth.api.model.dto.UmPermissioninfoDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UmUserpermissionDTO;
import com.wanlianyida.fssuserauth.api.model.query.UmPermissioninfoQuery;
import com.wanlianyida.fssuserauth.api.model.query.UmUserpermissionQuery;
import com.wanlianyida.partner.api.inter.EnterpriseAccountInter;
import com.wanlianyida.partner.api.inter.MemberInter;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import com.wanlianyida.partner.api.model.command.UmCompanyBizTypeUpdateCommand;
import com.wanlianyida.partner.api.model.command.UmCompanyUpdateCommand;
import com.wanlianyida.partner.api.model.command.UmInviteCodeCommand;
import com.wanlianyida.partner.api.model.dto.*;
import com.wanlianyida.partner.api.model.query.EnterpriseQuery;
import com.wanlianyida.partner.api.model.query.UmCompanyPageMiniQuery;
import com.wanlianyida.partner.api.model.query.UmCompanyQuery;
import com.wanlianyida.platform.application.assembler.operator.OperatorInfoAssembler;
import com.wanlianyida.platform.infrastructure.exception.CtpOrchPlatformException;
import com.wanlianyida.platform.infrastructure.exception.CtpOrchPlatformExceptionEnum;
import com.wanlianyida.platform.infrastructure.exchange.BasicDataExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.UmCompanyExchangeService;
import com.wanlianyida.platform.infrastructure.exchange.UserAuthExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 企业信息表 AppService
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Slf4j
@Service
public class UmCompanyAppService {

    @Resource
    private UmCompanyInter umCompanyInter;

    @Resource
    private UserAuthExchangeService userAuthExchangeService;

    @Resource
    private BasicDataExchangeService basicDataExchangeService;

    @Resource
    private UmCompanyExchangeService umCompanyExchangeService;

    @Resource
    private EnterpriseAccountInter enterpriseAccountInter;
    @Resource
    private MdmExchangeService mdmExchangeService;
    @Resource
    private MemberInter memberInter;

    /**
     * 分页查询
     *
     * @param pagingInfo 分页查询参数
     * @return {@link UmCompanyDTO}>
     */
    public ResultMode<List<UmCompanyDTO>> queryPage(PagingInfo<UmCompanyQuery> pagingInfo) {
        return umCompanyInter.queryPage(pagingInfo);
    }

    private List<MdmOperatorDTO> processMdmCompany(List<String> creditCodeList) {
        //再关联查主数据企业--mdm
        MdmCompanyInfoQuery query = new MdmCompanyInfoQuery();
        query.setLicenseNoList(creditCodeList);
        List<MdmCompanyInfoDTO> mdmCompanyInfoDTOList = mdmExchangeService.getMdmCmdCompanyByLicenseNos(query);
        if (CollUtil.isEmpty(mdmCompanyInfoDTOList)) {
            throw new CtpOrchPlatformException(CtpOrchPlatformExceptionEnum.DATA_EMPTY_ERROR, "未查询到主数据企业信息");
        }
        Map<String, MdmCompanyInfoDTO> mdmCompanyInfoDTOMap = mdmCompanyInfoDTOList.stream().collect(Collectors.toMap(MdmCompanyInfoDTO::getLicenseNo, item -> item, (v1, v2) -> v1));
        //mdmCompanyInfoDTOList按creditCodeList的顺序排序
        List<MdmCompanyInfoDTO> mdmCompanyInfoDTOListSort = new ArrayList<>();
        for (String creditCode : creditCodeList) {
            if (mdmCompanyInfoDTOMap.get(creditCode) != null) {
                mdmCompanyInfoDTOListSort.add(mdmCompanyInfoDTOMap.get(creditCode));
            }
        }
        //组装数据
        List<MdmOperatorDTO> mdmOperatorDTOList = OperatorInfoAssembler.assemblerMdmOperatorDTOList(mdmCompanyInfoDTOListSort);
        //从mdm企业拿到企业ID集合
        List<String> mdmCompanyIdList = mdmCompanyInfoDTOListSort.stream().map(item->item.getId().toString()).collect(Collectors.toList());
        //再关联主数据操作员(企业管理员)：--mdm
        PagingInfo<MdmOperatorQuery> pageOperatorQuery = new PagingInfo<>();
        MdmOperatorQuery mdmOperatorQuery = new MdmOperatorQuery();
        mdmOperatorQuery.setCompanyIdList(mdmCompanyIdList);
        mdmOperatorQuery.setOperatorType(UserAuthEnums.OperatorCategoryEnum.ADMIN.getCode());
        pageOperatorQuery.setFilterModel(mdmOperatorQuery);
        ResultMode<List<MdmOperatorDTO>> resultModeMdmOperator = mdmExchangeService.queryCmdOperatorPage(pageOperatorQuery);
        if (!resultModeMdmOperator.isSucceed()) {
            return mdmOperatorDTOList;
        }
        //根据mdm企业id转map
        Map<String, MdmOperatorDTO> mdmOperatorDTOMap = resultModeMdmOperator.getModel().stream().collect(Collectors.toMap(MdmOperatorDTO::getCompanyId, item -> item, (v1, v2) -> v1));
        //根据mdm企业id关联mdm企业管理员
        for (MdmOperatorDTO mdmOperatorDTO : mdmOperatorDTOList) {
            MdmOperatorDTO mdmOperatorDTO1 = mdmOperatorDTOMap.get(mdmOperatorDTO.getCompanyId());
            if (mdmOperatorDTO1 != null) {
                mdmOperatorDTO.setOperatorName(mdmOperatorDTO1.getOperatorName());
                mdmOperatorDTO.setOperatorPhone(mdmOperatorDTO1.getOperatorPhone());
                mdmOperatorDTO.setOperatorAccount(mdmOperatorDTO1.getOperatorAccount());
            }
        }
        return mdmOperatorDTOList;
    }

    private List<MdmOperatorDTO> processMdmCompanyAdmin(List<String> mdmCompanyIdList) {
        //再关联用户角色：用户已是管理员，那他的角色就是企业的角色了--auth
        UmUserpermissionQuery queryUserPermission = new UmUserpermissionQuery();
        queryUserPermission.setAccountIdList(mdmCompanyIdList);
        ResultMode<List<UmUserpermissionDTO>> umUserpermissionMode = userAuthExchangeService.queryUserRoleList(queryUserPermission);
        if (!umUserpermissionMode.isSucceed()) {
            return Collections.emptyList();
        }
        //从auth用户角色拿到角色ID集合
        Set<Long> permissionIds = umUserpermissionMode.getModel().stream().map(UmUserpermissionDTO::getPermissionId).collect(Collectors.toSet());
        Map<String, List<UmUserpermissionDTO>> umUserpermissionMap = umUserpermissionMode.getModel().stream().collect(Collectors.groupingBy(UmUserpermissionDTO::getAccountId));
        //再关联角色：--auth
        UmPermissioninfoQuery queryPermission = new UmPermissioninfoQuery();
        queryPermission.setPermissionIdList(new ArrayList<>(permissionIds));
        ResultMode<List<UmPermissioninfoDTO>> umPermissioninfoMode = userAuthExchangeService.queryPermissionList(queryPermission);
        if (!umPermissioninfoMode.isSucceed()) {
            return Collections.emptyList();
        }
        Map<Long, UmPermissioninfoDTO> umPermissioninfoMap = umPermissioninfoMode.getModel().stream().collect(Collectors.toMap(UmPermissioninfoDTO::getPermissionId, item -> item, (v1, v2) -> v1));
        return mdmCompanyIdList.stream().map(item -> {
            List<UmUserpermissionDTO> itemUmUserpermissionDTOList = umUserpermissionMap.get(item);
            if (CollUtil.isEmpty(itemUmUserpermissionDTOList)) {
                return null;
            }
            MdmOperatorDTO mdmOperatorDTO = new MdmOperatorDTO();
            mdmOperatorDTO.setCompanyId(item);
            mdmOperatorDTO.setPermissionIdList(itemUmUserpermissionDTOList.stream().map(UmUserpermissionDTO::getPermissionId).collect(Collectors.toList()));
            mdmOperatorDTO.setPermissionNameList(itemUmUserpermissionDTOList.stream().map(up -> {
                if (umPermissioninfoMap.get(up.getPermissionId()) != null) {
                    return umPermissioninfoMap.get(up.getPermissionId()).getPermissionName();
                }
                return null;
            }).collect(Collectors.toList()));
            // 没有自定义授权，标记全是false
            mdmOperatorDTO.setUserFunctionsFlag(false);
            return mdmOperatorDTO;
        }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
    }

    private ResultMode<List<UmCompanyDTO>> processMdmCompanyAdmin(PagingInfo<MdmOperatorQuery> pagingInfo) {
        //先查询企业--ctp
        PagingInfo<UmCompanyQuery> paggeInfo = new PagingInfo<>();
        UmCompanyQuery umCompanyQuery = new UmCompanyQuery();
        umCompanyQuery.setCompanyName(pagingInfo.getFilterModel().getSearchCompanyName());
        umCompanyQuery.setSearchLoginName(pagingInfo.getFilterModel().getLoginName());
        umCompanyQuery.setAptitudeStatus(UserAuthEnums.CompanyAptitudeStatusEnum.PASS.getCode());
        BeanUtil.copyProperties(pagingInfo, paggeInfo);
        paggeInfo.setFilterModel(umCompanyQuery);
        return umCompanyExchangeService.queryPage(paggeInfo);
    }

    /**
     * 管理员账号分页查询
     *
     * @param pagingInfo 分页查询参数
     * @return {@link MdmOperatorDTO}>
     */
    public ResultMode<List<MdmOperatorDTO>> queryPageAdmin(PagingInfo<MdmOperatorQuery> pagingInfo) {

        //分页ctp企业
        ResultMode<List<UmCompanyDTO>> resultModeUmCompany = processMdmCompanyAdmin(pagingInfo);
        if (!resultModeUmCompany.isSucceed() || CollUtil.isEmpty(resultModeUmCompany.getModel())) {
            return ResultMode.success();
        }
        //从ctp企业拿到企业信用代码集合
        List<String> creditCodeList = resultModeUmCompany.getModel().stream().map(UmCompanyDTO::getSocialCreditCode).collect(Collectors.toList());
        //关联主数据企业
        List<MdmOperatorDTO> mdmOperatorListOne = processMdmCompany(creditCodeList);
        //从mdm企业拿到企业ID集合
        List<String> mdmCompanyIdList = mdmOperatorListOne.stream().map(MdmOperatorDTO::getCompanyId).collect(Collectors.toList());
        //关联角色授权
        List<MdmOperatorDTO> mdmOperatorListTwo = processMdmCompanyAdmin(mdmCompanyIdList);
        mdmOperatorListOne.forEach(item -> mdmOperatorListTwo.forEach(itemTwo -> {
            if (StrUtil.equals(item.getCompanyId(), itemTwo.getCompanyId())) {
                item.setPermissionIdList(itemTwo.getPermissionIdList());
                item.setPermissionNameList(itemTwo.getPermissionNameList());
                item.setUserFunctionsFlag(itemTwo.getUserFunctionsFlag());
            }
        }));
        return ResultMode.successPageList(mdmOperatorListOne, resultModeUmCompany.getTotal());
    }

    /**
     * 列表查询
     *
     * @param condition 查询参数
     * @return {@link List}<{@link UmCompanyDTO}>
     */
    public ResultMode<List<UmCompanyDTO>> queryList(UmCompanyQuery condition) {
        return umCompanyInter.queryList(condition);
    }

    /**
     * 关联修改：审核保存
     *
     * @param bo
     */
    public ResultMode<?> update(UmCompanyUpdateCommand bo) {
        return umCompanyInter.update(bo);
    }

    /**
     * 资质审核
     *
     * @param bo
     */
    public ResultMode<?> audit(UmCompanyUpdateCommand bo) {
        return umCompanyInter.audit(bo);
    }

    /**
     * 配置
     *
     * @param bo
     */
    public ResultMode<?> config(UmCompanyUpdateCommand bo) {
        return umCompanyInter.config(bo);
    }

    /**
     * 详情
     *
     * @param id
     * @return {@link UmCompanyDTO}
     */
    public ResultMode<UmCompanyDTO> detail(Long id) {
        return umCompanyInter.detail(id);
    }

    /**
     * 用户所属企业信息
     *
     * @param userBaseId
     * @return {@link UmCompanyDTO}
     */
    public ResultMode<UmCompanyDTO> detailByUserBaseId(String userBaseId) {
        return umCompanyInter.detailByUserBaseId(userBaseId);
    }

    /**
     * 统计审核状态
     *
     * @return {@link UmCompanyCntAuditStatusDTO}
     */
    public ResultMode<UmCompanyCntAuditStatusDTO> cntAuditStatus() {
        return umCompanyInter.cntAuditStatus();
    }

    /**
     * 更新法人授权书
     *
     * @param bo
     * @return {@link ResultMode}
     */
    public ResultMode<?> updateAuthUrl(UmCompanyUpdateCommand bo) {
        return umCompanyInter.updateAuthUrl(bo);
    }

    /**
     * 企业审核校验工商信息
     *
     * @param bo
     * @return {@link ResultMode}
     */
    public ResultMode<?> companyBusinessCheck(UmCompanyQuery bo) {
        PlatformUmCompanyQurey condition = new PlatformUmCompanyQurey();
        condition.setSocialCreditCode(bo.getSocialCreditCode());
        condition.setCompanyName(bo.getCompanyName());
        return basicDataExchangeService.companyBusinessCheck(condition);
    }

    public ResultMode<List<UmCompanyMiniDTO>> queryPageMini(PagingInfo<UmCompanyPageMiniQuery> pagingInfo) {
        return umCompanyExchangeService.queryPageMini(pagingInfo);
    }

    public ResultMode updateBizType(UmCompanyBizTypeUpdateCommand updateCommand){
        return umCompanyExchangeService.updateBizType(updateCommand);
    }

    public ResultMode<?> unbindManager(IdCommand command) {
        return umCompanyInter.unbindManager(command);
    }

    /**
     *
     * 企业信息详情页
     * @param query
     * @return
     */
    public ResultMode<EnterpriseInfoDTO> getEntProfileDetailPage(EnterpriseQuery query) {
        return enterpriseAccountInter.getPlatformCompanyDetail(query);
    }

    public ResultMode<UmInviteCodeDTO> getInviteCode(@RequestBody UmInviteCodeCommand cmd){
        return umCompanyInter.getInviteCode(cmd);
    }
}
