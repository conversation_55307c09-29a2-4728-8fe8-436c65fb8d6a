package com.wanlianyida.platform.application.assembler.settlement;

import com.wanlianyida.order.api.model.dto.SettlementOrderShipProductDTO;
import com.wanlianyida.partner.api.model.dto.BdPricingTypeConfigDTO;
import com.wanlianyida.sett.api.model.dto.ExportSettlementListDTO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;

public class ExportAssembler {

    /**
     * 导出的金额和数量格式化
     */
    public static void buildExportSettlementDTO(ExportSettlementListDTO detail, Map<Integer, List<BdPricingTypeConfigDTO>> measureUnitMap, SettlementOrderShipProductDTO ship) {
        // 设置千分位和两位小数
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        // 格式化处理
        String priceUnit = measureUnitMap.get(ship.getPriceUnit().intValue()).get(0).getPricingTypeName();
        String purchaseQuantityUnit = measureUnitMap.get(ship.getPurchaseQuantityUnit().intValue()).get(0).getPricingTypeName();
        String unitCon = new BigDecimal(detail.getUnitCon()).setScale(7, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
        detail.setUnitCon("1" + purchaseQuantityUnit + " = " + unitCon + priceUnit);
        detail.setContractUnitPrice(new DecimalFormat("¥#,##0.00").format(new BigDecimal(detail.getContractUnitPrice())) + "/" + priceUnit);
        detail.setShipmentQty(new BigDecimal(detail.getShipmentQty()).setScale(3, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + purchaseQuantityUnit);
        detail.setReceiveQty(new BigDecimal(detail.getReceiveQty()).setScale(3, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + purchaseQuantityUnit);
        detail.setSmentQuantity(new BigDecimal(detail.getSmentQuantity()).setScale(3, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + purchaseQuantityUnit);
        detail.setSmentPrice(new DecimalFormat("¥#,##0.00").format(new BigDecimal(detail.getSmentPrice())) + "/" + priceUnit);
        detail.setSementSubtotal(decimalFormat.format(new BigDecimal(detail.getSementSubtotal())));
        detail.setAdjustAmount(decimalFormat.format(new BigDecimal(detail.getAdjustAmount())));
        detail.setPayableSettAmount(decimalFormat.format(new BigDecimal(detail.getPayableSettAmount())));
        detail.setSettTotalAmount(decimalFormat.format(new BigDecimal(detail.getSettTotalAmount())));
        detail.setPayTotalAmount(decimalFormat.format(new BigDecimal(detail.getPayTotalAmount())));
        detail.setPrepaidTotalAmount(decimalFormat.format(new BigDecimal(detail.getPrepaidTotalAmount())));
    }
}
