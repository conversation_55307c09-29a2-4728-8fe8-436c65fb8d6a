package com.wanlianyida.platform.application.aspect;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.platform.application.annotation.CreatorAssignment;
import com.wanlianyida.platform.application.annotation.UpdaterAssignment;
import com.wanlianyida.platform.infrastructure.util.UserBaseInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2025/02/18/15:13
 */
@Slf4j
@Aspect
@Component
public class BaseUserInfoAspect {



    @Before("@annotation(com.wanlianyida.platform.application.annotation.CreatorAssignment) || @annotation(com.wanlianyida.platform.application.annotation.UpdaterAssignment)")
    public void beforeMethod(JoinPoint joinPoint) {
        log.info("设置用户基础字段:{}", JSONUtil.toJsonStr(joinPoint.getArgs()));
        Object[] args = joinPoint.getArgs();
        if (args.length > 0) {
            Object firstArg = IterUtil.getFirst(Arrays.asList(args));
            log.info("第一个参数: {}", firstArg);
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();

            // 获取方法上的所有注解
            Annotation[] annotations = method.getAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation.annotationType() == CreatorAssignment.class) {
                    log.info("当前触发的是 CreatorAssignment 注解的方法：" + method.getName());
                    UserBaseInfoUtil.setAddBaseInfo(firstArg);
                } else if (annotation.annotationType() == UpdaterAssignment.class) {
                    log.info("当前触发的是 UpdaterAssignment 注解的方法：" + method.getName());
                    UserBaseInfoUtil.setUpdateBaseInfo(firstArg);
                }
            }
        }

    }
}
