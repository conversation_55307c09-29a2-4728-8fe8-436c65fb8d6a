package com.wanlianyida.platform.infrastructure.util;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;

public class ConvertUtil {
    public static <T> PagingInfo<T> toFssPagingInfo(com.wanlianyida.framework.ctpcommon.entity.PagingInfo<T> pagingInfo) {
        PagingInfo<T> newInfo = new PagingInfo<>();
        newInfo.setCurrentPage(pagingInfo.currentPage);
        newInfo.setFilterModel(pagingInfo.getFilterModel());
        newInfo.setPageLength(pagingInfo.getPageLength());
        newInfo.setSort(pagingInfo.getSort());
        newInfo.setCountTotal(pagingInfo.getCountTotal());
        return newInfo;
    }

    public static <T> ResultMode<T> toResultMode(ResponseMessage<T> responseMessage) {
        ResultMode<T> resultMode = new ResultMode<>();
        resultMode.setCode(resultMode.getCode());
        resultMode.setMessage(resultMode.getMessage());
        resultMode.setModel(responseMessage.getModel());
        resultMode.setSucceed(responseMessage.isSucceed());
        resultMode.setTotal(responseMessage.getTotal());
        return resultMode;
    }
}
