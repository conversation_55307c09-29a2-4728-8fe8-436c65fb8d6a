package com.wanlianyida.platform.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
public enum InputFlagEnum {
    // 是否必填(0非必填 1必填)
    MUST_INPUT(1, "是"),
    NOT_MUST_INPUT(0, "否");

    private final Integer code;
    private final String desc;
    InputFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过描述获取code
     */
    public static Integer getCode(String desc) {
        if (StrUtil.isBlank(desc)) {
            return null;
        }
        Optional<InputFlagEnum> first = Arrays.stream(values()).filter(v -> StrUtil.equals(v.getDesc(), desc)).findFirst();
        return first.map(InputFlagEnum::getCode).orElse(null);
    }

}
