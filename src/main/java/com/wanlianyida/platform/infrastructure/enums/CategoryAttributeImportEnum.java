package com.wanlianyida.platform.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum CategoryAttributeImportEnum {

    FIRST_LEVEL_CATEGORY("一级品类名称", "firstLevelName"),
    SECOND_LEVEL_CATEGORY("二级品类名称", "secondLevelName"),
    THIRD_LEVEL_CATEGORY("三级品类名称", "thirdLevelName"),
    ATTRIBUTE_NAME("规格名称", "attributeName"),
    INPUT_TYPE("输入方式(选项、文本)", "inputType"),
    TYPE_FLAG("是否销售规格（否，是）", "typeFlag"),
    INPUT_FLAG("是否必填（否，是）", "inputFlag"),
    VALUES("规格值（多个用\",\"分隔）", "values");

    private final String columnName;
    private final String propertyName;

    CategoryAttributeImportEnum(String columnName, String propertyName) {
        this.columnName = columnName;
        this.propertyName = propertyName;
    }

    /**
     * 获取字段名称
     */
    public static String getPropertyName(String columnName) {
        if (StrUtil.isBlank(columnName)) {
            return "";
        }
        Optional<CategoryAttributeImportEnum> first = Arrays.stream(values()).filter(v -> StrUtil.equals(v.getColumnName(), columnName)).findFirst();
        return first.map(CategoryAttributeImportEnum::getPropertyName).orElse(null);
    }

}
