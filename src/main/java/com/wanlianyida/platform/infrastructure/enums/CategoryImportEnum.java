package com.wanlianyida.platform.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum CategoryImportEnum {

    FIRST_LEVEL_CATEGORY("一级品类名称", "firstLevelName"),
    SECOND_LEVEL_CATEGORY("二级品类名称", "secondLevelName"),
    THIRD_LEVEL_CATEGORY("三级品类名称", "thirdLevelName"),
    ;

    private final String columnName;
    private final String propertyName;

    CategoryImportEnum(String columnName, String propertyName) {
        this.columnName = columnName;
        this.propertyName = propertyName;
    }

    /**
     * 获取字段名称
     */
    public static String getPropertyName(String columnName) {
        if (StrUtil.isBlank(columnName)) {
            return "";
        }
        Optional<CategoryImportEnum> first = Arrays.stream(values()).filter(v -> StrUtil.equals(v.getColumnName(), columnName)).findFirst();
        return first.map(CategoryImportEnum::getPropertyName).orElse(null);
    }

}
