package com.wanlianyida.platform.infrastructure.enums;

import lombok.Getter;

/**
 * 平台品类树形结构枚举
 * <AUTHOR>
 */
@Getter
public enum PlatformCategoryTreeEnum {
    PORTAL_LOGISTICS("10", "门户首页找物流使用"),
    USER_PUBLISH_PRODUCT("20", "用户端发布商品使用"),
    PLATFORM_CATEGORY_MANAGE("30", "运营端品类管理使用"),
    PORTAL_SHOP_PRODUCT_CATEGORY("40", "门户商品自定义店铺使用"),
    PORTAL_NAVBAR("50", "门户首页导航栏使用"),
    PLATFORM_PRODUCT_AUDIT("60", "运营端商品自动审核规则使用");

    private final String queryType;
    private final String desc;

    PlatformCategoryTreeEnum(String queryType, String desc) {
        this.queryType = queryType;
        this.desc = desc;
    }
}
