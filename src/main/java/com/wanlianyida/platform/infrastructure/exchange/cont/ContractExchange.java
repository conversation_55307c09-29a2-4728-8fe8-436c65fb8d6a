package com.wanlianyida.platform.infrastructure.exchange.cont;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.basecont.api.inter.OffLineContractInter;
import com.wanlianyida.basecont.api.model.dto.ContractBaseDTO;
import com.wanlianyida.basecont.api.model.dto.ContractOrderDTO;
import com.wanlianyida.basecont.api.model.dto.ContractOrderGroupDTO;
import com.wanlianyida.basecont.api.model.query.ContractAndOrderQuery;
import com.wanlianyida.basecont.api.model.query.ContractOrderQuery;
import com.wanlianyida.basecont.api.model.query.ContractQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.platform.infrastructure.enums.ContractNatureEnum;
import com.wanlianyida.platform.infrastructure.exception.CtpOrchPlatformExceptionEnum;
import com.wanlianyida.platform.interfaces.model.dto.APIContractBaseDTO;
import com.wanlianyida.platform.interfaces.model.dto.APIContractOrderGroupDTO;
import com.wanlianyida.platform.interfaces.model.query.APIContractAndOrderQuery;
import com.wanlianyida.platform.interfaces.model.query.APIContractOrderQuery;
import com.wanlianyida.platform.interfaces.model.query.ContOrderQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/17 13:50
 */
@Slf4j
@Service
public class ContractExchange {

    @Resource
    private OffLineContractInter offLineContractInter;



    /**
     * 合同分页
     * @param pagingInfo 分页查询参数
     * @return {@link }<{@link APIContractBaseDTO}>
     */
    public ResultMode<List<APIContractBaseDTO>> pageQueryAllContract(PagingInfo<APIContractAndOrderQuery> pagingInfo) {
        log.info("分页查询大宗合同,入参:{}", JSONUtil.toJsonStr(pagingInfo));
        PagingInfo<ContractAndOrderQuery> contracPagingInfo = this.convertPagingInfo(pagingInfo, ContractAndOrderQuery.class);
        ResponseMessage<List<ContractBaseDTO>> queryAllContractMode = offLineContractInter.pageQueryAllContract(contracPagingInfo);
        log.info("分页查询大宗合同,返参:{}", JSONUtil.toJsonStr(queryAllContractMode));
        if (!queryAllContractMode.isSucceed()) {
            return ResultMode.fail(CtpOrchPlatformExceptionEnum.MIDDLEWARE_CALL_ERROR.getCode(),queryAllContractMode.getMessage());
        }
        return ResultMode.successPageList(BeanUtil.copyToList(queryAllContractMode.getModel(), APIContractBaseDTO.class), (int) queryAllContractMode.getTotal());
    }


    /**
     * 根据订单查询大宗合同包括附件以及附件名称
     */
    public ResultMode<List<APIContractOrderGroupDTO>> queryBulkTradeByBizId(APIContractOrderQuery contractOrderQuery) {
        ContractOrderQuery orderQuery = BeanUtil.toBean(contractOrderQuery, ContractOrderQuery.class);
        log.info("根据订单查询大宗合同包括附件以及附件名称,入参:{}", JSONUtil.toJsonStr(orderQuery));
        ResponseMessage<List<ContractOrderGroupDTO>> contractOrderGroupMode = offLineContractInter.queryBulkTradeByBizId(orderQuery);
        log.info("根据订单查询大宗合同包括附件以及附件名称,返参:{}", JSONUtil.toJsonStr(contractOrderGroupMode));
        if (!contractOrderGroupMode.isSucceed()) {
            return ResultMode.fail(CtpOrchPlatformExceptionEnum.MIDDLEWARE_CALL_ERROR.getCode(), contractOrderGroupMode.getMessage());
        }

        List<APIContractOrderGroupDTO> apiContractOrderGroupDTOList = new ArrayList<>();
        List<ContractOrderGroupDTO> contractOrderGroupList = contractOrderGroupMode.getModel();
        for (ContractOrderGroupDTO entity : contractOrderGroupList) {
            List<ContractBaseDTO> contractBaseList = entity.getContractBaseList();
            Map<String, List<ContractBaseDTO>> contractsByNature = contractBaseList.stream()
                    .collect(Collectors.groupingBy(ContractBaseDTO::getContractNature));

            List<APIContractBaseDTO> apiContractBaseDTOList = new ArrayList<>();

            // 处理单笔合同（contractNature=10）
            processMainContract(contractsByNature, ContractNatureEnum.SINGLE_TRANSACTION.getCode(), contractBaseList, apiContractBaseDTOList);

            // 处理长期合同（contractNature=20）
            processMainContract(contractsByNature, ContractNatureEnum.LONG_TERM.getCode(), contractBaseList, apiContractBaseDTOList);

            APIContractOrderGroupDTO dto = new APIContractOrderGroupDTO();
            dto.setBizId(entity.getBizId());
            dto.setContractBaseList(apiContractBaseDTOList);
            apiContractOrderGroupDTOList.add(dto);
        }
        return ResultMode.success(apiContractOrderGroupDTOList);
    }

    /**
     * 通用处理主合同及其子合同的方法
     */
    private void processMainContract(Map<String, List<ContractBaseDTO>> contractsByNature,
                                     String natureKey,
                                     List<ContractBaseDTO> fullList,
                                     List<APIContractBaseDTO> targetList) {
        List<ContractBaseDTO> mainContracts = contractsByNature.getOrDefault(natureKey, Collections.emptyList());
        if (!mainContracts.isEmpty()) {
            APIContractBaseDTO dto = new APIContractBaseDTO();
                    ContractBaseDTO main = mainContracts.get(0);
            if(StringUtils.isEmpty(main.getMainContractId()) || main.getId().toString().equals(main.getMainContractId())){
                 dto = BeanUtil.toBean(main, APIContractBaseDTO.class);
            }
            List<ContractBaseDTO> children = fullList.stream()
                    .filter(c -> main.getId().toString().equals(c.getMainContractId()))
                    .collect(Collectors.toList());

            dto.setChildrenList(BeanUtil.copyToList(children, APIContractBaseDTO.class));
            targetList.add(dto);
        }
    }



    /**
     * 查看订单
     */
    public ResultMode<List<String>> contOrderQuery(PagingInfo<ContOrderQuery> pagingInfo) {

        PagingInfo<ContractQuery> queryPagingInfo = this.convertPagingInfo(pagingInfo, ContractQuery.class);
        // 透传
        log.info("根据订单查询大宗合同关联的订单编号,入参:{}", JSONUtil.toJsonStr(queryPagingInfo));
        ResponseMessage<List<ContractOrderDTO>> responseMessage = offLineContractInter.pageQueryOrderByContractId(queryPagingInfo);
        log.info("根据订单查询大宗合同关联的订单编号,返参:{}", JSONUtil.toJsonStr(responseMessage));
        if (!responseMessage.isSucceed()){
            log.error("根据合同id查询订单编码失败 {}",responseMessage.getMessage());
            return ResultMode.fail(CtpOrchPlatformExceptionEnum.MIDDLEWARE_CALL_ERROR.getCode(),responseMessage.getMessage());
        }
        List<String> collect = responseMessage.getModel().stream().map(ContractOrderDTO::getBusinessOrderId).collect(Collectors.toList());
        return ResultMode.successPageList(collect,responseMessage.getTotal());
    }

    private <S, T> PagingInfo<T> convertPagingInfo(PagingInfo<S> sourcePage, Class<T> targetClass) {
        T filterModel = (T)BeanUtil.copyProperties(sourcePage.getFilterModel(), targetClass, new String[0]);
        PagingInfo<T> targetPage = new PagingInfo<>();
        targetPage.setFilterModel(filterModel);
        targetPage.setCountTotal(sourcePage.getCountTotal());
        targetPage.setCurrentPage(sourcePage.getCurrentPage());
        targetPage.setPageLength(sourcePage.getPageLength());
        targetPage.setSort(sourcePage.getSort());
        return targetPage;
    }
}
