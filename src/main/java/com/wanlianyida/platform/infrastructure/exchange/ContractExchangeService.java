package com.wanlianyida.platform.infrastructure.exchange;

import cn.hutool.core.collection.IterUtil;
import com.wanlianyida.basecont.api.inter.OffLineContractInter;
import com.wanlianyida.basecont.api.model.dto.ContractSignArchiveDTO;
import com.wanlianyida.basecont.api.model.query.ContractSignArchiveQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 合同
 *
 * <AUTHOR>
 * @since 2024/12/11 17:23
 */
@Service
public class ContractExchangeService {

    @Resource
    private OffLineContractInter offLineContractInter;

    public ContractSignArchiveDTO queryContractBaseDTO(ContractSignArchiveQuery query) {
        PagingInfo<ContractSignArchiveQuery> pagingInfo = new PagingInfo<>();
        pagingInfo.setCurrentPage(1);
        pagingInfo.setPageLength(1);
        pagingInfo.setCountTotal(false);
        pagingInfo.setFilterModel(query);
        ResponseMessage<List<ContractSignArchiveDTO>> resultMode = offLineContractInter.pageFromBulkTrade(pagingInfo);
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return IterUtil.getFirst(resultMode.getModel());
    }

}
