package com.wanlianyida.platform.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.inter.*;
import com.wanlianyida.fssbasecontent.api.model.command.*;
import com.wanlianyida.fssbasecontent.api.model.dto.*;
import com.wanlianyida.fssbasecontent.api.model.query.AdSpaceListQuery;
import com.wanlianyida.fssbasecontent.api.model.query.BannerInfoListQuery;
import com.wanlianyida.fssbasecontent.api.model.query.ChInformationListQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.support.api.inter.SensitiveWordsInter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/02/08/09:18
 */
@Slf4j
@Service
public class BaseContentExchangeService {

    @Resource
    private AdSpaceInter adSpaceInter;
    @Resource
    private ChSiteInter chSiteInter;

    @Resource
    private BannerInfoInter bannerInfoInter;

    @Resource
    private ChCategoryCenterInter chCategoryCenterInter;


    @Resource
    private SensitiveWordsInter sensitiveWordsInter;


    @Resource
    private ChInformationInter chInformationInter;


    @Resource
    private SiteResourceInter siteResourceInter;

    public ResultMode<Boolean> addSpaceInterAdd(AdSpaceAddCommand command) {
        ResponseMessage<Boolean> responseMessage = adSpaceInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> addSpaceInterUpdate(AdSpaceUpdateCommand command) {
        ResponseMessage<Boolean> responseMessage = adSpaceInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> addSpaceInterDelete(AdSpaceDeleteCommand command) {
        ResponseMessage<Boolean> responseMessage = adSpaceInter.batchDelete(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<AdSpaceDTO>> addSpaceInterQueryList(AdSpaceListQuery query) {
        ResponseMessage<List<AdSpaceDTO>> responseMessage = adSpaceInter.queryList(query);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(BeanUtil.copyToList(responseMessage.getModel(), AdSpaceDTO.class), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<SiteDTO> chSiteInterQueryByPlatformCode(String platformCode) {
        ResponseMessage<SiteDTO> responseMessage = chSiteInter.queryByPlatformCode(platformCode);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterAdd(BannerInfoAddCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterUpdate(BannerInfoUpdateCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterDelete(BannerInfoDeleteCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.batchDelete(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> bannerInfoInterQueryPage(PagingInfo<BannerInfoListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<BannerInfoListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<?> responseMessage = bannerInfoInter.queryPage(pagingInfo);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<BannerDTO>> bannerInfoInterQueryList(Long relAdId) {
        ResponseMessage<List<BannerDTO>> responseMessage = bannerInfoInter.queryList(relAdId);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(BeanUtil.copyToList(responseMessage.getModel(), BannerDTO.class), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> bannerInfoInterBatchAudit(BannerAuditCommand command) {
        ResponseMessage<Boolean> responseMessage = bannerInfoInter.batchAudit(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<String> chInformationInterAdd(ChinformationAddICommand addCommand) {
        ResponseMessage<String> responseMessage = chInformationInter.add(addCommand);
        log.info("chInformationInterAdd:{}", responseMessage);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getMessage());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> chInformationInterUpdate(ChinformationUpdateICommand updateCommand) {
        ResponseMessage<?> responseMessage = chInformationInter.update(updateCommand);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<ChInformationListDTO>> chInformationInterList(PagingInfo<ChInformationListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<ChInformationListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<List<ChInformationListDTO>> responseMessage = chInformationInter.list(pagingInfo);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }


    public ResultMode<Boolean> siteResourceInterAdd(SiteResourceAddCommand command) {
        ResponseMessage<Boolean> responseMessage = siteResourceInter.add(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<Boolean> siteResourceInterUpdate(SiteResourceUpdateCommand command) {
        ResponseMessage<Boolean> responseMessage = siteResourceInter.update(command);
        if (responseMessage.isSucceed()) {
            return ResultMode.success(responseMessage.getModel());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<List<SiteResourceDTO>> siteResourceInterQueryList() {
        ResponseMessage<List<SiteResourceDTO>> responseMessage = siteResourceInter.queryList();
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

}
