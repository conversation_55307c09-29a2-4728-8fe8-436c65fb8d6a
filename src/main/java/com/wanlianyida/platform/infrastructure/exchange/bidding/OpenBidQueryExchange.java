package com.wanlianyida.platform.infrastructure.exchange.bidding;

import com.wanlianyida.bidding.api.inter.AttachmentInter;
import com.wanlianyida.bidding.api.inter.BidOpeningInter;
import com.wanlianyida.bidding.api.model.dto.CompressFileInfoDTO;
import com.wanlianyida.bidding.api.model.dto.OpenSupplierListDTO;
import com.wanlianyida.bidding.api.model.dto.OpeningSingPriceDTO;
import com.wanlianyida.bidding.api.model.dto.SupplierConfDTO;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.AttachmentDTO;
import com.wanlianyida.bidding.api.model.query.*;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/7 09:12
 */
@Slf4j
@Service
public class OpenBidQueryExchange {

    @Resource
    private BidOpeningInter bidOpeningInter;

    @Resource
    private AttachmentInter attachmentInter;

    /**
     * 不开标设置分页列表
     */
    public ResultMode<List<SupplierConfDTO>> pageQueryNobidSet(PagingInfo<SupplierConfQuery> query) {
        return bidOpeningInter.pageQueryNobidSet(query);
    }

    /**
     * 唱价分页列表
     */
    public ResultMode<List<OpeningSingPriceDTO>> pageQuerySingPrice(PagingInfo<OpeningSingPriceQuery> query) {
        return bidOpeningInter.pageQuerySingPrice(query);
    }

    /**
     * 按单个业务id 和 附件类型 查询附件列表
     */
    public ResultMode<List<AttachmentDTO>> queryList(AttachmentQuery query) {
        return attachmentInter.queryList(query);
    }

    /**
     * 开标记录
     */
    public ResultMode<List<OpenSupplierListDTO>> pageQueryOpenBidRecord(PagingInfo<SupplierConfQuery> query) {
        return bidOpeningInter.pageQueryOpenBidRecord(query);
    }

    /**
     * 供应商签到
     */
    public ResultMode<List<AttachmentDTO>> querySupplierSign(PagingInfo<ReviewAttachmentQuery> query) {
        return bidOpeningInter.querySupplierSign(query);
    }

    /**
     * 开标结束
     */
    public ResultMode<List<AttachmentDTO>> queryOpenbidEnd(PagingInfo<ReviewAttachmentQuery> query) {
        return bidOpeningInter.queryOpenbidEnd(query);
    }

    /**
     * 附件列表分页查询（按多个附件类型）
     */
    public ResultMode<List<AttachmentDTO>> pageQueryAttachment(PagingInfo<AttachmentPageQuery> pageQuery) {
        return attachmentInter.pageQueryAttachment(pageQuery);
    }

    public ResultMode<List<CompressFileInfoDTO>> querysupplierSignUrlList(ReviewAttachmentQuery query) {
        return bidOpeningInter.querysupplierSignUrlList(query);
    }

    public ResultMode<List<CompressFileInfoDTO>> queryOpenbidEndUrlList(ReviewAttachmentQuery query) {
        return bidOpeningInter.queryOpenbidEndUrlList(query);
    }
}
