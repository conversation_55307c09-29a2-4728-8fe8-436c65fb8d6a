package com.wanlianyida.platform.infrastructure.exchange.bidding;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.bidding.api.inter.*;
import com.wanlianyida.bidding.api.model.command.TenderPackageSupplierPackageDTO;
import com.wanlianyida.bidding.api.model.dto.*;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.ProjectTenderPackageDTO;
import com.wanlianyida.bidding.api.model.query.BiddingListQuery;
import com.wanlianyida.bidding.api.model.query.InviteQuery;
import com.wanlianyida.bidding.api.model.query.TenderConfirmListQuery;
import com.wanlianyida.bidding.api.model.query.TenderPackagePurchasePageQuery;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.EnterpriseAccountInter;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import com.wanlianyida.partner.api.model.dto.BusinessLicenseInfoDTO;
import com.wanlianyida.partner.api.model.dto.EnterpriseInfoDTO;
import com.wanlianyida.partner.api.model.dto.UmCompanyBusinessLicenseDTO;
import com.wanlianyida.platform.infrastructure.exception.CtpOrchPlatformExceptionEnum;
import com.wanlianyida.platform.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.platform.interfaces.model.dto.SupplierDetailsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/4 13:44
 */
@Slf4j
@Service
public class SupplierResponseExchange {

    @Resource
    private InviteInter inviteInter;

    @Resource
    private TenderRegisterInter tenderRegisterInter;

    @Resource
    private UmCompanyInter umCompanyInter;

    @Resource
    private BiddingInter biddingInter;

    @Resource
    private TenderPackagePurchaseInter tenderPackagePurchaseInter;

    @Resource
    private TenderPackageSupplierResponseInter tenderPackageSupplierResponseInter;

    @Resource
    private EnterpriseAccountInter enterpriseAccountInter;

    @Resource
    private TenderInformationInter tenderInformationInter;

    @Resource
    private TenderPackageInter tenderPackageInter;

    @Resource
    private UmCompanyInter companyInter;

    @Resource
    private UploadExchangeService uploadExchangeService;


    /**
     * 项目邀请-供应商邀请信息列表
     * @param pagingInfo
     * @return
     */
    public ResultMode querySupplierInvitePage(PagingInfo<InviteQuery> pagingInfo) {
        return inviteInter.getSuppliersByTenderPackageId(pagingInfo);
    }

    /**
     * 项目报名-供应商报名信息列表
     * @param pagingInfo
     * @return
     */
    public ResultMode querySupplierRegisterPage(PagingInfo<TenderConfirmListQuery> pagingInfo) {
        // return tenderRegisterInter.querySupplierRegisterPage(pagingInfo);
        return tenderRegisterInter.confirmList(pagingInfo);
    }

    /**
     * 项目报名-项目报名-报名信息-供应商详情
     * @param id
     * @return
     */
    public ResultMode<SupplierDetailsDTO> querySupplierDetails(Long id) {
        try {
            // 参数校验
            validateId(id);

            // 获取供应商响应详情
            TenderPackageSupplierPackageDTO detailDTO = getSupplierResponseDetail(id);

            // 获取公司信息
            EnterpriseInfoDTO companyDTO = getCompanyInfo(detailDTO.getSupplierResponse().getSupplierId());

            // 构建返回结果
            return buildSuccessResult(detailDTO, companyDTO);
        } catch (RuntimeException e) {
            log.error("查询供应商详情失败: {}", e.getMessage());
            return ResultMode.fail(e.getMessage());
        }
    }

    /**
     * 标书款项信息列表
     * @param
     * @return
     */
    public ResultMode<List<TenderPackagePurchaseListDTO>> queryBidpaymentPage(PagingInfo<TenderPackagePurchasePageQuery> pagingInfo) {
        return tenderPackagePurchaseInter.pageCondition(pagingInfo);
    }

    /**
     * 投标信息列表
     *  @param
     *  @return
     */
    public ResultMode<List<BiddingSupplierListDTO>> queryBidInfoPage(PagingInfo<BiddingListQuery> pagingInfo) {
        return biddingInter.queryBidInfoPage(pagingInfo);
    }


    private void validateId(Long id) {
        if (Objects.isNull(id)) {
            throw new RuntimeException(CtpOrchPlatformExceptionEnum.SUPPLIER_ID_ERROR.getMsg());
        }
    }

    private TenderPackageSupplierPackageDTO getSupplierResponseDetail(Long id) {
        TenderPackageSupplierPackageDTO detailDTO = new TenderPackageSupplierPackageDTO();

        ResultMode<TenderRegisterInfoDTO> result = tenderRegisterInter.tenderRegisterInfo(String.valueOf(id));
        if (!result.isSucceed() || ObjectUtil.isEmpty(result.getModel())) {
            log.error("查询供应商响应详情失败，ID: {}", id);
            throw new RuntimeException(CtpOrchPlatformExceptionEnum.SUPPLIER_DETAILS_ERROR.getMsg());
        }
        TenderPackageSupplierResponseDTO supplierResponse = new TenderPackageSupplierResponseDTO();
        supplierResponse.setProjectNo(result.getModel().getProjectNo());
        supplierResponse.setProjectName(result.getModel().getProjectName());
        supplierResponse.setTenderPackageName(result.getModel().getTenderPackageName());
        supplierResponse.setResponseDate(result.getModel().getRegisterTime());
        supplierResponse.setSupplierId(result.getModel().getSupplierId());
        // 获取报名截止时间
        IdQuery query = new IdQuery();
        query.setId(result.getModel().getRelTenderPackageId());
        ResultMode<ProjectTenderPackageDTO> packageResultMode = tenderPackageInter.queryDetailTenderPackageProject(query);
        if (!packageResultMode.isSucceed() || ObjectUtil.isEmpty(packageResultMode.getModel())) {
            log.error("查询供应商响应详情失败，ID: {}", id);
            throw new RuntimeException(CtpOrchPlatformExceptionEnum.SUPPLIER_DETAILS_ERROR.getMsg());
        }
        supplierResponse.setSaleEndTime(packageResultMode.getModel().getTenderPackageTime().getSaleEndTime());
        supplierResponse.setTenderNo(packageResultMode.getModel().getTenderPackage().getTenderNo());
        detailDTO.setSupplierResponse(supplierResponse);
        //联系信息
        TenderRegisterInfoDTO tenderRegisterInfo = new TenderRegisterInfoDTO();
        tenderRegisterInfo.setAddress(result.getModel().getAddress());
        tenderRegisterInfo.setContactUserName(result.getModel().getContactUserName());
        tenderRegisterInfo.setContactUserPhone(result.getModel().getContactUserPhone());
        tenderRegisterInfo.setContactUserMailbox(result.getModel().getContactUserMailbox());
        detailDTO.setTenderRegisterInfo(tenderRegisterInfo);
        return detailDTO;
    }

    private EnterpriseInfoDTO getCompanyInfo(String supplierId) {
//        EnterpriseQuery enterpriseQuery = new EnterpriseQuery();
//        enterpriseQuery.setCompanyId(supplierId);
//        ResultMode<EnterpriseInfoDTO> result = enterpriseAccountInter.getEntProfileDetailPage(enterpriseQuery);
        EnterpriseInfoDTO enterpriseInfoDTO = new EnterpriseInfoDTO();
        ResultMode<UmCompanyBusinessLicenseDTO> businessLicenseResult = companyInter.queryBusinessLicense(supplierId);
        if (!businessLicenseResult.isSucceed() || ObjectUtil.isEmpty(businessLicenseResult.getModel())) {
            log.error("查询供应商主数据失败");
            throw new RuntimeException(CtpOrchPlatformExceptionEnum.SUPPLIER_DETAILS_ERROR.getMsg());
        }
        BusinessLicenseInfoDTO businessLicenseInfo = BeanUtil.toBean(businessLicenseResult.getModel(), BusinessLicenseInfoDTO.class);
        businessLicenseInfo.setCompanyName(businessLicenseResult.getModel().getLicenseName());
        businessLicenseInfo.setSocialCreditCode(businessLicenseResult.getModel().getLicenseNo());
        businessLicenseInfo.setFileUrl(convertUrl(businessLicenseResult.getModel().getForntFileUrl()));
        enterpriseInfoDTO.setBusinessLicenseInfo(businessLicenseInfo);
        return enterpriseInfoDTO;
    }

    private ResultMode<SupplierDetailsDTO> buildSuccessResult(
            TenderPackageSupplierPackageDTO detailDTO,
            EnterpriseInfoDTO companyDTO) {
        SupplierDetailsDTO dto = SupplierDetailsDTO.builder()
                .tenderPackageSupplierResponseDetailDTO(detailDTO)
                .umCompanyDTO(companyDTO)
                .build();
        return ResultMode.success(dto);
    }

    public ResultMode<List<CompressFileInfoDTO>> queryBidpaymentUrlList(TenderPackagePurchasePageQuery query) {
        return tenderPackagePurchaseInter.queryBidpaymentUrlList(query);
    }

    private String convertUrl(String url){
        if(!StringUtils.isEmpty(url)){
            String fileUrl = uploadExchangeService.convertUrl(url);
            return fileUrl;
        }
        return null;
    }
}
