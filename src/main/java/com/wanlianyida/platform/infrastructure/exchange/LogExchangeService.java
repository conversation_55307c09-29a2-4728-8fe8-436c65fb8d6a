package com.wanlianyida.platform.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbaselog.api.inter.LogOperationRecordInter;
import com.wanlianyida.fssbaselog.api.model.command.LogOperationRecordCommand;
import com.wanlianyida.fssbaselog.api.model.dto.LogOperationRecordDTO;
import com.wanlianyida.fssbaselog.api.model.query.LogOperationRecordQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日志交换服务
 *
 * <AUTHOR>
 * @since 20250206
 */
@Slf4j
@Component
public class LogExchangeService {

    @Resource
    private LogOperationRecordInter logOperationRecordInter;

    public ResultMode<List<LogOperationRecordDTO>> queryPage(PagingInfo<LogOperationRecordQuery> query) {
        com.wanlianyida.fssmodel.PagingInfo<LogOperationRecordQuery> pagingInfo = BeanUtil.toBean(query, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(query.getFilterModel());
        long startTime = System.currentTimeMillis(); // 记录开始时间
        ResponseMessage<List<LogOperationRecordDTO>> responseMessage = logOperationRecordInter.queryPage(pagingInfo);
        long endTime = System.currentTimeMillis(); // 记录结束时间
        log.info("LogExchangeService.queryPage查询耗时: {} 毫秒", endTime - startTime); // 打印耗时
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    public ResultMode<?> add(LogOperationRecordCommand logTableOperationRecord) {
        ResponseMessage<?> responseMessage = logOperationRecordInter.add(logTableOperationRecord);
        if (responseMessage.isSucceed()) {
            return ResultMode.success();
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }
}
