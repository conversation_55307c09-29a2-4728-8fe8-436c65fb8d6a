package com.wanlianyida.platform.infrastructure.exchange;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.BdUserFeedbackInter;
import com.wanlianyida.support.api.model.command.BdUserFeedbackAndAttachReplyUpdateCommand;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackAndAttachDTO;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackDTO;
import com.wanlianyida.support.api.model.query.BdUserFeedbackQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@Component
public class BdUserFeedbackExchangeService {

    @Resource
    private BdUserFeedbackInter bdUserFeedbackInter;

    /**
     * 创建用户反馈回复
     */
    public ResultMode updateReplyFeedback(@Validated @RequestBody BdUserFeedbackAndAttachReplyUpdateCommand command){
        return bdUserFeedbackInter.updateReplyFeedback(command);
    };

    /**
     * 用户反馈列表
     */
    public ResultMode<List<BdUserFeedbackDTO>> pageList(@RequestBody PagingInfo<BdUserFeedbackQuery> pagingInfo){
        return bdUserFeedbackInter.pageList(pagingInfo);
    };


    /**
     * 用户反馈详情
     */
    public ResultMode<BdUserFeedbackAndAttachDTO> getDetail(@RequestBody @Valid BdUserFeedbackQuery query){
        return bdUserFeedbackInter.getDetail(query);
    };
}
