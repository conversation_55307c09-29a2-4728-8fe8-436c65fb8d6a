package com.wanlianyida.platform.infrastructure.exchange;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.utils.ResultModeUtil;
import com.wanlianyida.platform.interfaces.model.dto.PlatFormProductSkuDetailDTO;
import com.wanlianyida.platform.interfaces.model.dto.PlatFormProductSpuDetailDTO;
import com.wanlianyida.product.api.inter.InventoryInter;
import com.wanlianyida.product.api.inter.ProductInter;
import com.wanlianyida.product.api.model.command.ProductCheckCommand;
import com.wanlianyida.product.api.model.dto.ProductCheckDTO;
import com.wanlianyida.product.api.model.dto.ProductInventoryDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuPortalDetailDTO;
import com.wanlianyida.product.api.model.query.ProductInventoryQuery;
import com.wanlianyida.product.api.model.query.ProductPortalQuery;
import com.wanlianyida.product.api.model.query.ProductSkuQuery;
import com.wanlianyida.product.api.model.query.ProductSpuQuery;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

@Service
public class ProductExchangeService {

    @Resource
    private InventoryInter inventoryInter;
    @Resource
    private ProductInter productInter;

    public ResultMode<ProductCheckDTO> checkProduct(ProductCheckCommand checkCommand) {
        ResultMode<ProductCheckDTO> resultMode = productInter.checkProductStock(checkCommand);
        return resultMode;
    }

    public List<ProductInventoryDTO> queryProductInventory(List<String> skuCodeList) {
        ProductInventoryQuery query = new ProductInventoryQuery();
        query.setSkuCodeList(skuCodeList);
        ResultMode<List<ProductInventoryDTO>> result = inventoryInter.queryBySkuCodeList(query);
        if (!result.isSucceed() || CollectionUtil.isEmpty(result.getModel())) {
            return Collections.EMPTY_LIST;
        }
        return result.getModel();
    }

    public ResultMode<ProductSkuPortalDetailDTO> querySkuPortalDetail(ProductPortalQuery query) {
        return productInter.querySkuPortalDetail(query);
    }

    /**
     * 查询sku信息
     * @param skuCodes
     * @return
     */
    public List<ProductSkuDetailDTO> queryProductSkuDetail(List<String> skuCodes){
        if(CollUtil.isEmpty(skuCodes)){
            return null;
        }
        ProductSkuQuery query = new ProductSkuQuery();
        query.setSkuCodeList(skuCodes);
        ResultMode<List<ProductSkuDetailDTO>> resultMode = productInter.innerQueryProductSkuDetail(query);
        if(ObjUtil.isNull(resultMode) || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return resultMode.getModel();
    }

    /**
     * 查询spu信息
     * @param spuCodes
     * @return
     */
    public List<PlatFormProductSpuDetailDTO> queryRfqProductSpuDetail(List<String> spuCodes){
        if(CollUtil.isEmpty(spuCodes)){
            return null;
        }
        ProductSpuQuery query = new ProductSpuQuery();
        query.setSpuCodeList(spuCodes);
        ResultMode<List<PlatFormProductSpuDetailDTO>> resultMode = ResultModeUtil.convertResultModeList(productInter.queryProductSpuDetail(query), PlatFormProductSpuDetailDTO.class);
        if(ObjUtil.isNull(resultMode) || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return resultMode.getModel();
    }

    /**
     * 查询sku信息
     * @param skuCodes
     * @return
     */
    public List<PlatFormProductSkuDetailDTO> queryRfqProductSkuDetail(List<String> skuCodes){
        if(CollUtil.isEmpty(skuCodes)){
            return null;
        }
        ProductSkuQuery query = new ProductSkuQuery();
        query.setSkuCodeList(skuCodes);
        ResultMode<List<PlatFormProductSkuDetailDTO>> resultMode = ResultModeUtil.convertResultModeList(productInter.innerQueryProductSkuDetail(query), PlatFormProductSkuDetailDTO.class);
        if(ObjUtil.isNull(resultMode) || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return resultMode.getModel();
    }
}
