package com.wanlianyida.platform.interfaces.model.query;

import lombok.Data;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserPermissionFunctionsQuery {

    /**
     * 角色组管理表
     */
    private Long permissionId;
    /**
     * 公司主键
     */
    private String companyId;
    /**
     * 账号id
     */
    private String accountId;
    /**
     * 用户基本信息主键
     */
    private String userBaseId;
    /**
     * 角色名称
     */
    private String permissionName;
    /**
     * appId:默认5
     */
    private String appId;
    /**
     * 系统列别
     */
    private String sysType;
}
