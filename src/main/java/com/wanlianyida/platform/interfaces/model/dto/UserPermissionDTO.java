package com.wanlianyida.platform.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserPermissionDTO {


    /**
     * 角色组管理表
     */
    private Long permissionId;
    /**
     * 公司主键
     */
    private String companyId;
    /**
     * 用户基本信息主键
     */
    private String userBaseId;

    /**
     * 角色名称
     */
    private String permissionName;

    /**
     * 角色代码
     */
    private String permissionCode;


    /**
     * 系统列别
     */
    private String sysType;

    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    /**
     * 修改人
     */
    private String modifyBy;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;

    /**
     * 用于展示根据账户id查询 角色名称
     */
    private String ex;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 预留字段
     */
    private String item1;

    /**
     * 预留字段
     */
    private String item2;

    /**
     * 预留字段
     */
    private String item3;

    /**
     * 预留字段
     */
    private String item4;
}
