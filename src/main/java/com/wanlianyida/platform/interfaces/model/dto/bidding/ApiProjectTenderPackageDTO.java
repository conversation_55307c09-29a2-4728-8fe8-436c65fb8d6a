package com.wanlianyida.platform.interfaces.model.dto.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.AttachmentDTO;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.TenderPackageContentDTO;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.TenderPackageDTO;
import com.wanlianyida.bidding.api.model.dto.tenderpackageissue.TenderPackageTimeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 项目标包详情
 */
@Data
public class ApiProjectTenderPackageDTO {

    @ApiModelProperty("项目基本信息")
    private ProjectDto projectDto;

    @ApiModelProperty("标包")
    TenderPackageDTO tenderPackage;

    @ApiModelProperty("标包时间信息")
    TenderPackageTimeDTO tenderPackageTime;

    @ApiModelProperty("附件列表")
    List<AttachmentDTO> attachmentList;

    @ApiModelProperty("内容信息列表")
    List<TenderPackageContentDTO> tenderPackageContentList;
    /**
     * 项目基本信息
     */
    @Data
    public static class ProjectDto {

        /**
         * id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 项目状态 10待提交 20审批中 30立项完成 40审批驳回
         */
        private Integer projectStatus;

        /**
         * 项目编号
         */
        private String projectNo;

        /**
         * 项目名称
         */
        private String projectName;

        /**
         * 所属年份
         */
        private Integer yearNo;

        /**
         * 采购公司主体id
         */
        private String purchaseCompanyId;

        /**
         * 采购公司主体名称
         */
        private String purchaseCompanyName;

        /**
         * 项目经理用户id
         */
        private String projectManagerId;

        /**
         * 项目经理用户名
         */
        private String projectManagerName;

        /**
         * 项目说明
         */
        private String projectDesc;

        /**
         * 部门id
         */
        private Integer purchaseDeptId;

        /**
         * 部门名称
         */
        private String purchaseDeptName;

        /**
         * 需求接收时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private LocalDate receiveDemandDate;

        /**
         * 预算金额(元)
         */
        private BigDecimal budgetAmount;

        /**
         * 是否含税 0否 1是
         */
        private Integer taxFlag;

        /**
         * 创建部门id
         */
        private Integer creatorDeptId;

        /**
         * 创建部门名称
         */
        private String creatorDeptName;

        /**
         * 创建人姓名
         */
        private String creatorName;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createdDate;

    }

}
