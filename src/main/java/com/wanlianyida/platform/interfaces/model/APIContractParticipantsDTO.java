package com.wanlianyida.platform.interfaces.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 大宗线下合同参与方 DTO
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
public class APIContractParticipantsDTO {


	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 合同信息id
	 */
    @JsonSerialize(using = ToStringSerializer.class)
	private Long contractId;

	/**
	 * 名称
	 */
	private String partsName;

    /**
     * 简称
     */
    private String partsShortName;

	/**
	 * 证件号
	 */
	private String certNumber;

	/**
	 * 参与方类型:10甲方,20乙方,30丙方
	 */
	private String partsType;

    /**
     * 参与方用户类型:10企业,20个人
     */
    private String partsUserType;

	/**
	 * 企业id/用户id
	 */
	private String partsId;

	/**
	 * 确认状态:10待确认,20已确认
	 */
	private String confirmStatus;

}
