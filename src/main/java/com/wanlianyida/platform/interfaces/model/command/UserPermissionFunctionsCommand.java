package com.wanlianyida.platform.interfaces.model.command;

import lombok.Data;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserPermissionFunctionsCommand {
    /**
     * 角色组管理表
     */
    private Long permissionId;

    /**
     * 角色名称
     */
    private String permissionName;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 权限标识
     */
    private boolean exFlag;

    /**
     * 系统类型
     */
    private String sysType;

    /**
     * 功能ID集合
     */
    private List<Long> funcIdList;
}
