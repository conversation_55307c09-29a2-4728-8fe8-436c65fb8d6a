package com.wanlianyida.platform.interfaces.model.dto.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wanlianyida.framework.ctpcommon.model.dto.FacadeBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 标包列表分页返回DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApiPageQueryPackageDTO extends FacadeBaseDTO {

    /**
     * 项目id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long projectId;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 标包id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tenderPackageId;

    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 标包编号
     */
    private String tenderNo;

    /**
     * 采购方式
     */
    private String purchaseMethod;

    /**
     * 所属年份
     */
    private Integer yearNo;

    /**
     * 创建部门id
     */
    private Integer creatorDeptId;

    /**
     * 创建部门名称
     */
    private String creatorDeptName;

    /**
     * 需求单位部门id
     */
    private Integer purchaseDeptId;

    /**
     * 需求单位部门名称
     */
    private String purchaseDeptName;

    /**
     * 招标负责人id
     */
    private String tenderLeaderId;

    /**
     * 招标负责人姓名
     */
    private String tenderLeaderName;

    /**
     * 采购公司主体名称
     */
    private String purchaseCompanyName;

    /**
     * 标包子阶段
     */
    private Integer tenderSubStage;

    /**
     * 项目经理用户id
     */
    private String projectManagerId;

    /**
     * 项目经理用户名
     */
    private String projectManagerName;

    /**
     * 预算金额(元)
     */
    private BigDecimal budgetAmount;

    /**
     * 是否含税 0否 1是
     */
    private Integer taxFlag;

    /**
     * 是否线下递交投标文件 0否 1是
     */
    private Integer offlineSubmit;

    /**
     * 审核供应商报名标识 0否 1是
     */
    private Integer reviewSupplierSignupFlag;

    /**
     * 评标方式 10线上 20线下
     */
    private String tenderEvaluationMethod;

    /**
     * 创建时间，有标包取标包创建时间，没有标包取项目创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 标包创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packageCreatedDate;

    /**
     * 项目创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date projectCreatedDate;

    /**
     * 立项时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date projectApprovalTime;

    /**
     * 标书售卖开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date saleStartTime;

    /**
     * 标书售卖截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date saleEndTime;

    /**
     * 投标截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidEndTime;

    /**
     * 开标时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidOpeningTime;

    /**
     * 标包结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packageEndTime;
}
