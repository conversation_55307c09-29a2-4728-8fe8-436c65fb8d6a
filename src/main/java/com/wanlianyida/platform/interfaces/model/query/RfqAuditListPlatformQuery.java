package com.wanlianyida.platform.interfaces.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月09日 10:01
 */
@Data
public class RfqAuditListPlatformQuery {


    @ApiModelProperty("询比价单状态[10-待审核,20-待开始,60-审核驳回,30-询价中,40-已中标,50-已流标]")
    private Integer rfqStatus;

    @ApiModelProperty("询比价单状态列表")
    private List<Integer> rfaStatusList;

    @ApiModelProperty("询比价单号")
    private String rfqNo;

    @ApiModelProperty("发布企业id")
    private String publishCompanyId;

    @ApiModelProperty("发布企业名称")
    private String publishCompanyName;

    @ApiModelProperty("询比价单范围[10-定向]")
    private Integer rfqScope;

    @ApiModelProperty("询比价类型[10-销售,20-采购]")
    private Integer rfqType;


}
