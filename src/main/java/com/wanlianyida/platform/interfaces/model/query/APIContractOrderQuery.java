package com.wanlianyida.platform.interfaces.model.query;

import com.wanlianyida.framework.ctpcommon.model.query.FacadeBaseQuery;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 大宗线下合同-订单Query
 */
@Data
public class APIContractOrderQuery extends FacadeBaseQuery {

    /**
     * 业务单号
     */
    @NotEmpty(message = "订单号不能为空")
    private List<String> bizIds;
}
