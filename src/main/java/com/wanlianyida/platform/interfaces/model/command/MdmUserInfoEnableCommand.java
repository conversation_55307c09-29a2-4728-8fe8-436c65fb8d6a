package com.wanlianyida.platform.interfaces.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 用户信息表 entity
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
public class MdmUserInfoEnableCommand {

	@ApiModelProperty("用户id")
	@NotNull(message = "用户id不能为空")
	private Long id;

	@ApiModelProperty("是否启用:1是,0否")
	@NotBlank(message = "启用状态不能为空")
	private String enableFlag;

	@ApiModelProperty("停用原因")
	private String enableReason;
}
