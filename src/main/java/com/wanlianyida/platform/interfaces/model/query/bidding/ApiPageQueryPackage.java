package com.wanlianyida.platform.interfaces.model.query.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.ctpcommon.model.query.FacadeBaseQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 标包列表分页查询你
 */
@Data
public class ApiPageQueryPackage extends FacadeBaseQuery {

    /**
     * 标包名称模糊查询
     */
    private String packageNameLike;

    /**
     * 招标编号模糊查询
     */
    private String tenderNoLike;

    /**
     * 项目名称模糊查询
     */
    private String projectNameLike;

    /**
     * 标包子阶段
     */
    private Integer tenderSubStage;

    /**
     * 创建部门id
     */
    private List<Long> creatorDeptIdList;

    /**
     * 立项时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date projectApprovalTimeStart;

    /**
     * 立项时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date projectApprovalTimeEnd;

    /**
     * 需求单位/部门
     */
    private List<Long> purchaseDeptIdList;

    /**
     * 招标负责人姓名
     */
    private String tenderLeaderNameLike;

    /**
     * 项目结束-开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packageEndTimeStart;

    /**
     * 项目结束-结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packageEndTimeEnd;

    /**
     * 采购方式 10公开竞谈 20邀请竞谈
     */
    private String purchaseMethod;

    /**
     * 标包子阶段集合
     */
    private List<Integer> tenderSubStageList;

}
