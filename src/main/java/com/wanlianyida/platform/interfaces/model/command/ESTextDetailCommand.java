package com.wanlianyida.platform.interfaces.model.command;

import lombok.Data;

import java.util.List;

/**
 * 描述: 自动审核日志记录es文本详情
 */
@Data
public class ESTextDetailCommand {

    /**
     * 列表直接展示（备注）
     */
    private String operateContent;

    /**
     * json串
     */
    private String oldValue;

    /**
     * json 数组/对象
     */
    private String newValueList;

    /**
     * 改变对象集合
     */
    private List<ChangeObject> changeList;

    @Data
    class ChangeObject {

        private String filedKey;

        private String filedName;

        private String oldValue;

        private String newValue;

    }

}
