package com.wanlianyida.platform.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wanlianyida.framework.ctpcommon.model.dto.FacadeBaseDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 大宗线下合同 DTO
 *
 * <AUTHOR>
 * @date 2025/7/17
 */
@Data
public class APIContractBaseDTO extends FacadeBaseDTO {


	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 合同类型:dicid=20241015180000100001
	 */
	private String contractType;

    /**
     * 合同类型名称:dicid=20241015180000100001
     */
    private String contractTypeName;

	/**
	 * 合同模板:dicid=20241015180000100012
	 */
	private String contractTemplate;

	/**
	 * 合同名称
	 */
	private String contractName;

	/**
	 * 合同编号
	 */
	private String contractNumber;

	/**
	 * 签订日期
	 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date signDate;

	/**
	 * 有效期起
	 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date effectiveDateStart;

	/**
	 * 有效期止
	 */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date effectiveDateEnd;

	/**
	 * 合同状态:dicid=20241015180000100018
	 */
	private String contractStatus;

    /**
     * 合同状态名称:dicid=20241015180000100018
     */
    private String contractStatusName;

	/**
	 * 审核意见
	 */
	private String auditOpinion;

	/**
	 * 是否有效:1是,0否
	 */
	private String validFlag;

	/**
	 * 创建人id
	 */
	private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

    /**
     * 甲方信息
     */
    private List<APIContractParticipantsDTO> contractFirstPartsVOs;

    /**
     * 乙方信息
     */
    private List<APIContractParticipantsDTO> contractSecondPartsVOs;

    /**
     * 有效期止到期提醒：1是0否
     */
    private Integer effectiveDateEndTip = 0;

	/**
	 *合同附件
	 */
	private List<APIContractBaseAttachmentDTO> attachmentList;

    /**
     *  大宗合同性质:10单笔,20长期,30单笔合同补充协议,40长期合同补充协议
     */
    private String contractNature;

    /**
     * 大宗合同性质名称
     */
    private String contractNatureName;

	/**
	 * 大宗合同签署方式名称
	 */
	private String signModeName;

	/**
	 * 大宗合同关联订单是否取消
	 */
	private Boolean orderCancelStatus = false;


	/**
	 * 主合同id
	 */
	private String mainContractId;


	/**
	 * 主合同编号
	 */
	private String mainContractNo;

	private List<APIContractBaseDTO> childrenList;

}
