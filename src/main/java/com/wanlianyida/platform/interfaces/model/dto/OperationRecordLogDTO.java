package com.wanlianyida.platform.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class OperationRecordLogDTO {

    /**
     * 业务对象id，（司机id、车辆id、商品id）
     */
    private String bizId;
    /**
     * 业务对象类型，（司机、车辆、商品）
     */
    private String bizType;
    /**
     * 用户id
     */
    private String userBaseId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户组id，（企业id，部门id）
     */
    private String userGroupId;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户组名
     */
    private String userGroupName;
    /**
     * 操作时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;
    /**
     * 操作类型，（新增、修改、删除）
     */
    private String operateType;
    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 附件列表
     */
    private List<Attachment> attachmentList;

    /**
     * 详情id
     */
    private String detailId;

    @Data
    public static class Attachment {
        /**
         * 附件url
         */
        private String fileUrl;

        /**
         * 附件名
         */
        private String fileName;
    }

}
