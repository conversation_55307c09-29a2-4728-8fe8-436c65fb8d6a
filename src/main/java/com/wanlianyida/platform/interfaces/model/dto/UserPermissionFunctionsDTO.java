package com.wanlianyida.platform.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserPermissionFunctionsDTO {


    /**
     * 角色组管理表
     */
    private Long permissionId;
    /**
     * 公司主键
     */
    private String companyId;
    /**
     * 用户基本信息主键
     */
    private String userBaseId;

    /**
     * 角色名称
     */
    private String permissionName;

    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    /**
     * 修改人
     */
    private String modifyBy;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;
}
