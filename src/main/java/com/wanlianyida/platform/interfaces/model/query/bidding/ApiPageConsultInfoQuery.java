package com.wanlianyida.platform.interfaces.model.query.bidding;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

@Data
public class ApiPageConsultInfoQuery {

    @NotNull(message = "标包id不能为空")
    @ApiModelProperty("标包id")
    private Long tenderPackageId;

    @ApiModelProperty("供应商账号")
    private String supplierAccount;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("咨询开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String consultTimeStart;

    @ApiModelProperty("咨询结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String consultTimeEnd;

}
