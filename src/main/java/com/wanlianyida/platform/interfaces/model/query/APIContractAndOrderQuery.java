package com.wanlianyida.platform.interfaces.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.ctpcommon.model.query.FacadeBaseQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 大宗线下合同-订单+合同字段 Query
 */
@Data
public class APIContractAndOrderQuery extends FacadeBaseQuery {

    /**
     * 合同编号
     */
    private String contractNumber;


    /**
     * 订单编号
     */
    private String businessOrderId;


    /**
     * 签订日期起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDateStart;

    /**
     * 签订日期止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDateEnd;


    /**
     * 合同状态
     */
    private String contractStatus;


    /**
     * 大宗合同性质:10单笔,20长期,30单笔合同补充协议,40长期合同补充协议
     */
    private String contractNature;



    /**
     * 合同相关方企业socialCreditCode
     */
    private String relatedSocialCreditCode;


    /**
     * 大宗甲方企业socialCreditCode  买方
     */
    private String firstPartsSocialCreditCode;

    /**
     * 大宗乙方企业socialCreditCode 卖方
     */
    private String secondPartsSocialCreditCode;

}
