package com.wanlianyida.platform.interfaces.model.command;

import lombok.Data;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserLoginInfoCommand {

    /**
     * 用户id
     */
    private String userBaseId;
    /**
     * 登录密码
     */
    private String password;
    /**
     * 确认密码
     */
    private String confirmNewPassword;

    /**
     * 新密码
     */
    private String newPassword;


    /**
     * 登录账号
     */
    private String loginName;
    /**
     * 登录标识(web:1-4PL登录,2-3PL登录 app:1-密码登录,2-验证码登录)
     */
    private String exLoginFlag;

}
