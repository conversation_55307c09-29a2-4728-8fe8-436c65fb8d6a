package com.wanlianyida.platform.interfaces.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MsgTemplateCommand {
    /**
     * 消息模版id
     */
    @NotNull
    private String templateId;
    /**
     * 消息模板标题
     */
    @NotNull
    private String templateTitle;
    /**
     * 消息模板内容
     */
    @NotNull
    private String templateContent;

    /**
     * 消息模板状态【checkbox:11-启用,21-禁用】
     */
    @NotNull
    private int status;

    /**
     * 消息模版类型【checkbox:1-短信,2-邮件,3-app,4-微信,5-站内信】
     */
    @NotNull
    private String templateType;

    /**
     * 消息类型id
     */
    @NotNull
    private String msgTypeId;

    /**
     * 消息类型名称
     */
    @NotNull
    private String msgTypeName;

    /**
     * 触发条件
     */
    @NotNull
    private String triggerCondition;

}
