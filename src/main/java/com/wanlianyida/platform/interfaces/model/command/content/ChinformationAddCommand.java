package com.wanlianyida.platform.interfaces.model.command.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/23/11:40
 */
@Data
public class ChinformationAddCommand {

    /**
     * 业务大类 10资讯分类 20帮助 30平台规则40公告
     */
    private String bussCategory;

    /**
     * 类别ID
     */
    private Long relCategoryId;

    /**
     * 标签列表ID
     */
    private List<Long> tagsIds;

    /**
     * 标签列表名称
     */
    private List<String> tagsName;
    /**
     * 内容封面图url
     */
    private String imageCoverUrl;

    /**
     * 图片名称
     */
    private String imageName;

    /**
     * 标题
     */
    private String articleTitle;

    /**
     * 显示顺序(排序数字)
     */
    private Integer sortNumber;

    /**
     * 文章来源
     */
    private String articleSource;

    /**
     * 文章作者
     */
    private String articleWriter;

    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date articleTime;

    /**
     * "评论开关 1 开 0关
     */
    private Short commentsAllow;


    /**
     * 调用别名
     */
    private String callAlias;

    /**
     * 内容摘要
     */
    private String contentSummary;


    /**
     * 内容详情
     */
    private String contentText;


}
