package com.wanlianyida.platform.interfaces.facade.bidding;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.bidding.TenderAppService;
import com.wanlianyida.platform.interfaces.model.dto.bidding.ApiPageQueryPackageDTO;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageQueryPackage;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 招采管理接口
 */
@RestController
@RequestMapping("/tender")
public class TenderController {

    @Resource
    private TenderAppService tenderAppService;

    /**
     * 标包列表分页查询
     */
    @RequestMapping("/page-query-package")
    public ResultMode<List<ApiPageQueryPackageDTO>> pageQueryPackage(@RequestBody PagingInfo<ApiPageQueryPackage> pageQueryPackage) {
        return tenderAppService.pageQueryPackage(pageQueryPackage);
    }

}
