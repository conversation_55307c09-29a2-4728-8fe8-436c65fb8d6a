package com.wanlianyida.platform.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.product.ProductCategoryConfigAppService;
import com.wanlianyida.product.api.model.command.RelateAttributesCommand;
import com.wanlianyida.product.api.model.command.RelateBrandCommand;
import com.wanlianyida.product.api.model.dto.CategoryRelatedAttributeDTO;
import com.wanlianyida.product.api.model.dto.CategoryRelatedBrandDTO;
import com.wanlianyida.product.api.model.dto.ProductCategoryConfigPageDTO;
import com.wanlianyida.product.api.model.query.ProductCategoryConfigPageQuery;
import com.wanlianyida.product.api.model.query.RelatedAttributeQuery;
import com.wanlianyida.product.api.model.query.RelatedBrandQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商品品类配置（关联）
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product/category/relation")
@Validated
public class ProductCategoryConfigController {

    @Resource
    private ProductCategoryConfigAppService categoryConfigAppService;

    /**
     * 获取已经关联的规格列表信息
     */
    @PostMapping(value = {"/queryRelatedAttributes"})
    public ResultMode<List<CategoryRelatedAttributeDTO>> queryRelatedAttributes(@RequestBody @Valid RelatedAttributeQuery query) {
        return categoryConfigAppService.queryRelatedAttributes(query);
    }

    /**
     * 获取已经关联的品牌列表信息
     */
    @PostMapping(value = {"/queryRelatedBrands"})
    public ResultMode<List<CategoryRelatedBrandDTO>> queryRelatedBrands(@RequestBody @Valid RelatedBrandQuery query) {
        return categoryConfigAppService.queryRelatedBrands(query);
    }

    /**
     * 关联规格
     * @param commandList 参数
     */
    @PostMapping(value = {"/relateAttributes"})
    public ResultMode<?> relateAttributes(@RequestBody @Valid List<RelateAttributesCommand> commandList) {
        return categoryConfigAppService.relateAttributes(commandList);
    }

    /**
     * 关联品牌
     */
    @PostMapping(value = {"/relateBrands"})
    public ResultMode<?> relateBrands(@RequestBody @Valid RelateBrandCommand command) {
        return categoryConfigAppService.relateBrands(command);
    }

    /**
     * 分页查询商品品类配置
     * @param query 分页参数
     */
    @PostMapping(value = {"/pageQuery"})
    public ResultMode<List<ProductCategoryConfigPageDTO>> pageQuery(@RequestBody PagingInfo<ProductCategoryConfigPageQuery> query) {
        return categoryConfigAppService.pageQuery(query);
    }

}
