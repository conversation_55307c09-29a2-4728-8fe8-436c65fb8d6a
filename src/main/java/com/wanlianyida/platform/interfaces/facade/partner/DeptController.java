package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.DeptAddCommand;
import com.wanlianyida.partner.api.model.command.DeptDeleteCommand;
import com.wanlianyida.partner.api.model.command.DeptUpdateCommand;
import com.wanlianyida.partner.api.model.dto.OrgAndDeptTreeDTO;
import com.wanlianyida.partner.api.model.query.DeptTreeQuery;
import com.wanlianyida.platform.application.service.partner.DeptAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 部门管理
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@RestController
@RequestMapping("/partner/um/dept")
public class DeptController {
    @Resource
    private DeptAppService appService;

    /**
     * 部门新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/add")
    public ResultMode<Boolean> add(@RequestBody @Validated DeptAddCommand command){
        return appService.add(command);
    }

    /**
     * 部门更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/update")
    public ResultMode<Boolean> update(@RequestBody @Validated DeptUpdateCommand command){
        return appService.update(command);
    }

    /**
     * 部门删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/delete")
    public ResultMode<Boolean> delete(@RequestBody @Validated DeptDeleteCommand command){
        return appService.delete(command);
    }

    /**
     * 构建部门树
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link OrgAndDeptTreeDTO }>
     */
    @PostMapping("/buildDeptTree")
    public ResultMode<OrgAndDeptTreeDTO> buildDeptTree(@RequestBody @Validated DeptTreeQuery query){
        return appService.buildDeptTree(query);
    }
}
