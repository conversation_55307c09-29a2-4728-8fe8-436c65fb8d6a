package com.wanlianyida.platform.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.StatisticsCountAppService;
import com.wanlianyida.support.api.model.query.BdStatisticsCountQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/03/13
 *  pv uv统计
 */
@RestController
@RequestMapping("/statisticsCount")
public class StatisticsCountController {
    @Resource
    private StatisticsCountAppService appService;

    /**
     * 根据业务类型查询pv uv
     * @param pagingInfo
     * @return
     */
    @PostMapping("/queryStatisticsCountByType")
    public ResultMode queryStatisticsCountByType(@RequestBody PagingInfo<BdStatisticsCountQuery> pagingInfo){
        return appService.queryStatisticsCountByType(pagingInfo);
    }

}
