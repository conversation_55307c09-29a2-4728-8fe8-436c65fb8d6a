package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.PostAddCommand;
import com.wanlianyida.partner.api.model.command.PostDeleteCommand;
import com.wanlianyida.partner.api.model.command.PostUpdateCommand;
import com.wanlianyida.partner.api.model.query.PostPageQuery;
import com.wanlianyida.platform.application.service.partner.PostAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 岗位管理
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@RestController
@RequestMapping("/partner/um/post")
public class PostController {
    @Resource
    private PostAppService appService;

    /**
     * 岗位新增
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    @PostMapping("/add")
    public ResultMode<Boolean> add(@RequestBody @Validated PostAddCommand command){
        return appService.add(command);
    }

    /**
     * 岗位更新
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    @PostMapping("/update")
    public ResultMode<Boolean> update(@RequestBody @Validated PostUpdateCommand command){
        return appService.update(command);
    }

    /**
     * 岗位删除
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    @PostMapping("/delete")
    public ResultMode<Boolean> delete(@RequestBody @Validated PostDeleteCommand command){
        return appService.delete(command);
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 寻呼信息
     * @return {@code ResultMode }
     */
    @PostMapping("/queryPage")
    public ResultMode queryPage(@RequestBody @Validated PagingInfo<PostPageQuery> pagingInfo){
        return appService.queryPage(pagingInfo);
    }

}
