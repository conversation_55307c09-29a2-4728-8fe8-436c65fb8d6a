package com.wanlianyida.platform.interfaces.facade.bidding;

import com.wanlianyida.bidding.api.model.dto.ProjectPackageListDTO;
import com.wanlianyida.bidding.api.model.dto.ProjectProductDto;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.bidding.ProjectInitiationAppService;
import com.wanlianyida.platform.interfaces.model.dto.bidding.ApiProjectTenderPackageDTO;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageProjectProductQuery;
import com.wanlianyida.platform.interfaces.model.query.bidding.ApiPageTenderPackageQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

/**
 * 立项阶段
 */
@Api(value = "立项阶段")
@RequestMapping(value = "/project/initiation")
@RestController
public class ProjectInitiationController {

    @Autowired
    private ProjectInitiationAppService projectInitiationAppService;

    @ApiOperation("标包详情基本信息")
    @PostMapping("/query-detail-tender-package")
    public ResultMode<ApiProjectTenderPackageDTO> queryDetailTenderPackageProject(@RequestBody @Valid IdQuery query) {
        return projectInitiationAppService.queryDetailTenderPackageProject(query);
    }

    @ApiOperation("项目商品列表-分页")
    @PostMapping("/page-query-project-product")
    public ResultMode<List<ProjectProductDto>> queryProductPage(@RequestBody @Validated PagingInfo<ApiPageProjectProductQuery> pagingInfoQuery) {
        return projectInitiationAppService.queryProductPage(pagingInfoQuery);
    }



    @ApiOperation("项目标包列表-分页")
    @PostMapping("/page-query-project-package")
    public ResultMode<List<ProjectPackageListDTO>> queryProjectPackagePage(@RequestBody @Validated PagingInfo<ApiPageTenderPackageQuery> pagingInfoQuery) {
        return projectInitiationAppService.queryProjectPackagePage(pagingInfoQuery);
    }

}
