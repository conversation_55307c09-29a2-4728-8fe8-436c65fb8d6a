package com.wanlianyida.platform.interfaces.facade.settlement;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.settlement.SettPaymentAppService;
import com.wanlianyida.sett.api.model.dto.SettPaymentDTO;
import com.wanlianyida.sett.api.model.dto.SettPaymentDetailDTO;
import com.wanlianyida.sett.api.model.enums.SystemSourceEnum;
import com.wanlianyida.sett.api.model.query.SettPaymentDetailQuery;
import com.wanlianyida.sett.api.model.query.SettPaymentListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 *
 * 付款单接口
 */
@RestController
@RequestMapping("/sett/settPayment")
public class SettPaymentController {

    @Resource
    private SettPaymentAppService settPaymentAppService;

    /**
     * 查询付款单分页列表
     * @param query
     * @return
     */
    @PostMapping("/querySettPaymentPage")
    public ResultMode<List<SettPaymentDTO>> querySettPaymentPage(@Validated @RequestBody PagingInfo<SettPaymentListQuery> query) {
        query.getFilterModel().setSource(SystemSourceEnum.PLATFORM.getCode());
        return settPaymentAppService.querySettPaymentPage(query);
    }
    /**
     * 查询付款单分页导出列表
     * @param query
     * @return
     */
    @PostMapping("/querySettPaymentexportPage")
    public ResultMode<List<SettPaymentDTO>> querySettPaymentexportPage(@Validated @RequestBody PagingInfo<SettPaymentListQuery> query) {
        query.getFilterModel().setSource(SystemSourceEnum.PLATFORM.getCode());
        return settPaymentAppService.querySettPaymentexportPage(query);
    }
    /**
     * 付款单详情页查询
     */
    @PostMapping("/queryPaymentDetailsAfterSubmit")
    public ResultMode<SettPaymentDetailDTO> queryPaymentDetailsAfterSubmit(@Validated @RequestBody SettPaymentDetailQuery query){
        return settPaymentAppService.queryPaymentDetailsAfterSubmit(query);
    }



}
