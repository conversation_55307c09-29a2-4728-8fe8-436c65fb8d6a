package com.wanlianyida.platform.interfaces.facade.bidding;

import com.wanlianyida.bidding.api.model.command.EvalExpertDTO;
import com.wanlianyida.bidding.api.model.dto.*;
import com.wanlianyida.bidding.api.model.query.*;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.bidding.EvalBidQueryAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 评标阶段查询接口
 */
@RestController
@RequestMapping("/eval/bid")
public class EvalBidQueryController {

    @Resource
    private EvalBidQueryAppService evalExpertQueryAppService;

    /**
     * 评委会成员信息列表查询
     *
     * @param query
     * @return
     */
    @PostMapping("/query-eval-expert")
    public ResultMode<List<EvalExpertDTO>> queryEvalExpert(@RequestBody @Validated EvalExpertListQuery query) {
        return evalExpertQueryAppService.queryEvalExpert(query);
    }

    /**
     * 评标结果信息列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = {"/query-eval-result"})
    public ResultMode<List<TenderPackageScoreListDTO>> queryEvalResult(@RequestBody @Validated PackageWinQuery query) {
        return evalExpertQueryAppService.queryEvalResult(query);
    }

    /**
     * 中标候选人列表查询
     *
     * @param query
     * @return
     */
    @PostMapping(value = {"/query-candidate"})
    public ResultMode<List<EvalScoreResultListDTO>> queryCandidate(@RequestBody @Validated EvalScoreResultQuery query) {
        return evalExpertQueryAppService.queryCandidate(query);
    }

    /**
     * 轮次信息分页查询
     *
     * @param pageQuery
     * @return
     */
    @PostMapping(value = {"/page-query-round-list"})
    public ResultMode<List<BiddingRoundDTO>> pageQueryRoundList(@RequestBody @Validated PagingInfo<BiddingRoundQuery> pageQuery) {
        return evalExpertQueryAppService.pageQueryRoundList(pageQuery);
    }

    /**
     * 澄清发布信息列表查询
     * @param pagingInfo
     * @return
     */
    @PostMapping("/page-query-clarify-publish")
    public ResultMode<List<ClarifyFyDTO>> pageQueryClarifyPublish(@RequestBody PagingInfo<ClarifyQuery> pagingInfo) {
        return evalExpertQueryAppService.pageQueryClarifyPublish(pagingInfo);
    }

    /**
     * 澄清应答信息列表查询
     * @param pagingInfo
     * @return
     */
    @PostMapping("/page-query-clarify-reply")
    public ResultMode<List<ClarifyReplyFyDTO>> pageQueryClarifyReply(@RequestBody PagingInfo<ClarifyQuery> pagingInfo) {
        return evalExpertQueryAppService.pageQueryClarifyReply(pagingInfo);
    }

    /**
     * 候选人排名修改记录分页查询
     * @param pagingInfo
     * @return
     */
    @PostMapping("/page-query-eval-result-change")
    public ResultMode<List<BidChangeLogDTO>> pageQueryEvalResultChange(@RequestBody PagingInfo<BidChangeLogQuery> pagingInfo) {
        return evalExpertQueryAppService.pageQueryEvalResultChange(pagingInfo);
    }

    /**
     * 评审文件管理查询
     * @param query
     * @return
     */
    @PostMapping("/query-review-attachment-manage")
    public ResultMode<List<AttachmentEvaDTO>> queryReviewAttachment(@RequestBody ReviewAttachmentQuery query) {
        return evalExpertQueryAppService.queryReviewAttachment(query);
    }


}
