package com.wanlianyida.platform.interfaces.facade.process;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbaseprocess.api.model.command.CreateDeploymentCommand;
import com.wanlianyida.fssbaseprocess.api.model.dto.*;
import com.wanlianyida.fssbaseprocess.api.model.query.*;
import com.wanlianyida.platform.infrastructure.exchange.FssBaseProcessExchangeService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/flow/manager")
public class ProcessManagerController {

    @Resource
    FssBaseProcessExchangeService fssBaseProcessExchangeService;

    /**
     * 查询流程配置列表
     */
    @PostMapping("/queryProcessConfigList")
    ResultMode<List<PsProcessConfigDTO>> queryProcessConfigList(@RequestBody PagingInfo<PsProcessConfigQuery> pageQuery) {
        return fssBaseProcessExchangeService.queryProcessConfigList(pageQuery);
    }

    /**
     * 查询流程部署列表
     */
    @PostMapping("/queryDeployList")
    ResultMode<List<PsDeploymentDTO>> queryDeployList(@RequestBody PagingInfo<PsProcessDeployQuery> pageQuery) {
        return fssBaseProcessExchangeService.queryDeployList(pageQuery);
    }

    /**
     * 查询节点配置列表
     */
    @PostMapping("/queryActivityConfigList")
    ResultMode<List<PsActivityConfigDTO>> queryActivityConfigList(@RequestBody @Valid PagingInfo<PsActivityConfigQuery> pageQuery) {
        return fssBaseProcessExchangeService.queryActivityConfigList(pageQuery);
    }

    /**
     * 查询流程实例列表
     */
    @PostMapping("/queryProcessInstanceList")
    ResultMode<List<PsProcessInstanceDTO>> queryProcessInstanceList(@RequestBody @Valid PagingInfo<PsProcessInstanceQuery> pageQuery) {
        return fssBaseProcessExchangeService.queryProcessInstanceList(pageQuery);
    }

    /**
     * 查询活动列表
     */
    @PostMapping("/queryActivityList")
    ResultMode<List<PsActivityDTO>> queryActivityList(@RequestBody @Valid PagingInfo<PsActivityQuery> pageInfo) {
        return fssBaseProcessExchangeService.queryActivityList(pageInfo);
    }

    /**
     * 查询运行实例流程图信息
     */
    @PostMapping("/queryRunningFlowChart")
    ResultMode<PsFlowChartDTO> queryRunningFlowChart(@RequestBody @Valid PsFlowCharQuery pageInfo) {
        return fssBaseProcessExchangeService.queryRunningFlowChart(pageInfo);
    }

    /**
     * 查询部署流程图信息
     */
    @PostMapping("/queryDeploymentFlowChart")
    ResultMode<PsSimpleFlowChartDTO> queryDeploymentFlowChart(@RequestBody @Valid PsSimpleFlowCharQuery query) {
        return fssBaseProcessExchangeService.queryDeploymentFlowChart(query);
    }

    /**
     * 查询流程变量-运行中和已完成的
     */
    @GetMapping("/queryVars/{processInstanceId}")
    ResultMode<PsVarsDTO> queryVars(@PathVariable("processInstanceId") String processInstanceId) {
        return fssBaseProcessExchangeService.queryDeploymentFlowChart(processInstanceId);
    }

    /**
     * 部署流程
     */
    @PostMapping("/deployProcess")
    ResultMode<CreateDeploymentDTO> deployProcess(@RequestBody @Valid CreateDeploymentCommand dto) {
        return fssBaseProcessExchangeService.deployProcess(dto);
    }
}
