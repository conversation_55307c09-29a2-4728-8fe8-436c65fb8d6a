package com.wanlianyida.platform.interfaces.facade.bi;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.bi.BigDataAppService;
import com.wanlianyida.platform.interfaces.model.query.BatchBigDataQuery;
import com.wanlianyida.platform.interfaces.model.query.BigDataQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 大数据透传接口
 * @ClassName BaseBiController
 * @Description
 * <AUTHOR>
 * @Veriosn 1.0
 **/


@RestController
@RequestMapping("/bi")
public class BigDataController {

    @Resource
    private BigDataAppService bigDataAppService;

    /**
     * 大屏通用透传接口
     */
    @PostMapping("/bi-statistics")
    public ResultMode<Map<String,Object>> biStatistics(@RequestBody @Valid BigDataQuery query) {
        return bigDataAppService.biStatistics(query);
    }


    /**
     * 大屏批量透传接口
     */
    @PostMapping("/bi-batch-statistics")
    public ResultMode<Map<String,Object>> biBatchStatistics(@RequestBody @Valid BatchBigDataQuery query){
        return bigDataAppService.biBatchStatistics(query);
    }


}
