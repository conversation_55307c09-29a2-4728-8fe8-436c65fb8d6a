package com.wanlianyida.platform.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.product.ProductViewLogAppService;
import com.wanlianyida.product.api.model.command.ProductViewLogAddCommand;
import com.wanlianyida.product.api.model.dto.ProductViewLogDTO;
import com.wanlianyida.product.api.model.query.ProductViewLogListQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户浏览商品记录
 * <AUTHOR>
 * @since 2025-01-23
 */
@RestController
@RequestMapping("/product/productViewLog")
public class ProductViewLogController {

    @Resource
    private ProductViewLogAppService productViewLogAppService;

    /**
     * 新增商品浏览记录
     *
     * @param addCommand
     * @return {@link Boolean }
     */
    @PostMapping("/addViewLog")
    public ResultMode<?> addViewLog(@RequestBody @Valid ProductViewLogAddCommand addCommand) {
        return productViewLogAppService.addViewLog(addCommand);
    }


    /**
     * 查询浏览记录
     *
     * @param query 条件
     * @return {@code List<ProductCollectEntity> }
     */
    @PostMapping("/queryViewLogListPage")
    public ResultMode<List<ProductViewLogDTO>> queryViewLogListPage(@RequestBody @Valid PagingInfo<ProductViewLogListQuery> query) {
        return productViewLogAppService.queryViewLogListPage(query);
    }

}
