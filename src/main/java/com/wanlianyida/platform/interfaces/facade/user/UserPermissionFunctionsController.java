package com.wanlianyida.platform.interfaces.facade.user;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.UserPermissionFunctionsAppService;
import com.wanlianyida.platform.interfaces.model.command.UserPermissionFunctionsCommand;
import com.wanlianyida.platform.interfaces.model.command.UserUmPermissionCommand;
import com.wanlianyida.platform.interfaces.model.dto.UserFunctionsDTO;
import com.wanlianyida.platform.interfaces.model.query.UserPermissionFunctionsQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user/userPermFun")
public class UserPermissionFunctionsController {

    @Resource
    UserPermissionFunctionsAppService userPermissionFunctionsAppService;

    @PostMapping("/assignUserPermission")
    public ResultMode assignUserPermissions(@RequestBody UserPermissionFunctionsCommand userPermissionFunctionsCommand) {
        return userPermissionFunctionsAppService.assignUserPermissions(userPermissionFunctionsCommand);
    }

    @PostMapping("/queryAssignedList")
    public ResultMode<List<UserFunctionsDTO>> queryAssignedList(@RequestBody UserPermissionFunctionsQuery query) {
        return userPermissionFunctionsAppService.queryAssignedList(query);
    }

    @PostMapping("/queryNotBindMenusList")
    public ResultMode<List<UserFunctionsDTO>> queryNotBindMenusList(@RequestBody UserPermissionFunctionsQuery query) {
        return userPermissionFunctionsAppService.queryNotBindMenusList(query);
    }
    @PostMapping("/queryUmUserFunctionsList")
    public ResultMode<List<UserFunctionsDTO>> queryUmUserFunctionsList(@RequestBody UserPermissionFunctionsQuery query) {
        return userPermissionFunctionsAppService.queryUmUserFunctionsList(query);
    }
    @PostMapping("/saveUserFunctions")
    public ResultMode<String> saveUserFunctions(@RequestBody UserUmPermissionCommand command) {
        return userPermissionFunctionsAppService.saveUserFunctions(command);
    }
    @PostMapping("/deleteUmUserFunctions")
    public ResultMode<String> deleteUmUserFunctions(@RequestBody UserUmPermissionCommand command) {
        return userPermissionFunctionsAppService.deleteUmUserFunctions(command);
    }
}
