package com.wanlianyida.platform.interfaces.facade.content;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.inter.ChInformationInter;
import com.wanlianyida.fssbasecontent.api.model.command.ChInformationAuditDeleteICommand;
import com.wanlianyida.fssbasecontent.api.model.command.ChInformationBatchDeleteICommand;
import com.wanlianyida.fssbasecontent.api.model.command.ChinformationAddICommand;
import com.wanlianyida.fssbasecontent.api.model.command.ChinformationUpdateICommand;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationListDTO;
import com.wanlianyida.fssbasecontent.api.model.query.ChInformationListQuery;
import com.wanlianyida.platform.application.service.content.ChInformationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 *  内容管理
 * <AUTHOR>
 * @since 2024/11/21/19:19
 */
@RestController
@RequestMapping("/content/information")
public class ChInformationController {

    @Resource
    private ChInformationInter chInformationInter;

    @Resource
    private ChInformationService chInformationService;


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping("/listPage")
    public ResultMode<List<ChInformationListDTO>> list(@Validated @RequestBody PagingInfo<ChInformationListQuery> query) {
        return chInformationService.chInformationInterList(query);
    }

    /**
     * 内容详情
     * @param infoId
     * @return
     */
    @GetMapping("/detailById")
    public ResultMode<ChInformationDetailDTO> detailById(@RequestParam(name = "infoId") String infoId){
        return BeanUtil.toBean(chInformationInter.detailById(infoId), ResultMode.class);
    }

    /**
     * 新增
     * @param addCommand
     * @return
     */
    @PostMapping("/add")
    public ResultMode add(@Validated @RequestBody ChinformationAddICommand addCommand) {
        return chInformationService.add(addCommand);
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/update")
    public ResultMode update(@RequestBody ChinformationUpdateICommand updateCommand){
        return chInformationService.update(updateCommand);
    }


    /**
     * 批量删除内容
     * @param deleteCommand
     * @return
     */
    @PostMapping("/batchDelete")
    public ResultMode<Boolean> batchDelete(@RequestBody ChInformationBatchDeleteICommand deleteCommand) {
        return BeanUtil.toBean(chInformationInter.batchDelete(deleteCommand), ResultMode.class);
    }
    /**
     * 批量审核
     * @param auditCommand
     * @return
     */
    @PostMapping("/batchAudit")
    public ResultMode<Boolean> batchAudit(@RequestBody ChInformationAuditDeleteICommand auditCommand) {
        return BeanUtil.toBean(chInformationInter.batchAudit(auditCommand), ResultMode.class);
    }

}
