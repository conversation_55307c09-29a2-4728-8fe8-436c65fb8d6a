package com.wanlianyida.platform.interfaces.facade.transaction;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.transaction.LogisticsEntrustAppService;
import com.wanlianyida.transaction.api.model.command.LogisticsEntrustFollowedUpCommand;
import com.wanlianyida.transaction.api.model.dto.LogisticsEntrustDetailDTO;
import com.wanlianyida.transaction.api.model.dto.LogisticsEntrustListDTO;
import com.wanlianyida.transaction.api.model.query.LogisticsEntrustQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 物流委托
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/logisticsEntrust")
public class LogisticsEntrustController {

    @Resource
    private LogisticsEntrustAppService logisticsEntrustAppService;

    @ApiOperation("查询物流委托")
    @RequestMapping("/queryPage")
    public ResultMode<List<LogisticsEntrustListDTO>> queryPage(@RequestBody PagingInfo<LogisticsEntrustQuery> pageQuery) {
        return logisticsEntrustAppService.queryPage(pageQuery);
    }

    @ApiOperation("查看物流委托详情")
    @RequestMapping("/detail")
    public ResultMode<LogisticsEntrustDetailDTO> detail(@RequestBody @Validated IdQuery query) {
        return logisticsEntrustAppService.detail(query);
    }

    @ApiOperation("跟进物流委托")
    @RequestMapping("/followedUp")
    public ResultMode followedUp(@RequestBody @Validated LogisticsEntrustFollowedUpCommand command) {
        return logisticsEntrustAppService.followedUp(command);
    }
}
