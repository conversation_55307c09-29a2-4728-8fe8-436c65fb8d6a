package com.wanlianyida.platform.interfaces.facade.transaction;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.utils.PagingUtil;
import com.wanlianyida.platform.application.service.transaction.PurchaseAppService;
import com.wanlianyida.platform.interfaces.model.query.IdUserQuery;
import com.wanlianyida.transaction.api.model.dto.*;
import com.wanlianyida.transaction.api.model.query.PurchaseListQuery;
import com.wanlianyida.transaction.api.model.query.PurchaseSupplierQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * @Author: ershuai
 * @CreateTime: 2025-05-21
 * @Description: 求购
 */
@Api("求购")
@RestController
@RequestMapping("/purchase")
public class PurchaseController {

    @Resource
    private PurchaseAppService purchaseAppService;

    @ApiOperation("求购列表")
    @PostMapping("/pageCondition")
    public ResultMode<List<PurchaseListDTO>> pageCondition(@RequestBody PagingInfo<PurchaseListQuery> pageQuery) {
        return purchaseAppService.pageCondition(pageQuery);
    }

    @ApiOperation("求购单详情")
    @PostMapping("/queryDetail")
    public ResultMode<PurchaseDetailDTO> queryDetail(@RequestBody IdQuery query) {
        return purchaseAppService.queryDetail(query);
    }

    @ApiOperation("定向客户列表")
    @PostMapping("/queryPurchaseCompany")
    public ResultMode<List<PurchaseCompanyDTO>> queryPurchaseCompany(@RequestBody @Validated PurchaseSupplierQuery purchaseSupplierQuery) {
        return purchaseAppService.queryPurchaseCompany(purchaseSupplierQuery);
    }

    @ApiOperation("求购商品分页列表")
    @PostMapping("/pageProductList")
    ResultMode<List<PurchaseProductDTO>> pageProductList(@RequestBody @Validated PagingInfo<IdUserQuery> pageQuery){
        PagingInfo<IdQuery> pagingInfo = PagingUtil.convertPagingInfo(pageQuery, IdQuery.class);
        return purchaseAppService.pageProductList(pagingInfo);
    }
}
