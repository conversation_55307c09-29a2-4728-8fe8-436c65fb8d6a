package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.support.BdPricingTypeConfigAppService;
import com.wanlianyida.support.api.model.command.BdPricingTypeConfigAddCommand;
import com.wanlianyida.support.api.model.command.BdPricingTypeConfigUpdCommand;
import com.wanlianyida.support.api.model.dto.BdPricingTypeConfigDTO;
import com.wanlianyida.support.api.model.query.BdPricingTypeConfigQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/pricingType")
public class BdPricingTypeConfigController {

    @Resource
    private BdPricingTypeConfigAppService pricingTypeConfigAppService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public ResultMode<Void> add(@RequestBody @Validated BdPricingTypeConfigAddCommand command) {
        return pricingTypeConfigAppService.add(command);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public ResultMode<Void> update(@RequestBody @Validated BdPricingTypeConfigUpdCommand command) {
        return pricingTypeConfigAppService.update(command);
    }

    /**
     * 分页查询
     */
    @PostMapping("/queryPage")
    public ResultMode<List<BdPricingTypeConfigDTO>> queryPage(@RequestBody @Validated PagingInfo<BdPricingTypeConfigQuery> pagingInfo) {
        return pricingTypeConfigAppService.queryPage(pagingInfo);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    public ResultMode delete(@RequestBody @Validated IdCommand command) {
        pricingTypeConfigAppService.delete(command);
        return ResultMode.success();
    }

}
