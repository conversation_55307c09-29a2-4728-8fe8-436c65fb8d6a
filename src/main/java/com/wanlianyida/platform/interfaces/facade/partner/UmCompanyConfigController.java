package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmCompanyConfigCommand;
import com.wanlianyida.partner.api.model.dto.UmCompanyConfigDTO;
import com.wanlianyida.partner.api.model.query.UmCompanyConfigQuery;
import com.wanlianyida.platform.application.service.partner.UmCompanyConfigAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业配置表
 * <AUTHOR>
 * @since 2025/03/22/09:43
 */
@RestController
@RequestMapping("/umCompanyConfig")
public class UmCompanyConfigController {

    @Resource
    private UmCompanyConfigAppService umCompanyConfigAppService;

    @PostMapping("/addUmCompanyConfig")
    public ResultMode<Boolean> addUmCompanyConfig(@RequestBody @Validated List<UmCompanyConfigCommand> command) {
        return umCompanyConfigAppService.addUmCompanyConfig(command);
    }

    @PostMapping("/queryList")
    public ResultMode<List<UmCompanyConfigDTO>> queryList(@RequestBody @Validated UmCompanyConfigQuery condition) {
        return umCompanyConfigAppService.queryList(condition);
    }
}
