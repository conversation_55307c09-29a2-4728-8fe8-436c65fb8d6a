package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.dto.ActivityUserDetailDTO;
import com.wanlianyida.partner.api.model.dto.ActivityUserPageDTO;
import com.wanlianyida.partner.api.model.query.ActivityUserPageQuery;
import com.wanlianyida.platform.application.service.partner.ActivityUserAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 活动用户控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/activity")
public class ActivityUserController {

    @Resource
    private ActivityUserAppService activityUserAppService;

    /**
     * 活动用户分页
     *
     */
    @PostMapping("/page-list")
    public ResultMode<List<ActivityUserPageDTO>> pageList(@RequestBody PagingInfo<ActivityUserPageQuery> query){
        return activityUserAppService.pageList(query);
    }

    /**
     * 活动用户详情
     *
     */
    @PostMapping("/user-detail")
    public ResultMode<ActivityUserDetailDTO> userDetail(@RequestBody IdQuery query){
        return activityUserAppService.userDetail(query);
    }

}
