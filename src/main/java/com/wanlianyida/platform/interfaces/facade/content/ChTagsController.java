package com.wanlianyida.platform.interfaces.facade.content;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.inter.ChTagsInter;
import com.wanlianyida.fssbasecontent.api.model.command.ChTagsAddICommand;
import com.wanlianyida.fssbasecontent.api.model.command.ChTagsBatchDeleteICommand;
import com.wanlianyida.fssbasecontent.api.model.command.ChTagsUpdateICommand;
import com.wanlianyida.fssbasecontent.api.model.query.ChTagsListQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.platform.application.assembler.content.UserBaseInfossemble;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 标签管理
 * <AUTHOR>
 * @since 2024/11/21/19:18
 */
@Slf4j
@RestController
@RequestMapping("/content/tags")
public class ChTagsController {

    @Resource
    private ChTagsInter chTagsInter;
    /**
     * 查询列表分页
     * @param pageInfo
     * @return
     */
    @PostMapping("/listPage")
    public ResultMode list(@RequestBody PagingInfo<ChTagsListQuery> pageInfo) {
        com.wanlianyida.fssmodel.PagingInfo<ChTagsListQuery> pagingInfo = BeanUtil.toBean(pageInfo, com.wanlianyida.fssmodel.PagingInfo.class);
        pagingInfo.setFilterModel(pageInfo.getFilterModel());
        ResponseMessage<?> responseMessage = chTagsInter.list(pagingInfo);
        if (responseMessage.isSucceed()) {
            return ResultMode.successPageList(responseMessage.getModel(), responseMessage.getTotal());
        }
        return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
    }

    /**
     * 查询列表不分页
     * @param query
     * @return
     */
    @PostMapping("/list")
    public ResultMode list(@RequestBody ChTagsListQuery query) {
        ResponseMessage<?> res = chTagsInter.list(query);
        return BeanUtil.toBean(res, ResultMode.class);
    }


    /**
     * 新增
     * @param command
     * @return
     */
    @PostMapping("/add")
    public ResultMode add(@Validated @RequestBody ChTagsAddICommand command) {
        UserBaseInfossemble.setAddBaseInfo(command);
        log.info("新增标签:{}", command);
        return BeanUtil.toBean(chTagsInter.add(command), ResultMode.class);
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/update")
    public ResultMode update(@RequestBody ChTagsUpdateICommand updateCommand) {
        UserBaseInfossemble.setUpdateBaseInfo(updateCommand);
        log.info("更新标签:{}", updateCommand);
        return BeanUtil.toBean(chTagsInter.update(updateCommand), ResultMode.class);
    }

    /**
     * 批量删除标签
     * @param deleteCommand
     * @return
     */
    @PostMapping("/batchDelete")
    public ResultMode<Boolean> batchDelete(@RequestBody ChTagsBatchDeleteICommand deleteCommand) {
        log.info("批量删除标签:{}", deleteCommand);
        return BeanUtil.toBean(chTagsInter.batchDelete(deleteCommand), ResultMode.class);
    }

}
