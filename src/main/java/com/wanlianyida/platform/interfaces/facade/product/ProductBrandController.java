package com.wanlianyida.platform.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.product.ProductBrandAppService;
import com.wanlianyida.product.api.model.command.ProductBrandAddCommand;
import com.wanlianyida.product.api.model.command.ProductBrandDeleteCommand;
import com.wanlianyida.product.api.model.command.ProductBrandEditCommand;
import com.wanlianyida.product.api.model.command.RelateCategoryCommand;
import com.wanlianyida.product.api.model.dto.BrandRelatedCategoryDTO;
import com.wanlianyida.product.api.model.dto.ProductBrandDTO;
import com.wanlianyida.product.api.model.dto.ProductBrandPageDTO;
import com.wanlianyida.product.api.model.dto.ProductBrandPicDTO;
import com.wanlianyida.product.api.model.query.ProductBrandPageQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商品品牌管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product/brand")
@Valid
public class ProductBrandController {

    @Resource
    private ProductBrandAppService brandAppService;

    /**
     * 新增商品品牌
     * @param command 参数
     */
    @PostMapping(value = {"/add"})
    public ResultMode<?> add(@RequestBody @Validated ProductBrandAddCommand command) {
        return brandAppService.add(command);
    }

    /**
     * 编辑商品品牌
     * @param command 参数
     */
    @PostMapping(value = {"/edit"})
    public ResultMode<?> edit(@RequestBody @Validated ProductBrandEditCommand command) {
        return brandAppService.edit(command);
    }

    /**
     * 删除商品品牌
     * @param command 参数
     */
    @PostMapping(value = {"/delete"})
    public ResultMode<?> delete(@RequestBody @Validated ProductBrandDeleteCommand command) {
        return brandAppService.delete(command);
    }

    /**
     * 分页查询商品品牌
     * @param query 分页参数
     */
    @PostMapping(value = {"/pageQuery"})
    public ResultMode<List<ProductBrandPageDTO>> pageQuery(@RequestBody PagingInfo<ProductBrandPageQuery> query) {
        return brandAppService.pageQuery(query);
    }

    /**
     * 根据主键列表查询
     * @param idList 参数
     */
    @PostMapping(value = {"/queryByIds"})
    public ResultMode<List<ProductBrandDTO>> queryByIds(@RequestBody List<Long> idList) {
        return brandAppService.queryByIds(idList);
    }

    /**
     * 根据主键查询品牌图片
     * @param query 参数
     */
    @PostMapping(value = {"/queryBrandLogoById"})
    public ResultMode<ProductBrandPicDTO> queryBrandLogoById(@RequestBody IdQuery query) {
        return brandAppService.queryBrandLogoById(query);
    }

    /**
     * 品牌已经关联的品类列表
     */
    @PostMapping(value = {"/queryRelatedCategory"})
    public ResultMode<List<BrandRelatedCategoryDTO>> queryRelatedCategory(@RequestBody @Valid IdQuery query){
        return brandAppService.queryRelatedCategory(query);
    }

    /**
     * 品牌批量关联品类
     */
    @PostMapping(value = {"/relateCategory"})
    public ResultMode<?> relateCategory(@RequestBody @Valid RelateCategoryCommand command){
        return brandAppService.relateCategory(command);
    }

}
