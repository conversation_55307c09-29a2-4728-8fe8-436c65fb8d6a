package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmShopCollectActionCommand;
import com.wanlianyida.partner.api.model.dto.UmShopCollectDTO;
import com.wanlianyida.partner.api.model.query.UmShopCollectListQuery;
import com.wanlianyida.platform.application.service.partner.ShopCollectService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户收藏店铺
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@RestController
@RequestMapping("/partner/umShopCollect")
public class ShopCollectController {


    @Resource
    private ShopCollectService shopCollectService;

    /**
     * 店铺收藏操作
     *
     * @param addCommand
     * @return {@link Boolean }
     */
    @PostMapping("/shopCollectAction")
    public ResultMode<?> addShopCollect(@RequestBody @Valid UmShopCollectActionCommand addCommand) {
        return shopCollectService.addShopCollect(addCommand);
    }

    /**
     * 查询收藏店铺列表
     *
     * @param query
     * @return {@link List<UmShopCollectDTO>}
     */
    @PostMapping("/queryCollectShopListPage")
    public ResultMode<List<UmShopCollectDTO>> queryCollectListPage(@RequestBody @Valid PagingInfo<UmShopCollectListQuery> query) {
        return shopCollectService.queryCollectListPage(query);
    }

}
