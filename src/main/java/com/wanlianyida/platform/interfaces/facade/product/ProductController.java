package com.wanlianyida.platform.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.product.ProductAppService;
import com.wanlianyida.platform.interfaces.model.dto.RichTextDTO;
import com.wanlianyida.platform.interfaces.model.query.ProductRichTextQuery;
import com.wanlianyida.product.api.model.dto.ProductMoreInfoDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuListDTO;
import com.wanlianyida.product.api.model.dto.ProductSpuDetailDTO;
import com.wanlianyida.product.api.model.query.ProductMoreInfoQuery;
import com.wanlianyida.product.api.model.query.ProductSkuListQuery;
import com.wanlianyida.product.api.model.query.ProductSkuQuery;
import com.wanlianyida.product.api.model.query.ProductSpuQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月27日 14:12
 */
@Api("商品信息")
@RequestMapping("/product")
@RestController
public class ProductController {

    @Resource
    private ProductAppService productAppService;

    @ApiOperation(("sku列表查询"))
    @PostMapping("/skuPageCondition")
    public ResultMode<List<ProductSkuListDTO>> skuPageCondition(@RequestBody PagingInfo<ProductSkuListQuery> query) {
        return productAppService.skuPageCondition(query);
    }

    @ApiOperation("查询商品详情(spu纬度)")
    @PostMapping("/queryProductSpuDetail")
    public ResultMode<List<ProductSpuDetailDTO>> queryProductSpuDetail(@RequestBody ProductSpuQuery query) {
        return productAppService.queryProductSpuDetail(query);
    }

    @ApiOperation("查询商品详情(sku纬度)")
    @PostMapping("/queryProductSkuDetail")
    public ResultMode<List<ProductSkuDetailDTO>> queryProductSkuDetail(@RequestBody ProductSkuQuery query) {
        return productAppService.queryProductSkuDetail(query);
    }

    @ApiOperation("查询商品更多信息:图片、资质")
    @PostMapping("/queryProductMoreInfo")
    public ResultMode<ProductMoreInfoDTO> queryProductMoreInfo(@RequestBody @Validated ProductMoreInfoQuery query) {
        return productAppService.queryProductMoreInfo(query);
    }

    @ApiOperation("查询商品富文本详情")
    @PostMapping("/queryRichText")
    public ResultMode<RichTextDTO> queryRichText(@RequestBody @Validated ProductRichTextQuery query) {
        return productAppService.queryProductRichText(query);
    }
}
