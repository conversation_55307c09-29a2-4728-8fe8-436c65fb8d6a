package com.wanlianyida.platform.interfaces.facade.support;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.support.BdAutoAuditConfigService;
import com.wanlianyida.support.api.model.command.AutoAuditConfigAddCommand;
import com.wanlianyida.support.api.model.command.AutoAuditConfigUpdateCommand;
import com.wanlianyida.support.api.model.dto.AutoAuditConfigListDTO;
import com.wanlianyida.support.api.model.query.AutoAuditConfigListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 自动审核-配置表
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@RestController
@RequestMapping("/support/autoAuditConfig")
public class BdAutoAuditConfigController {


    @Resource
    private BdAutoAuditConfigService autoAuditConfigService;


    /**
     * 新增
     * @param addCommand
     * @return
     */
    @PostMapping("/addConfig")
    public ResultMode add(@Validated @RequestBody AutoAuditConfigAddCommand addCommand) {
        return autoAuditConfigService.add(addCommand);
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/updateConfig")
    public ResultMode update(@RequestBody @Validated AutoAuditConfigUpdateCommand updateCommand) {
        return autoAuditConfigService.update(updateCommand);
    }


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping("/listPageConfig")
    public ResultMode<List<AutoAuditConfigListDTO>> list(@Validated @RequestBody PagingInfo<AutoAuditConfigListQuery> query) {
        return autoAuditConfigService.list(query);
    }

}
