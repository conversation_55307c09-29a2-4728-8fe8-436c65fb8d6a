package com.wanlianyida.platform.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.product.ProductBasicAppService;
import com.wanlianyida.product.api.model.dto.ImportResultDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 商品基础信息导入
 * <AUTHOR>
 */
@RestController
@RequestMapping("/product/basic")
@Valid
public class ProductBasicController {

    @Resource
    private ProductBasicAppService productBasicAppService;

    /**
     * 品类导入
     */
    @PostMapping(value = "/importCategory")
    public ResultMode<ImportResultDto> importCategory(@NotNull(message = "文件不能为空") MultipartFile file) throws Exception {
        return productBasicAppService.importCategory(file);
    }

    /**
     * 品牌导入
     */
    @PostMapping(value = "/importBrand")
    public ResultMode<ImportResultDto> importBrand(@NotNull(message = "文件不能为空") MultipartFile file) throws Exception {
        return productBasicAppService.importBrand(file);
    }

    /**
     * 规格导入
     */
    @PostMapping(value = "/importAttribute")
    public ResultMode<ImportResultDto> importAttribute(@NotNull(message = "文件不能为空") MultipartFile file) throws Exception {
        return productBasicAppService.importAttribute(file);
    }

    /**
     * 品类品牌导入
     */
    @PostMapping(value = "/importCategoryBrand")
    public ResultMode<ImportResultDto> importCategoryBrand(@NotNull(message = "文件不能为空") MultipartFile file) throws Exception {
        return productBasicAppService.importCategoryBrand(file);
    }

    /**
     * 品类规格导入
     */
    @PostMapping(value = "/importCategoryAttribute")
    public ResultMode<ImportResultDto> importCategoryAttribute(@NotNull(message = "文件不能为空") MultipartFile file) throws Exception {
        return productBasicAppService.importCategoryAttribute(file);
    }

}
