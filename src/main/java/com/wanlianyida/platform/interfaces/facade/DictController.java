package com.wanlianyida.platform.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.DictAppService;
import com.wanlianyida.platform.interfaces.model.command.DictInfoCommand;
import com.wanlianyida.support.api.model.command.DictValueCommand;
import com.wanlianyida.support.api.model.dto.DictInfoDTO;
import com.wanlianyida.support.api.model.dto.DictValueDTO;
import com.wanlianyida.support.api.model.query.DictQuery;
import com.wanlianyida.support.api.model.query.DictValueQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年09月06 17:25
 */
@RestController
@RequestMapping("/dict")
public class DictController {

    @Resource
    private DictAppService dictAppService;

    @PostMapping("/list")
    @ApiOperation("字典列表")
    public ResultMode<List<DictInfoDTO>> list(@RequestBody DictQuery query) {
        return dictAppService.list(query);
    }

    @PostMapping("/info")
    @ApiOperation("字典信息")
    public ResultMode<List<DictValueDTO>> info(@RequestBody DictValueQuery query) {
        return dictAppService.info(query);
    }

    @PostMapping("/add")
    @ApiOperation("新增字典")
    public ResultMode add(@RequestBody @Validated DictInfoCommand command) {
        return dictAppService.add(command);
    }

    @PostMapping("/addValue")
    @ApiOperation("新增字典值")
    public ResultMode addValue(@RequestBody @Validated DictValueCommand command) {
        return dictAppService.addValue(command);
    }

    @PostMapping("/update")
    @ApiOperation("修改字典")
    public ResultMode update(@RequestBody @Validated DictInfoCommand command) {
        return dictAppService.update(command);
    }

    @PostMapping("/updateValue")
    @ApiOperation("修改字典值")
    public ResultMode updateValue(@RequestBody @Validated DictValueCommand command) {
        return dictAppService.updateValue(command);
    }

    @PostMapping("/delete")
    @ApiOperation("删除字典")
    public ResultMode delete(@RequestBody @Validated IdCommand command) {
        return dictAppService.delete(command);
    }

    @PostMapping("/deleteValue")
    @ApiOperation("删除字典值")
    public ResultMode deleteValue(@RequestBody @Validated IdCommand command) {
        return dictAppService.deleteValue(command);
    }
}
