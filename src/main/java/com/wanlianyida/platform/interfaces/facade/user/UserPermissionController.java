package com.wanlianyida.platform.interfaces.facade.user;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.UserPermissionAppService;
import com.wanlianyida.platform.interfaces.model.command.UserPermissionCommand;
import com.wanlianyida.platform.interfaces.model.command.UserUmPermissionCommand;
import com.wanlianyida.platform.interfaces.model.dto.UserPermissionDTO;
import com.wanlianyida.platform.interfaces.model.query.UserPermissionQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user/userPermission")
public class UserPermissionController{

    @Resource
    UserPermissionAppService userPermissionAppService;

    @PostMapping("/addUserPermission")
    public ResultMode<String> addUserPermission(@RequestBody UserPermissionCommand userPermissionCommand) {
        return userPermissionAppService.addUserPermission(userPermissionCommand);
    }

    @PostMapping("/updateUserPermission")
    public ResultMode<String> updateUserPermission(@RequestBody UserPermissionCommand userPermissionCommand) {
        return userPermissionAppService.updateUserPermission(userPermissionCommand);
    }

    @PostMapping("/delUserPermission")
    public ResultMode<String> delUserPermission(@RequestBody UserPermissionCommand userPermissionCommand) {
        return userPermissionAppService.deleteUserPermission(userPermissionCommand);
    }

    @PostMapping("/queryList")
    public ResultMode<List<UserPermissionDTO>> queryList(@RequestBody PagingInfo<UserPermissionQuery> pagingInfo) {
        return userPermissionAppService.queryUserPermission(pagingInfo);
    }

    @PostMapping("/queryListByAccountId")
    public ResultMode<List<UserPermissionDTO>> queryListByAccountId(@RequestBody PagingInfo<UserPermissionQuery> pagingInfo) {
        return userPermissionAppService.queryListByAccountId(pagingInfo);
    }

    @PostMapping("/batchSaveUserPermission")
    public ResultMode<String> batchSaveUserPermission(@RequestBody UserUmPermissionCommand command) {
        return userPermissionAppService.batchSaveUserPermission(command);
    }

    @PostMapping("/deleteUserPermissionBind")
    public ResultMode<String> deleteUserPermissionBind(@RequestBody UserUmPermissionCommand command) {
        return userPermissionAppService.deleteUserPermissionBind(command);
    }
}
