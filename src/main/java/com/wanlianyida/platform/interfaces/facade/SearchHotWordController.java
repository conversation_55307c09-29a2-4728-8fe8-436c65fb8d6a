package com.wanlianyida.platform.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.SearchHotWordAppService;
import com.wanlianyida.support.api.model.command.SearchHotWordCommand;
import com.wanlianyida.support.api.model.dto.SearchHotWordDTO;
import com.wanlianyida.support.api.model.query.SearchHotWordPageQuery;
import com.wanlianyida.support.api.model.query.SearchHotWordQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/searchHotWord")
public class SearchHotWordController {
    @Resource
    private SearchHotWordAppService appService;

    /**
     * 新增或更新搜索热词
     * @param command
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public ResultMode<Void> saveOrUpdate(@RequestBody SearchHotWordCommand command) {
        return  appService.saveOrUpdate(command);
    }

    /**
     * 查询详情
     * @param query
     * @return
     */
    @PostMapping("/queryDetail")
    public ResultMode<SearchHotWordDTO> queryDetail(@RequestBody @Validated SearchHotWordQuery query) {
        return appService.queryDetail(query);
    }

    /**
     * 删除搜索热词
     * @param command
     * @return
     */
    @PostMapping("/delete")
    public ResultMode<Void> delete(@RequestBody SearchHotWordCommand command) {
        return appService.delete(command);
    }

    /**
     * 分页查询搜索热词
     * @param pagingInfo
     * @return
     */
    @PostMapping("/queryPage")
    public ResultMode queryPage(@RequestBody PagingInfo<SearchHotWordPageQuery> pagingInfo) {
        return appService.queryPage(pagingInfo);
    }

    /**
     * 查询指定条数的数据
     * @param query
     * @return
     */
    @PostMapping("/queryByNum")
    public ResultMode<List<SearchHotWordDTO>> queryLimitedData(@RequestBody SearchHotWordPageQuery query) {
        return appService.queryLimitedData(query);
    }
}
