package com.wanlianyida.platform.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbaselog.api.model.dto.LogOperationRecordDTO;
import com.wanlianyida.fssbaselog.api.model.query.LogOperationRecordQuery;
import com.wanlianyida.platform.application.service.OperateLogAppService;
import com.wanlianyida.platform.interfaces.model.dto.OperationRecordLogDTO;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 操作日志 Controller
 *
 * <AUTHOR>
 * @date 2024-12-5
 */
@Api(value = "操作日志api")
@RequestMapping("/baseLog")
@RestController
public class OperateLogController {

	@Resource
	private OperateLogAppService operateLogAppService;

	/**
	 * 操作日志分页查询
	 *
	 * @param query
	 * @return
	 */
	@PostMapping("/logOperationRecord/query")
	ResultMode<List<LogOperationRecordDTO>> queryPage(@RequestBody PagingInfo<LogOperationRecordQuery> query) {
		return operateLogAppService.queryPage(query);
	}

	@PostMapping("/logOperationRecord/query-operate-log")
	ResultMode<List<OperationRecordLogDTO>> queryRecordPage(@RequestBody PagingInfo<LogOperationRecordQuery> query) {
		return operateLogAppService.queryRecordPage(query);
	}
}

