package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.partner.SensitiveWordsAppService;
import com.wanlianyida.support.api.model.command.CheckSensitiveWordsCommand;
import com.wanlianyida.support.api.model.command.SensitiveWordsAddCommand;
import com.wanlianyida.support.api.model.command.SensitiveWordsDeleteCommand;
import com.wanlianyida.support.api.model.command.SensitiveWordsUpdateCommand;
import com.wanlianyida.support.api.model.query.SensitiveWordsListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 敏感词库管理
 *
 * <AUTHOR>
 * @date 2024/11/26
 */
@RestController
@RequestMapping("/partner/sensitive/words")
public class SensitiveWordsController {
    @Resource
    private SensitiveWordsAppService appService;
    /**
     * 新增/批量新增敏感词
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/batchInsert")
    public ResultMode<Boolean> batchInsert(@RequestBody @Validated SensitiveWordsAddCommand command) {
        return appService.batchInsert(command);
    }

    /**
     * 更新敏感词
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/update")
    public ResultMode<Boolean> update(@RequestBody @Validated SensitiveWordsUpdateCommand command) {
        return appService.update(command);
    }

    /**
     * 批量删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/batchDelete")
    public ResultMode<Boolean> batchDelete(@RequestBody @Validated SensitiveWordsDeleteCommand command) {
        return appService.batchDelete(command);
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    @PostMapping("/queryPage")
    public ResultMode queryPage(@RequestBody PagingInfo<SensitiveWordsListQuery> pagingInfo){
        return appService.queryPage(pagingInfo);
    }

    /**
     * 敏感词检测
     *
     * @param command 内容
     * @return {@link ResultMode }<{@link String }>
     */
    @PostMapping("/checkSensitiveWords")
    public ResultMode<String>checkSensitiveWords(@RequestBody @Validated CheckSensitiveWordsCommand command){
        return appService.checkSensitiveWords(command);
    }
}
