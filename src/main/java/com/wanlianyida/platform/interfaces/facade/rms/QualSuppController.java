package com.wanlianyida.platform.interfaces.facade.rms;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmQualSuppUpdateCommand;
import com.wanlianyida.partner.api.model.dto.AuditStatisticsDTO;
import com.wanlianyida.partner.api.model.dto.UmQualSuppInfoDTO;
import com.wanlianyida.partner.api.model.query.UmQualSuppPageQuery;
import com.wanlianyida.platform.application.service.rms.QualSuppAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业资质补充资料表 Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Api(value = "企业资质补充资料管理api")
@RequestMapping("/qual-supp")
@RestController
public class QualSuppController {

	@Resource
	private QualSuppAppService qualSuppAppService;

	/**
	 * 分页查询
	 * @param pagingInfo 分页查询参数
	 * @return {@link ResultMode}<{@link UmQualSuppInfoDTO}>
	 */
	@ApiOperation("分页查询")
	@PostMapping("/page-query-supp-apply")
	public ResultMode<List<UmQualSuppInfoDTO>> queryPage(@RequestBody PagingInfo<UmQualSuppPageQuery> pagingInfo) {
		return qualSuppAppService.queryPage(pagingInfo);
	}

	/**
	 * 审核补充资料
	 * @param command
	 * @return {@link ResultMode}
	 */
	@ApiOperation("审核补充资料")
	@PostMapping("/update-supp-apply")
	ResultMode<?> updateSuppApply(@RequestBody UmQualSuppUpdateCommand command) {
		return qualSuppAppService.updateSuppApply(command);
	};

	/**
	 * 查询补充资料-运营端
	 * @param query
	 * @return {@link ResultMode}
	 */
	@ApiOperation("查询补充资料-运营端")
	@PostMapping("/query-detail-supp-apply")
	ResultMode<UmQualSuppInfoDTO> querySuppApplyForPlatform(@RequestBody @Validated IdQuery query) {
		return qualSuppAppService.querySuppApplyForPlatform(query);
	};

	/**
	 * 数据统计
	 * @return {@link ResultMode}<{@link AuditStatisticsDTO}>
	 */
	@ApiOperation("数据统计")
	@PostMapping("/statistics")
	ResultMode<AuditStatisticsDTO> statistics(){
		return qualSuppAppService.statistics();
	};

}

