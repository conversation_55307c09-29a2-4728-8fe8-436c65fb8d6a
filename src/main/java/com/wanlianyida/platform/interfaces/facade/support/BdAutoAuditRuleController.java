package com.wanlianyida.platform.interfaces.facade.support;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.support.BdAutoAuditRuleService;
import com.wanlianyida.support.api.model.command.BdAutoAuditRuleAddCommand;
import com.wanlianyida.support.api.model.dto.BdAutoAuditRuleListDTO;
import com.wanlianyida.support.api.model.query.BdAutoAuditRuleListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 自动审核-规则表
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@RestController
@RequestMapping("/support/autoAuditRule")
public class BdAutoAuditRuleController {

    @Resource
    private BdAutoAuditRuleService autoAuditRuleService;


    /**
     * 新增与更新
     * @param addCommand
     * @return
     */
    @PostMapping("/addOrUpdateRule")
    public ResultMode add(@Validated @RequestBody List<BdAutoAuditRuleAddCommand> addCommand) {
        return autoAuditRuleService.addOrUpdateRule(addCommand);
    }

    /**
     * 查询详情
     *
     * @param query
     * @return
     */
    @PostMapping("/ruleDetail")
    public ResultMode<List<BdAutoAuditRuleListDTO>> detail(@RequestBody @Validated BdAutoAuditRuleListQuery query) {
        return autoAuditRuleService.detail(query);
    }


}
