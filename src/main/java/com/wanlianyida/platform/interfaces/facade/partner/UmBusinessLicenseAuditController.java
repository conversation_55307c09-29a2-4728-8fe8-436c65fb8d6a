package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmBusinessLicenseAuditUpdateCommand;
import com.wanlianyida.partner.api.model.dto.AuditStatisticsDTO;
import com.wanlianyida.partner.api.model.dto.UmBusinessLicenseAuditDetailDTO;
import com.wanlianyida.partner.api.model.dto.UmBusinessLicenseAuditListDTO;
import com.wanlianyida.partner.api.model.query.UmBusinessLicenseAuditQuery;
import com.wanlianyida.platform.application.service.partner.UmBusinessLicenseAuditAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月29日 17:38
 */
@Api("营业执照审核")
@RestController
@RequestMapping("/businessLicense")
public class UmBusinessLicenseAuditController {

    @Resource
    private UmBusinessLicenseAuditAppService businessLicenseAuditAppService;

    @ApiOperation("分页列表")
    @PostMapping("/pageCondition")
    public ResultMode<List<UmBusinessLicenseAuditListDTO>> pageCondition(@RequestBody @Validated PagingInfo<UmBusinessLicenseAuditQuery> pageQuery) {
        return businessLicenseAuditAppService.pageCondition(pageQuery);
    }

    @ApiOperation("查询详情")
    @PostMapping("/queryDetail")
    public ResultMode<UmBusinessLicenseAuditDetailDTO> queryDetail(@RequestBody @Validated IdQuery query) {
        return businessLicenseAuditAppService.queryDetail(query);
    }

    @ApiOperation("审核")
    @PostMapping("/audit")
    public ResultMode<?> audit(@RequestBody @Validated UmBusinessLicenseAuditUpdateCommand command) {
        return businessLicenseAuditAppService.audit(command);
    }

    @ApiOperation("数据统计")
    @PostMapping("/statistics")
    public ResultMode<AuditStatisticsDTO> statistics() {
        return businessLicenseAuditAppService.statistics();
    }
}
