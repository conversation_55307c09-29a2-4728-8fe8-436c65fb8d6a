package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.OrgCommand;
import com.wanlianyida.partner.api.model.command.OrgDeleteCommand;
import com.wanlianyida.partner.api.model.dto.OrgDTO;
import com.wanlianyida.partner.api.model.query.OrgTreeQuery;
import com.wanlianyida.platform.application.service.partner.OrgAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 组织管理
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@RestController
@RequestMapping("/partner/um/org")
public class OrgController {
    @Resource
    private OrgAppService appService;

    /**
     * 组织新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/add")
    public ResultMode<Boolean> add(@RequestBody @Validated OrgCommand command){
        return appService.add(command);
    }

    /**
     * 组织更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/update")
    public ResultMode<Boolean> update(@RequestBody @Validated OrgCommand command){
        return appService.update(command);
    }

    /**
     * 组织删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/delete")
    public ResultMode<Boolean> delete(@RequestBody @Validated OrgDeleteCommand command){
        return appService.delete(command);
    }

    /**
     * 构建组织结构树
     *
     * @param query OrgDTO
     * @return {@link ResultMode }<{@link OrgDTO }>
     */
    @PostMapping("/buildOrganizationTree")
    public ResultMode<OrgDTO> buildOrganizationTree(@RequestBody @Validated OrgTreeQuery query){
        return appService.buildOrganizationTree(query);
    }
}
