package com.wanlianyida.platform.interfaces.facade.support;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.platform.application.service.support.MsgTemplateTypeAppService;
import com.wanlianyida.platform.interfaces.model.command.MsgTemplateCommand;
import com.wanlianyida.platform.interfaces.model.dto.MsgTemplateDTO;
import com.wanlianyida.platform.interfaces.model.query.MsgTemplateQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 消息模版管理
 * <AUTHOR>
 * @date 2025/05/24
 */
@RestController
@RequestMapping("/msg/template/type")
public class MsgTemplateTypeController {
    @Resource
    private MsgTemplateTypeAppService appService;

    @ApiOperation("编辑消息模版")
    @PostMapping("/updateMsgTemplate")
    public ResultMode<Void> updateMsgTemplate(@RequestBody MsgTemplateCommand command){

        return appService.updateMsgTemplate(command);
    }

    @ApiOperation("查询模版详情")
    @PostMapping("/queryDetail")
    public ResultMode<MsgTemplateDTO> queryDetail(@RequestBody MsgTemplateQuery query){
        return appService.queryDetail(query);
    }

    @ApiOperation("分页查询消息类型")
    @PostMapping("/queryPage")
    public ResultMode<List<MsgTemplateDTO>> queryPage(@RequestBody PagingInfo<MsgTemplateQuery> pagingInfo){
        return appService.queryPage(pagingInfo);
    }
}
