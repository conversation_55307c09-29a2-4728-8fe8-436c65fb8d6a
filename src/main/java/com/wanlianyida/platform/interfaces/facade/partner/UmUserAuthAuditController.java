package com.wanlianyida.platform.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmUserAuthAuditUpdateCommand;
import com.wanlianyida.partner.api.model.dto.*;
import com.wanlianyida.partner.api.model.query.UmUserAuthAuditQuery;
import com.wanlianyida.platform.application.service.partner.UmUserApproveAuditAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月31日 09:22
 */
@Api("实名认证审核")
@RestController
@RequestMapping("/userAuth")
public class UmUserAuthAuditController {

    @Resource
    private UmUserApproveAuditAppService userApproveAuditAppService;

    @ApiOperation("分页列表")
    @PostMapping("/pageCondition")
    public ResultMode<List<UmUserAuthAuditListDTO>> pageCondition(@RequestBody @Validated PagingInfo<UmUserAuthAuditQuery> pageQuery) {
        return userApproveAuditAppService.pageCondition(pageQuery);
    }

    @ApiOperation("查询详情")
    @PostMapping("/queryDetail")
    public ResultMode<UmUserAuthAuditDetailDTO> queryDetail(@RequestBody @Validated IdQuery query) {
        return userApproveAuditAppService.queryDetail(query);
    }

    @ApiOperation("审核")
    @PostMapping("/audit")
    public ResultMode<?> audit(@RequestBody @Validated UmUserAuthAuditUpdateCommand command) {
        return userApproveAuditAppService.audit(command);
    }

    @ApiOperation("数据统计")
    @PostMapping("/statistics")
    public ResultMode<AuditStatisticsDTO> statistics() {
        return userApproveAuditAppService.statistics();
    }
}
