package com.wanlianyida.basemdm.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.wanlianyida.basemdm.domain.model.entity.CmdEmployeeInfoEntity;
import com.wanlianyida.basemdm.domain.repository.CmdEmployeeInfoRepository;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdEmployeeInfoPO;
import com.wanlianyida.basemdm.interfaces.model.command.CmdEmployeeInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.query.CmdEmployeeInfoQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 员工appService
 */
@Slf4j
@RestController
public class CmdEmployeeInfoAppService {

    @Resource
    private CmdEmployeeInfoRepository cmdEmployeeInfoRepository;


    /**
     * 条件列表查询
     *
     * @param filter 查询参数
     * @return {@link ResultMode}<{@link CmdEmployeeInfoPO}>
     */
    public List<CmdEmployeeInfoPO> queryList(CmdEmployeeInfoQuery filter) {
        return cmdEmployeeInfoRepository.queryList(filter);
    }

    /**
     * 保存
     *
     * @param command
     * @return {@link ResultMode}<{@link Integer}>
     */
    public Long save(CmdEmployeeInfoCommand command) {
        CmdEmployeeInfoEntity record = new CmdEmployeeInfoEntity();
        CmdEmployeeInfoQuery filter = new CmdEmployeeInfoQuery();
        filter.setEmpIdCardNo(command.getEmpIdCardNo());
        List<CmdEmployeeInfoPO> exists = queryList(filter);
        BeanUtil.copyProperties(command, record, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        if (CollUtil.isNotEmpty(exists)) {
            record.setId(CollUtil.getFirst(exists).getId());
            return update(record);
        } else {
            return insert(record);
        }
    }

    /**
     * 新增
     *
     * @param record
     * @return {@link ResultMode}<{@link Integer}>
     */
    public Long insert(CmdEmployeeInfoEntity record) {
        return cmdEmployeeInfoRepository.insert(record);
    }


    /**
     * 根据主键修改
     *
     * @param record
     * @return {@link ResultMode}<{@link Long}>
     */
    public Long update(CmdEmployeeInfoEntity record) {
        return cmdEmployeeInfoRepository.update(record);
    }

    /**
     * 根据入参查询，返回参数，如果入参为空，返回空；注意场景
     * @param filter
     * @return
     */
    public List<CmdEmployeeInfoPO> queryByCondition(CmdEmployeeInfoQuery filter) {
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(filter))) {
            return new ArrayList<>();
        }
        return cmdEmployeeInfoRepository.queryList(filter);
    }

    public CmdEmployeeInfoPO queryByEmpName(String empName) {
        CmdEmployeeInfoQuery filter = new CmdEmployeeInfoQuery();
        filter.setEmpName(empName);
        List<CmdEmployeeInfoPO> list = queryByCondition(filter);
        return CollUtil.isEmpty(list) ? null : CollUtil.getFirst(list);
    }
}
