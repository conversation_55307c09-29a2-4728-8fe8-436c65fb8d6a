package com.wanlianyida.basemdm.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyAccountEntity;
import com.wanlianyida.basemdm.domain.model.entity.MdmRegionEntity;
import com.wanlianyida.basemdm.domain.repository.MdmRegionRepository;
import com.wanlianyida.basemdm.domain.service.MdmRegionDomainService;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmExceptionEnum;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmRegionDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmRegionQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业管理员账号表 Service
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Service
public class MdmRegionAppService {

    @Resource
    private MdmRegionRepository mdmRegionRepository;

    @Resource
    private MdmRegionDomainService mdmRegionDomainService;


    public List<MdmRegionDTO> queryByParentCode(String parentCode) {
        if (parentCode == null) {
            throw new CtpCoreMdmException("参数不能为空");
        }
        List<MdmRegionEntity> list = mdmRegionDomainService.queryByParentCode(parentCode);
        // 分页列表vo转换
        List<MdmRegionDTO> vos = BeanUtil.copyToList(list, MdmRegionDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return vos;
    }

    /**
     * 列表查询
     *
     * @param filter
     * @return {@link List}<{@link CmdCompanyAccountEntity}>
     */
    public List<MdmRegionDTO> queryList(MdmRegionQuery filter) {
        List<MdmRegionEntity> list = mdmRegionRepository.queryList(filter);
        // 分页列表vo转换
        List<MdmRegionDTO> vos = BeanUtil.copyToList(list, MdmRegionDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return vos;
    }

    public ResultMode<String> add(MdmRegionEntity entity) {
        ResultMode resultMode = checkRegionArgsForAdd(entity);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }
        return mdmRegionDomainService.add(entity);
    }

    public ResultMode<String> update(MdmRegionEntity entity) {
        ResultMode resultMode = checkRegionArgsForUpdate(entity);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }
        return mdmRegionDomainService.update(entity);
    }


    public ResultMode<String> checkRegionArgsForAdd(MdmRegionEntity entity) {
        List<MdmRegionEntity> list = mdmRegionRepository.selectAll();
        if (!CollectionUtils.isEmpty(list)) {
            //行政区代码
            boolean codeExists = list.stream().anyMatch(item -> entity.getCode().equals(item.getCode()));
            if (codeExists) {
                return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区代码已经存在,请重试");
            }
            //行政区名称
            boolean nameExists = list.stream().anyMatch(item -> entity.getName().equals(item.getName()));
            if (nameExists) {
                return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区名称已经存在,请重试");
            }
            //排序号（只校验同一父级下）
            boolean sortNoExists = list.stream()
                    .anyMatch(item -> entity.getParentCode().equals(item.getParentCode()) && entity.getSortNo().intValue() == item.getSortNo().intValue());
            if (sortNoExists) {
                return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "排序编号已经存在,请重试");
            }
        }
        return ResultMode.success();
    }

    /**
     * 修改参数校验
     */
    public ResultMode<String> checkRegionArgsForUpdate(MdmRegionEntity entity) {
        List<MdmRegionEntity> list = mdmRegionRepository.selectAll();
        if (!CollectionUtils.isEmpty(list)) {
            //行政区名称
            boolean nameExists = list.stream().anyMatch(item -> !entity.getCode().equals(item.getCode()) && entity.getName().equals(item.getName()));
            if (nameExists) {
                return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区名称已经存在,请重试");
            }
            //排序号（只校验同一父级下，且排除自己）
            boolean sortNoExists = list.stream()
                    .anyMatch(item -> !entity.getCode().equals(item.getCode())
                            && entity.getParentCode().equals(item.getParentCode())
                            && entity.getSortNo().intValue() == item.getSortNo().intValue());
            if (sortNoExists) {
                return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "排序编号已经存在,请重试");
            }
        }
        return ResultMode.success();
    }

}
