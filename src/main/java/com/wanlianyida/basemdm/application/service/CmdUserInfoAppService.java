package com.wanlianyida.basemdm.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.domain.model.bo.UserCompanyBindCountBO;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserContactEntity;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserInfoEntity;
import com.wanlianyida.basemdm.domain.model.entity.MdmUserIdentityCardEntity;
import com.wanlianyida.basemdm.domain.repository.MdmOperatorRepository;
import com.wanlianyida.basemdm.domain.repository.MdmUserIdentityCardRepository;
import com.wanlianyida.basemdm.domain.service.CmdUserContactDomainService;
import com.wanlianyida.basemdm.domain.service.CmdUserInfoDomainService;
import com.wanlianyida.basemdm.infrastructure.enums.UserContactTypeEnum;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.interfaces.model.command.CmdUserInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.command.MdmUserIdentityCardCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdUserInfoDTO;
import com.wanlianyida.basemdm.interfaces.model.dto.PlatformUserInfoDetailDTO;
import com.wanlianyida.basemdm.interfaces.model.dto.PlatformUserInfoListDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdGetUserListQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserContactQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserInfoQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserQuery;
import com.wanlianyida.basemdm.interfaces.model.query.PlatformUserInfoListQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 用户信息表 AppService
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Service
public class CmdUserInfoAppService {

    @Resource
    private CmdUserInfoDomainService cmdUserInfoDomainService;
    @Resource
    private CmdUserContactDomainService userContactDomainService;
    @Resource
    private MdmOperatorRepository operatorRepository;
    @Resource
    private MdmUserIdentityCardRepository userIdentityCardRepository;

    /**
     * 分页查询
     *
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link CmdUserInfoDTO}>
     */
    public PageInfo<CmdUserInfoDTO> queryPage(PagingInfo<CmdUserInfoQuery> pagingInfo) {
        return cmdUserInfoDomainService.queryPage(pagingInfo);
    }

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return {@link ResultMode}<{@link CmdUserInfoEntity}>
     */
    public List<CmdUserInfoEntity> queryList(CmdUserInfoQuery query) {
        return cmdUserInfoDomainService.queryList(query);
    }

    /**
     * 新增
     *
     * @param command
     * @return Long
     */
    public Long insert(CmdUserInfoCommand command) {
        return cmdUserInfoDomainService.insert(command);
    }

    /**
     * 修改
     *
     * @param command
     * @return Long
     */
    public Long update(CmdUserInfoCommand command) {
        return cmdUserInfoDomainService.update(command);
    }

    /**
     * 逻辑删除
     *
     * @param id
     * @return Long
     */
    public Long delete(@PathVariable("id") Long id) {
        return cmdUserInfoDomainService.delete(id);
    }

    public CmdUserInfoDTO queryByLoginName(CmdUserInfoQuery query) {
        CmdUserInfoEntity cmdUserInfoEntity = cmdUserInfoDomainService.queryByLoginName(query.getLoginName());
        if (ObjectUtil.isNotEmpty(cmdUserInfoEntity)) {
            return BeanUtil.copyProperties(cmdUserInfoEntity, CmdUserInfoDTO.class);
        }
        return null;
    }

    public CmdUserInfoDTO queryByUserPhone(CmdUserInfoQuery query) {
        CmdUserInfoEntity cmdUserInfoEntity = cmdUserInfoDomainService.queryByUserPhone(query.getMobile());
        if (ObjectUtil.isNotEmpty(cmdUserInfoEntity)) {
            return BeanUtil.copyProperties(cmdUserInfoEntity, CmdUserInfoDTO.class);
        }
        return null;
    }

    public CmdUserInfoDTO queryDetail(Long id) {
        CmdUserInfoEntity cmdUserInfoEntity = cmdUserInfoDomainService.queryDetail(id);
        if (ObjectUtil.isNotEmpty(cmdUserInfoEntity)) {
            return BeanUtil.copyProperties(cmdUserInfoEntity, CmdUserInfoDTO.class);
        }
        return null;
    }

    /**
     * 平台按个人账号列表
     */
    public ResultMode<List<PlatformUserInfoListDTO>> platformPageList(PagingInfo<PlatformUserInfoListQuery> pageQuery) {
        Page<CmdUserInfoEntity> page = PageHelper.startPage(pageQuery.getCurrentPage(), pageQuery.getPageLength(), pageQuery.getCountTotal());
        CmdUserInfoQuery userInfoQuery = BeanUtil.toBean(pageQuery.getFilterModel(), CmdUserInfoQuery.class);
        userInfoQuery.setOrderBy("updated_date desc,id");
        userInfoQuery.setDelFlag("0");
        List<CmdUserInfoEntity> list = cmdUserInfoDomainService.queryList(userInfoQuery);
        if (CollectionUtil.isEmpty(list)) {
            return ResultMode.successPageList(Collections.EMPTY_LIST, 0);
        }
        List<PlatformUserInfoListDTO> result = BeanUtil.copyToList(list, PlatformUserInfoListDTO.class);
        List<Long> userIdList = result.stream().map(PlatformUserInfoListDTO::getId).collect(Collectors.toList());
        // 联系方式
        CmdUserContactQuery contactQuery = new CmdUserContactQuery();
        contactQuery.setUserIdList(userIdList);
        List<CmdUserContactEntity> contactList = userContactDomainService.queryList(contactQuery);
        Map<Long, List<CmdUserContactEntity>> contactGroup = contactList.stream().collect(Collectors.groupingBy(CmdUserContactEntity::getUserId));
        // 绑定企业数量
        Map<Long, UserCompanyBindCountBO> bindMapping = operatorRepository.countBind(userIdList);
        // 封装数据
        Date now = new Date();
        for (PlatformUserInfoListDTO userInfo : result) {
            userInfo.setValidFlag(transferIsValidLongTime(userInfo.getUntilDate()));
            List<CmdUserContactEntity> contacts = contactGroup.get(userInfo.getId());
            if (CollectionUtil.isNotEmpty(contacts)) {
                for (CmdUserContactEntity contact : contacts) {
                    if (UserContactTypeEnum.MOBILE.getCode().equals(contact.getCategory())) {
                        userInfo.setMobile(contact.getContactInfo());
                    }
                    if (UserContactTypeEnum.EMAIL.getCode().equals(contact.getCategory())) {
                        userInfo.setEmail(contact.getContactInfo());
                    }
                    if (UserContactTypeEnum.WECHAT.getCode().equals(contact.getCategory())) {
                        userInfo.setWechat(contact.getContactInfo());
                    }
                }
            }
            if (userInfo.getUntilDate() != null) {
                userInfo.setExpireFlag(userInfo.getUntilDate().before(now) ? "1" : "0");
            }
            UserCompanyBindCountBO bindMap = bindMapping.get(userInfo.getId());
            if (bindMap != null) {
                userInfo.setBindCount(bindMap.getCount());
            } else {
                userInfo.setBindCount(0);
            }
            userInfo.setRealStatus(StrUtil.isBlank(userInfo.getIdCardNo()) ? 0 : 1);
        }
        return ResultMode.successPageList(result, (int) page.getTotal());
    }

    public ResultMode<PlatformUserInfoDetailDTO> platformDetail(Long id) {
        CmdUserInfoQuery userInfoQuery = new CmdUserInfoQuery();
        userInfoQuery.setId(id);
        List<CmdUserInfoEntity> list = cmdUserInfoDomainService.queryList(userInfoQuery);
        if (CollectionUtil.isEmpty(list)) {
            throw new CtpCoreMdmException("未查询到账号信息");
        }
        PlatformUserInfoDetailDTO detail = BeanUtil.toBean(list.get(0), PlatformUserInfoDetailDTO.class);
        CmdUserContactQuery contactQuery = new CmdUserContactQuery();
        contactQuery.setUserId(detail.getId());
        List<CmdUserContactEntity> contactList = userContactDomainService.queryList(contactQuery);
        if (CollectionUtil.isNotEmpty(contactList)) {
            CmdUserContactEntity contact = contactList.get(0);
            if (UserContactTypeEnum.MOBILE.getCode().equals(contact.getCategory())) {
                detail.setMobile(contact.getContactInfo());
            }
            if (UserContactTypeEnum.EMAIL.getCode().equals(contact.getCategory())) {
                detail.setEmail(contact.getContactInfo());
            }
            if (UserContactTypeEnum.WECHAT.getCode().equals(contact.getCategory())) {
                detail.setWechat(contact.getContactInfo());
            }
        }
        detail.setValidFlag(transferIsValidLongTime(detail.getUntilDate()));
        detail.setRealStatus(StrUtil.isBlank(detail.getIdCardNo()) ? 0 : 1);
        MdmUserIdentityCardEntity identityCard = userIdentityCardRepository.queryByUserId(id);
        BeanUtil.copyProperties(identityCard, detail);
        if (detail.getUntilDate() != null) {
            detail.setExpireFlag(detail.getUntilDate().before(new Date()) ? "1" : "0");
        }
        return ResultMode.success(detail);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertIdentityCard(MdmUserIdentityCardCommand command) {
        // 删除原数据
        userIdentityCardRepository.delete(String.valueOf(command.getUserId()));
        userIdentityCardRepository.insert(BeanUtil.toBean(command, MdmUserIdentityCardEntity.class));
    }

    public Integer transferIsValidLongTime(Date time) {
        DateTime limit = DateUtil.parse("2099-12-31 00:00:00");
        return DateUtil.compare(time, limit) < 0 ? 0 : 1;
    }

    public CmdUserInfoDTO queryUserInfo(CmdUserQuery query) {
        if (StrUtil.isEmpty(query.getUserId()) && StrUtil.isEmpty(query.getUserBaseId())){
            throw new CtpCoreMdmException("userId和userBaseId不能同时为空");
        }
        CmdUserInfoEntity cmdUserInfoEntity = cmdUserInfoDomainService.queryUserInfo(query);
        if (ObjectUtil.isNotEmpty(cmdUserInfoEntity)){
            return BeanUtil.toBean(cmdUserInfoEntity, CmdUserInfoDTO.class);
        }
        return null;
    }

    public List<CmdUserInfoDTO> batchQueryUserList(CmdGetUserListQuery query) {
        // 如果所有查询条件都为空，直接返回空列表
        if (allQueryConditionsEmpty(query)) {
            return Collections.emptyList();
        }

        List<CmdUserInfoEntity> cmdUserInfoEntities = cmdUserInfoDomainService.batchQueryUserList(query);
        if (CollectionUtil.isNotEmpty(cmdUserInfoEntities)) {
            return BeanUtil.copyToList(cmdUserInfoEntities, CmdUserInfoDTO.class);
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 判断所有查询条件是否都为空
     * @return boolean
     */
    private boolean allQueryConditionsEmpty(CmdGetUserListQuery query) {
        return CollUtil.isEmpty(query.getUserBaseIdList())
                && CollUtil.isEmpty(query.getPhoneList())
                && CollUtil.isEmpty(query.getLoginNameList())
                && CollUtil.isEmpty(query.getUserIdList())
                && CollUtil.isEmpty(query.getUserNameList());
    }

    public void delUserInfo(Long userId){
        cmdUserInfoDomainService.delUserInfo(userId);
    }

}
