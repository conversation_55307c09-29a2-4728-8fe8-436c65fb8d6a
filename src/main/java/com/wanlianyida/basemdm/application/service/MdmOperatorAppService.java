package com.wanlianyida.basemdm.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.api.model.dto.OperatorDTO;
import com.wanlianyida.basemdm.api.model.query.OperatorQuery;
import com.wanlianyida.basemdm.application.assembler.MdmOperatorAssemble;
import com.wanlianyida.basemdm.domain.model.condition.MdmOperatorCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmOperatorEntity;
import com.wanlianyida.basemdm.domain.service.MdmOperatorDomainService;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmExceptionEnum;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmOperatorPO;
import com.wanlianyida.basemdm.interfaces.model.command.MdmOperatorCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmOperatorDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmOperatorQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class MdmOperatorAppService {
    @Resource
    private MdmOperatorDomainService domainService;


    public Long add(MdmOperatorCommand command) {
        String companyName = command.getCompanyName();
        if (StrUtil.isEmpty(companyName)){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ARGUMENT_EMPTY);
        }

        // req 转entity
        MdmOperatorEntity mdmOperatorEntity = BeanUtil.copyProperties(command, MdmOperatorEntity.class, "id");
        return domainService.add(mdmOperatorEntity);
    }

    public void batchDelete(MdmOperatorCommand command) {
        List<String> idList = command.getIdList();
        if (CollUtil.isEmpty(idList)){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ARGUMENT_EMPTY);
        }

        domainService.batchDelete(idList);
    }

    public MdmOperatorDTO queryDetail(MdmOperatorQuery query) {
        if (ObjectUtil.isEmpty(query.getId())){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ARGUMENT_EMPTY);
        }

        // query 转condition
        MdmOperatorCondition condition = BeanUtil.copyProperties(query, MdmOperatorCondition.class);
        MdmOperatorEntity entity = domainService.queryDetail(condition);
        if (ObjectUtil.isNotEmpty(entity)){
            return BeanUtil.copyProperties(entity, MdmOperatorDTO.class);
        }

        return null;
    }

    public void unbindUser(MdmOperatorCommand command) {
        if (ObjectUtil.isEmpty(command.getId())){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ID_NOT_EMPTY);
        }
        domainService.unbindUser(command.getId(),command.getCompanyId());
    }

    public void update(MdmOperatorCommand command) {
        if (ObjectUtil.isEmpty(command.getId())){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ID_NOT_EMPTY);
        }

        // req 转entity
        MdmOperatorEntity mdmOperatorEntity = BeanUtil.copyProperties(command, MdmOperatorEntity.class);
        domainService.update(mdmOperatorEntity);
    }

    public PageInfo<MdmOperatorDTO> queryPage(PagingInfo<MdmOperatorQuery> pagingInfo) {
        // req 转condition
        MdmOperatorCondition condition = BeanUtil.copyProperties(pagingInfo.getFilterModel(), MdmOperatorCondition.class);
        PagingInfo<MdmOperatorCondition> conditionPage = new PagingInfo<>(condition,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        PageInfo<MdmOperatorEntity> list = domainService.queryPage(conditionPage);
        return MdmOperatorAssemble.getInstance().queryPage(list);
    }

    public List<MdmOperatorDTO> queryList(MdmOperatorQuery query) {
        // query 转condition
        MdmOperatorCondition condition = BeanUtil.copyProperties(query, MdmOperatorCondition.class);
        List<MdmOperatorEntity> list =domainService.queryList(condition);
        if (CollUtil.isNotEmpty(list)){
            return BeanUtil.copyToList(list, MdmOperatorDTO.class);
        }
        return Collections.emptyList();
    }

    public void updateOperatorPhone(MdmOperatorCommand command) {
        String userId = command.getUserId();
        if (StrUtil.isEmpty(userId)){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ARGUMENT_EMPTY.getCode(), "userId不能为空");
        }
        String operatorPhone = command.getOperatorPhone();
        if (StrUtil.isEmpty(operatorPhone)){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ARGUMENT_EMPTY.getCode(), "operatorPhone不能为空");
        }
        // req 转entity
        MdmOperatorEntity mdmOperatorEntity = BeanUtil.copyProperties(command, MdmOperatorEntity.class);
        domainService.updateOperatorPhone(mdmOperatorEntity);

    }

    public void deleteAdmin(String companyId) {
        domainService.deleteAdmin(companyId);
    }

    public List<MdmOperatorDTO> queryOperatorByOperatorCodeList(List<String> operatorCodeList) {
        if (CollUtil.isEmpty(operatorCodeList)){
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ARGUMENT_EMPTY.getCode(), "operatorCode不能为空");
        }
        List<MdmOperatorEntity> operatorEntities = domainService.queryOperatorByOperatorCodeList(operatorCodeList);
        if (CollUtil.isNotEmpty(operatorEntities)){
            return BeanUtil.copyToList(operatorEntities, MdmOperatorDTO.class);
        }
        return null;
    }

    public void accountSuspension(String operatorId) {
        domainService.accountSuspension(operatorId);
    }

    public void accountActivation(String operatorId) {
        domainService.accountActivation(operatorId);
    }

    public List<OperatorDTO> listOperatorsBySysTypeAndIds(OperatorQuery operatorQuery){
        List<MdmOperatorEntity> operatorEntities = domainService.listOperatorsBySysTypeAndIds(operatorQuery);
        List<OperatorDTO> operatorDTOS = new ArrayList<>();
        for(MdmOperatorEntity entity : operatorEntities){
            operatorDTOS.add(new OperatorDTO()
                    .setUserBaseId(String.valueOf(entity.getId()))
                    .setUsername(entity.getOperatorName()));
        }
        return operatorDTOS;
    }

    public MdmOperatorDTO getByOperatorCode(String operatorCode){
        MdmOperatorPO mdmOperatorPO = domainService.getByOperatorCode(operatorCode);
        return BeanUtil.copyProperties(mdmOperatorPO, MdmOperatorDTO.class);
    }

    public Map<String, String> getByOperatorIds(Set<Long> operatorIds){
        List<MdmOperatorPO> list = domainService.getByOperatorIds(operatorIds);
        return list.stream().collect(Collectors.toMap(po -> String.valueOf(po.getId()),
                MdmOperatorPO::getOperatorName));
    }
}
