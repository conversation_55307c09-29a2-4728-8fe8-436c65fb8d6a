package com.wanlianyida.basemdm.infrastructure.exception;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025-06-27
 */
public enum CtpCoreSupportExceptionEnum {

    CODE_ALREADY_EXISTS("CODE_ALREADY_EXISTS", "编码已存在"),
    DICTIONARY_VALUE_EXISTS("DICTIONARY_VALUE_EXISTS", "字典值已存在");

    private String code;
    private String message;

    CtpCoreSupportExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
} 