package com.wanlianyida.basemdm.infrastructure.enums;

import lombok.Getter;

/**
 * 节假日类型枚举
 * <AUTHOR>
 * @date 2024/12/25
 */
@Getter
public enum HolidayTypeEnum {
    LEGAL_HOLIDAY(10, "法定节假日"),
    LEGAL_VACATION(20, "法定休假"),
    WEEKEND_WORKDAY(30, "法定周未转补班");

    public int code;

    public String desc;

    HolidayTypeEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static HolidayTypeEnum getByCode(int codeVal){
        for(HolidayTypeEnum resultCodeEnum : HolidayTypeEnum.values()){
            if(resultCodeEnum.code == codeVal){
                return resultCodeEnum;
            }
        }
        return null;
    }
} 