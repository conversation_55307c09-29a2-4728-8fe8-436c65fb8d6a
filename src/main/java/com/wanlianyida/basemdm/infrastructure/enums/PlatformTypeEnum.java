package com.wanlianyida.basemdm.infrastructure.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/06/16:57
 */
@Getter
public enum PlatformTypeEnum {

    PLATFORM("21","平台端"),
    USER("22","用户端"),
    ;

    public String code;

    public String desc;

    PlatformTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static PlatformTypeEnum getByCode(String codeVal){
        for(PlatformTypeEnum resultCodeEnum : PlatformTypeEnum.values()){
            if(Objects.equals(resultCodeEnum.code, codeVal)){
                return resultCodeEnum;
            }
        }
        return null;
    }

    public static Boolean checkEnum(Integer codeVal){
        for(PlatformTypeEnum resultCodeEnum : PlatformTypeEnum.values()){
            if(Objects.equals(resultCodeEnum.code, codeVal)){
                return true;
            }
        }
        return false;
    }
}
