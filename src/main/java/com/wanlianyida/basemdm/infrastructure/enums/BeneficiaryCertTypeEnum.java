package com.wanlianyida.basemdm.infrastructure.enums;

import lombok.Getter;

/**
 * 企业受益人
 * <AUTHOR>
 * @date 2025/04/27
 */
@Getter
public enum BeneficiaryCertTypeEnum {
    RESIDENT_ID_CARD("1", "身份证"),
    PASSPORT("2", "护照"),
    HONG_KONG_MAINLAND_TRAVEL_PERMIT("3", "香港来往内地通行证"),
    MACAU_MAINLAND_TRAVEL_PERMIT("4", "澳门来往内地通行证"),
    TAIWAN_MAINLAND_TRAVEL_PERMIT("5", "台湾来往内地通行证");

    private String code;
    private String desc;

    BeneficiaryCertTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static BeneficiaryCertTypeEnum getByCode(String codeVal){
        for(BeneficiaryCertTypeEnum resultCodeEnum : BeneficiaryCertTypeEnum.values()){
            if(resultCodeEnum.code == codeVal){
                return resultCodeEnum;
            }
        }
        return null;
    }
}
