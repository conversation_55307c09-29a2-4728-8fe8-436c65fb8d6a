package com.wanlianyida.basemdm.infrastructure.config;

import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.basemdm.infrastructure.enums.PlatformTypeEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 权限类型验证器
 *
 * <AUTHOR>
 * @date 2024/12/07
 */
public class PlatformTypeValidator implements ConstraintValidator<ValidPlatformType, String> {

    @Override
    public void initialize(ValidPlatformType constraintAnnotation) {
    }

    @Override
    public boolean isValid(String platformType, ConstraintValidatorContext context) {
        return ObjectUtil.equal(platformType, PlatformTypeEnum.PLATFORM.getCode()) ||
                ObjectUtil.equal(platformType, PlatformTypeEnum.USER.getCode());
    }
}
