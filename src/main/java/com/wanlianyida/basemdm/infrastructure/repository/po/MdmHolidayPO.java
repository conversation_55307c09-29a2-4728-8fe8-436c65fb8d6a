package com.wanlianyida.basemdm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 国家法定节假日表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@Setter
@TableName("mdm_holiday")
public class MdmHolidayPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日期
     */
    @TableField("cal_date")
    private Date calDate;

    /**
     * 日期范围开始（非数据库字段，用于查询）
     */
    @TableField(exist = false)
    private Date calDateStart;

    /**
     * 日期范围结束（非数据库字段，用于查询）
     */
    @TableField(exist = false)
    private Date calDateEnd;

    /**
     * 10 法定节假日 20 法定休假  30 法定周未转补班
     */
    @TableField("day_type")
    private Integer dayType;

    /**
     * 日期名称（如：春节第一天）
     */
    @TableField("holiday_name")
    private String holidayName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后更新人id
     */
    @TableField("last_updater_id")
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    @TableField("last_update_time")
    private Date lastUpdateTime;
} 