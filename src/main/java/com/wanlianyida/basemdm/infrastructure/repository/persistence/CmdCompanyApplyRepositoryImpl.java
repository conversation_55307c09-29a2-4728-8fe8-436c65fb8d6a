package com.wanlianyida.basemdm.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wanlianyida.basemdm.domain.model.bo.AuditStatisticsBO;
import com.wanlianyida.basemdm.domain.model.bo.StatusStatisticsBO;
import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyApplyEntity;
import com.wanlianyida.basemdm.domain.repository.CmdCompanyApplyRepository;
import com.wanlianyida.basemdm.infrastructure.constant.ServiceNameConstants;
import com.wanlianyida.basemdm.infrastructure.enums.CompanyAuditStatusEnum;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmExceptionEnum;
import com.wanlianyida.basemdm.infrastructure.repository.mapper.CmdCompanyApplyMapper;
import com.wanlianyida.basemdm.interfaces.model.command.CmdCompanyApplyCommand;
import com.wanlianyida.basemdm.interfaces.model.query.CmdCompanyApplyQuery;
import com.wanlianyida.framework.fsscommon.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 企业审核过程表 ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class CmdCompanyApplyRepositoryImpl implements CmdCompanyApplyRepository {

    @Resource
    private CmdCompanyApplyMapper cmdcompanyapplyMapper;
    @Resource
    private IdUtil idUtil;

    /**
     * 条件列表查询
     *
     * @param filter
     * @return {@link List}<{@link CmdCompanyApplyEntity}>
     */
    @Override
    public List<CmdCompanyApplyEntity> queryList(CmdCompanyApplyQuery filter) {
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(filter))) {
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.QUERY_CONDITION_EMPTY);
        }
        List<CmdCompanyApplyEntity> list = cmdcompanyapplyMapper.queryByCondition(filter);
        if (IterUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    /**
     * 新增
     *
     * @param record
     * @return Long
     */
    @Override
    public Long insertSelective(CmdCompanyApplyEntity record) {
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(record))) {
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.PARAMETER_EMPTY);
        }
        setInsertDefaultValue(record);
        cmdcompanyapplyMapper.insertSelective(record);
        return record.getId();
    }

    /**
     * 根据主键修改
     *
     * @param record
     * @return Long
     */
    @Override
    public Long updateByPrimaryKeySelective(CmdCompanyApplyEntity record) {
        checkId(record.getId());
        CmdCompanyApplyEntity recordCheck = new CmdCompanyApplyEntity();
        BeanUtil.copyProperties(record, recordCheck, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        recordCheck.setId(null);
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(recordCheck))) {
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.PARAMETER_EMPTY);
        }
        setUpdateDefaultValue(record);
        cmdcompanyapplyMapper.updateByPrimaryKeySelective(record);
        return record.getId();
    }

    /**
     * 根据主键查询
     *
     * @param id
     * @return {@link CmdCompanyApplyEntity}
     */
    @Override
    public CmdCompanyApplyEntity selectByPrimaryKey(Long id) {
        return checkId(id);
    }


    private void setInsertDefaultValue(CmdCompanyApplyEntity record) {
        record.setId(idUtil.generateId(ServiceNameConstants.BASE_MDM));
        record.setCreatedDate(DateUtil.date());
        record.setCreatorId(Opt.ofBlankAble(record.getCreatorId()).orElse(""));
        record.setUpdatedDate(Opt.ofNullable(record.getUpdatedDate()).orElseGet(DateUtil::date));
        record.setUpdaterId(Opt.ofBlankAble(record.getUpdaterId()).orElse(""));
    }

    private void setUpdateDefaultValue(CmdCompanyApplyEntity record) {
        record.setUpdatedDate(Opt.ofNullable(record.getUpdatedDate()).orElseGet(DateUtil::date));
        record.setUpdaterId(Opt.ofBlankAble(record.getUpdaterId()).orElse(""));
    }

    private CmdCompanyApplyEntity checkId(Long id) {
        Opt.ofNullable(id).orElseThrow(() -> new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ID_NOT_EMPTY));
        CmdCompanyApplyEntity record = cmdcompanyapplyMapper.selectByPrimaryKey(id);
        Opt.ofNullable(record).orElseThrow(() -> new CtpCoreMdmException(CtpCoreMdmExceptionEnum.RECORD_NOT_FOUND));
        return record;
    }

    @Override
    public AuditStatisticsBO statistics(CmdCompanyApplyQuery query) {
        Map<Integer, StatusStatisticsBO> map = cmdcompanyapplyMapper.statistics(query);
        AuditStatisticsBO bo = new AuditStatisticsBO();
        if (map.get(CompanyAuditStatusEnum.WAITING.getCode()) != null) {
            bo.setWaiting(map.get(CompanyAuditStatusEnum.WAITING.getCode()).getCount());
        }
        if (map.get(CompanyAuditStatusEnum.PASS.getCode()) != null) {
            bo.setPass(map.get(CompanyAuditStatusEnum.PASS.getCode()).getCount());
        }
        if (map.get(CompanyAuditStatusEnum.REJECT.getCode()) != null) {
            bo.setReject(map.get(CompanyAuditStatusEnum.REJECT.getCode()).getCount());
        }
        bo.setTotal(bo.getPass() + bo.getReject() + bo.getWaiting());
        return bo;
    }

    @Override
    public Integer updateCompanyApplyStatus(CmdCompanyApplyCommand entity) {
        CmdCompanyApplyEntity record = new CmdCompanyApplyEntity();
        BeanUtil.copyProperties(entity, record, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        int count = cmdcompanyapplyMapper.update(null, new LambdaUpdateWrapper<CmdCompanyApplyEntity>()
                .set(CmdCompanyApplyEntity::getDelFlag, record.getDelFlag())
                .eq(CmdCompanyApplyEntity::getApplySource, record.getApplySource())
                .eq(CmdCompanyApplyEntity::getLicenseNo, record.getLicenseNo()));

        return count;
    }
}
