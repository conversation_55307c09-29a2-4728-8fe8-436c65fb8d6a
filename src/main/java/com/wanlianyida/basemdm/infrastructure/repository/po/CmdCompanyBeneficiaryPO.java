package com.wanlianyida.basemdm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 企业受益人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Getter
@Setter
@TableName("cmd_company_beneficiary")
public class CmdCompanyBeneficiaryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 公司id
     */
    @TableField("company_id")
    private String companyId;

    /**
     * 公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 公司社会信用代码
     */
    @TableField("social_credit_code")
    private String socialCreditCode;

    /**
     * 受益人姓名
     */
    @TableField("beneficiary_name")
    private String beneficiaryName;

    /**
     * 受益人证件类型
     * 1-居民身份证号 2-护照 3-香港往来内地通行证 4-澳门来往内地通行证 5-台湾来往内地通行证
     */
    @TableField("beneficiary_cert_type")
    private String beneficiaryCertType;

    /**
     * 受益人证件号码
     */
    @TableField("beneficiary_cert_number")
    private String beneficiaryCertNumber;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 截止日期
     */
    @TableField("until_date")
    private Date untilDate;

    /**
     * 长期有效标志[0-否,1-是]
     */
    @TableField("long_term_valid_flag")
    private Integer longTermValidFlag;

    /**
     * 创建用户id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 删除标志[0-正常,1-删除]
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 法人标志[0-否,1-是]
     */
    @TableField("legal_person_flag")
    private Integer legalPersonFlag;

    /**
     * 身份证国徽面地址
     */
    @TableField("front_url")
    private String frontUrl;

    /**
     * 身份证人像面地址
     */
    @TableField("behind_url")
    private String behindUrl;
}
