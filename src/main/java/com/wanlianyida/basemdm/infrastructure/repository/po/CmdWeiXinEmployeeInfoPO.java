package com.wanlianyida.basemdm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 企业组织结构表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@TableName("cmd_employee_info")
public class CmdWeiXinEmployeeInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 企微userId
     */
    @TableField("emp_id")
    private String empId;

    /**
     * 员工姓名
     */
    @TableField("emp_name")
    private String empName;

    /**
     * 员工手机号
     */
    @TableField("emp_mobile")
    private String empMobile;

    /**
     * 员工编号
     */
    @TableField("emp_no")
    private String empNo;

    /**
     * 员工主要部门id
     */
    @TableField("emp_main_department")
    private String empMainDeptId;

    /**
     * 员工职位
     */
    @TableField("emp_position")
    private String empPosition;

    /**
     * 员工状态[10-启用,20-禁用,30-离职]
     */
    @TableField("emp_status")
    private String empStatus;

    /**
     * 状态
     */
    @TableField("valid_flag")
    private String validFlag;


    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    @TableField("del_flag")
    private String delFlag;
}

