package com.wanlianyida.basemdm.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserInfoEntity;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserInfoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息表 Mapper
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Mapper
public interface CmdUserInfoMapper extends BaseMapper<CmdUserInfoEntity> {

	/**
	 * 条件查询列表
	 *
	 * @param query
	 * @return {@link List}<{@link CmdUserInfoQuery}>
	 */
	List<CmdUserInfoEntity> queryByCondition(CmdUserInfoQuery query);

	/**
     * 新增
     *
     * @param entity
     */
	void insertSelective(CmdUserInfoEntity entity);

	/**
	 * 根据主键修改
	 *
	 * @param entity
	 */
	void updateByPrimaryKeySelective(CmdUserInfoEntity entity);

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link CmdUserInfoEntity}
	 */
	CmdUserInfoEntity selectByPrimaryKey(@Param("id") Long id);

}
