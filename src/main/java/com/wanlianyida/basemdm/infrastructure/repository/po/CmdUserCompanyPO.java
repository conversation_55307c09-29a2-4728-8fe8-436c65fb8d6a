package com.wanlianyida.basemdm.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 企业组织结构表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@TableName("cmd_user_company")
public class CmdUserCompanyPO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户身份证号
     */
    @TableField("license_no")
    private String userIdCardNo;

    /**
     * 用户手机号
     */
    @TableField("user_mobile")
    private String userMobile;

    /**
     * 企业id
     */
    @TableField("company_id")
    private Long companyId;

    /**
     * 企业统一社会信用代码
     */
    @TableField("company_social_credit_code")
    private String companySocialCreditCode;

    /**
     * 状态
     */
    @TableField("valid_flag")
    private String validFlag;


    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    @TableField("del_flag")
    private String delFlag;
}

