package com.wanlianyida.basemdm.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.basemdm.domain.model.bo.UserCompanyBindCountBO;
import com.wanlianyida.basemdm.domain.model.condition.MdmOperatorCondition;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmOperatorPO;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 操作员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
public interface CmdOperatorMapper extends BaseMapper<MdmOperatorPO> {

    /**
     * 条件查询
     * @param condition
     * @return
     */
    List<MdmOperatorPO> queryPage(MdmOperatorCondition condition);

    @MapKey("userId")
    Map<Long, UserCompanyBindCountBO> countBind(List<Long> userIdList);

}
