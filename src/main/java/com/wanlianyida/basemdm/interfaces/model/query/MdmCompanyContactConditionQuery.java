package com.wanlianyida.basemdm.interfaces.model.query;

import lombok.Data;

@Data
public class MdmCompanyContactConditionQuery {
    /**
     * 主键id
     */
    private String id;
    /**
     * 企业id
     */
    private String companyId;
    /**
     *  企业编码
     */
    private String companyCode;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 默认联系人标识 0-否 1-是
     */
    private Integer defaultContactFlag;
    /**
     * 平台编码
     */
    private String platformCode;
}
