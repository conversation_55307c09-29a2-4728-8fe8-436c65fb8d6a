package com.wanlianyida.basemdm.interfaces.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class CmdWeiXinEmployeeDeptInfoQuery {

    private Long id;

    /**
     * 员工id
     */
    private String empId;


    /**
     * (企业微信)部门id
     */
    private String deptId;

    /**
     * 是否是领导[1-是,0-否]
     */
    private String empIsLeader;

    /**
     * 领导Id
     */
    private String leaderEmpId;

    /**
     * 状态
     */
    private String validFlag;


    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;

    /**
     * 员工id列表
     */
    private List<String> empIdList;
}
