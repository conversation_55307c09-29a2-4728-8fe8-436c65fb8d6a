package com.wanlianyida.basemdm.interfaces.model.command;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 操作员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
@Data
public class MdmOperatorCommand {

    /**
     * id
     */
    private String id;

    /**
     * id集合
     */
    private List<String> idList;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 授权书id
     */
    private String accreditId;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业简称
     */
    private Short companyShortName;

    /**
     * 企业编码
     */
    private String companyCode;


    /**
     * 操作员类型[10-管理员,20-用户]
     */
    private String operatorType;

    /**
     * 操作员账号
     */
    private String operatorAccount;

    /**
     * 操作员编码
     */
    private String operatorCode;


    /**
     * 默认企业标志[0-否,1-是]
     */
    private Integer defaultCompanyFlag;

    /**
     * 操作员名称
     */
    private String operatorName;

    /**
     * 操作员手机号
     */
    private String operatorPhone;

    /**
     * 平台编码[21-大宗管理端 22-大宗用户端]
     */
    private String platformCode;



    /**
     * 状态[10-启用,20-停用]
     */
    private String validFlag;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 创建用户名称
     */
    private String creatorName;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新人名称
     */
    private String updaterName;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer delFlag;

    /**
     * 邀请状态[10-未邀请,20-邀请中,30-已接收邀请]
     */
    private String inviteStatus;

}
