package com.wanlianyida.basemdm.interfaces.model.command;

import com.wanlianyida.basemdm.infrastructure.config.ValidPlatformType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 组织添加
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class UmOrgCommand {
    /**
     * 主键
     */
    private Long id;

    /**
     * 上级统一信用代码
     */
    private String parentLicenseNo;

    /**
     * 描述
     */
    private String remark;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @ValidPlatformType
    @NotNull(message = "平台类型不能为空")
    private String platformType;

    /**
     * 更新人id
     */
    private String updaterId;
}
