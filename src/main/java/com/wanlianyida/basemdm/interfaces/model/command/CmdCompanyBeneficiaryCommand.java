package com.wanlianyida.basemdm.interfaces.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <p>
 * 企业受益人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
public class CmdCompanyBeneficiaryCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @NotNull(groups = {UpdateGroup.class, DeleteGroup.class},message = "id不能为空")
    private String id;

    /**
     * 公司id
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "公司id不能为空")
    private String companyId;

    /**
     * 公司名称
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "公司名称不能为空")
    private String companyName;

    /**
     * 公司社会信用代码
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "公司社会信用代码不能为空")
    @Size(max = 30, groups = {CreateGroup.class, UpdateGroup.class}, message = "公司社会信用代码长度不能超过30")
    private String socialCreditCode;

    /**
     * 受益人姓名
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "受益人姓名不能为空")
    private String beneficiaryName;

    /**
     * 受益人证件类型
     * 1-居民身份证号 2-护照 3-香港往来内地通行证 4-澳门来往内地通行证 5-台湾来往内地通行证
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "受益人证件类型不能为空")
    @Size(max = 4, groups = {CreateGroup.class, UpdateGroup.class}, message = "受益人证件类型长度不能超过4")
    private String beneficiaryCertType;

    /**
     * 受益人证件号码
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "受益人证件号码不能为空")
    @Size(max = 36, groups = {CreateGroup.class, UpdateGroup.class}, message = "受益人证件号码类型长度不能超过36")
    private String beneficiaryCertNumber;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 截止日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date untilDate;

    /**
     * 长期有效标志[0-否,1-是]
     */
    private Integer longTermValidFlag;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 法人标志[0-否,1-是]
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "法人标志不能为空")
    private Integer legalPersonFlag;

    /**
     * 身份证国徽面地址
     */
    private String frontUrl;

    /**
     * 身份证人像面地址
     */
    private String behindUrl;
}
