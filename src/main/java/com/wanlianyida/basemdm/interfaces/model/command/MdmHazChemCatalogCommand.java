package com.wanlianyida.basemdm.interfaces.model.command;

import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 危化品目录Command
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class MdmHazChemCatalogCommand implements Serializable {

    private static final long serialVersionUID = 1L;
     @NotNull(groups = {UpdateGroup.class, DeleteGroup.class},message = "id不能为空")
    private Long id;                    // 主键

    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "危化品名称不能为空")
    @Size(max = 150, groups = {CreateGroup.class, UpdateGroup.class}, message = "危化品名称不能超过150")
    private String hazchemName;         // 危化品名称

    private Integer disableFlag;        // 禁用标志[1-禁用,0-启用]
    @Size(max = 150, groups = {CreateGroup.class, UpdateGroup.class}, message = "备注不能超过150")
    private String remark;              // 备注
    @NotBlank(groups = {CreateGroup.class},message = "新增人id不能为空")
    private String creatorId;           // 创建人id
    @NotBlank(groups = {UpdateGroup.class, DeleteGroup.class},message = "修改人id不能为空")
    private String lastUpdaterId;       // 最后更新人id
} 
