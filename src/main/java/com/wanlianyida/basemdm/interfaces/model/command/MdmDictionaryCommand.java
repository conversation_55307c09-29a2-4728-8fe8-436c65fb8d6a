package com.wanlianyida.basemdm.interfaces.model.command;

import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 字典表(MdmDictionary)命令对象
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
public class MdmDictionaryCommand {
    // 主键id
    @NotNull(groups = {UpdateGroup.class, DeleteGroup.class},message = "id不能为空")
    private Long id;

    // 平台编码
    @NotBlank(groups =  {CreateGroup.class, UpdateGroup.class},message = "平台编码不能为空")
    private String platCode;

    // 字典编码
    @NotBlank(groups =  {CreateGroup.class, UpdateGroup.class},message = "字典编码不能为空")
    @Size(max = 30, groups = {CreateGroup.class, CreateGroup.class}, message = "字典编码不能超过30位")
    @Pattern(regexp = "[a-zA-Z]+(_[a-zA-Z]+)+", message = "请输入规范的编码格式")
    private String dictCode;

    // 字典名称

    @NotBlank(groups =  {CreateGroup.class, UpdateGroup.class},message = "字典名称不能为空")
    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "字典名称不能超过50位")
    private String dictName;

    // 字典描述
    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "字典描述不能超过50位")
    private String dictDesc;

    // 创建人id
    private String creatorId;

    // 最后更新人id
    private String lastUpdaterId;
} 
