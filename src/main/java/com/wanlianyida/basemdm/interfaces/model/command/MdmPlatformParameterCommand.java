package com.wanlianyida.basemdm.interfaces.model.command;

import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 平台参数Command
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class MdmPlatformParameterCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    @NotNull(groups = { UpdateGroup.class, DeleteGroup.class},message = "id不能为空")
    private Long id;                    // 参数id


    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "平台编码不能为空")
    private String plfCode;             // 平台编码


    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "参数编码不能为空")
    @Size(max = 36, groups = {CreateGroup.class, UpdateGroup.class}, message = "参数编码不能为空不能超过36")
    private String paraCode;            // 参数编码


    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "参数值不能为空")
    @Size(max = 100, groups = {CreateGroup.class, UpdateGroup.class}, message = "参数值不能超过100")
    private String paraValue;           // 参数值


    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "参数名称不能为空不能超过50")
    private String paraName;            // 参数名称


    @Size(max = 100, groups = {CreateGroup.class, UpdateGroup.class}, message = "参数说明不能为空不能超过100")
    private String paraDesc;            // 参数说明


    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "参数分组编码长度不能超过50")
    private String groupCode;           // 参数分组编码


    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "参数分组名称长度不能超过50")
    private String groupName;           // 参数分组名称

    private Integer delFlag;            // 删除标志[0-否,1-是]

    private String creatorId;           // 创建人id

    private String lastUpdaterId;       // 最后更新人id
} 
