package com.wanlianyida.basemdm.interfaces.model.command;

import lombok.Data;

import java.util.Date;

@Data
public class UpdateCompanyTaxNoAndOrgInfoCommand {
    /**
     * 企业信用编码
     */
    private String licenseNo;
    /**
     * 组织机构代码
     */
    private String orgCode;

    /**
     * 组织机构代码证书开始日期'
     */
    private Date orgCodeCertStartDate;

    /**
     * 组织机构代码有效期证书结束日期
     */
    private Date orgCodeCertEndDate;

    /**
     * 组织机构文件url
     */
    private String orgFileUrl;

    /**
     * 组织机构长期有效标志[0-否,1-是]
     */
    private String orgLongTermFlag;

    /**
     * 税务号
     */
    private String taxNo;

    /**
     * 税务文件url
     */
    private String taxFileUrl;
}
