package com.wanlianyida.basemdm.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月09日 10:53
 */
@Data
public class PlatformUserInfoDetailDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("登录用户名")
    private String loginName;

    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("微信号")
    private String wechat;

    @ApiModelProperty("实名认证状态，1已认证，0未认证")
    private Integer realStatus;

    @ApiModelProperty("是否启用:1是,0否")
    private String enableFlag;

    @ApiModelProperty("是否长期有效，1是0否")
    private Integer validFlag;

    @ApiModelProperty("是否过期，1已过期，0未过期")
    private String expireFlag;

    @ApiModelProperty("停用原因")
    private String enableReason;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty("性别，1男2女")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countyCode;

    @ApiModelProperty("县名称")
    private String countyName;

    @ApiModelProperty("详细地址")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issuingAgency;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date untilDate;
}
