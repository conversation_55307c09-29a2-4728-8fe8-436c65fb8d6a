package com.wanlianyida.basemdm.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 国家法定节假日表DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class MdmHolidayDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date calDate;

    /**
     * 10 法定节假日 20 法定休假  30 法定周未转补班
     */
    private Integer dayType;

    /**
     * 节假日类型描述
     */
    private String dayTypeDesc;

    /**
     * 日期名称（如：春节第一天）
     */
    private String holidayName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer delFlag;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后更新人id
     */
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateTime;
} 