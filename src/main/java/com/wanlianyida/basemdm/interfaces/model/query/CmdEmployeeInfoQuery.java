package com.wanlianyida.basemdm.interfaces.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 企业组织结构
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
@Data
public class CmdEmployeeInfoQuery {

    private Long id;

    /**
     * 用户名称
     */
    private String empName;

    /**
     * 用户身份证号
     */
    private String empIdCardNo;

    /**
     * 用户手机号
     */
    private String empMobile;

    /**
     * 员工编号
     */
    private String empNo;

    /**
     * 企业统一社会信用代码
     */
    private String companySocialCreditCode;

    /**
     * 企业名称全称
     */
    private String companyName;

    /**
     * 企业简称
     */
    private String companyShortName;

    /**
     * 状态
     */
    private String validFlag;


    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;
}
