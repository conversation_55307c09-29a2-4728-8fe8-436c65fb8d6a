package com.wanlianyida.basemdm.interfaces.model.dto;

import lombok.Data;

/**
 * 企业组织结构
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
@Data
public class CmdWeiXinEmployeeDeptDTO {

    private Long id;

    /**
     * 员工id
     */
    private String empId;


    /**
     * 员工名称
     */
    private String empName;


    /**
     * 员工编号
     */
    private String empNo;


    /**
     * 员工职位
     */
    private String empPosition;


    /**
     * (企业微信)部门id
     */
    private String deptId;


    /**
     * (企业微信)部门名称
     */
    private String deptName;

    /**
     * 是否是领导[1-是,0-否]
     */
    private String empIsLeader;

    /**
     * 领导Id
     */
    private String leaderEmpId;

    /**
     * 领导名称
     */
    private String leaderEmpName;

    /**
     * 状态
     */
    private String validFlag;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;
}
