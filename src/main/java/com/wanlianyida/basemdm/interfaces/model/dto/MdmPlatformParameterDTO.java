package com.wanlianyida.basemdm.interfaces.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 平台参数DTO
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class MdmPlatformParameterDTO {
    
    private static final long serialVersionUID = 1L;
    
    private Long id;                    // 参数id
    private String plfCode;             // 平台编码
    private String paraCode;            // 参数编码
    private String paraValue;           // 参数值
    private String paraName;            // 参数名称
    private String paraDesc;            // 参数说明
    private String groupCode;           // 参数分组编码
    private String groupName;           // 参数分组名称
    private Integer delFlag;            // 删除标志[0-否,1-是]
    private String creatorId;           // 创建人id
    private Date createTime;            // 创建时间
    private String lastUpdaterId;       // 最后更新人id
    private Date lastUpdateTime;        // 最后更新时间
} 