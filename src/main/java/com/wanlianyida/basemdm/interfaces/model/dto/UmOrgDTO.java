package com.wanlianyida.basemdm.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 组织DTO
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class UmOrgDTO {

    /**
     * 主键
     */
    private String id;

    /**
     * 企业名称
     */
    private String licenseName;

    /**
     * 信用代码
     */
    private String licenseNo;

    /**
     * 企业联系人，发送验证码用
     */
    private String phone;

    /**
     * 上级组织id
     */
    private String parentId;

    /**
     * 上级组织名称
     */
    private String parentLicenseName;

    /**
     * 上级组织信用代码
     */
    private String parentLicenseNo;

    /**
     * 平台类型 10用户端 20管理端
     */
    private String platformType;


    /**
     * 描述
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
