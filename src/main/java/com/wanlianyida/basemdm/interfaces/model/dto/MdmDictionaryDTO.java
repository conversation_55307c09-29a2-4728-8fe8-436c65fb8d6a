package com.wanlianyida.basemdm.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 字典表(MdmDictionary)数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
public class MdmDictionaryDTO {
    // 主键id
    private Long id;

    // 平台编码
    private String platCode;

    // 字典编码
    private String dictCode;

    // 字典名称
    private String dictName;

    // 字典描述
    private String dictDesc;

    // 删除标志[0-否,1-是]
    private Integer delFlag;

    // 创建人id
    private String creatorId;

    // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 最后更新人id
    private String lastUpdaterId;

    // 最后更新时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateTime;
} 