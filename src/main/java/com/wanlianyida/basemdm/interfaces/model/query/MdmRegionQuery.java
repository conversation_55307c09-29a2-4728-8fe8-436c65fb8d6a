package com.wanlianyida.basemdm.interfaces.model.query;

import lombok.Data;

import java.util.List;

/**
 * 企业管理员账号表 Query
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Data
public class MdmRegionQuery {

	private Long id;           // id
	private String code;          // 区域编码
	private List<String> codes;
	private String parentCode;    // 父节点区域编码
	private String name;            // 名字
	private Integer level;          // 层级（固定10层）
	private Integer sortNo;           // 排序
	private Integer delFlag;
}
