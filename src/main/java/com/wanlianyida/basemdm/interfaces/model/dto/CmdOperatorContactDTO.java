package com.wanlianyida.basemdm.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 操作员信息表 DTO
 *
 * <AUTHOR>
 * @date 2025-05-23 10:01:42
 */
@Data
public class CmdOperatorContactDTO {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 操作员id
	 */
	private Long operatorId;

	/**
	 * 操作员姓名
	 */
	private String operatorName;

	/**
	 * 登录名
	 */
	private String loginName;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 启用标识[1-启用,0-停用]
	 */
	private String enableFlag;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

}
