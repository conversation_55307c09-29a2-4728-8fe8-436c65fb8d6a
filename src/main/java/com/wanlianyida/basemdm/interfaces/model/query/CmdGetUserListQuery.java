package com.wanlianyida.basemdm.interfaces.model.query;

import lombok.Data;

import java.util.List;

@Data
public class CmdGetUserListQuery {
    /**
     * 用户登录名列表
     */
    private List<String> loginNameList;

    /**
     * userBaseId列表
     */
    private List<String> userBaseIdList;

    /**
     * userId列表
     */
    private List<String> userIdList;

    /**
     * 手机号列表
     */
    private List<String> phoneList;

    /**
     * 用户名列表
     */
    private List<String> userNameList;

}
