package com.wanlianyida.basemdm.interfaces.model.command;

import lombok.Data;

@Data
public class MdmCompanyContactCommand {
    /**
     * id
     */
    private String id;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;


    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 默认联系人标志[0-否,1-是]
     */
    private Integer defaultContactFlag;

     /**
     * 平台编码[10-大宗,20-物流]
     */
    private String platformCode;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 创建用户名称
     */
    private String creatorName;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新人名称
     */
    private String updaterName;

}
