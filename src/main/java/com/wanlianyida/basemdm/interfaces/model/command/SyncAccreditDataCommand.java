package com.wanlianyida.basemdm.interfaces.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月23日 09:46
 */
@Data
public class SyncAccreditDataCommand {

    @NotNull(message = "操作员信息不能为空")
    private MdmOperatorCommand operatorCommand;

    @NotNull(message = "授权信息不能为空")
    private MdmAccreditOperatorInsertCommand accreditCommand;
}
