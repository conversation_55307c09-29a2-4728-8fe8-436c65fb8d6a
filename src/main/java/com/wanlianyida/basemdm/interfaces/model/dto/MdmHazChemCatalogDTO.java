package com.wanlianyida.basemdm.interfaces.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 危化品目录DTO
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class MdmHazChemCatalogDTO {
    
    private static final long serialVersionUID = 1L;
    
    private Long id;                    // 主键
    private String hazchemName;         // 危化品名称
    private Integer disableFlag;        // 禁用标志[1-禁用,0-启用]
    private String remark;              // 备注
    private String creatorId;           // 创建人id
    private Date createTime;            // 创建时间
    private String lastUpdaterId;       // 最后更新人id
    private Date lastUpdateTime;        // 最后更新时间
} 