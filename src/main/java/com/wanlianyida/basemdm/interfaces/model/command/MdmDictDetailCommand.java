package com.wanlianyida.basemdm.interfaces.model.command;

import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 字典值(MdmDictDetail)命令对象
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
public class MdmDictDetailCommand {
    // 主键id
    @NotNull(groups = {UpdateGroup.class, DeleteGroup.class},message = "id不能为空")
    private Long id;

    // 平台编码
    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "字典编码不能为空")
    private String platCode;

    // 字典编码
    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "字典编码不能为空")
    @Size(max = 30, groups = {CreateGroup.class, UpdateGroup.class}, message = "字典编码不能超过30位")
    private String dictCode;

    // 字典值
    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "字典值不能为空")
    @Size(max = 30, groups = {CreateGroup.class, UpdateGroup.class}, message = "字典值不能超过30位")
    private String dictValue;

    // 字典值名称
    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class},message = "字典值名称不能为空")
    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "字典值名称不能超过50位")
    private String valueName;

    // 排序号
    @NotNull(groups ={CreateGroup.class, UpdateGroup.class},message = "排序号不能为空")
    private Integer sortNo;

    // 字典值描述
    @Size(max = 50, groups = {CreateGroup.class, UpdateGroup.class}, message = "字典值描述不能超过50位")
    private String detailDesc;

    // 删除标志[0-否,1-是]
    private Integer delFlag;

    // 禁用标志[0-否,1-是]
    private Integer disableFlag;

    // 创建人id
    private String creatorId;

    // 最后更新人id
    private String lastUpdaterId;
} 
