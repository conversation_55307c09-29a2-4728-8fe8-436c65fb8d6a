package com.wanlianyida.basemdm.interfaces.model.command;

import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 国家法定节假日表Command
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class    MdmHolidayCommand {

    /**
     * 主键
     */
    @NotNull(groups = {UpdateGroup.class, DeleteGroup.class},message = "id不能为空")
    private Long id;

    /**
     * ID列表（用于批量删除）
     */
    private List<Long> idList;

    /**
     * 日期
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "节假日日期不能为空")
    private Date calDate;

    /**
     * 10 法定节假日 20 法定休假  30 法定周未转补班
     */
    @NotNull(groups = {CreateGroup.class,UpdateGroup.class},message = "节假日类型不能为空")
    private Integer dayType;

    /**
     * 日期名称（如：春节第一天）
     */
    private String holidayName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 最后更新人id
     */
    private String lastUpdaterId;
} 
