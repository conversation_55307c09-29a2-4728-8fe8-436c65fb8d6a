package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.application.service.MdmHazChemCatalogAppService;
import com.wanlianyida.basemdm.domain.model.entity.MdmHazChemCatalogEntity;
import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import com.wanlianyida.basemdm.interfaces.model.command.MdmHazChemCatalogCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmHazChemCatalogDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmHazChemCatalogQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 危化品目录管理
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@RestController
@RequestMapping("/mdm/hazchem")
public class MdmHazChemCatalogController {
    @Resource
    private MdmHazChemCatalogAppService mdmHazChemCatalogAppService;

//    /**
//     * 条件查询危化品目录
//     *
//     * @param filter 查询条件
//     * @return {@link ResultMode}<{@link List}<{@link MdmHazChemCatalogDTO}>>
//     */
//    @PostMapping("/list-query")
//    public ResultMode<List<MdmHazChemCatalogDTO>> listQuery(@RequestBody MdmHazChemCatalogQuery filter) {
//        List<MdmHazChemCatalogDTO> list = mdmHazChemCatalogAppService.queryList(filter);
//        return ResultMode.success(list);
//    }

    /**
     * 分页查询危化品目录
     *
     * @param pagingInfo 分页参数
     * @return {@link ResultMode}<{@link List}<{@link MdmHazChemCatalogDTO}>>
     */
    @PostMapping("/page")
    public ResultMode<List<MdmHazChemCatalogDTO>> listQueryPage(@RequestBody PagingInfo<MdmHazChemCatalogQuery> pagingInfo) {
        PageInfo<MdmHazChemCatalogDTO> pageInfo = mdmHazChemCatalogAppService.queryPage(pagingInfo);
        if (IterUtil.isEmpty(pageInfo.getList())) {
            return ResultMode.success();
        }
        ResultMode<List<MdmHazChemCatalogDTO>> resultMode = new ResultMode<>();
        int total = (int) pageInfo.getTotal();
        resultMode.setTotal(total);
        resultMode.setModel(pageInfo.getList());
        resultMode.setSucceed(true);
        return ResultMode.successPageList(resultMode.getModel(), resultMode.getTotal());

    }

//    /**
//     * 根据ID查询危化品目录
//     *
//     * @param id 主键
//     * @return {@link ResultMode}<{@link MdmHazChemCatalogDTO}>
//     */
//    @PostMapping("/query-by-id")
//    public ResultMode<MdmHazChemCatalogDTO> queryById(@RequestParam("id") Long id) {
//        MdmHazChemCatalogDTO dto = mdmHazChemCatalogAppService.selectById(id);
//        return ResultMode.success(dto);
//    }

    /**
     * 根据危化品名称查询危化品目录
     *
     * @param hazchemName 危化品名称
     * @return {@link ResultMode}<{@link MdmHazChemCatalogDTO}>
     */
    @PostMapping("/hazchem-name")
    public ResultMode<MdmHazChemCatalogDTO> queryByHazchemName(@RequestParam("hazchemName") String hazchemName) {
        MdmHazChemCatalogDTO dto = mdmHazChemCatalogAppService.selectByHazchemName(hazchemName);
        return ResultMode.success(dto);
    }

    /**
     * 新增记录
     *
     * @param command 命令对象
     * @return {@link ResultMode}<{@link String}>
     */
    @PostMapping("/add")
    public ResultMode<String> add(@RequestBody @Validated(CreateGroup.class) MdmHazChemCatalogCommand command) {

        MdmHazChemCatalogEntity entity = BeanUtil.toBean(command, MdmHazChemCatalogEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmHazChemCatalogAppService.add(entity);
    }

    /**
     * 修改记录
     *
     * @param command 命令对象
     * @return {@link ResultMode}<{@link String}>
     */
    @PostMapping("/update")
    public ResultMode<String> update(@RequestBody @Validated(UpdateGroup.class) MdmHazChemCatalogCommand command) {
        MdmHazChemCatalogEntity entity = BeanUtil.toBean(command, MdmHazChemCatalogEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmHazChemCatalogAppService.update(entity);
    }

    /**
     * 删除记录（逻辑删除）
     *
     * @param command 命令对象（包含ID和更新人信息）
     * @return {@link ResultMode}<{@link String}>
     */
    @PostMapping("/delete")
    public ResultMode<String> delete(@RequestBody @Validated(DeleteGroup.class) MdmHazChemCatalogCommand command) {
        MdmHazChemCatalogEntity entity = BeanUtil.toBean(command, MdmHazChemCatalogEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmHazChemCatalogAppService.deleteById(entity);
    }


} 
