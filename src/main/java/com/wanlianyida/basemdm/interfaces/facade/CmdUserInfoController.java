package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.application.service.CmdUserInfoAppService;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserInfoEntity;
import com.wanlianyida.basemdm.interfaces.model.command.CmdUserInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.command.MdmUserIdentityCardCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdUserInfoDTO;
import com.wanlianyida.basemdm.interfaces.model.dto.PlatformUserInfoDetailDTO;
import com.wanlianyida.basemdm.interfaces.model.dto.PlatformUserInfoListDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdGetUserListQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserInfoQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserQuery;
import com.wanlianyida.basemdm.interfaces.model.query.PlatformUserInfoListQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 用户信息表 Controller
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@RequestMapping("/cmdUserInfo")
@RestController
public class CmdUserInfoController {

	@Resource
    private CmdUserInfoAppService cmdUserInfoAppService;

	/**
	 * 分页查询
	 * @param pagingInfo 分页查询参数
	 * @return {@link ResultMode}<{@link CmdUserInfoDTO}>
	 */
	@PostMapping("/queryPage")
	public ResultMode<List<CmdUserInfoDTO>> queryPage(@RequestBody PagingInfo<CmdUserInfoQuery> pagingInfo) {
		PageInfo<CmdUserInfoDTO> pageInfo = cmdUserInfoAppService.queryPage(pagingInfo);
		if (IterUtil.isEmpty(pageInfo.getList())) {
            return ResultMode.success();
        }
        ResultMode<List<CmdUserInfoDTO>> resultMode = new ResultMode<>();
        int total = (int) pageInfo.getTotal();
        resultMode.setTotal(total);
        resultMode.setModel(pageInfo.getList());
        resultMode.setSucceed(true);
        return resultMode;
	}

	/**
	 * 列表查询
	 * @param query 查询参数
	 * @return {@link ResultMode}<{@link CmdUserInfoDTO}>
	 */
	@PostMapping("/queryList")
	public ResultMode<List<CmdUserInfoDTO>> queryList(@RequestBody CmdUserInfoQuery query) {
		List<CmdUserInfoEntity> list = cmdUserInfoAppService.queryList(query);
		if (IterUtil.isEmpty(list)) {
			return ResultMode.success();
		}
		// 分页列表vo转换
		List<CmdUserInfoDTO> vos = BeanUtil.copyToList(list, CmdUserInfoDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return ResultMode.success(vos);
	}

    /**
	 * 新增
	 * @param command
	 * @return Long
	 */
	@PostMapping("/insert")
	public ResultMode<Long> insert(@RequestBody CmdUserInfoCommand command) {
        return ResultMode.success(cmdUserInfoAppService.insert(command));
	}

	/**
	 * 修改
	 * @param command
	 * @return Long
	 */
	@PostMapping("/update")
	public ResultMode<Long> update(@RequestBody CmdUserInfoCommand command) {
        return ResultMode.success(cmdUserInfoAppService.update(command));
	}

	/**
	 * 逻辑删除
	 * @param id
	 * @return Long
	 */
	@PostMapping("/delete/{id}")
	public ResultMode<?> delete(@PathVariable("id") Long id) {
        return ResultMode.success(cmdUserInfoAppService.delete(id));
	}

	/**
	 * 根据登录名查询
	 * @param query
	 * @return
	 */
	@PostMapping("/queryByLoginName")
	public ResultMode<CmdUserInfoDTO> queryByLoginName(@RequestBody CmdUserInfoQuery query) {
		CmdUserInfoDTO cmdUserInfoDTO =  cmdUserInfoAppService.queryByLoginName(query);
		return ResultMode.success(cmdUserInfoDTO);
	}

	/**
	 * 根据用户手机号查询
	 * @param query
	 * @return
	 */
	@PostMapping("/queryByUserPhone")
	public ResultMode<CmdUserInfoDTO> queryByUserPhone(@RequestBody CmdUserInfoQuery query) {
		CmdUserInfoDTO cmdUserInfoDTO =  cmdUserInfoAppService.queryByUserPhone(query);
		return ResultMode.success(cmdUserInfoDTO);
	}

	/**
	 * 查询详情
	 * @param id
	 * @return
	 */
	@PostMapping("/queryDetail/{id}")
	public ResultMode<CmdUserInfoDTO> queryDetail(@PathVariable("id") Long id) {
		CmdUserInfoDTO cmdUserInfoDTO =  cmdUserInfoAppService.queryDetail(id);
		return ResultMode.success(cmdUserInfoDTO);
	}

	@ApiOperation("平台端个人账号列表")
	@PostMapping("/platformPageList")
	public ResultMode<List<PlatformUserInfoListDTO>> platformPageList(@RequestBody PagingInfo<PlatformUserInfoListQuery> pageQuery) {
		return cmdUserInfoAppService.platformPageList(pageQuery);
	}

	@ApiOperation("平台端个人账号详情")
	@PostMapping("/platformDetail/{id}")
	public ResultMode<PlatformUserInfoDetailDTO> platformDetail(@PathVariable("id") Long id) {
		return cmdUserInfoAppService.platformDetail(id);
	}

	@ApiOperation("添加用户身份证信息")
	@PostMapping("/insertIdentityCard")
	public ResultMode<?> insertIdentityCard(@RequestBody @Validated MdmUserIdentityCardCommand command) {
		cmdUserInfoAppService.insertIdentityCard(command);
		return ResultMode.success();
	}

	@ApiOperation("查询用户基础信息")
	@PostMapping("/queryUserInfo")
	public ResultMode<CmdUserInfoDTO> queryUserInfo(@RequestBody CmdUserQuery query) {
		CmdUserInfoDTO cmdUserInfoDTO =  cmdUserInfoAppService.queryUserInfo(query);
		return ResultMode.success(cmdUserInfoDTO);
	}

	@ApiOperation("批量获取用户信息")
	@PostMapping("/batchQueryUserList")
	public ResultMode<List<CmdUserInfoDTO>> batchQueryUserList(@RequestBody CmdGetUserListQuery query) {
		List<CmdUserInfoDTO> list = cmdUserInfoAppService.batchQueryUserList(query);
		return ResultMode.success(list);
	}

	/**
	 * 逻辑删除用户信息
	 *
	 * @param userId 用户id
	 * @return 无
	 */
	@PostMapping("/del/user-info/{userId}")
	public ResultMode<Void> delUserInfo(@PathVariable("userId") Long userId){
		cmdUserInfoAppService.delUserInfo(userId);
		return ResultMode.success();
	}
}
