package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.collection.IterUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.application.service.MdmHolidayAppService;
import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import com.wanlianyida.basemdm.interfaces.model.command.MdmHolidayCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmHolidayDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmHolidayQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * 国家法定节假日管理
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@RestController
@RequestMapping("/mdm/holiday")
public class MdmHolidayController {

    @Resource
    private MdmHolidayAppService appService;

    /**
     * 新增节假日
     *
     * @param command
     * @return {@link ResultMode}<{@link Long}>
     */
    @PostMapping("/add")
    public ResultMode<Long> add(@RequestBody @Validated(CreateGroup.class) MdmHolidayCommand command) {
        return ResultMode.success(appService.add(command));
    }

    /**
     * 更新节假日
     *
     * @param command
     * @return {@link ResultMode}<{@link Void}>
     */
    @PostMapping("/update")
    public ResultMode<Void> update(@RequestBody @Validated(UpdateGroup.class) MdmHolidayCommand command) {
        appService.update(command);
        return ResultMode.success();
    }

    /**
     * 删除节假日
     *
     * @param id
     * @return {@link ResultMode}<{@link Void}>
     */
    @PostMapping("/delete")
    public ResultMode<Void> delete(@RequestBody @Validated(DeleteGroup.class) MdmHolidayCommand command) {
        appService.delete(command.getId());
        return ResultMode.success();
    }


    /**
     * 查询节假日详情
     *
     * @param query
     * @return {@link ResultMode}<{@link MdmHolidayDTO}>
     */
    @PostMapping("/detail")
    public ResultMode<MdmHolidayDTO> queryDetail(@RequestBody MdmHolidayQuery query) {
        MdmHolidayDTO dto = appService.queryDetail(query);
        return ResultMode.success(dto);
    }


    /**
     * 分页查询节假日列表
     *
     * @param pagingInfo 分页参数
     * @return {@link ResultMode}<{@link List}<{@link MdmHolidayDTO}>>
     */
    @PostMapping("/page")
    public ResultMode<List<MdmHolidayDTO>> queryList(@RequestBody PagingInfo<MdmHolidayQuery> pagingInfo) {
        PageInfo<MdmHolidayDTO> pageInfo = appService.queryPage(pagingInfo);
        if (IterUtil.isEmpty(pageInfo.getList())) {
            return ResultMode.success();
        }
        ResultMode<List<MdmHolidayDTO>> resultMode = new ResultMode<>();
        int total = (int) pageInfo.getTotal();
        resultMode.setTotal(total);
        resultMode.setModel(pageInfo.getList());
        resultMode.setSucceed(true);
        return ResultMode.successPageList(resultMode.getModel(), resultMode.getTotal());
    }

    /**
     * 根据日期查询节假日
     *
     * @param calDate 节假日日期
     * @return {@link ResultMode}<{@link MdmHolidayDTO}>
     */
    @PostMapping("/query-by-date")
    public ResultMode<MdmHolidayDTO> queryByCalDate(@RequestBody Date calDate) {
        MdmHolidayDTO dto = appService.queryByCalDate(calDate);
        return ResultMode.success(dto);
    }
} 
