package com.wanlianyida.basemdm.interfaces.facade;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.api.model.dto.OperatorDTO;
import com.wanlianyida.basemdm.api.model.query.OperatorQuery;
import com.wanlianyida.basemdm.application.service.MdmOperatorAppService;
import com.wanlianyida.basemdm.interfaces.model.command.MdmOperatorCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmOperatorDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmOperatorQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

/**
 * 企业操作员管理
 *
 * <AUTHOR>
 * @date 2025/03/30
 */
@RestController
@RequestMapping("/mdm/operator")
public class MdmOperatorController {
    @Resource
    private MdmOperatorAppService appService;

    /**
     * 新增操作员
     *
     * @param command
     * @return {@link ResultMode }<{@link Void }>
     */
    @PostMapping("/add")
    public ResultMode<Long> add(@RequestBody MdmOperatorCommand command) {
        return ResultMode.success(appService.add(command));
    }

    /**
     * 更新操作员
     *
     * @param command
     * @return
     */
    @PostMapping("/update")
    public ResultMode<Void> update(@RequestBody MdmOperatorCommand command) {
        appService.update(command);
        return ResultMode.success();
    }

    /**
     * 批量删除操作员
     *
     * @param command
     * @return {@link ResultMode }<{@link Void }>
     */
    @PostMapping("/batchDelete")
    public ResultMode<Void> batchDelete(@RequestBody MdmOperatorCommand command) {
        appService.batchDelete(command);
        return ResultMode.success();
    }

    /**
     * 查询操作员详情
     *
     * @param query
     * @return {@link ResultMode }<{@link Object }>
     */
    @PostMapping("/queryDetail")
    public ResultMode<MdmOperatorDTO> queryDetail(@RequestBody MdmOperatorQuery query) {
        MdmOperatorDTO dto = appService.queryDetail(query);
        return ResultMode.success(dto);
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    @PostMapping("/queryPage")
    public ResultMode<List<MdmOperatorDTO>> queryPage(@RequestBody PagingInfo<MdmOperatorQuery> pagingInfo) {
        PageInfo<MdmOperatorDTO> pageInfo = appService.queryPage(pagingInfo);
        return ResultMode.successPageList(pageInfo.getList(), (int) pageInfo.getTotal());
    }

    /**
     * 解绑用户
     *
     * @param command
     * @return
     */
    @PostMapping("/unbindUser")
    public ResultMode<Void> unbindUser(@RequestBody MdmOperatorCommand command) {
        appService.unbindUser(command);
        return ResultMode.success();
    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    @PostMapping("/queryList")
    public ResultMode<List<MdmOperatorDTO>> queryList(@RequestBody MdmOperatorQuery query) {
        List<MdmOperatorDTO> list = appService.queryList(query);
        return ResultMode.success(list);
    }

    /**
     * 更新操作员手机号
     *
     * @param command
     * @return
     */
    @PostMapping("/updateOperatorPhone")
    public ResultMode<Void> updateOperatorPhone(@RequestBody MdmOperatorCommand command) {
        appService.updateOperatorPhone(command);
        return ResultMode.success();
    }

    /**
     * 删除企业管理员
     *
     * @param companyId
     * @return
     */
    @PostMapping("/deleteAdmin")
    public ResultMode<Void> deleteAdmin(@RequestBody String companyId) {
        appService.deleteAdmin(companyId);
        return ResultMode.success();
    }

    /**
     * 根据operatorCode查询操作员信息
     */
    @PostMapping("/queryOperatorByOperatorCodeList")
    public ResultMode<List<MdmOperatorDTO>> queryOperatorByOperatorCodeList(@RequestBody List<String> operatorCodeList) {
        List<MdmOperatorDTO> dtos = appService.queryOperatorByOperatorCodeList(operatorCodeList);
        return ResultMode.success(dtos);
    }

    /**
     * 账号停用
     */
    @PostMapping("/accountSuspension/{operatorId}")
    public ResultMode<Void> accountSuspension(@PathVariable("operatorId") String operatorId) {
        appService.accountSuspension(operatorId);
        return ResultMode.success();
    }

    /**
     * 账号启用
     */
    @PostMapping("/accountActivation/{operatorId}")
    public ResultMode<Void> accountActivation(@PathVariable("operatorId") String operatorId) {
        appService.accountActivation(operatorId);
        return ResultMode.success();
    }

    /**
     * 根据系统类型和操作员id集合查询
     *
     * @param operatorQuery 查询条件
     * @return 操作员信息
     */
    @PostMapping("/listOperatorsBySysTypeAndIds")
    public ResultMode<List<OperatorDTO>> listOperatorsBySysTypeAndIds(@RequestBody OperatorQuery operatorQuery) {
        return ResultMode.success(appService.listOperatorsBySysTypeAndIds(operatorQuery));
    }

    /**
     * 根据operatorCode获取操作员信息
     *
     * @param operatorCode 操作员编码（userBaseId）
     * @return 操作员信息
     */
    @PostMapping("/one/by-operatorCode/{operatorCode}")
    public ResultMode<MdmOperatorDTO> getByOperatorCode(@PathVariable("operatorCode") String operatorCode) {
        return ResultMode.success(appService.getByOperatorCode(operatorCode));
    }

    /**
     * 根据操作员id集合查询
     *
     * @param operatorIds 操作员id集合
     * @return 操作员名称
     */
    @PostMapping("/map/by-operatorIds")
    public ResultMode<Map<String, String>> getByOperatorIds(@RequestBody Set<Long> operatorIds){
        return ResultMode.success(appService.getByOperatorIds(operatorIds));
    }
}
