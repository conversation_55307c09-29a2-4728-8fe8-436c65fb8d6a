package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.basemdm.application.service.CmdCompanyApplyAppService;
import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyApplyEntity;
import com.wanlianyida.basemdm.interfaces.model.command.CmdCompanyApplyCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.AuditStatisticsDTO;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdCompanyApplyDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdCompanyApplyQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业审核过程表 Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Api(value = "企业审核过程 api")
@RequestMapping(value = "/mdmCmdCompanyApply")
@RestController
public class CmdCompanyApplyController {

	@Resource
    private CmdCompanyApplyAppService cmdCompanyApplyAppService;

	/**
	 * 条件列表查询
	 * @param filter 查询参数
	 * @return {@link ResultMode}<{@link CmdCompanyApplyDTO}>
	 */
	@RequestMapping(value = "/queryList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	public ResultMode<List<CmdCompanyApplyDTO>> queryList(@RequestBody CmdCompanyApplyQuery filter) {
		List<CmdCompanyApplyEntity> list = cmdCompanyApplyAppService.queryList(filter);
		if (IterUtil.isEmpty(list)) {
			return ResultMode.success();
		}
		// 分页列表vo转换
		List<CmdCompanyApplyDTO> vos = BeanUtil.copyToList(list, CmdCompanyApplyDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
		return ResultMode.success(vos);
	}

    /**
	 * 新增
	 * @param command
	 * @return {@link ResultMode}<{@link Integer}>
	 */
	@RequestMapping(value = "/insert", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	public ResultMode<Long> insert(@RequestBody CmdCompanyApplyCommand command) {
		CmdCompanyApplyQuery query = new CmdCompanyApplyQuery();
		query.setLicenseNo(command.getLicenseNo());
		query.setAptitudeStatus(command.getAptitudeStatus());
		query.setApplySource(command.getApplySource());
		List<CmdCompanyApplyEntity> list = cmdCompanyApplyAppService.queryList(query);
		if (CollUtil.isEmpty(list)) {
			CmdCompanyApplyEntity record = new CmdCompanyApplyEntity();
			BeanUtil.copyProperties(command, record, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
			return ResultMode.success(cmdCompanyApplyAppService.insert(record));
		}
		return ResultMode.success(CollUtil.getFirst(list).getId());
	}


	/**
	 * 根据主键修改
	 * @param command
	 * @return {@link ResultMode}<{@link Integer}>
	 */
	@RequestMapping(value = "/update", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	public ResultMode<Long> update(@RequestBody CmdCompanyApplyCommand command) {
		CmdCompanyApplyEntity record = new CmdCompanyApplyEntity();
		BeanUtil.copyProperties(command, record, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
		return ResultMode.success(cmdCompanyApplyAppService.update(record));
	}

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link CmdCompanyApplyDTO}
	 */
	@RequestMapping(value = "/getById/{id}", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	public ResultMode<CmdCompanyApplyDTO> selectByPrimaryKey(@PathVariable("id") Long id) {
		CmdCompanyApplyEntity record = cmdCompanyApplyAppService.selectByPrimaryKey(id);
		if (ObjectUtil.isNull(record)) {
			return ResultMode.success();
		}
		// 实体vo转换
		CmdCompanyApplyDTO vo = new CmdCompanyApplyDTO();
		BeanUtil.copyProperties(record, vo, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
		return ResultMode.success(vo);
	}

	@ApiOperation("分页列表")
	@PostMapping("/pageList")
	public ResultMode<List<CmdCompanyApplyDTO>> pageList(@RequestBody PagingInfo<CmdCompanyApplyQuery> pageQuery) {
		return cmdCompanyApplyAppService.pageList(pageQuery);
	}

	@ApiOperation("数据统计")
	@PostMapping("/statistics")
	public ResultMode<AuditStatisticsDTO> statistics(@RequestBody CmdCompanyApplyQuery query) {
		return cmdCompanyApplyAppService.statistics(query);
	}


	/**
	 * 根据信用代码更新审核数据
	 * @param entity
	 * @return
	 */
	@PostMapping(value = "/updateCompanyApplyStatus")
	public ResultMode<Integer> updateCompanyApplyStatus(@RequestBody CmdCompanyApplyCommand entity) {
		return cmdCompanyApplyAppService.updateCompanyApplyStatus(entity);
	}

}
