package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wanlianyida.basemdm.application.service.MdmRegionAppService;
import com.wanlianyida.basemdm.domain.model.entity.MdmRegionEntity;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmExceptionEnum;
import com.wanlianyida.basemdm.interfaces.model.command.MdmRegionCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmRegionDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmRegionQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 组织管理
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@RestController
@RequestMapping("/mdm-region")
public class MdmRegionController {
    @Resource
    private MdmRegionAppService mdmRegionAppService;

    /**
     * 根据父节点获取城市数据
     *
     * @param parentcode 父节点id
     * @return {@link ResultMode }<{@link List }<{@link MdmRegionDTO }>>
     */
    @PostMapping("/list/by-parent-code")
    public ResultMode<List<MdmRegionDTO>> listByParentCode(@RequestParam("parentCode") String parentCode) {
        List<MdmRegionDTO> list = mdmRegionAppService.queryByParentCode(parentCode);
        return ResultMode.success(list);
    }


    /**
     * 根据节点获取城市数据
     *
     * @param filter
     * @return {@link ResultMode }<{@link List }<{@link MdmRegionDTO }>>
     */
    @PostMapping("/list/by-codes")
    public ResultMode<List<MdmRegionDTO>> listByCodes(@RequestBody MdmRegionQuery filter) {
        List<MdmRegionDTO> list = mdmRegionAppService.queryList(filter);
        return ResultMode.success(list);
    }

    /**
     * 获取所有行政区划数据
     *
     * @return {@link ResultMode }<{@link List }<{@link MdmRegionDTO }>>
     */
    @PostMapping("/list/query-all")
    public ResultMode<List<MdmRegionDTO>> listQueryall() {
        MdmRegionQuery filter = new MdmRegionQuery();
        List<MdmRegionDTO> list = mdmRegionAppService.queryList(filter);
        return ResultMode.success(list);
    }


    /**
     * 新增记录
     *
     * @param command
     * @return
     */
    @PostMapping("/add")
    public ResultMode<String> add(@RequestBody MdmRegionCommand command) {
        ResultMode resultModel = checkmdmRegionArgs(command);
        //校验参数
        if (!resultModel.isSucceed()) {
            return resultModel;
        }
        MdmRegionEntity entity = BeanUtil.toBean(command, MdmRegionEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmRegionAppService.add(entity);
    }

    /**
     * 参数校验
     *
     * @param command
     * @return
     */

    public ResultMode<String> checkmdmRegionArgs(@RequestBody MdmRegionCommand command) {
        if (StringUtils.isEmpty(command.getCode().trim())) {
            return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区代码不能为空");
        }
        if (command.getCode().trim().length() > 10) {
            return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区代码长度不能超过" + 10);
        }
        if (StringUtils.isEmpty(command.getName().trim())) {
            return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区名称不能为空");
        }
        if (command.getName().trim().length() > 100) {
            return ResultMode.fail(CtpCoreMdmExceptionEnum.ARGUMENT_ERROR.getCode(), "行政区名称长度不能超过" + 100);
        }
        return ResultMode.success();
    }

    public static void main(String[] args) {
        System.out.println("测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试".trim().length());
    }

    /**
     * 修改，删除记录
     *
     * @param command
     * @return
     */
    @PostMapping("/update")
    public ResultMode<String> update(@RequestBody MdmRegionCommand command) {
        ResultMode resultModel = checkmdmRegionArgs(command);
        //校验参数
        if (!resultModel.isSucceed()) {
            return resultModel;
        }
        MdmRegionEntity entity = BeanUtil.toBean(command, MdmRegionEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmRegionAppService.update(entity);
    }


}
