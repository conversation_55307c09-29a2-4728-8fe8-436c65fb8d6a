package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import com.wanlianyida.basemdm.application.service.CmdWeiXinDepartmentInfoAppService;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdWeiXinDepartmentInfoPO;
import com.wanlianyida.basemdm.interfaces.model.command.CmdWeiXinDepartmentInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdWeiXinDepartmentInfoDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdWeiXinDepartmentInfoQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业微信组织机构controller
 */
@Api(value = "企业微信组织机构 api")
@RequestMapping(value = "/mdmWeiXinDepartment")
@RestController
public class CmdWeiXinDepartmentInfoController {

    @Resource
    private CmdWeiXinDepartmentInfoAppService userDepartmentInfoAppService;


    /**
     * @param filter 查询参数
     * @return {@link ResultMode}<{@link CmdWeiXinDepartmentInfoDTO}>
     */
    @RequestMapping(value = "/queryByQuery", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<List<CmdWeiXinDepartmentInfoDTO>> queryByQuery(@RequestBody CmdWeiXinDepartmentInfoQuery filter) {
        List<CmdWeiXinDepartmentInfoPO> list = userDepartmentInfoAppService.queryByCondition(filter);
        if (IterUtil.isEmpty(list)) {
            return ResultMode.success();
        }
        // 分页列表vo转换
        List<CmdWeiXinDepartmentInfoDTO> vos = BeanUtil.copyToList(list, CmdWeiXinDepartmentInfoDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        if (CollUtil.isNotEmpty(vos)) {
            return ResultMode.success(vos);
        }
        return ResultMode.success();
    }

    /**
     * 保存
     *
     * @param command 查询参数
     * @return {@link ResultMode}<{@link CmdWeiXinDepartmentInfoDTO}>
     */
    @RequestMapping(value = "/save", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<?> save(@RequestBody CmdWeiXinDepartmentInfoCommand command) {
        return ResultMode.success(userDepartmentInfoAppService.save(command));
    }

    /**
     * 保存
     *
     * @param command 查询参数
     * @return {@link ResultMode}<{@link CmdWeiXinDepartmentInfoDTO}>
     */
    @RequestMapping(value = "/initData", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<?> initData(@RequestBody CmdWeiXinDepartmentInfoCommand command) {
        userDepartmentInfoAppService.initData(command);
        return ResultMode.success();
    }

}
