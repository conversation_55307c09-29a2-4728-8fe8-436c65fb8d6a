package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import com.wanlianyida.basemdm.application.service.CmdEmployeeInfoAppService;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdEmployeeInfoPO;
import com.wanlianyida.basemdm.interfaces.model.command.CmdEmployeeInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdEmployeeInfoDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdEmployeeInfoQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 员工controller
 */
@Api(value = "员工 api")
@RequestMapping(value = "/mdmCmdEmployeeInfo")
@RestController
public class CmdEmployeeInfoController {

    @Resource
    private CmdEmployeeInfoAppService userEmployeeInfoAppService;


    /**
     * @param filter 查询参数
     * @return {@link ResultMode}<{@link CmdEmployeeInfoDTO}>
     */
    @RequestMapping(value = "/queryByQuery", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<CmdEmployeeInfoDTO> queryByQuery(@RequestBody CmdEmployeeInfoQuery filter) {
        List<CmdEmployeeInfoPO> list = userEmployeeInfoAppService.queryByCondition(filter);
        if (IterUtil.isEmpty(list)) {
            return ResultMode.success();
        }
        // 分页列表vo转换
        List<CmdEmployeeInfoDTO> vos = BeanUtil.copyToList(list, CmdEmployeeInfoDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        if (CollUtil.isNotEmpty(vos)) {
            CmdEmployeeInfoDTO item = CollUtil.getFirst(vos);
            return ResultMode.success(item);
        }
        return ResultMode.success();
    }

    /**
     * 保存
     *
     * @param command 查询参数
     * @return {@link ResultMode}<{@link CmdEmployeeInfoDTO}>
     */
    @RequestMapping(value = "/save", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<?> save(@RequestBody CmdEmployeeInfoCommand command) {
        return ResultMode.success(userEmployeeInfoAppService.save(command));
    }

}
