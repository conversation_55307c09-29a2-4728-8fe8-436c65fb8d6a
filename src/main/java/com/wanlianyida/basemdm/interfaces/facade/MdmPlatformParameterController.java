package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.application.service.MdmPlatformParameterAppService;
import com.wanlianyida.basemdm.domain.model.entity.MdmPlatformParameterEntity;
import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import com.wanlianyida.basemdm.interfaces.model.command.MdmPlatformParameterCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmPlatformParameterDTO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmPlatformParameterQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 平台参数管理
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@RestController
@RequestMapping("/mdm/parameter")
public class MdmPlatformParameterController {
    @Resource
    private MdmPlatformParameterAppService mdmPlatformParameterAppService;

    /**
     * 条件查询平台参数
     *
     * @param filter 查询条件
     * @return {@link ResultMode}<{@link List}<{@link MdmPlatformParameterDTO}>>
     */
//    @PostMapping("/list-query")
//    public ResultMode<List<MdmPlatformParameterDTO>> listQuery(@RequestBody MdmPlatformParameterQuery filter) {
//        List<MdmPlatformParameterDTO> list = mdmPlatformParameterAppService.queryList(filter);
//        return ResultMode.success(list);
//    }

    /**
     * 分页查询平台参数
     *
     * @param pagingInfo 分页参数
     * @return {@link ResultMode}<{@link List}<{@link MdmPlatformParameterDTO}>>
     */
    @PostMapping("/page")
    public ResultMode<List<MdmPlatformParameterDTO>> listQueryPage(@RequestBody PagingInfo<MdmPlatformParameterQuery> pagingInfo) {
        PageInfo<MdmPlatformParameterDTO> pageInfo = mdmPlatformParameterAppService.queryPage(pagingInfo);
        if (IterUtil.isEmpty(pageInfo.getList())) {
            return ResultMode.success();
        }
        ResultMode<List<MdmPlatformParameterDTO>> resultMode = new ResultMode<>();
        int total = (int) pageInfo.getTotal();
        resultMode.setTotal(total);
        resultMode.setModel(pageInfo.getList());
        resultMode.setSucceed(true);
        return ResultMode.successPageList(resultMode.getModel(), resultMode.getTotal());
    }


    /**
     * 根据分组编码查询平台参数
     *
     * @param query 查询条件（包含分组编码和平台编码）
     * @return {@link ResultMode}<{@link List}<{@link MdmPlatformParameterDTO}>>
     */
    @PostMapping("/group-code")
    public ResultMode<List<MdmPlatformParameterDTO>> queryByGroupCode(@RequestBody MdmPlatformParameterQuery query) {
        List<MdmPlatformParameterDTO> list = mdmPlatformParameterAppService.queryList(query);
        return ResultMode.success(list);
    }


    /**
     * 根据多个参数编码批量查询平台参数
     *
     * @param query 查询条件（包含参数编码列表和平台编码）
     * @return {@link ResultMode}<{@link List}<{@link MdmPlatformParameterDTO}>>
     */
    @PostMapping("/para-code-list")
    public ResultMode<List<MdmPlatformParameterDTO>> queryByParaCodes(@RequestBody MdmPlatformParameterQuery query) {
        List<MdmPlatformParameterDTO> list = mdmPlatformParameterAppService.queryList(query);
        return ResultMode.success(list);
    }

    /**
     * 新增记录
     *
     * @param command 命令对象
     * @return {@link ResultMode}<{@link String}>
     */
    @PostMapping("/add")
    public ResultMode<String> add(@RequestBody @Validated(CreateGroup.class) MdmPlatformParameterCommand command) {

        MdmPlatformParameterEntity entity = BeanUtil.toBean(command, MdmPlatformParameterEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmPlatformParameterAppService.add(entity);
    }

    /**
     * 修改记录
     *
     * @param command 命令对象
     * @return {@link ResultMode}<{@link String}>
     */
    @PostMapping("/update")
    public ResultMode<String> update(@RequestBody @Validated(UpdateGroup.class) MdmPlatformParameterCommand command) {
        MdmPlatformParameterEntity entity = BeanUtil.toBean(command, MdmPlatformParameterEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return mdmPlatformParameterAppService.update(entity);
    }

    /**
     * 根据ID软删除
     *
     * @param command 命令对象
     * @return {@link ResultMode}<{@link String}>
     */
    @PostMapping("/delete")
    public ResultMode<String> delete(@RequestBody @Validated(DeleteGroup.class) MdmPlatformParameterCommand command) {
        return mdmPlatformParameterAppService.deleteById(command);
    }

} 
