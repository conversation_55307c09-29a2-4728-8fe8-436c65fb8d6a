package com.wanlianyida.basemdm.interfaces.facade;

import com.wanlianyida.basemdm.application.service.CmdCompanyBeneficiaryAppService;
import com.wanlianyida.basemdm.domain.repository.Validated.CreateGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.DeleteGroup;
import com.wanlianyida.basemdm.domain.repository.Validated.UpdateGroup;
import com.wanlianyida.basemdm.interfaces.model.command.CmdCompanyBeneficiaryCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdCompanyBeneficiaryDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdCompanyBeneficiaryQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@Api(value = "企业受益人")
@RequestMapping(value = "/company/beneficiary")
@RestController
public class CmdCompanyBeneficiaryController {
    @Resource
    private CmdCompanyBeneficiaryAppService appService;

    /**
     * 新增企业受益人
     * @param command
     * @return
     */
    @PostMapping("/add")
    public ResultMode<String> add(@RequestBody @Validated(CreateGroup.class) CmdCompanyBeneficiaryCommand command) {
        String id =appService.add(command);
        return ResultMode.success(id);
    }

    /**
     * 修改企业受益人
     * @param command
     * @return
     */
    @PostMapping("/update")
    public ResultMode<String> update(@RequestBody @Validated(UpdateGroup.class) CmdCompanyBeneficiaryCommand command) {
        String id =appService.update(command);
        return ResultMode.success(id);
    }

    /**
     * 删除企业受益人
     * @param command
     * @return
     */
    @PostMapping("/delete")
    public ResultMode<Void> delete(@RequestBody @Validated(DeleteGroup.class) CmdCompanyBeneficiaryCommand command) {
        appService.delete(command);
        return ResultMode.success();
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    @PostMapping("/queryDetail/{id}")
    public ResultMode<CmdCompanyBeneficiaryDTO> queryDetail(@PathVariable @NotNull(message = "ID 不能为空")String id) {
        CmdCompanyBeneficiaryDTO result = appService.queryDetail(id);
        return ResultMode.success(result);
    }

    /**
     * 条件查询
     * @param query
     * @return
     */
    @PostMapping("/queryByCondition")
    public ResultMode<List<CmdCompanyBeneficiaryDTO>> queryByCondition(@RequestBody CmdCompanyBeneficiaryQuery query) {
        List<CmdCompanyBeneficiaryDTO> dtoList = appService.queryByCondition(query);
        return ResultMode.success(dtoList);
    }

}
