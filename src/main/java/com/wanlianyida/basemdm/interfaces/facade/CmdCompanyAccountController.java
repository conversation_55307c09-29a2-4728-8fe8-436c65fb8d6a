package com.wanlianyida.basemdm.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import com.wanlianyida.basemdm.application.service.CmdCompanyAccountAppService;
import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyAccountEntity;
import com.wanlianyida.basemdm.interfaces.model.command.CmdCompanyAccountCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdCompanyAccountDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdCompanyAccountQuery;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业账号表 Controller
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Api(value = "企业账号 api")
@RequestMapping(value = "/mdmCmdCompanyAccount")
@RestController
public class CmdCompanyAccountController {

	@Resource
    private CmdCompanyAccountAppService cmdCompanyAccountAppService;

	/**
	 * 列表查询
	 *
	 * @param filter 查询参数
	 * @return {@link ResultMode}<{@link CmdCompanyAccountDTO}>
	 */
	@RequestMapping(value = "/queryList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	public ResultMode<List<CmdCompanyAccountDTO>> queryList(@RequestBody CmdCompanyAccountQuery filter) {
		List<CmdCompanyAccountEntity> list = cmdCompanyAccountAppService.queryList(filter);
		if (IterUtil.isEmpty(list)) {
			return ResultMode.success();
		}
		// 分页列表vo转换
		List<CmdCompanyAccountDTO> vos = BeanUtil.copyToList(list, CmdCompanyAccountDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
		return ResultMode.success(vos);
	}

	/**
	 * 同步管理员账号
	 *
	 * @param command
	 */
	@RequestMapping(value = "/syncAdminAccount", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	ResultMode<?> syncAdminAccount(@RequestBody CmdCompanyAccountCommand command) {
		CmdCompanyAccountEntity record = BeanUtil.toBean(command, CmdCompanyAccountEntity.class, CopyOptions.create().ignoreNullValue().ignoreError());
		cmdCompanyAccountAppService.syncAccount(record);
		return ResultMode.success();
	}
}
