package com.wanlianyida.basemdm.domain.model.entity;

import lombok.Data;

import java.util.Date;

/**
 * 组织DTO
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class UmOrgEntity {

    /**
     * 主键
     */
    private String id;

    /**
     * 企业名称
     */
    private String licenseName;

    /**
     * 信用代码
     */
    private String licenseNo;

    /**
     * 企业联系人，发送验证码用：去企业资质表查
     */
    private String phone;

    /**
     * 上级组织id
     */
    private String parentId;

    /**
     * 上级企业名称
     */
    private String parentLicenseName;

    /**
     * 上级统一信用代码
     */
    private String parentLicenseNo;


    /**
     * 描述
     */
    private String remark;

    /**
     * 更新人id
     */
    private String updaterId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 平台类型 10用户端 20管理端
     */
    private String platformType;
}
