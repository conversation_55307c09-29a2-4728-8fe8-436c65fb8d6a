package com.wanlianyida.basemdm.domain.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户联系方式表 entity
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@TableName("cmd_user_contact")
@Accessors(chain = true)
public class CmdUserContactEntity implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 用户id
	 */
	private Long userId;

	/**
	 * 用户编码
	 */
	private String userCode;

	/**
	 * 类别:10手机,20邮箱,30微信
	 */
	private String category;

	/**
	 * 联系方式
	 */
	private String contactInfo;

	/**
	 * 是否启用:1是,0否
	 */
	private String enableFlag;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;


}
