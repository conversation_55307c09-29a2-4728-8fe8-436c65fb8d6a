package com.wanlianyida.basemdm.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 企业组织结构
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class CmdWeiXinEmployeeDeptInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 员工id
     */
    private String empId;


    /**
     * (企业微信)部门id
     */
    private String deptId;

    /**
     * 是否是领导[1-是,0-否]
     */
    private String empIsLeader;

    /**
     * 领导Id
     */
    private String leaderEmpId;

    /**
     * 状态
     */
    private String validFlag;


    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;
}
