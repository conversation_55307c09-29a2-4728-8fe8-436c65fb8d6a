package com.wanlianyida.basemdm.domain.model.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 国家法定节假日表查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
public class MdmHolidayCondition {

    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date calDate;

    /**
     * 日期范围开始
     */
        private Date calDateStart;

        /**
         * 日期范围结束
         */
        private Date calDateEnd;

    /**
     * 10 法定节假日 20 法定休假  30 法定周未转补班
     */
    private Integer dayType;

    /**
     * 节假日类型列表
     */
    private List<Integer> dayTypeList;

    /**
     * 日期名称（如：春节第一天）
     */
    private String holidayName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer delFlag;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 最后更新人id
     */
    private String lastUpdaterId;
} 