package com.wanlianyida.basemdm.domain.model.condition;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 企业受益人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
public class CmdCompanyBeneficiaryCondition {

    /**
     * id
     */
    private String id;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司社会信用代码
     */
    private String socialCreditCode;

    /**
     * 受益人姓名
     */
    private String beneficiaryName;

    /**
     * 受益人证件类型 1-居民身份证号 2-护照 3-香港往来内地通行证 4-澳门来往内地通行证 5-台湾来往内地通行证
     */
    private String beneficiaryCertType;

    /**
     * 受益人证件号码
     */
    private String beneficiaryCertNumber;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 截止日期
     */
    private Date untilDate;

    /**
     * 长期有效标志[0-否,1-是]
     */
    private Integer longTermValidFlag;

    /**
     * 法人标志[0-否,1-是]
     */
    private Integer legalPersonFlag;
}
