package com.wanlianyida.basemdm.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业资质表 entity
 *
 * <AUTHOR>
 * @date 2024-12-29
 */
@Data
public class CmdCompanyBusinessLicenseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公司证照(资质)营业执照ID
     */
    private String companyBusinessLicenseId;

    /**
     * 公司信息ID 关联um_company表
     */
    private String companyId;

    /**
     * 经营主体ID
     */
    private String mainBodyId;

    /**
     * 证照(资质)类型【select:1-企业营业执照，2-个体工商户执照，3-道路运输资质。4-其他执照】即为证书附件的一级分类 按目前业务只有一种营业执照 即此表为营业执照
     */
    private String certificateType;

    /**
     * 预留字段 证件类型，属于二级分类，
     */
    private Integer licenseType;

    /**
     * 资质名称即：营业执照公司企业名称
     */
    private String licenseName;

    /**
     * 企业简称
     */
    private String licenseShortName;

    /**
     * 预留（冗余）字段 证照(资质)号码企业营业执照  即为统一信用代码
     */
    private String licenseNo;

    /**
     * 企业法人
     */
    private String legalPerson;

    /**
     * 企业法人身份证号
     */
    private String legalPersonIdCard;

    /**
     * 发证机关单位(广东，北京，或者某区工商所)
     */
    private String licenseDepartmentGov;

    /**
     * 初次发证日期(签发证日期)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseFirstTime;

    /**
     * 资质(营业执照)有效期起日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseStartDate;

    /**
     * 资质(营业执照)有效期止日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseEndDate;

    /**
     * 有效期总数，此字段设置只为前端使用，后台可以先计算存储直接拿出来显示，通过start-end得到的结果
     */
    private Integer licenseValidDate;

    /**
     * 资质(营业执照)是否为长期有效【select:11-是,21-否】当是时清空license_end_date
     */
    private String licenseValidIsLong;

    /**
     * 资质(营业执照)正面照片地址
     */
    private String forntFileUrl;

    /**
     * 资质(营业执照)反面照片地址
     */
    private String backFileUrl;

    /**
     * 资质(营业执照)照片地址（预留字段可以使用比如上传三维图片)
     */
    private String otherFileUrl;

    /**
     * 把以上三个附件地址字段以json的格式存储下来，没有就空，以逗号分隔，以后可以把附件表的ID存储下来，如果有需要的。
     */
    private String fileAttId;

    /**
     * 经营范围
     */
    private String manageScope;

    /**
     * 经营性质
     */
    private String businessNature;

    /**
     * 企业类型(这个是营业执照里面的文本)
     */
    private String companyType;

    /**
     * 企业地址（省Code）
     */
    private String province;

    /**
     * 企业地址（省中文）
     */
    private String provinceName;

    /**
     * 企业地址（市Code）
     */
    private String city;

    /**
     * 企业地址（市中文）
     */
    private String cityName;

    /**
     * 企业地址（区/县Code）
     */
    private String area;

    /**
     * 企业地址（区/县中文）
     */
    private String areaName;

    /**
     * 企业地址（镇/街道Code）
     */
    private String street;

    /**
     * 企业地址（镇/街道中文）
     */
    private String streetName;

    /**
     * 企业地址（详细地址）
     */
    private String addressDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    private String versionCode;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyDate;

    /**
     * 预留字段
     */
    private String item1;

    /**
     * 预留字段
     */
    private String item2;

    /**
     * 预留字段
     */
    private String item3;

    /**
     * 预留字段
     */
    private String item4;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 电话
     */
    private String phone;

    /**
     * 主管税务机关
     */
    private String taxAuthority;

    /**
     * 打标标识【0-未达标 1-宽限期(有效) 2-已过期(失效)】
     */
    private String tagStatus;

    /**
     * 宽限期至
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graceTime;

    /**
     * 会员类型 【select:10-项目会员,20-一般会员】同步 platform_um_company
     */
    private String memberType;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     * 申请来源:1大宗平台,2物流平台
     */
    private String applySource;

    /**
     * 成立日期
     */
    private String foundDate;


}
