package com.wanlianyida.basemdm.domain.service;

import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.basemdm.domain.model.condition.UmOrgConditionCondition;
import com.wanlianyida.basemdm.domain.model.entity.UmOrgEntity;
import com.wanlianyida.basemdm.domain.repository.UmCompanyInfoRepository;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmExceptionEnum;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class UmCompanyInfoDomainService {
    @Resource
    private UmCompanyInfoRepository repository;

    public Boolean add(UmOrgEntity umOrgEntity) {
        return repository.add(umOrgEntity);
    }

    public Boolean update(UmOrgEntity umOrgEntity) {
        return repository.update(umOrgEntity);
    }

    public Boolean delete(UmOrgEntity umOrgEntity) {
        return repository.delete(umOrgEntity);
    }

    public List<UmOrgEntity> queryAll(String platformType) {
        return repository.queryAll(platformType);
    }

    public UmOrgEntity queryDetail(UmOrgConditionCondition condition) {
        return repository.queryDetail(condition);
    }

    /**
     * 验证公司 ID
     *
     * @param companyId 公司 ID
     */
    private void validateCompanyId(String companyId) {
        if (ObjectUtil.isEmpty(companyId)) {
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.ENTERPRISE_ID_NOT_FOUND);
        }
    }

    public List<UmOrgEntity> batchQuery(List<Long> ids) {
        return repository.batchQuery(ids);
    }
}
