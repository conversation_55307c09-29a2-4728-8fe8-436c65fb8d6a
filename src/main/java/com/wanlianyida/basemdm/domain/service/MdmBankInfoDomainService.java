package com.wanlianyida.basemdm.domain.service;

import com.wanlianyida.basemdm.domain.model.condition.MdmBankInfoCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmBankInfoEntity;
import com.wanlianyida.basemdm.domain.repository.MdmBankInfoRepository;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 20:51
 */
@Service
public class MdmBankInfoDomainService {

    @Resource
    private MdmBankInfoRepository mdmBankInfoRepository;
    public void insert(MdmBankInfoEntity entity) {
        mdmBankInfoRepository.insert(entity);
    }

    public void updateById(MdmBankInfoEntity entity) {
        mdmBankInfoRepository.updateById(entity);
    }

    public void delete(Long id) {
        mdmBankInfoRepository.delete(id);
    }

    public MdmBankInfoEntity queryByCode(String bankCode) {
        return mdmBankInfoRepository.queryByCode(bankCode);
    }

    public List<MdmBankInfoEntity> queryCondition(MdmBankInfoCondition condition) {
        return mdmBankInfoRepository.queryCondition(condition);
    }
}
