package com.wanlianyida.basemdm.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.api.model.command.CmdOperatorUpdateCommand;
import com.wanlianyida.basemdm.domain.model.entity.CmdOperatorContactEntity;
import com.wanlianyida.basemdm.domain.repository.CmdOperatorContactRepository;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdOperatorContactPO;
import com.wanlianyida.basemdm.interfaces.model.command.CmdOperatorContactCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdOperatorContactDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdOperatorContactQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 用户联系方式表 DomainService
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Slf4j
@Service
public class CmdOperatorContactDomainService {

    @Resource
    private CmdOperatorContactRepository CmdOperatorContactRepository;

    /**
     * 分页查询
     *
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link CmdOperatorContactDTO}>
     */
    public PageInfo<CmdOperatorContactDTO> queryPage(PagingInfo<CmdOperatorContactQuery> pagingInfo) {
        log.info("queryPage#分页查询->{}", JSONUtil.toJsonStr(pagingInfo));
        return CmdOperatorContactRepository.queryPage(pagingInfo);
    }

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return {@link ResultMode}<{@link CmdOperatorContactEntity}>
     */
    public List<CmdOperatorContactEntity> queryList(CmdOperatorContactQuery query) {
        log.info("queryList#列表查询->{}", JSONUtil.toJsonStr(query));
        return CmdOperatorContactRepository.queryList(query);
    }

    /**
     * 新增
     *
     * @param command
     * @return Long
     */
    public Long insert(CmdOperatorContactCommand command) {
        log.info("insert#新增->{}", JSONUtil.toJsonStr(command));
        CmdOperatorContactEntity entity = new CmdOperatorContactEntity();
        entity.setOperatorName(command.getUsername());
        entity.setMobile(command.getTelephone());
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return CmdOperatorContactRepository.insertSelective(entity);
    }

    /**
     * 修改
     *
     * @param command
     * @return Long
     */
    public Long update(CmdOperatorContactCommand command) {
        log.info("update#修改->{}", JSONUtil.toJsonStr(command));
        CmdOperatorContactEntity entity = new CmdOperatorContactEntity();
        entity.setOperatorName(command.getUsername());
        entity.setMobile(command.getTelephone());
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return CmdOperatorContactRepository.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据operatorId修改
     *
     * @param command 操作员联系方式
     * @return Long
     */
    public Long updateByOperatorId(CmdOperatorUpdateCommand command) {
        log.info("updateByOperatorId#修改->{}", JSONUtil.toJsonStr(command));
        CmdOperatorContactEntity entity = new CmdOperatorContactEntity();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return CmdOperatorContactRepository.updateByOperatorId(entity);
    }


    /**
     * 逻辑删除
     *
     * @param id
     * @return Long
     */
    public Long delete(Long id) {
        log.info("delete#逻辑删除->{}", id);
        return CmdOperatorContactRepository.delete(id);
    }

    public CmdOperatorContactDTO queryDetail(Long id) {
        CmdOperatorContactPO cmdOperatorContactPO = CmdOperatorContactRepository.getById(id);
        CmdOperatorContactDTO cmdOperatorContactDTO = new CmdOperatorContactDTO();
        BeanUtil.copyProperties(cmdOperatorContactPO, cmdOperatorContactDTO, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return cmdOperatorContactDTO;
    }

    public void accountSuspension(String operatorId) {
        CmdOperatorContactRepository.accountSuspension(operatorId);
    }

    public void accountActivation(String operatorId) {
        CmdOperatorContactRepository.accountActivation(operatorId);
    }

    public CmdOperatorContactDTO queryOperatorContactByOperatorId(String operatorId) {
        return CmdOperatorContactRepository.queryOperatorContactByOperatorId(operatorId);
    }

    public void deleteByOperatorId(String operatorId) {
        CmdOperatorContactRepository.deleteByOperatorId(operatorId);
    }
}
