package com.wanlianyida.basemdm.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.api.model.command.MiniProgramsAddCommand;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserContactEntity;
import com.wanlianyida.basemdm.domain.repository.CmdUserContactRepository;
import com.wanlianyida.basemdm.infrastructure.enums.DeletedFlagEnum;
import com.wanlianyida.basemdm.infrastructure.enums.EnableFlagEnum;
import com.wanlianyida.basemdm.infrastructure.enums.UserContactTypeEnum;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.interfaces.model.command.CmdUserContactCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdUserContactDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserContactQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.*;

import javax.annotation.Resource;

/**
 * 用户联系方式表 DomainService
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Slf4j
@Service
public class CmdUserContactDomainService {

    @Resource
    private CmdUserContactRepository cmdUserContactRepository;

    /**
     * 分页查询
     *
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link CmdUserContactDTO}>
     */
    public PageInfo<CmdUserContactDTO> queryPage(PagingInfo<CmdUserContactQuery> pagingInfo) {
        log.info("queryPage#分页查询->{}", JSONUtil.toJsonStr(pagingInfo));
        return cmdUserContactRepository.queryPage(pagingInfo);
    }

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return {@link ResultMode}<{@link CmdUserContactEntity}>
     */
    public List<CmdUserContactEntity> queryList(CmdUserContactQuery query) {
        log.info("queryList#列表查询->{}", JSONUtil.toJsonStr(query));
        return cmdUserContactRepository.queryList(query);
    }

    /**
     * 新增
     *
     * @param command
     * @return Long
     */
    public Long insert(CmdUserContactCommand command) {
        log.info("insert#新增->{}", JSONUtil.toJsonStr(command));
        CmdUserContactEntity entity = new CmdUserContactEntity();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return cmdUserContactRepository.insertSelective(entity);
    }

    /**
     * 修改
     *
     * @param command
     * @return Long
     */
    public Long update(CmdUserContactCommand command) {
        log.info("update#修改->{}", JSONUtil.toJsonStr(command));
        CmdUserContactEntity entity = new CmdUserContactEntity();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return cmdUserContactRepository.updateByPrimaryKeySelective(entity);
    }

    /**
     * 逻辑删除
     *
     * @param id
     * @return Long
     */
    public Long delete(@PathVariable("id") Long id) {
        log.info("delete#逻辑删除->{}", id);
        return cmdUserContactRepository.delete(id);
    }

    public void updateByUserId(CmdUserContactCommand command) {
        Long userId = command.getUserId();
        if (ObjectUtil.isEmpty(userId)) {
            throw new CtpCoreMdmException("userId不能为空");
        }
        CmdUserContactEntity entity = new CmdUserContactEntity();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        cmdUserContactRepository.updateByUserId(entity);

    }

    public void bindMiniPrograms(MiniProgramsAddCommand command) {
        //查询用户是否绑定过小程序
        checkAndSave(UserContactTypeEnum.MINI_PROGRAMS.getCode(), command);
        //查询用户是否绑定过小微信开放平台
        if(StrUtil.isNotBlank(command.getUnionId())){
            checkAndSave(UserContactTypeEnum.WECHAT_OPEN_PLATFORM.getCode(), command);
        }
    }

    public void delUserContact(Long userId){
        cmdUserContactRepository.delUserContact(userId);
    }

    public void checkAndSave(String category, MiniProgramsAddCommand command) {
        CmdUserContactEntity entity = cmdUserContactRepository.getByUserIdAndCategory(command.getUserId(), category);
        String newContactInfo = getContactInfo(category, command);

        if (ObjectUtil.isNotEmpty(entity)) {
            updateExistingEntity(category, entity, newContactInfo);
            cmdUserContactRepository.updateByUserId(entity);
        } else {
            entity = new CmdUserContactEntity()
                    .setUserId(command.getUserId())
                    .setContactInfo(newContactInfo)
                    .setCategory(category)
                    .setDelFlag(String.valueOf(DeletedFlagEnum.UN_DELETED.getCode()))
                    .setEnableFlag(String.valueOf(EnableFlagEnum.ENABLE.getCode()))
                    .setCreatorId(command.getCreatorId())
                    .setUpdaterId(command.getUpdaterId());
            cmdUserContactRepository.insertSelective(entity);
        }
    }

    private String getContactInfo(String category, MiniProgramsAddCommand command) {
        if (UserContactTypeEnum.MINI_PROGRAMS.getCode().equals(category)) {
            return command.getAppId() + ":" + command.getOpenId();
        } else if (UserContactTypeEnum.WECHAT_OPEN_PLATFORM.getCode().equals(category)) {
            return command.getUnionId();
        }
        return "";
    }

    private void updateExistingEntity(String category, CmdUserContactEntity entity, String newContactInfo) {
        if (UserContactTypeEnum.MINI_PROGRAMS.getCode().equals(category)) {
            // 解析处理
            Map<String, String> appOpenMap = convertToMap(entity.getContactInfo());
            List<String> newAppOpen = Arrays.asList(newContactInfo.split(":"));
            appOpenMap.put(newAppOpen.get(0), newAppOpen.get(1));
            // 转换回字符串
            String contactInfo = convertToString(appOpenMap);
            entity.setContactInfo(contactInfo);
        } else if (UserContactTypeEnum.WECHAT_OPEN_PLATFORM.getCode().equals(category)) {
            entity.setContactInfo(newContactInfo);
        }
    }

    public static Map<String, String> convertToMap(String input) {
        Map<String, String> resultMap = new HashMap<>();
        if (StrUtil.isBlank(input)) {
            return resultMap;
        }

        // 按逗号分隔每个键值对
        String[] pairs = input.split(",");

        for (String pair : pairs) {
            // 按冒号分隔键和值
            String[] keyValue = pair.split(":", 2);

            if (keyValue.length == 2) {
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();
                resultMap.put(key, value);
            }
        }
        return resultMap;
    }

    public static String convertToString(Map<String, String> map) {
        StringBuilder result = new StringBuilder();

        boolean first = true;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (!first) {
                result.append(",");
            }
            result.append(entry.getKey()).append(":").append(entry.getValue());
            first = false;
        }

        return result.toString();
    }

    public boolean listUserByTelephone(String telephone) {
        List<CmdUserContactEntity> list = cmdUserContactRepository.listUserByTelephone(telephone);
        if (list != null && !list.isEmpty()) {
            return true;
        }else {
            return false;
        }
    }

    public CmdUserContactEntity getUserByUnionId(String unionId) {
        return cmdUserContactRepository.getContactByUnionId(unionId);
    }

    public List<CmdUserContactEntity> getUserContactByUserId(Long userId) {
        return cmdUserContactRepository.getUserContactByUserId(userId);
    }
}
