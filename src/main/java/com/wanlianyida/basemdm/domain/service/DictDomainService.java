package com.wanlianyida.basemdm.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.basemdm.domain.model.condition.DictCondition;
import com.wanlianyida.basemdm.domain.model.condition.DictValueCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmDictDetailEntity;
import com.wanlianyida.basemdm.domain.model.entity.MdmDictionaryEntity;
import com.wanlianyida.basemdm.domain.repository.MdmDictDetailRepository;
import com.wanlianyida.basemdm.domain.repository.MdmDictionaryRepository;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmExceptionEnum;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmDictDetailPO;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmDictionaryPO;
import com.wanlianyida.basemdm.interfaces.model.query.MdmDictDetailBatchQuery;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * 字典领域服务
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class DictDomainService {

    @Resource
    private MdmDictionaryRepository mdmDictionaryRepository;
    @Resource
    private MdmDictDetailRepository mdmDictDetailRepository;

    /**
     * 条件查询字典列表
     */
    public List<MdmDictionaryEntity> queryCondition(DictCondition condition) {
        return mdmDictionaryRepository.queryCondition(condition);
    }
    /**
     * 条件查询字典值列表
     */
    public List<MdmDictDetailEntity> queryValueCondition(DictValueCondition condition) {
        condition.setDelFlag(0); // 未删除
        return mdmDictDetailRepository.queryCondition(condition);
    }

    /**
     * 查询有效的字典值列表
     */
    public List<MdmDictDetailEntity> queryValidValue(String dictCode) {
        DictValueCondition condition = new DictValueCondition();
        condition.setDictCode(dictCode);
        condition.setDelFlag(0); // 未删除
        condition.setDisableFlag(0); // 未禁用
        return mdmDictDetailRepository.queryCondition(condition);
    }

    /**
     * 批量查询有效的字典值列表
     */
    public List<MdmDictDetailEntity> queryValidValueList(MdmDictDetailBatchQuery query) {
        DictValueCondition condition = new DictValueCondition();
        condition.setDictCodeList(query.getDictCodeList());
        condition.setPlatCode(query.getPlatCode());
        condition.setDelFlag(0); // 未删除
        condition.setDisableFlag(0); // 未禁用
        return mdmDictDetailRepository.queryCondition(condition);
    }

    /**
     * 新增字典
     */
    public void add(MdmDictionaryEntity entity) {
        // 检查字典编码是否已存在
        DictCondition condition = new DictCondition();
        condition.setDictCode(entity.getDictCode());
        condition.setPlatCode(entity.getPlatCode());
        if (CollectionUtil.isNotEmpty(mdmDictionaryRepository.queryCondition(condition))) {
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.CODE_ALREADY_EXISTS);
        }

        MdmDictionaryPO po = BeanUtil.toBean(entity, MdmDictionaryPO.class);
        po.setDelFlag(0); // 默认未删除
        mdmDictionaryRepository.save(po);
    }

    /**
     * 新增字典值
     */
    public void addValue(MdmDictDetailEntity entity) {
        // 校验字典值是否重复
        DictValueCondition condition = new DictValueCondition();
        condition.setDictCode(entity.getDictCode());
        condition.setPlatCode(entity.getPlatCode());
        condition.setDictValue(entity.getDictValue());
        condition.setDelFlag(0);
        if (CollectionUtil.isNotEmpty(mdmDictDetailRepository.queryCondition(condition))) {
            throw new CtpCoreMdmException(CtpCoreMdmExceptionEnum.DICTIONARY_VALUE_EXISTS);
        }

        MdmDictDetailPO po = BeanUtil.toBean(entity, MdmDictDetailPO.class);
        po.setDelFlag(0); // 默认未删除
        po.setCreateTime(new Date());
        mdmDictDetailRepository.save(po);
    }

    /**
     * 更新字典
     */
    public void update(MdmDictionaryEntity entity) {
        MdmDictionaryPO po = BeanUtil.toBean(entity, MdmDictionaryPO.class);
        mdmDictionaryRepository.updateById(po);
    }

    /**
     * 更新字典值
     */
    public void updateValue(MdmDictDetailEntity entity) {
        MdmDictDetailPO po = BeanUtil.toBean(entity, MdmDictDetailPO.class);
        mdmDictDetailRepository.updateById(po);
    }

    /**
     * 删除字典（逻辑删除）
     */
    public void delete(MdmDictionaryEntity entity) {
        // 先查询字典信息
        MdmDictionaryPO po = mdmDictionaryRepository.selectById(entity.getId());
        
        // 批量删除字典值（一次性数据库交互）
        mdmDictDetailRepository.batchDeleteByDictCode(po, entity.getLastUpdaterId());
        
        // 删除字典
        po.setDelFlag(1); // 逻辑删除
        po.setLastUpdaterId(entity.getLastUpdaterId());
        po.setLastUpdateTime(new Date());
        mdmDictionaryRepository.updateById(po);
    }

    /**
     * 删除字典值（逻辑删除）
     */
    public void deleteValue(MdmDictDetailEntity entity) {
        mdmDictDetailRepository.removeById(entity.getId(), entity.getLastUpdaterId());
    }
} 
