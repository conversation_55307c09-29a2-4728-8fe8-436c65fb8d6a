package com.wanlianyida.basemdm.domain.service;

import com.wanlianyida.basemdm.domain.model.condition.MdmAccreditOperatorCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmAccreditOperatorEntity;
import com.wanlianyida.basemdm.domain.repository.MdmAccreditOperatorRepository;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class MdmAccreditOperatorDomainService {

    @Resource
    private MdmAccreditOperatorRepository accreditOperatorRepository;

    @Transactional(rollbackFor = Exception.class)
    public void insert(MdmAccreditOperatorEntity entity) {
        // 删除原有的授权委托书
        accreditOperatorRepository.deletedByCompanyId(entity.getCompanyId(), entity.getPlatformCode());
        if (!accreditOperatorRepository.insert(entity)) {
            throw new CtpCoreMdmException("授权委托书创建失败");
        }
    }

    public void updateById(MdmAccreditOperatorEntity entity) {
        accreditOperatorRepository.updateById(entity);
    }

    public List<MdmAccreditOperatorEntity> queryCondition(MdmAccreditOperatorCondition condition) {
        return accreditOperatorRepository.queryCondition(condition);
    }

    public MdmAccreditOperatorEntity queryById(Long id) {
        return accreditOperatorRepository.queryById(id);
    }
}
