package com.wanlianyida.basemdm.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserInfoEntity;
import com.wanlianyida.basemdm.domain.repository.CmdUserInfoRepository;
import com.wanlianyida.basemdm.infrastructure.exception.CtpCoreMdmException;
import com.wanlianyida.basemdm.interfaces.model.command.CmdUserInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdUserInfoDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdGetUserListQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserInfoQuery;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

import javax.annotation.Resource;

/**
 * 用户信息表 DomainService
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Slf4j
@Service
public class CmdUserInfoDomainService {

    @Resource
    private CmdUserInfoRepository cmdUserInfoRepository;

	/**
     * 分页查询
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link CmdUserInfoDTO}>
     */
    public PageInfo<CmdUserInfoDTO> queryPage(PagingInfo<CmdUserInfoQuery> pagingInfo) {
        log.info("queryPage#分页查询->{}", JSONUtil.toJsonStr(pagingInfo));
        return cmdUserInfoRepository.queryPage(pagingInfo);
    }

    /**
     * 列表查询
     * @param query 查询参数
     * @return {@link ResultMode}<{@link CmdUserInfoEntity}>
     */
    public List<CmdUserInfoEntity> queryList(CmdUserInfoQuery query) {
        log.info("queryList#列表查询->{}", JSONUtil.toJsonStr(query));
        return cmdUserInfoRepository.queryList(query);
    }

    /**
	 * 新增
	 * @param command
	 * @return Long
	 */
	public Long insert(CmdUserInfoCommand command) {
        log.info("insert#新增->{}", JSONUtil.toJsonStr(command));
        if (StrUtil.isBlank(command.getMobile())) {
            throw new CtpCoreMdmException("手机号不能为空");
        }
        CmdUserInfoQuery query = new CmdUserInfoQuery();
        query.setMobile(command.getMobile());
        if (CollUtil.isNotEmpty(cmdUserInfoRepository.queryList(query))) {
            throw new CtpCoreMdmException("手机号已存在");
        }
        CmdUserInfoEntity entity = new CmdUserInfoEntity();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
		return cmdUserInfoRepository.insertSelective(entity);
	}

    /**
     * 修改
     * @param command
     * @return Long
     */
    public Long update(CmdUserInfoCommand command) {
        log.info("update#修改->{}", JSONUtil.toJsonStr(command));
        CmdUserInfoEntity entity = new CmdUserInfoEntity();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return cmdUserInfoRepository.updateByPrimaryKeySelective(entity);
    }

    /**
     * 逻辑删除
     * @param id
     * @return Long
     */
    public Long delete(@PathVariable("id") Long id) {
        log.info("delete#逻辑删除->{}", id);
        return cmdUserInfoRepository.delete(id);
    }

    public CmdUserInfoEntity queryByLoginName(String loginName) {
        return cmdUserInfoRepository.queryByLoginName(loginName);
    }

    public CmdUserInfoEntity queryByUserPhone(String phone) {
        return cmdUserInfoRepository.queryByUserPhone(phone);
    }

    public CmdUserInfoEntity queryDetail(Long id) {
        return cmdUserInfoRepository.queryDetail(id);
    }

    public CmdUserInfoEntity queryUserInfo(CmdUserQuery query) {
        return cmdUserInfoRepository.queryUserInfo(query);
    }

    public List<CmdUserInfoEntity> batchQueryUserList(CmdGetUserListQuery query) {
        return cmdUserInfoRepository.batchQueryUserList(query);
    }

    public void delUserInfo(Long userId){
        cmdUserInfoRepository.delUserInfo(userId);
    }
}
