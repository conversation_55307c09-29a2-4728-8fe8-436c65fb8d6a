package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.condition.UmOrgConditionCondition;
import com.wanlianyida.basemdm.domain.model.entity.UmOrgEntity;

import java.util.List;

public interface UmCompanyInfoRepository {
    /**
     * 组织关系新增
     *
     * @param umOrgEntity UM 组织实体
     * @return {@link Boolean }
     */
    Boolean add(UmOrgEntity umOrgEntity);

    /**
     * 组织关系更新
     *
     * @param umOrgEntity UM 组织实体
     * @return {@link Boolean }
     */
    Boolean update(UmOrgEntity umOrgEntity);

    /**
     * 组织关系删除
     *
     * @param umOrgEntity UM 组织实体
     * @return {@link UmOrgEntity }
     */
    Boolean delete(UmOrgEntity umOrgEntity);

    /**
     * 查询全部
     *
     * @param platformType 平台类型
     * @return {@link List }<{@link UmOrgEntity }>
     */
    List<UmOrgEntity> queryAll(String platformType);

    /**
     * 查询详情
     *
     * @param condition 条件
     * @return {@link UmOrgEntity }
     */
    UmOrgEntity queryDetail(UmOrgConditionCondition condition);

    /**
     * 批量查询
     *
     * @param ids IDS
     * @return {@link List }<{@link UmOrgEntity }>
     */
    List<UmOrgEntity> batchQuery(List<Long> ids);
}
