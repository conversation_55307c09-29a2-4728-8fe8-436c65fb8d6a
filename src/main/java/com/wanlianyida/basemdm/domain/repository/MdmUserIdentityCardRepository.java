package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.MdmUserIdentityCardEntity;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月09日 10:15
 */
public interface MdmUserIdentityCardRepository {

    boolean insert(MdmUserIdentityCardEntity entity);

    boolean updateByUserId(MdmUserIdentityCardEntity entity);

    MdmUserIdentityCardEntity queryByUserId(Long userId);

    void delete(String userId);
}
