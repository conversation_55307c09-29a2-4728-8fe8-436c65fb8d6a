package com.wanlianyida.basemdm.domain.repository;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserContactEntity;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdUserContactDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserContactQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;

import java.util.List;

/**
 * 用户联系方式表 Service
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
public interface CmdUserContactRepository {

    /**
     * 分页查询
     *
     * @param pagingInfo 分页参数
     * @return {@link PageInfo}<{@link CmdUserContactDTO}>
     */
    PageInfo<CmdUserContactDTO> queryPage(PagingInfo<CmdUserContactQuery> pagingInfo);

    /**
     * 列表查询
     *
     * @param condition
     * @return {@link List}<{@link CmdUserContactEntity}>
     */
    List<CmdUserContactEntity> queryList(CmdUserContactQuery condition);

    /**
     * 新增
     *
     * @param entity
     * @return int
     */
    Long insertSelective(CmdUserContactEntity entity);

    /**
     * 修改
     *
     * @param entity
     * @return int
     */
    Long updateByPrimaryKeySelective(CmdUserContactEntity entity);

    /**
     * 逻辑删除
     *
     * @param id
     */
    Long delete(Long id);

    /**
     * 根据主键查询
     *
     * @param id
     * @return {@link CmdUserContactEntity}
     */
    CmdUserContactEntity selectByPrimaryKey(Long id);

    /**
     * 根据用户id更新用户信息
     *
     * @param entity
     */
    void updateByUserId(CmdUserContactEntity entity);

    CmdUserContactEntity getByUserIdAndCategory(Long userId, String category);

    void delUserContact(Long userId);

    List<CmdUserContactEntity> listUserByTelephone(String telephone);

    CmdUserContactEntity getContactByUnionId(String unionId);

    List<CmdUserContactEntity> getUserContactByUserId(Long userId);
}
