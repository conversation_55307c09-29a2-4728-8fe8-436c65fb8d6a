package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyBusinessLicenseEntity;
import com.wanlianyida.basemdm.interfaces.model.query.CmdCompanyLicenseQuery;

import java.util.List;

/**
 * 企业资质表 Service
 *
 * <AUTHOR>
 * @date 2024-12-29
 */
public interface CmdCompanyBusinessLicenseRepository {

	/**
	 * 列表查询
	 *
	 * @param filter
	 * @return {@link List}<{@link CmdCompanyBusinessLicenseEntity}>
	 */
	List<CmdCompanyBusinessLicenseEntity> queryList(CmdCompanyLicenseQuery filter);

	/**
     * 新增
     *
     * @param record
     * @return String
     */
	String insertSelective(CmdCompanyBusinessLicenseEntity record);

	/**
	 * 根据主键修改
	 *
	 * @param record
	 * @return String
	 */
	String updateByPrimaryKeySelective(CmdCompanyBusinessLicenseEntity record);

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link CmdCompanyBusinessLicenseEntity}
	 */
	CmdCompanyBusinessLicenseEntity selectByPrimaryKey(String id);
	
}
