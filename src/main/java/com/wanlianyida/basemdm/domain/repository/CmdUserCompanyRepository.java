package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyInfoEntity;
import com.wanlianyida.basemdm.domain.model.entity.CmdUserCompanyEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdUserCompanyPO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdUserCompanyQuery;

import java.util.List;

/**
 * 企业组织结构表 Service
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface CmdUserCompanyRepository {

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link CmdCompanyInfoEntity}>
	 */
	List<CmdUserCompanyPO> queryList(CmdUserCompanyQuery condition);

	/**
     * 新增
     *
     * @param bo
     */
	Long insert(CmdUserCompanyEntity bo);

	/**
	 * 修改
	 *
	 * @param bo
	 */
	Long update(CmdUserCompanyEntity bo);

}
