package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.condition.MdmCompanyContactCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmCompanyContactEntity;

import java.util.List;

public interface MdmCompanyContactRepository {
    /**
     * 新增联系人
     * @param contactEntity
     */
    void add(MdmCompanyContactEntity contactEntity);

    /**
     * 修改联系人
     * @param contactEntity
     */
    void update(MdmCompanyContactEntity contactEntity);

    /**
     * 批量删除联系人
     * @param idList
     */
    void batchDelete(List<String> idList);

    /**
     * 根据条件查询联系人
     * @param condition
     * @return {@link List }<{@link MdmCompanyContactEntity }>
     */
    List<MdmCompanyContactEntity> queryByCondition(MdmCompanyContactCondition condition);

    /**
     * 设置默认联系人
     * @param id
     * @param nowDefaultContactId
     */
    void setDefault(String id, Long nowDefaultContactId);

    /**
     * 查询联系人详情
     * @param id
     * @return {@link MdmCompanyContactEntity }
     */
    MdmCompanyContactEntity queryDetail(String id);
}
