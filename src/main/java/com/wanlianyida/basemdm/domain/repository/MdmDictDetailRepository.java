package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.condition.DictValueCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmDictDetailEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmDictDetailPO;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmDictionaryPO;

import java.util.List;

/**
 * 字典值(MdmDictDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface MdmDictDetailRepository {

    List<MdmDictDetailEntity> queryCondition(DictValueCondition condition);

    void save(MdmDictDetailPO po);

    void updateById(MdmDictDetailPO po);

    void removeById(Long id, String lastUpdaterId);

    MdmDictDetailPO selectById(Long id);

    /**
     * 根据字典编码批量删除字典值
     */
    void batchDeleteByDictCode(MdmDictionaryPO dictionaryPO, String lastUpdaterId);
} 
