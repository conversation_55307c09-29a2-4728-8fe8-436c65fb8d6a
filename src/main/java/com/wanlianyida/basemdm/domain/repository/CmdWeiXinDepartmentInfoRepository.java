package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.CmdWeiXinDepartmentInfoEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdWeiXinDepartmentInfoPO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdWeiXinDepartmentInfoQuery;

import java.util.List;

/**
 * 企业组织结构表 Service
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface CmdWeiXinDepartmentInfoRepository {

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link CmdWeiXinDepartmentInfoPO}>
	 */
	List<CmdWeiXinDepartmentInfoPO> queryList(CmdWeiXinDepartmentInfoQuery condition);
	public List<CmdWeiXinDepartmentInfoPO> queryListByParentDeptId(String deptId);
	/**
     * 新增
     *
     * @param bo
     */
	Long insert(CmdWeiXinDepartmentInfoEntity bo);

	/**
	 * 修改
	 *
	 * @param bo
	 */
	Long update(CmdWeiXinDepartmentInfoEntity bo);

	/**
	 * 根据统一社会信息用代码查询父部门查询
	 *
	 * @param deptSocialCreditCode
	 * @return {@link List}<{@link CmdWeiXinDepartmentInfoPO}>
	 */
	CmdWeiXinDepartmentInfoPO queryParentByCreditCode(String deptSocialCreditCode);

	/**
	 * 根据统一社会信息用代码查询部门信息
	 *
	 * @param deptSocialCreditCode
	 * @return {@link CmdWeiXinDepartmentInfoPO}
	 */
	CmdWeiXinDepartmentInfoPO queryByCreditCode(String deptSocialCreditCode);

	/**
	 * 根据部门ID列表查询部门信息
	 *
	 * @param deptIds 部门ID列表
	 * @return {@link List}<{@link CmdWeiXinDepartmentInfoPO}>
	 */
	List<CmdWeiXinDepartmentInfoPO> queryByParentDeptIds(List<String> deptIds);
}
