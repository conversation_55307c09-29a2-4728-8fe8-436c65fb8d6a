package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyInfoEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdCompanyInfoPO;
import com.wanlianyida.basemdm.interfaces.model.command.UpdateCompanyTaxNoAndOrgInfoCommand;
import com.wanlianyida.basemdm.interfaces.model.query.CmdCompanyInfoQuery;

import java.util.List;

/**
 * 企业组织结构表 Service
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface CmdCompanyInfoRepository {

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link CmdCompanyInfoEntity}>
	 */
	List<CmdCompanyInfoPO> queryList(CmdCompanyInfoQuery condition);

	/**
     * 新增
     *
     * @param bo
     */
	Long insert(CmdCompanyInfoEntity bo);

	/**
	 * 修改
	 *
	 * @param bo
	 */
	Long update(CmdCompanyInfoEntity bo);

	/**
	 * 更新企业组织结构、税号信息
	 * @param command
	 */
	void updateCompanyTaxNoAndOrgInfo(UpdateCompanyTaxNoAndOrgInfoCommand command);



	/**
	 * 根据统一社会信用代码查询
	 *
	 * @param licenseNo
	 * @return {@link List}<{@link CmdCompanyInfoEntity}>
	 */
	CmdCompanyInfoPO queryByLicenseNo(String licenseNo);
}
