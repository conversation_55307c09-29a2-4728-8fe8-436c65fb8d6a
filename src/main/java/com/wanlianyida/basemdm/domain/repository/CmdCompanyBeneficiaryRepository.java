package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.condition.CmdCompanyBeneficiaryCondition;
import com.wanlianyida.basemdm.domain.model.entity.CmdCompanyBeneficiaryEntity;

import java.util.List;

public interface CmdCompanyBeneficiaryRepository {
    /**
     * 新增受益人
     * @param entity
     * @return
     */
    String add(CmdCompanyBeneficiaryEntity entity);

    /**
     * 修改受益人
     * @param cmdCompanyBeneficiaryEntity
     * @return
     */
    String update(CmdCompanyBeneficiaryEntity cmdCompanyBeneficiaryEntity);

    /**
     * 删除受益人
     * @param id
     */
    void delete(String id);

    /**
     * 查询受益人详情
     * @param id
     * @return <p>
     */
    CmdCompanyBeneficiaryEntity queryDetail(String id);

    /**
     * 条件查询受益人
     * @param condition
     * @return
     */
    List<CmdCompanyBeneficiaryEntity> queryByCondition(CmdCompanyBeneficiaryCondition condition);
}
