package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.CmdWeiXinEmployeeDeptInfoEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdWeiXinEmployeeDeptInfoPO;
import com.wanlianyida.basemdm.interfaces.model.dto.CmdWeiXinEmployeeDeptDTO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdWeiXinEmployeeDeptInfoQuery;

import java.util.List;

/**
 * 企业组织结构表 Service
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface CmdWeiXinEmployeeDeptInfoRepository {

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link CmdWeiXinEmployeeDeptInfoPO}>
	 */
	List<CmdWeiXinEmployeeDeptInfoPO> queryList(CmdWeiXinEmployeeDeptInfoQuery condition);

	/**
	 * 根据员工id查询
	 *
	 * @param condition
	 * @return {@link List}<{@link CmdWeiXinEmployeeDeptInfoPO}>
	 */
	List<CmdWeiXinEmployeeDeptDTO> queryEmpIds(CmdWeiXinEmployeeDeptInfoQuery condition);

	/**
     * 新增
     *
     * @param bo
     */
	Long insert(CmdWeiXinEmployeeDeptInfoEntity bo);

	/**
	 * 修改
	 *
	 * @param bo
	 */
	Long update(CmdWeiXinEmployeeDeptInfoEntity bo);

	/**
	 * 删除
	 *
	 * @param bo
	 */
	Long updateByEmpId(CmdWeiXinEmployeeDeptInfoEntity bo);

	/**
	 * 删除
	 *
	 * @param bo
	 */
	Long updateByDeptId(CmdWeiXinEmployeeDeptInfoEntity bo);

}
