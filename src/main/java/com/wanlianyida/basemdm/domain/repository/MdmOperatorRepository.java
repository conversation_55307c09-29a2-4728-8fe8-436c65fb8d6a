package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.api.model.query.OperatorQuery;
import com.wanlianyida.basemdm.domain.model.bo.UserCompanyBindCountBO;
import com.wanlianyida.basemdm.domain.model.condition.MdmOperatorCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmOperatorEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.MdmOperatorPO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface MdmOperatorRepository {
    /**
     * 新增
     * @param cmdOperatorEntity
     */
    Long add(MdmOperatorEntity cmdOperatorEntity);

    /**
     * 批量删除
     * @param idList)
     */
    void batchDelete(List<String> idList);

    /**
     * 查询详情
     * @param condition
     * @return <p>
     */
    MdmOperatorEntity queryDetail(MdmOperatorCondition condition);

    /**
     * 解绑用户
     * @param id
     */
    void unbindUser(String id,String companyId);

    /**
     * 修改
     * @param cmdOperatorEntity
     */
    void update(MdmOperatorEntity cmdOperatorEntity);

    /**
     * 分页查询
     * @param condition
     * @return
     */
    List<MdmOperatorEntity> queryPage(MdmOperatorCondition condition);

    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<MdmOperatorEntity> queryList(MdmOperatorCondition condition);

    /**
     * 统计用户绑定企业数量
     */
    Map<Long, UserCompanyBindCountBO> countBind(List<Long> userIdList);

    /**
     * 修改手机号
     * @param entity
     */
    void updateOperatorPhone(MdmOperatorEntity entity);

    /**
     * 删除管理员
     * @param companyId
     */
    void deleteAdmin(String companyId);

    /**
     * 根据操作员编码查询操作员信息
     * @param operatorCodeList
     * @return <p>
     */
    List<MdmOperatorEntity> queryOperatorByOperatorCodeList(List<String> operatorCodeList);

    /**
     * 停用操作员
     * @param operatorId
     */
    void accountSuspension(String operatorId);

    /**
     * 启用操作员
     * @param operatorId
     */
    void accountActivation(String operatorId);

    List<MdmOperatorEntity> listOperatorsBySysTypeAndIds(OperatorQuery operatorQuery);

    MdmOperatorPO getByOperatorCode(String operatorCode);

    List<MdmOperatorPO> getByOperatorIds(Set<Long> operatorIds);
}
