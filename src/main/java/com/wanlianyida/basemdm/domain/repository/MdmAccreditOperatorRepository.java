package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.condition.MdmAccreditOperatorCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmAccreditOperatorEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月03日 09:09
 */
public interface MdmAccreditOperatorRepository {

    boolean insert(MdmAccreditOperatorEntity entity);

    boolean updateById(MdmAccreditOperatorEntity entity);

    List<MdmAccreditOperatorEntity> queryCondition(MdmAccreditOperatorCondition condition);

    MdmAccreditOperatorEntity queryById(Long id);

    boolean deletedByCompanyId(String companyId, String platformCode);

    void batchDeleteOperator(List<String> idList);
}
