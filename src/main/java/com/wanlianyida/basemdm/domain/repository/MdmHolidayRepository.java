package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.condition.MdmHolidayCondition;
import com.wanlianyida.basemdm.domain.model.entity.MdmHolidayEntity;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 国家法定节假日表Repository接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface MdmHolidayRepository {
    
    /**
     * 新增
     * @param mdmHolidayEntity
     * @return 主键ID
     */
    Long add(MdmHolidayEntity mdmHolidayEntity);

    /**
     * 修改
     * @param mdmHolidayEntity
     */
    void update(MdmHolidayEntity mdmHolidayEntity);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);


    /**
     * 查询详情
     * @param condition
     * @return
     */
    MdmHolidayEntity queryDetail(MdmHolidayCondition condition);


    /**
     * 列表查询
     * @param condition
     * @return
     */
    List<MdmHolidayEntity> queryList(MdmHolidayCondition condition);

    /**
     * 根据日期查询
     * @param calDate
     * @return
     */
    MdmHolidayEntity queryByCalDate(Date calDate);

    /**
     * 检查日期是否已存在
     * @param calDate
     * @param excludeId 排除的ID（用于更新时检查）
     * @return
     */
    boolean existsByCalDate(Date calDate, Long excludeId);
} 
