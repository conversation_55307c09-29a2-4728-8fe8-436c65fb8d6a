package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.CmdWeiXinEmployeeInfoEntity;
import com.wanlianyida.basemdm.infrastructure.repository.po.CmdWeiXinEmployeeInfoPO;
import com.wanlianyida.basemdm.interfaces.model.query.CmdWeiXinEmployeeInfoQuery;

import java.util.List;

/**
 * 企业组织结构表 Service
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface CmdWeiXinEmployeeInfoRepository {

    /**
     * 列表查询
     *
     * @param condition
     * @return {@link List}<{@link CmdWeiXinEmployeeInfoPO}>
     */
    List<CmdWeiXinEmployeeInfoPO> queryList(CmdWeiXinEmployeeInfoQuery condition);

    /**
     * 新增
     *
     * @param bo
     */
    Long insert(CmdWeiXinEmployeeInfoEntity bo);

    /**
     * 修改
     *
     * @param bo
     */
    Long update(CmdWeiXinEmployeeInfoEntity bo);

    /**
     * 查询最大员工编号
     *
     * @return
     */
    String selectMaxEmpNo();

    /**
     * 根据empId获取企业员工信息
     *
     * @param empId
     * @return
     */
    CmdWeiXinEmployeeInfoPO selectByEmpId(String empId);

    /**
     * 查询需要发送的员工信息
     *
     * @return
     */
    List<CmdWeiXinEmployeeInfoPO> selectHookData(int days);

    /**
     * 查询所有需要发送通知的员工
     * @return
     */
    List<String> queryAllNoticeEmpIds();

}
