package com.wanlianyida.basemdm.domain.repository;

import com.wanlianyida.basemdm.domain.model.entity.MdmPlatformParameterEntity;
import com.wanlianyida.basemdm.interfaces.model.query.MdmPlatformParameterQuery;

import java.util.List;

/**
 * 平台参数仓储接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface MdmPlatformParameterRepository {

    /**
     * 条件查询列表
     *
     * @param filter 查询条件
     * @return {@link List}<{@link MdmPlatformParameterEntity}>
     */
    List<MdmPlatformParameterEntity> queryList(MdmPlatformParameterQuery filter);

    /**
     * 新增
     *
     * @param entity 实体
     */
    void add(MdmPlatformParameterEntity entity);

    /**
     * 更新
     *
     * @param entity 实体
     */
    void update(MdmPlatformParameterEntity entity);

    /**
     * 根据ID查询
     *
     * @param id 主键
     * @return {@link MdmPlatformParameterEntity}
     */
    MdmPlatformParameterEntity selectById(Long id);

    /**
     * 根据参数编码查询
     *
     * @param paraCode 参数编码
     * @return {@link MdmPlatformParameterEntity}
     */
    MdmPlatformParameterEntity selectByParaCode(String paraCode);

    /**
     * 根据分组编码查询
     *
     * @param groupCode 分组编码
     * @return {@link List}<{@link MdmPlatformParameterEntity}>
     */
    List<MdmPlatformParameterEntity> selectByGroupCode(String groupCode);

    /**
     * 查询所有
     *
     * @return {@link List}<{@link MdmPlatformParameterEntity}>
     */
    List<MdmPlatformParameterEntity> selectAll();

    /**
     * 根据多个参数编码批量查询
     *
     * @param paraCodes 参数编码列表
     * @return {@link List}<{@link MdmPlatformParameterEntity}>
     */
    List<MdmPlatformParameterEntity> selectByParaCodes(List<String> paraCodes);

    /**
     * 根据参数编码和平台编码查询未删除的记录
     *
     * @param paraCode 参数编码
     * @param plfCode 平台编码
     * @return {@link MdmPlatformParameterEntity}
     */
    MdmPlatformParameterEntity selectByParaCodeAndPlfCode(String paraCode, String plfCode);

    /**
     * 根据ID软删除
     *
     * @param entity 删除实体
     */
    void deleteById(MdmPlatformParameterEntity entity);
} 