package com.wanlianyida.inner.application.service;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.api.inter.AccountTransInter;
import com.wanlianyida.sett.api.inter.BankInitiateCallInter;
import com.wanlianyida.sett.api.model.command.BankCardBindCallBackCommand;
import com.wanlianyida.sett.api.model.command.BankOpenCallBackCommand;
import com.wanlianyida.sett.api.model.command.BankOpenInfoQueryCommand;
import com.wanlianyida.sett.api.model.command.BankWithdrawCallBackCommand;
import com.wanlianyida.sett.api.model.dto.BankOpenInfoQueryResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BankCallbackAppService {

    @Resource
    private AccountTransInter accountTransInter;

    @Resource
    BankInitiateCallInter bankInitiateCallInter;

    /**
     * 提现回调
     */
    public ResultMode withdrawCallBack(BankWithdrawCallBackCommand command) {
        return accountTransInter.withdrawCallback(command);
    }

    /**
     * 银行开户回调
     */
    public ResultMode<Void> bankOpenCallback(BankOpenCallBackCommand command) {
        return bankInitiateCallInter.bankOpenCallback(command);
    }

    public ResultMode<Void> bindCardCallback(BankCardBindCallBackCommand command) {
        return bankInitiateCallInter.bindCardCallback(command);
    }

    public ResultMode<BankOpenInfoQueryResDTO> bankOpenInfoQuery(BankOpenInfoQueryCommand command) {
        return bankInitiateCallInter.bankOpenInfoQuery(command);
    }
}
