package com.wanlianyida.baselog.infrastructure.repository.po;

import com.wanlianyida.baselog.infrastructure.annotation.QueryOp;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Qin
 * @Date: 2024/12/18 14:49
 * @Description:
 **/
@Data
public class LogUserRequestRecordQueryPO {

    /**
     * 业务对象id，（司机id、车辆id、商品id）
     */
    @QueryOp(fieldName = "biz_id", operator = QueryOp.QueryOperator.EQUALS)
    private String bizId;
    /**
     * 业务对象类型，（司机、车辆、商品）
     */
    @QueryOp(fieldName = "biz_type", operator = QueryOp.QueryOperator.EQUALS)
    private String bizType;
    /**
     * 用户id
     */
    @QueryOp(fieldName = "user_base_id", operator = QueryOp.QueryOperator.EQUALS)
    private String userBaseId;
    /**
     * 用户名
     */
    @QueryOp(fieldName = "user_name", operator = QueryOp.QueryOperator.EQUALS)
    private String userName;
    /**
     * 用户账号
     */
    @QueryOp(fieldName = "user_account", operator = QueryOp.QueryOperator.EQUALS)
    private String userAccount;
    /**
     * 用户组id，（企业id，部门id）
     */
    @QueryOp(fieldName = "user_group_id", operator = QueryOp.QueryOperator.EQUALS)
    private String userGroupId;
    /**
     * 用户组名
     */
    @QueryOp(fieldName = "user_group_name", operator = QueryOp.QueryOperator.EQUALS)
    private String userGroupName;

    @QueryOp(fieldName = "request_url", operator = QueryOp.QueryOperator.EQUALS)
    private String requestUrl;

    @QueryOp(fieldName = "request_time", operator = QueryOp.QueryOperator.GREATER_THAN_OR_EQUALS)
    private Date requestTimeStart;

    @QueryOp(fieldName = "request_time", operator = QueryOp.QueryOperator.LESS_THAN_OR_EQUALS)
    private Date requestTimeEnd;

    /**
     * 应用唯一标识
     */
    @QueryOp(fieldName = "app_code", operator = QueryOp.QueryOperator.EQUALS)
    private String appCode;
}
