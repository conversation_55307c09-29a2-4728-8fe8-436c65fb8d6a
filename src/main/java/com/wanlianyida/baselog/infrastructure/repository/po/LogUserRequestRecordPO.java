package com.wanlianyida.baselog.infrastructure.repository.po;

import com.wanlianyida.baselog.infrastructure.annotation.QueryField;
import com.wanlianyida.baselog.infrastructure.annotation.Table;
import com.wanlianyida.baselog.infrastructure.annotation.Tag;
import lombok.Data;

import java.util.Date;

@Data
@Table("log_user_request_record")
public class LogUserRequestRecordPO {

    /**
     * 任务id
     */
    @Tag
    @QueryField(index = 1, fieldName = "biz_id")
    private String bizId;
    /**
     * 任务类型
     */
    @Tag
    @QueryField(index = 2, fieldName = "biz_type")
    private String bizType;
    /**
     * 审核人id
     */
    @Tag
    @QueryField(index = 3, fieldName = "user_base_id")
    private String userBaseId;
    /**
     * 用户编号
     */
    @Tag
    @QueryField(index = 4, fieldName = "user_identify_code")
    private String userIdentifyCode;
    /**
     * 审核人姓名
     */
    @Tag
    @QueryField(index = 5, fieldName = "user_name")
    private String userName;
    /**
     * 审核人账号
     */
    @Tag
    @QueryField(index = 6, fieldName = "user_account")
    private String userAccount;
    /**
     * 审核人部门
     */
    @Tag
    @QueryField(index = 7, fieldName = "user_group_id")
    private String userGroupId;
    /**
     * 审核人部门
     */
    @Tag
    @QueryField(index = 8, fieldName = "user_group_name")
    private String userGroupName;
    /**
     * 审核时间
     */
    @Tag
    @QueryField(index = 9, fieldName = "request_url")
    private String requestUrl;
    /**
     * 请求参数
     */
    @QueryField(index = 10, fieldName = "request_content")
    private String requestContent;
    /**
     * 审核意见
     */
    @Tag
    @QueryField(index = 11, fieldName = "request_ip")
    private String requestIp;
    /**
     * 请求时间
     */
    @QueryField(index = 12, fieldName = "request_time")
    private Date requestTime;

    /**
     * 响应结果
     */
    @QueryField(index = 13, fieldName = "response_content")
    private String responseContent;
    /**
     * 响应时间
     */
    @QueryField(index = 14, fieldName = "response_time")
    private Date responseTime;

    /**
     * 应用唯一标识
     */
    @Tag
    @QueryField(index = 15, fieldName = "app_code")
    private String appCode;

}
