package com.wanlianyida.baselog.infrastructure.repository.po;

import cn.hutool.core.date.DateUtil;
import com.wanlianyida.baselog.infrastructure.annotation.QueryField;
import com.wanlianyida.baselog.infrastructure.annotation.Table;
import com.wanlianyida.baselog.infrastructure.annotation.Tag;
import lombok.Data;

import java.util.Date;

/**
 * 行为记录
 *
 * <AUTHOR>
 * @since 20241209
 */
@Data
@Table("log_behavior_record")
public class LogBehaviorRecordPO {

    /**
     * 链路id
     */
    @Tag
    @QueryField(index = 1, fieldName = "trace_id")
    private String traceId;
    /**
     * 用户id
     */
    @Tag
    @QueryField(index = 2, fieldName = "user_base_id")
    private String userBaseId;
    /**
     * 用户名
     */
    @Tag
    @QueryField(index = 3, fieldName = "user_name")
    private String userName;
    /**
     * 用户账号
     */
    @Tag
    @QueryField(index = 4, fieldName = "user_account")
    private String userAccount;
    /**
     * 用户组id，（企业id，部门id）
     */
    @Tag
    @QueryField(index = 5, fieldName = "user_group_id")
    private String userGroupId;
    /**
     * 用户组名
     */
    @Tag
    @QueryField(index = 6, fieldName = "user_group_name")
    private String userGroupName;
    /**
     * 请求时间
     */
    @QueryField(index = 7, fieldName = "request_time")
    private Date requestTime = DateUtil.date();
    /**
     * 请求类型，GET/POST
     */
    @QueryField(index = 8, fieldName = "request_method")
    private String requestMethod;
    /**
     * 请求ip
     */
    @QueryField(index = 9, fieldName = "request_ip")
    private String requestIp;
    /**
     * 请求url
     */
    @QueryField(index = 10, fieldName = "request_url")
    private String requestUrl;
    /**
     * 请求参数，json格式
     */
    @QueryField(index = 11, fieldName = "request_params")
    private String requestParams;
    /**
     * 请求头，json格式
     */
    @QueryField(index = 12, fieldName = "request_headers")
    private String requestHeaders;
    /**
     * 请求体，json格式
     */
    @QueryField(index = 13, fieldName = "request_body")
    private String requestBody;
    /**
     * 请求状态，0-失败，1-成功
     */
    @QueryField(index = 14, fieldName = "request_status")
    private Integer requestStatus;
    /**
     * 请求耗时，单位ms
     */
    @QueryField(index = 15, fieldName = "request_cost")
    private Long requestCost;
    /**
     * 响应结果，json格式
     */
    @QueryField(index = 16, fieldName = "response_result")
    private String responseResult;
    /**
     * 响应头，json格式
     */
    @QueryField(index = 17, fieldName = "response_headers")
    private String responseHeaders;
    /**
     * 响应体，json格式
     */
    @QueryField(index = 18, fieldName = "response_body")
    private String responseBody;
    /**
     * 响应状态，0-失败，1-成功
     */
    @QueryField(index = 19, fieldName = "response_status")
    private Integer responseStatus;
    /**
     * 响应耗时，单位ms
     */
    @QueryField(index = 20, fieldName = "response_cost")
    private Long responseCost;

}
