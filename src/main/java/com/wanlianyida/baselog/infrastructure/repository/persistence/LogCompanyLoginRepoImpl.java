package com.wanlianyida.baselog.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wanlianyida.baselog.domain.model.entity.LogCompanyLoginEntity;
import com.wanlianyida.baselog.domain.repository.LogCompanyLoginRepo;
import com.wanlianyida.baselog.infrastructure.repository.mapper.LogCompanyLoginMapper;
import com.wanlianyida.baselog.infrastructure.repository.po.LogCompanyLoginPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 企业切换日志Repo实现类
 *
 * <AUTHOR>
 * @date 2025-06-24 10:48:49
 */
@Service
public class LogCompanyLoginRepoImpl extends ServiceImpl<LogCompanyLoginMapper, LogCompanyLoginPO> implements LogCompanyLoginRepo {

    @Resource
    private LogCompanyLoginMapper logCompanyLoginMapper;

    @Override
    public void insert(LogCompanyLoginEntity logCompanyLoginEntity) {
        LogCompanyLoginPO logCompanyLoginPo = BeanUtil.copyProperties(logCompanyLoginEntity, LogCompanyLoginPO.class);
        logCompanyLoginMapper.insert(logCompanyLoginPo);
    }
}
