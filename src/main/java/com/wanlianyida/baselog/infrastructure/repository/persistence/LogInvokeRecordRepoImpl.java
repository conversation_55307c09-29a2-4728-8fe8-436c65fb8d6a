package com.wanlianyida.baselog.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import com.aliyun.lindorm.tsdb.client.LindormTSDBClient;
import com.aliyun.lindorm.tsdb.client.model.Record;
import com.wanlianyida.baselog.domain.model.condition.LogInvokeCondition;
import com.wanlianyida.baselog.domain.model.entity.LogInvokeEntity;
import com.wanlianyida.baselog.domain.repository.LogInvokeRecordRepo;
import com.wanlianyida.baselog.infrastructure.config.LindormClient;
import com.wanlianyida.baselog.infrastructure.repository.mapper.LogRecordMapper;
import com.wanlianyida.baselog.infrastructure.repository.po.LogInvokePO;
import com.wanlianyida.baselog.infrastructure.repository.po.LogInvokeQueryPO;
import com.wanlianyida.baselog.infrastructure.util.LindormRecordConverter;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Component
public class LogInvokeRecordRepoImpl implements LogInvokeRecordRepo {

    @Resource
    private LogRecordMapper logRecordMapper;

    @Override
    public void insert(LogInvokeEntity entity) {
        LogInvokePO logInvokePO = BeanUtil.copyProperties(entity, LogInvokePO.class);
        Record record = LindormRecordConverter.toRecord(logInvokePO);
        LindormTSDBClient client = LindormClient.getClient();
        client.writeSync(Collections.singletonList(record));
    }

    @Override
    public ResultMode<List<LogInvokeEntity>> queryPage(PagingInfo<LogInvokeCondition> condition) {
        LogInvokeCondition filterModel = condition.getFilterModel();
        LogInvokeQueryPO logInvokePO = BeanUtil.copyProperties(filterModel, LogInvokeQueryPO.class);
        List<LogInvokePO> list = logRecordMapper.queryPage(LogInvokePO.class, logInvokePO, condition.getCurrentPage(), condition.getPageLength());

        if (IterUtil.isEmpty(list)) {
            return ResultMode.successPageList(Collections.emptyList(), 0);
        }
        ResultMode<List<LogInvokeEntity>> resultMode = ResultMode.success(BeanUtil.copyToList(list, LogInvokeEntity.class));
        if (condition.getCountTotal()) {
            int count = logRecordMapper.count(LogInvokePO.class, logInvokePO);
            resultMode.setTotal(count);
        }
        return resultMode;
    }
}
