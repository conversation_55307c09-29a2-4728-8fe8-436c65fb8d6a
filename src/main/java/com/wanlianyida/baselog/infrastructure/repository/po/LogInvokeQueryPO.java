package com.wanlianyida.baselog.infrastructure.repository.po;

import com.wanlianyida.baselog.infrastructure.annotation.QueryOp;
import lombok.Data;

import java.io.Serializable;

@Data
public class LogInvokeQueryPO implements Serializable {

    @QueryOp(fieldName = "biz_id", operator = QueryOp.QueryOperator.EQUALS)
    private String bizId;

    @QueryOp(fieldName = "biz_type", operator = QueryOp.QueryOperator.EQUALS)
    private String bizType;


    @QueryOp(fieldName = "face_name", operator = QueryOp.QueryOperator.EQUALS)
    private String faceName;


    @QueryOp(fieldName = "invoke_url", operator = QueryOp.QueryOperator.EQUALS)
    private String invokeUrl;


    @QueryOp(fieldName = "status", operator = QueryOp.QueryOperator.EQUALS)
    private String status;

    @QueryOp(fieldName = "biz_status", operator = QueryOp.QueryOperator.EQUALS)
    private String bizStatus;

    @QueryOp(fieldName = "invoke_param", operator = QueryOp.QueryOperator.EQUALS)
    private String invokeParam;

    @QueryOp(fieldName = "invoke_result", operator = QueryOp.QueryOperator.EQUALS)
    private String invokeResult;

    @QueryOp(fieldName = "invoke_cost_time", operator = QueryOp.QueryOperator.GREATER_THAN_OR_EQUALS)
    private String invokeCostTime;

    @QueryOp(fieldName = "start_time", operator = QueryOp.QueryOperator.GREATER_THAN_OR_EQUALS)
    private String startTime;

    @QueryOp(fieldName = "end_time", operator = QueryOp.QueryOperator.LESS_THAN_OR_EQUALS)
    private String endTime;

    @QueryOp(fieldName = "operator", operator = QueryOp.QueryOperator.EQUALS)
    private String operator;

    @QueryOp(fieldName = "operate_time", operator = QueryOp.QueryOperator.GREATER_THAN_OR_EQUALS)
    private String operateTime;

    @QueryOp(fieldName = "biz_key", operator = QueryOp.QueryOperator.LIKE)
    private String bizKey;
}
