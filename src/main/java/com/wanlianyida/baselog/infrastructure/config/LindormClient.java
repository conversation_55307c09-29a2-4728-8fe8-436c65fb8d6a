package com.wanlianyida.baselog.infrastructure.config;

import cn.hutool.core.lang.Singleton;
import cn.hutool.extra.spring.SpringUtil;
import com.aliyun.lindorm.tsdb.client.ClientOptions;
import com.aliyun.lindorm.tsdb.client.LindormTSDBClient;
import com.aliyun.lindorm.tsdb.client.impl.LindormTSDBClientImpl;

public class LindormClient {
    public static LindormTSDBClient getClient() {
        // 获取Lindorm时序引擎的HTTP连接地址
        String url = SpringUtil.getProperty("datasource.url");
        // 用户名
        String username = SpringUtil.getProperty("datasource.username");
        // 用户密码
        String password = SpringUtil.getProperty("datasource.password");
        // 访问的数据库
        String db = SpringUtil.getProperty("datasource.database");

        // ClientOptions参数的具体含义和默认值，可以参考'配置LindormTSDBClient'章节
        ClientOptions options = ClientOptions
                // 必选
                .newBuilder(url)
                // 若实例未开启鉴权，则可选；否则必选
                .setUsername(username)
                // 若实例未开启鉴权，则可选；否则必选
                .setPassword(password)
                // 可选
                .setDefaultDatabase(db)
                .build();

        // 创建LindormTSDBClient实例，LindormTSDBClient线程安全，可以重复使用，无需频繁创建和销毁
        return Singleton.get(LindormTSDBClientImpl.class, options);
    }

}
