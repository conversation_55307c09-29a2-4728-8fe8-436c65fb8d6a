package com.wanlianyida.baselog.infrastructure.exception;

import lombok.Getter;

/**
 * @Author: Qin
 * @Date: 2024/12/19 13:33
 * @Description:
 **/
@Getter
public enum LogErrorExceptionEnum {
    ERROR_10001("L10001","查询操作异常"),
    ERROR_10002("L10002","查询结果类型转换异常"),

    ;
    private String code;

    private String msg;

    LogErrorExceptionEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
