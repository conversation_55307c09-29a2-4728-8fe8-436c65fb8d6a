package com.wanlianyida.baselog.interfaces.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class LogUserRequestCommand {

    /**
     * 业务对象id，（司机id、车辆id、商品id）
     */
    private String bizId;
    /**
     * 业务对象类型，（司机、车辆、商品）
     */
    private String bizType;
    /**
     * 用户id
     */
    private String userBaseId;
    /**
     * 用户编号
     */
    private String userIdentifyCode;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户组id，（企业id，部门id）
     */
    private String userGroupId;
    /**
     * 用户组名
     */
    private String userGroupName;

    /**
     * 请求路径
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestContent;

    /**
     * 请求ip
     */
    private String requestIp;
    /**
     * 请求时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requestTime;


    /**
     * 相应结果
     */
    private String responseContent;
    /**
     * 响应时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date responseTime;


    /**
     * 应用唯一标识
     */
    private String appCode;

}
