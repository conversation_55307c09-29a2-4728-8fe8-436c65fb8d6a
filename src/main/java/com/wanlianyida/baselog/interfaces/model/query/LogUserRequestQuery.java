package com.wanlianyida.baselog.interfaces.model.query;

import lombok.Data;

@Data
public class LogUserRequestQuery {

    /**
     * 任务id
     */
    private String bizId;
    /**
     * 任务类型
     */
    private String bizType;
    /**
     * 审核人id
     */
    private String userBaseId;
    /**
     * 审核人姓名
     */
    private String userName;
    /**
     * 审核人账号
     */
    private String userAccount;
    /**
     * 审核人部门
     */
    private String userGroupId;
    /**
     * 审核人部门
     */
    private String userGroupName;

    /**
     * 请求路径
     */
    private String requestUrl;


    /**
     * 应用唯一标识
     */
    private String appCode;

}
