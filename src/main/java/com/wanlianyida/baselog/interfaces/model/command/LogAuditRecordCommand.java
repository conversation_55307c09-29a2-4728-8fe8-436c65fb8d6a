package com.wanlianyida.baselog.interfaces.model.command;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wanlianyida.baselog.infrastructure.config.CustomDateDeserializer;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

import java.util.Date;

@Data
public class LogAuditRecordCommand {

    /**
     * 任务id
     */
    @NotBlank(message = "bizId不能为空")
    private String bizId;
    /**
     * 任务类型
     */
    private String bizType;
    /**
     * 审核人id
     */
    private String userBaseId;
    /**
     * 审核人姓名
     */
    private String userName;
    /**
     * 审核人账号
     */
    private String userAccount;
    /**
     * 审核人部门
     */
    private String userGroupId;
    /**
     * 审核人部门
     */
    private String userGroupName;
    /**
     * 审核时间
     */
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date operateTime;
    /**
     * 审核状态
     */
    private String operateStatus;
    /**
     * 审核结论
     */
    private String operateConclusion;
    /**
     * 审核意见
     */
    private String operateContent;
    /**
     * 任务接收时间
     */
    @JsonDeserialize(using = CustomDateDeserializer.class)
    private Date receiveTime;

    /**
     * 业务扩展字段
     */
    private String auditExt;

}
