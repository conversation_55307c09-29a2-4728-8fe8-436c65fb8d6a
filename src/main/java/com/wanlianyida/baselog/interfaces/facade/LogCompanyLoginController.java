package com.wanlianyida.baselog.interfaces.facade;

import com.wanlianyida.baselog.application.service.LogCompanyLoginAppService;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbaselog.api.model.command.LogCompanyLoginCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 企业切换日志Controller
 *
 * <AUTHOR>
 * @date 2025-06-24 10:48:49
 */
@Slf4j
@RestController
@RequestMapping("/company/login")
public class LogCompanyLoginController {

    @Resource
    private LogCompanyLoginAppService logCompanyLoginAppService;

    /**
     * 新增操作日志
     */
    @PostMapping("/add")
    public ResultMode<Void> add(@RequestBody @Validated LogCompanyLoginCommand logCompanyLoginCommand) {
        logCompanyLoginAppService.insert(logCompanyLoginCommand);
        return ResultMode.success();
    }
}
