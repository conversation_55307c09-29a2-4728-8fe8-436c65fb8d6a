package com.wanlianyida.baselog.application.service;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.baselog.domain.model.condition.LogAuditRecordCondition;
import com.wanlianyida.baselog.domain.service.LogAuditRecordDomainService;
import com.wanlianyida.baselog.interfaces.model.command.LogAuditRecordCommand;
import com.wanlianyida.baselog.interfaces.model.dto.LogAuditRecordDTO;
import com.wanlianyida.baselog.interfaces.model.query.LogAuditRecordQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class LogAuditRecordAppService {

    @Resource
    LogAuditRecordDomainService logAuditRecordDomainService;

    public void insert(LogAuditRecordCommand logAuditRecordCommand) {
        logAuditRecordDomainService.insert(logAuditRecordCommand);
    }

    public ResultMode<List<LogAuditRecordDTO>> queryPage(PagingInfo<LogAuditRecordQuery> query) {
        LogAuditRecordCondition logAuditRecordCondition = BeanUtil.copyProperties(query.getFilterModel(), LogAuditRecordCondition.class);

        PagingInfo<LogAuditRecordCondition> conditionPagingInfo = new PagingInfo<>(logAuditRecordCondition, query.getCurrentPage(), query.getPageLength(), query.getCountTotal());
        return logAuditRecordDomainService.queryPage(conditionPagingInfo);
    }
}
