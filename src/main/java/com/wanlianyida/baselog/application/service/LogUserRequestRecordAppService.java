package com.wanlianyida.baselog.application.service;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.baselog.domain.model.condition.LogUserRequestRecordCondition;
import com.wanlianyida.baselog.domain.service.LogUserRequestRecordDomainService;
import com.wanlianyida.baselog.interfaces.model.command.LogUserRequestCommand;
import com.wanlianyida.baselog.interfaces.model.dto.LogUserRequestRecordDTO;
import com.wanlianyida.baselog.interfaces.model.query.LogUserRequestQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class LogUserRequestRecordAppService {

    @Resource
    LogUserRequestRecordDomainService logUserRequestRecordDomainService;

    public void insert(LogUserRequestCommand logAuditRecordCommand) {
        logUserRequestRecordDomainService.insert(logAuditRecordCommand);
    }

    public ResultMode<List<LogUserRequestRecordDTO>> queryPage(PagingInfo<LogUserRequestQuery> query) {
        LogUserRequestRecordCondition requestRecordCondition = BeanUtil.copyProperties(query.getFilterModel(), LogUserRequestRecordCondition.class);

        PagingInfo<LogUserRequestRecordCondition> conditionPagingInfo = new PagingInfo<>(requestRecordCondition, query.getCurrentPage(), query.getPageLength(), query.getCountTotal());
        return logUserRequestRecordDomainService.queryPage(conditionPagingInfo);
    }
}
