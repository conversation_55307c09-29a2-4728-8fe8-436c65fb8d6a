package com.wanlianyida.baselog.application.service;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.baselog.domain.model.condition.LogOperationRecordCondition;
import com.wanlianyida.baselog.domain.service.LogOperationRecordDomainService;
import com.wanlianyida.baselog.interfaces.model.command.LogOperationRecordCommand;
import com.wanlianyida.baselog.interfaces.model.dto.LogOperationRecordDTO;
import com.wanlianyida.baselog.interfaces.model.query.LogOperationRecordQuery;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class LogOperationRecordAppService {

    @Resource
    LogOperationRecordDomainService logOperationRecordDomainService;

    public void insert(LogOperationRecordCommand logOperationRecordCommand) {
        logOperationRecordDomainService.insert(logOperationRecordCommand);
    }

    public ResultMode<List<LogOperationRecordDTO>> queryPage(PagingInfo<LogOperationRecordQuery> query) {
        LogOperationRecordCondition logOperationRecordCondition = BeanUtil.copyProperties(query.getFilterModel(), LogOperationRecordCondition.class);

        PagingInfo<LogOperationRecordCondition> conditionPagingInfo = new PagingInfo<>(logOperationRecordCondition, query.getCurrentPage(), query.getPageLength(), query.getCountTotal());
        return logOperationRecordDomainService.queryPage(conditionPagingInfo);
    }
}
