package com.wanlianyida.baselog.application.service;

import com.wanlianyida.baselog.domain.service.LogCompanyLoginDomainService;
import com.wanlianyida.baselog.domain.service.LogOperationRecordDomainService;
import com.wanlianyida.baselog.interfaces.model.command.LogOperationRecordCommand;
import com.wanlianyida.fssbaselog.api.model.command.LogCompanyLoginCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 企业切换日志AppService
 *
 * <AUTHOR>
 * @date 2025-06-24 10:48:49
 */
@Slf4j
@Service
public class LogCompanyLoginAppService {

    @Resource
    private LogCompanyLoginDomainService logCompanyLoginDomainService;

    @Resource
    private LogOperationRecordDomainService logOperationRecordDomainService;

    public void insert(LogCompanyLoginCommand logCompanyLoginCommand) {
        // 新增lindorm日志
        LogOperationRecordCommand operationRecordCommand = covertLogOperationRecordCommand(logCompanyLoginCommand);
        logOperationRecordDomainService.insert(operationRecordCommand);
        // 新增mysql日志
        logCompanyLoginDomainService.insert(logCompanyLoginCommand);
    }

    /**
     * 实体转换
     *
     * @param logCompanyLoginCommand 企业切换日志command
     * @return LogOperationRecordCommand
     */
    public LogOperationRecordCommand covertLogOperationRecordCommand(LogCompanyLoginCommand logCompanyLoginCommand) {
        LogOperationRecordCommand operationRecordCommand = new LogOperationRecordCommand();
        operationRecordCommand.setBizId(String.valueOf(logCompanyLoginCommand.getCompanyId()));
        operationRecordCommand.setBizType("CTP110");
        operationRecordCommand.setUserBaseId(String.valueOf(logCompanyLoginCommand.getUserId()));
        operationRecordCommand.setUserName(logCompanyLoginCommand.getUserName());
        operationRecordCommand.setUserAccount(logCompanyLoginCommand.getLoginName());
        operationRecordCommand.setOperateType("切换企业");
        operationRecordCommand.setOperateTime(java.sql.Timestamp.valueOf(logCompanyLoginCommand.getCreateTime()));
        operationRecordCommand.setOperateContent(String.format("用户:【%s】登陆了企业【%s】", logCompanyLoginCommand.getUserName(), logCompanyLoginCommand.getCompanyName()));
        return operationRecordCommand;
    }
}
