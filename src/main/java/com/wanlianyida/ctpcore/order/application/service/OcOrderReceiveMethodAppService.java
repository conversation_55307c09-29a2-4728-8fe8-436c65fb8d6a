package com.wanlianyida.ctpcore.order.application.service;

import com.wanlianyida.ctpcore.order.domain.service.OcOrderReceiveMethodDomainService;
import com.wanlianyida.ctpcore.order.interfaces.facade.dto.OrderReceiveMethodDTO;
import com.wanlianyida.ctpcore.order.interfaces.facade.query.OrderReceiveMethodQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * OcOrderReceiveMethodAppService
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@Slf4j
@Service
public class OcOrderReceiveMethodAppService {

    @Resource
    private OcOrderReceiveMethodDomainService ocOrderReceiveMethodDomainService;

    /**
     * 查询订单收货方式列表
     * @param query
     * @return
     */
    public ResultMode<List<OrderReceiveMethodDTO>> queryList(OrderReceiveMethodQuery query) {
        return ocOrderReceiveMethodDomainService.queryList(query);
    }
}
