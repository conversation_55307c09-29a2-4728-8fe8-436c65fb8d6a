package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.OcPOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcPorderAddressEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcPorderBuySellEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcPorderProductEntity;
import lombok.Data;

import java.util.List;

/**
 * 意向订单列表
 */
@Data
public class PurOrderListBO {

   /**
    * 意向单信息
    */
   private OcPOrderInfoEntity purOrderInfo;
   /**
    *  意向单地址信息
    */
   private OcPorderAddressEntity ocPorderAddressEntity;

   /**
    * 意向单商品
    */
   private List<OcPorderProductEntity> productList;

   /**
    * 意向订单买卖信息
    */
   private List<OcPorderBuySellEntity> buySellList;

   /**
    * 订单编号
    */
   private String orderNo;

}
