package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单发货单商品表
 * <AUTHOR>
 * @since 2024/12/10/13:33
 */
@Data
public class OrderShipProductBO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货/提货单号
     */
    private String bizNo;

    /**
     * 订单商品id
     */
    private Long orderProductId;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 发货数量
     */
    private BigDecimal shipmentQuantity;

    /**
     * 收货数量
     */
    private BigDecimal receiceQuantity;

}
