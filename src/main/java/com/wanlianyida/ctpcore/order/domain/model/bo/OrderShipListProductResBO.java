package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 发/提货单查询列表
 */
@Data
public class OrderShipListProductResBO {

    /**
     * 发/提货单号
     */
    private String shipNo;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 购买数量
     */
    private BigDecimal orderQuantity;

    /**
     * 发货-提货数量
     */
    private BigDecimal shipPickupQuantity;

    /**
     * 收货数量
     */
    private BigDecimal receiveQuantity;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * 商品照片
     */
    private String picture;

    /**
     * 计价单位
     */
    private Integer priceUnit;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 数量单位
     */
    private Integer purchaseQuantityUnit;

    /**
     * 总发货数量
     */
    private BigDecimal totalShipmentQuantity;

}
