package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.LogisticsInquiryAddressEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: OcLogisticsPlanBO
 * @description:
 * @author: zhang<PERSON>hen
 * @date: 2025年04月23日
 * @version: 1.0
 */
@Getter
@Setter
public class LogisticsInquiryBO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;

    @ApiModelProperty("询价单号")
    private String inquiryNo;

    @ApiModelProperty("发/提货编号")
    private String shipNo;

    @ApiModelProperty("询价单状态[10-待报价,20-已报价,30-已成交,40-已拒绝,,50-已撤销 60-已过期]]")
    private Integer status;

    @ApiModelProperty("商品照片")
    private String picture;

    @ApiModelProperty("买家卖家类型[10-买家,20-卖家]")
    private Integer sellerBuyerType;

    @ApiModelProperty("询价单类型 [10-手动询价，20-货单询价，30-精准询价]")
    private Integer inquiryType;

    @ApiModelProperty("审批类型 [10-同意，20-拒绝] ")
    private Integer approveType;

    @ApiModelProperty("发货地详细地址")
    private String startAddressName;

    @ApiModelProperty("目的地详细地址")
    private String endAddressName;

    @ApiModelProperty("物流承运方ID")
    private String logisticsTransportId;

    @ApiModelProperty("物流承运方名称")
    private String logisticsTransportName;

    @ApiModelProperty("物流服务商ID")
    private String logisticsProviderId;

    @ApiModelProperty("物流服务商名称")
    private String logisticsProviderName;

    @ApiModelProperty("报价联系人")
    private String quotationLinkName;

    @ApiModelProperty("报价联系人电话")
    private String quotationLinkTel;

    @ApiModelProperty("运费总价")
    private BigDecimal freightFee;

    @ApiModelProperty("报价截止时间")
    private Date offerEndTime;

    @ApiModelProperty("询价结束时间")
    private Date enquiryEndTime;

    @ApiModelProperty("最早发运时间")
    private Date earliestShippingTime;

    @ApiModelProperty("最晚发运时间")
    private Date latestShippingTime;

    @ApiModelProperty("最早到货时间")
    private Date earliestArriveTime;

    @ApiModelProperty("最晚到货时间")
    private Date latestArriveTime;

    @ApiModelProperty("询价联系人")
    private String inquiryLinkName;

    @ApiModelProperty("询价联系人电话")
    private String inquiryLinkTel;

    @ApiModelProperty("商品编号")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品品类")
    private Integer categoryId3;

    @ApiModelProperty("商品品类名值")
    private String categoryName3;

    @ApiModelProperty("货品数量")
    private BigDecimal goodsQty;

    @ApiModelProperty("货值(元)")
    private BigDecimal goodsPrice;

    @ApiModelProperty("货品数量单位")
    private String goodsQtyUnits;

    @ApiModelProperty("货品体积（立方）")
    private BigDecimal goodsVolume;

    @ApiModelProperty("商品总重量")
    private BigDecimal goodsWeight;

    @ApiModelProperty("货品重量单位")
    private String goodsWeightUnits;

    @ApiModelProperty("运价含保险费标志[0-不包含,1-包含]")
    private String includeInsuranceFlag;

    @ApiModelProperty("签单类型[10-无需回单 20-原件返回 30-签单返回]")
    private String receiptType;

    @ApiModelProperty("结算方式[10-现结,20-月结,99-其他]")
    private String settlementType;

    @ApiModelProperty("结算周期[10-月结]")
    private String settlementCycle;

    @ApiModelProperty("指定结算重量类型")
    private String settlementWeightFlag;

    @ApiModelProperty("装车进场费(元)")
    private BigDecimal loadEntryFee;

    @ApiModelProperty("卸车进场费(元)")
    private BigDecimal unloadEntryFee;

    @ApiModelProperty("其他费用")
    private String otherExpenses;

    @ApiModelProperty("磅差")
    private BigDecimal weightDiff;

    @ApiModelProperty("装车时长")
    private BigDecimal loadDuration;

    @ApiModelProperty("卸车时长")
    private BigDecimal unloadDuration;

    @ApiModelProperty("是否标载[0-否,1-是]")
    private Integer standardLoadFlag;

    @ApiModelProperty("收货工作时间")
    private String recvTimeRange;

    @ApiModelProperty("发货工作时间")
    private String sendTimeRange;

    @ApiModelProperty("日均发运量")
    private String dailyAvgShipmentQty;

    @ApiModelProperty("辅助耗材")
    private String auxConsum;

    @ApiModelProperty("车型")
    private String carModel;

    @ApiModelProperty("车长")
    private String carLength;

    @ApiModelProperty("期望运费标识[0-暂无要求,1-单价，2-总价]0-否,1-是]")
    private Integer expectedPriceFlag;

    @ApiModelProperty("期望运费单价")
    private BigDecimal expectedUnitPrice;

    @ApiModelProperty("期望运费总价")
    private BigDecimal expectedTotalPrice;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审批备注")
    private String approveRemark;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("创建时间")
    private Date createdDate;

    @ApiModelProperty("创建用户id")
    private String creatorId;

    @ApiModelProperty("最后更新人id")
    private String updaterId;

    @ApiModelProperty("最后更新时间")
    private Date updatedDate;

    @ApiModelProperty("版本号")
    private Integer versionCode;

    @ApiModelProperty("删除标志[0-正常,1-删除]")
    private Integer delFlag;


    @ApiModelProperty("'装车进场费标识'[0-不包含,1-包含]")
    private String loadEntryFlag;

    @ApiModelProperty("'卸车进场费标识'[0-不包含,1-包含]")
    private String unloadEntryFlag;

    @ApiModelProperty("'其他费用标识'[0-不包含,1-包含]")
    private String otherExpensesFlag;

    @ApiModelProperty("'装车时长标识'[0-不包含,1-包含]")
    private String loadDurationFlag;

    @ApiModelProperty("'卸车时长标识'[0-不包含,1-包含]")
    private String unloadDurationFlag;

    @ApiModelProperty(value = "物流询价单号",hidden = true)
    private String enquiryOrderNo;

    @ApiModelProperty("买方卖方地址集合")
    private List<LogisticsInquiryAddressEntity> ocLogisticsInquiryAddressEntities;
}
