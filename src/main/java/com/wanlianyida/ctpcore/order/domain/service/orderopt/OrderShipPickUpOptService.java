package com.wanlianyida.ctpcore.order.domain.service.orderopt;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.orderopt.OrderStatusOptCommonBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderNodeEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderNodeEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * 创建提货单
 */
@Slf4j
@Service("shipPickUpOpt")
public class OrderShipPickUpOptService extends OrderOptCommonService {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Override
    public Class<OrderStatusOptCommonBO> optParamObj() {
        return OrderStatusOptCommonBO.class;
    }

    /**
     * 数据校验
     */
    @Override
    protected ResultMode checkParam(OrderStatusOptCommonBO bo) {
        return ResultMode.success();
    }

    /**
     * 封装数据
     */
    @Override
    protected void assembleOrderOptData(OrderStatusOptCommonBO bo) {
        OcOrderInfoEntity updateOrderInfo = super.assembleOrderUpdCommon(bo);
        bo.setOrderInfoUpd(updateOrderInfo);

        OcOrderNodeEntity ocOrderNodeQuery = orderStatusRepo.selectOcOrderNode(bo.getOrderNo(), OrderNodeEnum.STATUS_90.getStatus());
        if (ObjUtil.isNull(ocOrderNodeQuery)) {
            //履约节点
            TokenInfo tokenInfo = JwtUtil.getTokenInfo();
            OcOrderNodeEntity ocOrderNodeEntity = new OcOrderNodeEntity();
            ocOrderNodeEntity.setOrderNo(bo.getOrderNo());
            ocOrderNodeEntity.setNodeStatus(OrderNodeEnum.STATUS_90.getStatus());
            ocOrderNodeEntity.setOptTime(Optional.ofNullable(bo.getOperatorDate()).orElse(DateUtil.date()));
            ocOrderNodeEntity.setCreatorId(Opt.ofBlankAble(bo.getOperatorId()).orElse(tokenInfo.getUserBaseId()));
            ocOrderNodeEntity.setUpdaterId(Opt.ofBlankAble(bo.getOperatorId()).orElse(tokenInfo.getUserBaseId()));
            List<OcOrderNodeEntity> ocOrderNodeEntities = new ArrayList<>();
            ocOrderNodeEntities.add(ocOrderNodeEntity);
            bo.setOrderNodeInsert(ocOrderNodeEntities);
        }
    }

}
