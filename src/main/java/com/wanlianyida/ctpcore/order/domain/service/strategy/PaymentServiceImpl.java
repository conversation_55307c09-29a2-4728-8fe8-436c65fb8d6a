package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.order.domain.assembler.OrderStatusAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderPayBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.attach.AttachmentBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.OcOrderPaymentCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.model.message.OrderFinishMessage;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.consts.CtpKafkaConst;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.enums.pay.FinishTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.event.AttachmentEvent;
import com.wanlianyida.ctpcore.order.infrastructure.event.EventPublisher;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.enums.NumberBizTypeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.framework.ctpcore.utils.NumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * 订单状态变更（付款）
 */
@Slf4j
@Service("payment")
public class PaymentServiceImpl extends OrderStatusChangeService{

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private NumberGenerator numberGenerator;

    @Resource
    private EventPublisher eventPublisher;


    @Resource
    private MqEventPublisher mqEventPublisher;

    /**
     * 数据校验
     * @param bo
     * @return
     */
    @Override
    protected ResultMode checkActionCondition(OrderStatusBO bo) {
        //订单状态判断
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        if(!ObjUtil.equal(ocOrderInfo.getPayStatus(),OrderPayStatusEnum.PAY_STATUS_110.getPayStatus())){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_IS_NOT_PAYMENT_STATUS.getMsg());
        }
        OrderPayBO payInfo = bo.getPayInfo();
        if(payInfo.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_AMOUNT_CANNOT_BE_0.getMsg());
        }
        return ResultMode.success();
    }

    /**
     * 封装数据
     * @param bo
     * @return
     */
    @Override
    protected HandelStatusBO packActionData(OrderStatusBO bo) {
        HandelStatusBO handelStatusBO = new HandelStatusBO();
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        //查询订单付款信息
        OcOrderPaymentCondition paymentCondition = new OcOrderPaymentCondition().setRfOrderNo(bo.getOrderNo());
        OcOrderPaymentEntity ocOrderPayment = orderStatusRepo.getOcOrderPayment(paymentCondition);
        //预付
        String smentNo = null;
        if(ObjUtil.equal(ocOrderInfo.getStatus(),OrderStatusEnum.STATUS_20.getStatus())){
            smentNo =  this.processPrepay(bo,handelStatusBO,ocOrderPayment);
        }else {
            //结算付款 1、是否有预付
            smentNo = this.processSment(bo, handelStatusBO);
        }
        List<AttachmentBO> attachments = bo.getPayInfo().getAttachments();
        for (AttachmentBO attachment : attachments) {
            attachment.setBizNo(smentNo).setAttachmentType(AttachmentTypeEnum.TYPE_30.getType()+"");
        }
        handelStatusBO.setAttachment(BeanUtil.copyToList(attachments,AttachmentEntity.class));
        handelStatusBO.setOrderNo(ocOrderInfo.getOrderNo());
        return handelStatusBO;
    }

    /**
     * 入库
     * @param bo
     * @return
     */
    @Override
    protected int handleStatus(HandelStatusBO bo) {
        return orderStatusRepo.handleStatus(bo);
    }

    @Override
    protected void asynHandle(HandelStatusBO bo) {
        super.asynHandle(bo);
        List<AttachmentEntity> attachment = bo.getAttachment();
        eventPublisher.attachmentEvent(BeanUtil.copyToList(attachment, AttachmentEvent.class));
        //付款消息
        OrderFinishMessage orderFinishMessage = new OrderFinishMessage();
        orderFinishMessage.setOrderNo(bo.getOrderNo());
        OcPaymentEntity ocPaymentEntity = bo.getPaymentEntityForInsert();
        orderFinishMessage.setType(FinishTypeEnum.TYPE_10.getType());
        orderFinishMessage.setPaymentDate(ocPaymentEntity.getPaymentDate());
        orderFinishMessage.setOperator(JwtUtil.getTokenInfo().getUsername());
        orderFinishMessage.setOperatorId(JwtUtil.getTokenInfo().getUserBaseId());
        log.info("付款完成消息:{}", JSONUtil.toJsonStr(orderFinishMessage));
        mqEventPublisher.syncSendFifoMessage(MqEventMessage.buildEventMessage(CtpKafkaConst.CTP_ORDER_PAY_FINISH_TOPIC, orderFinishMessage),
                CtpKafkaConst.CTP_ORDER_FINISH_GROUP);
    }

    /**
     * 处理预付
     * @param bo
     * @param handelStatusBO
     * @param ocOrderPayment
     */
    private String processPrepay(OrderStatusBO bo, HandelStatusBO handelStatusBO, OcOrderPaymentEntity ocOrderPayment){
        String orderNo = bo.getOrderNo();
        //构建结算信息
        OcSmentEntity ocSmentEntity = new OcSmentEntity();
        ocSmentEntity.setOrderNo(orderNo);
        ocSmentEntity.setSmentStatus(SmentStatusEnum.STATUS_20.getCode());
        handelStatusBO.setOcSmentEntityForUpdate(ocSmentEntity);

        //构建结算单信息
        String smentNo = numberGenerator.getBizId(NumberBizTypeEnum.SETTLEMENT.getCode());
        OcSmentOrderEntity smentOrderEntity = OrderStatusAssembler.buildOcSmentOrderPrepay(smentNo,bo,ocOrderPayment);
        handelStatusBO.setSmentOrderEntityForInsert(smentOrderEntity);

        //付款信息
        String paymentNo = numberGenerator.getBizId(NumberBizTypeEnum.SERIAL.getCode());
        OcPaymentEntity ocPaymentEntity = OrderStatusAssembler.buildOcPayment(smentNo,paymentNo,bo);
        handelStatusBO.setPaymentEntityForInsert(ocPaymentEntity);
        return smentNo;
    }

    /**
     * 结算信息
     * @param bo
     * @param handelStatusBO
     */
    private String processSment(OrderStatusBO bo, HandelStatusBO handelStatusBO){
        String orderNo = bo.getOrderNo();
        OrderPayBO payInfo = bo.getPayInfo();
        //查询结算单
        OcSmentOrderEntity ocSmentOrderEntity = orderStatusRepo.selectSmentOrder(orderNo, SettlementStatusEnum.STATUS_210.getCode());
        String smentNo = ocSmentOrderEntity.getSmentNo();
        //更新接单单付款状态
        OcSmentOrderEntity updateSmentOrderEntity = new OcSmentOrderEntity();
        updateSmentOrderEntity.setSmentNo(smentNo);
        updateSmentOrderEntity.setSmentStatus(SettlementStatusEnum.STATUS_220.getCode());
        updateSmentOrderEntity.setPayMode(bo.getPayInfo().getPayMode());
        updateSmentOrderEntity.setPaymentAmount(payInfo.getPaymentAmount());
        updateSmentOrderEntity.setPaymentDate(Optional.ofNullable(payInfo.getPaymentDate()).orElse(new Date()));
        handelStatusBO.setSmentOrderEntityForUpdate(updateSmentOrderEntity);
        //构建付款信息
        String paymentNo = numberGenerator.getBizId(NumberBizTypeEnum.SERIAL.getCode());
        OcPaymentEntity ocPaymentEntity = OrderStatusAssembler.buildOcPayment(smentNo,paymentNo,bo);
        handelStatusBO.setPaymentEntityForInsert(ocPaymentEntity);
        return smentNo;
    }

}
