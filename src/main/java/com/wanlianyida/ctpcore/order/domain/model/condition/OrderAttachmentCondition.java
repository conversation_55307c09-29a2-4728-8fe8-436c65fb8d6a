package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单附件查询
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderAttachmentCondition {

    /**
     * 附件类型 AttachmentTypeEnum
     */
    private Integer attachmentType;

    /**
     * 附件类型 AttachmentTypeEnum
     */
    private List<Integer> attachmentTypeList;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 业务单号
     */
    private List<String> bizNoList;

}
