package com.wanlianyida.ctpcore.order.domain.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wanlianyida.ctpcore.order.domain.model.bo.ChangeResellStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderProductDetailBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.*;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderProductSkuAttrEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderProductSkuEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderProductSpuEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcProductResellEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
import com.wanlianyida.ctpcore.order.domain.repository.ResellRepo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
@Slf4j
public class ProductResellDomainService {

    @Resource
    ResellRepo resellRepo;

    @Resource
    OrderRepo orderRepo;

    /**
     * 更改转卖状态
     *
     * @param changeResellStatusBO 包含需要更改状态的项目列表
     */
    public void changeResellStatus(ChangeResellStatusBO changeResellStatusBO) {
        for (ChangeResellStatusBO.ItemBO itemBO : changeResellStatusBO.getItemList()) {
            OcProductResellEntity resellEntity = new OcProductResellEntity();
            resellEntity.setResellStatus(itemBO.getResellStatus());
            resellEntity.setResellDate(new Date());
            resellEntity.setResellSkuCode(itemBO.getResellSkuCode());
            resellRepo.updateResellByOrderNoAndOrderSkuCode(itemBO.getOrderNo(), itemBO.getOrderSkuCode(), resellEntity);
        }
    }

    /**
     * 获取转卖产品列表
     *
     * @param condition 查询条件
     * @return 转售产品实体列表
     */
    public List<OcProductResellEntity> list(ProductResellListCondition condition) {
        return resellRepo.list(condition);
    }

    /**
     * 批量获取订单SKU详情
     *
     * @param condition 查询条件
     * @return 包含订单SKU详情的结果模式
     */
    public ResultMode<List<OrderProductDetailBO>> batchGetOrderSkuDetail(BatchOrderProductCondition condition) {
        log.debug("batchGetOrderSkuDetail#参数 {}", JSON.toJSONString(condition));

        if (CollUtil.isEmpty(condition.getQueryList())) {
            return ResultMode.success(Lists.newArrayList());
        }

        List<String> orderNoList = extractOrderNos(condition.getQueryList());
        List<String> skuCodeList = extractSkuCodes(condition.getQueryList());

        List<OcOrderProductSkuEntity> skuList = fetchFilteredSkuList(orderNoList, skuCodeList);
        if (CollUtil.isEmpty(skuList)) {
            return ResultMode.success(Lists.newArrayList());
        }

        List<OcOrderProductSpuEntity> spuList = fetchSpuList(orderNoList);
        if (CollUtil.isEmpty(spuList)) {
            return ResultMode.success(Lists.newArrayList());
        }

        Map<String, OcOrderProductSpuEntity> spuMap = buildSpuMap(spuList);
        Map<String, OcOrderProductSkuEntity> skuMap = buildSkuMap(skuList);
        List<OcOrderProductSkuAttrEntity> skuAttrList = fetchSkuAttrList(orderNoList);

        List<OrderProductDetailBO> detailList = buildOrderProductDetailList(condition.getQueryList(), skuMap, spuMap, skuAttrList);

        log.debug("batchGetOrderSkuDetail#返回值 {}", JSON.toJSONString(detailList));
        return ResultMode.success(detailList);
    }

    private List<String> extractOrderNos(List<BatchOrderProductCondition.QueryItem> queryList) {
        return queryList.stream().map(BatchOrderProductCondition.QueryItem::getOrderNo).collect(Collectors.toList());
    }

    private List<String> extractSkuCodes(List<BatchOrderProductCondition.QueryItem> queryList) {
        return queryList.stream().map(BatchOrderProductCondition.QueryItem::getOrderSkuCode).collect(Collectors.toList());
    }

    private List<OcOrderProductSkuEntity> fetchFilteredSkuList(List<String> orderNoList, List<String> skuCodeList) {
        List<OcOrderProductSkuEntity> skuList = orderRepo.queryProductSkuCondition(OrderProductSkuCondition.builder().orderNoList(orderNoList).build());
        return skuList.stream().filter(e -> skuCodeList.contains(e.getSkuCode())).collect(Collectors.toList());
    }

    private List<OcOrderProductSpuEntity> fetchSpuList(List<String> orderNoList) {
        return orderRepo.queryProductSpuCondition(OrderProductSpuCondition.builder().orderNoList(orderNoList).build());
    }

    private Map<String, OcOrderProductSpuEntity> buildSpuMap(List<OcOrderProductSpuEntity> spuList) {
        Map<String, OcOrderProductSpuEntity> spuMap = new HashMap<>();
        spuList.forEach(e -> spuMap.putIfAbsent(e.getOrderNo() + e.getSpuCode(), e));
        return spuMap;
    }

    private Map<String, OcOrderProductSkuEntity> buildSkuMap(List<OcOrderProductSkuEntity> skuList) {
        return skuList.stream().collect(Collectors.toMap(e -> e.getOrderNo() + e.getSkuCode(), Function.identity()));
    }

    private List<OcOrderProductSkuAttrEntity> fetchSkuAttrList(List<String> orderNoList) {
        return orderRepo.queryProductSkuAttrCondition(OrderProductSkuAttrCondition.builder().orderNoList(orderNoList).build());
    }

    private List<OrderProductDetailBO> buildOrderProductDetailList(
            List<BatchOrderProductCondition.QueryItem> queryList,
            Map<String, OcOrderProductSkuEntity> skuMap,
            Map<String, OcOrderProductSpuEntity> spuMap,
            List<OcOrderProductSkuAttrEntity> skuAttrList) {

        List<OrderProductDetailBO> detailList = Lists.newArrayList();
        for (BatchOrderProductCondition.QueryItem queryItem : queryList) {
            String key = queryItem.getOrderNo() + queryItem.getOrderSkuCode();
            OcOrderProductSkuEntity skuEntity = skuMap.get(key);
            OcOrderProductSpuEntity spuEntity = spuMap.get(queryItem.getOrderNo() + skuEntity.getSpuCode());
            List<OcOrderProductSkuAttrEntity> attrEntityList = filterAttrEntities(skuAttrList, queryItem);
            detailList.add(buildOrderProductDetailBO(skuEntity, spuEntity, attrEntityList));
        }
        return detailList;
    }

    private List<OcOrderProductSkuAttrEntity> filterAttrEntities(List<OcOrderProductSkuAttrEntity> skuAttrList, BatchOrderProductCondition.QueryItem queryItem) {
        return skuAttrList.stream()
                .filter(e -> Objects.equals(queryItem.getOrderNo(), e.getOrderNo()) && Objects.equals(queryItem.getOrderSkuCode(), e.getSkuCode()))
                .collect(Collectors.toList());
    }

    private OrderProductDetailBO buildOrderProductDetailBO(OcOrderProductSkuEntity skuEntity, OcOrderProductSpuEntity spuEntity, List<OcOrderProductSkuAttrEntity> attrEntityList) {
        return OrderProductDetailBO.builder()
                .skuCode(skuEntity.getSkuCode())
                .orderNo(skuEntity.getOrderNo())
                .spu(spuEntity)
                .sku(skuEntity)
                .attrList(attrEntityList)
                .build();
    }
}
