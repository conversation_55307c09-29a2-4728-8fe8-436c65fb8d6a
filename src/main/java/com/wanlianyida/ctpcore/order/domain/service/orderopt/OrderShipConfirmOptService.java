package com.wanlianyida.ctpcore.order.domain.service.orderopt;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.orderopt.OrderStatusOptCommonBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderNodeEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderPaymentEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipQuantityEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.ctpcore.order.infrastructure.util.OrderBussUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 确认收货\确认提货
 */
@Slf4j
@Service("shipConfirmOpt")
public class OrderShipConfirmOptService extends OrderOptCommonService {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private OrderRepo orderRepo;

    @Override
    public Class<OrderStatusOptCommonBO> optParamObj() {
        return OrderStatusOptCommonBO.class;
    }

    /**
     * 数据校验
     */
    @Override
    protected ResultMode checkParam(OrderStatusOptCommonBO bo) {
        //订单状态校验
        OcOrderInfoEntity ocOrderInfo = bo.getOrderInfo();
        if (StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_31.getOpt())) {
            //确认收货
            if (!ObjUtil.equals(ocOrderInfo.getStatus(), OrderStatusEnum.STATUS_30.getStatus())
                    && !ObjUtil.equals(ocOrderInfo.getStatus(), OrderStatusEnum.STATUS_40.getStatus())) {
                return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_RECEIPT.getCode(), CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_RECEIPT.getMsg());
            }
        } else if (StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_40.getOpt())) {
            //确认提货
            if (!ObjUtil.equals(ocOrderInfo.getStatus(), OrderStatusEnum.STATUS_50.getStatus())){
                return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_PICK_UP.getCode(), CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_PICK_UP.getMsg());
            }
        }
        return ResultMode.success();
    }

    /**
     * 封装数据
     */
    @Override
    protected void assembleOrderOptData(OrderStatusOptCommonBO bo) {
        OcOrderInfoEntity updateOrderInfo = super.assembleOrderUpdCommon(bo);
        log.info("OrderShipConfirmOptService: {}", JSONUtil.toJsonStr(bo));
        bo.setOrderInfoUpd(updateOrderInfo);
        boolean isShip = StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_31.getOpt());
        //发货商品数量信息
        List<OcOrderShipQuantityEntity> quantityEntityList = orderStatusRepo.queryShipQuantityOrderNo(bo.getOrderNo());
        log.info("发货数量信息：{}", JSONUtil.toJsonStr(quantityEntityList));
        Boolean allReceive = OrderBussUtil.validateAllProductReceive(quantityEntityList);
        log.info("OrderShipConfirmOptService: 全部收货flag={}", allReceive);
        if (!allReceive) {
            if (!isShip) {
                //部分提货
                updateOrderInfo.setSubStatus(OrderSubStatusEnum.SUB_STATUS_51.getSubStatus());
                return;
            }

            Boolean allShipFlag = OrderBussUtil.validateAllProductShip(quantityEntityList);
            log.info("全部发完标志={}", allShipFlag);
            if (!allShipFlag) {
                //部分发货
                updateOrderInfo.setSubStatus(OrderSubStatusEnum.SUB_STATUS_31.getSubStatus());
                return;
            }
            //部分收货
            updateOrderInfo.setSubStatus(OrderSubStatusEnum.SUB_STATUS_41.getSubStatus());
            return;
        }

        //履约节点
        OcOrderNodeEntity ocOrderNodeEntity = super.assembleOrderNodeInit(bo);
        ocOrderNodeEntity.setNodeStatus(OrderNodeEnum.STATUS_50.getStatus());
        List<OcOrderNodeEntity> ocOrderNodeEntities = new ArrayList<>();
        ocOrderNodeEntities.add(ocOrderNodeEntity);
        bo.setOrderNodeInsert(ocOrderNodeEntities);

        //全部收货
        OcOrderPaymentEntity payment = orderRepo.queryPaymentByOrderNo(bo.getOrderNo());
        if (!OrderBussUtil.needSettlement(payment)) {
            //无需结算
            super.assembleOrderUpdSuccess(bo);
            return;
        }
        //需要结算
        updateOrderInfo.setStatus(OrderStatusEnum.STATUS_60.getStatus());
        updateOrderInfo.setSubStatus(isShip ? OrderSubStatusEnum.SUB_STATUS_42.getSubStatus() : OrderSubStatusEnum.SUB_STATUS_52.getSubStatus());
        updateOrderInfo.setPayStatus(OrderPayStatusEnum.PAY_STATUS_100.getPayStatus());
        updateOrderInfo.setSettStatus(OrderSettStatusEnum.STATUS_10.getCode());
        log.info("交易状态处理-需要结算: {}", JSONUtil.toJsonStr(updateOrderInfo));
        //订单状态
        super.assembleOrderStatusInit(bo);

    }

}
