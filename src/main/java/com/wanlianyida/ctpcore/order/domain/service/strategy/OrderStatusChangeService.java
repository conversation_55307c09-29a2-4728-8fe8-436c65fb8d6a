package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.ContextBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.OcOrderInfoCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.message.OrderStatusChangeMessage;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.domain.service.InitOrderDomainService;
import com.wanlianyida.ctpcore.order.infrastructure.consts.CtpKafkaConst;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.cache.lock.DistributedLocker;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import static com.wanlianyida.ctpcore.order.infrastructure.consts.CtpRedisConst.SIGN_CONTRACT_LOCK_PREFIX;

/**
 * 订单状态变更 模板
 */
@Slf4j
public abstract class OrderStatusChangeService<B extends HandelStatusBO> {

    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private MqEventPublisher mqEventPublisher;

    @Resource
    private InitOrderDomainService initOrderDomainService;

    /**
     * 订单状态处理
     * @param bo
     * @return
     */
    public ResultMode handleAction(OrderStatusBO bo){
        // 订单相关修改加锁
        String lockKey = SIGN_CONTRACT_LOCK_PREFIX + bo.getOrderNo();
        boolean lock = distributedLocker.tryLock(lockKey, -1, -1, TimeUnit.SECONDS);
        if (!lock) {
            return ResultMode.fail(StrUtil.format(CtpCoreOrderExceptionEnum.ORDER_PROCESSING.getMsg(), bo.getOrderNo()));
        }

        try {
            OcOrderInfoEntity ocOrderInfo = this.getOcOrderInfo(bo);
            if (ObjUtil.isNull(ocOrderInfo)) {
                return ResultMode.fail(StrUtil.format(CtpCoreOrderExceptionEnum.ORDER_NUM_DOES_NOT_EXIST.getMsg(), bo.getOrderNo()));
            }
            bo.setOcOrderInfo(ocOrderInfo);;
            bo.setContextBO(ContextBO.builder().orderInfoEntity(ocOrderInfo).build());

            // 前置校验
            ResultMode<?> checkRes = this.checkActionCondition(bo);
            if (!checkRes.isSucceed()) {
                return checkRes;
            }

            //封装数据
            B b = this.packActionData(bo);

            //保存入库
            this.handleStatus(b);

            //异步处理
            this.asynHandle(b);
        } catch (Exception e) {
            log.error("handleAction#订单状态处理:",e);
            return ResultMode.fail(e.getMessage());
        } finally {
            distributedLocker.unlock(lockKey);
        }
        return ResultMode.success();
    }

    private OcOrderInfoEntity getOcOrderInfo(OrderStatusBO bo) {
        OcOrderInfoCondition condition = new OcOrderInfoCondition();
        condition.setOrderNo(bo.getOrderNo());
        return orderStatusRepo.getOcOrderInfo(condition);
    }

    /**
     * 变更状态前置校验
     * @param bo
     * @return
     */
    protected abstract ResultMode checkActionCondition(OrderStatusBO bo);

    /**
     * 封装数据
     * @param bo
     * @return
     */
    protected abstract B packActionData(OrderStatusBO bo);


    /**
     * 异步处理
     * @param bo
     */
    protected void asynHandle(B bo){
        //订单状态变更
        if(ObjUtil.isNull(bo.getOrderInfoForUpdate())){
            return;
        }
        OrderStatusChangeMessage orderStatusChangeMessage = BeanUtil.toBean(bo.getOrderInfoForUpdate(), OrderStatusChangeMessage.class);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        orderStatusChangeMessage.setOperatorId(tokenInfo.getUserBaseId());
        orderStatusChangeMessage.setOperator(tokenInfo.getUsername());
        log.info("订单状态变更消息:{}", JSONUtil.toJsonStr(orderStatusChangeMessage));
        mqEventPublisher.syncSendFifoMessage(MqEventMessage.buildEventMessage(CtpKafkaConst.CTP_ORDER_STATUS_CHANGE_TOPIC, orderStatusChangeMessage),
                CtpKafkaConst.CTP_ORDER_STATUS_CHANGE_GROUP);
    }

    /**
     * 调用不同repo
     * @param bo
     * @return
     */
    protected abstract int handleStatus(B bo);

}
