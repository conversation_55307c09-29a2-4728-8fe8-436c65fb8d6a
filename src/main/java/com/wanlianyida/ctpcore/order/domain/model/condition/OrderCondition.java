package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.Data;

import java.util.Date;

/**
 * 订单查询
 */
@Data
public class OrderCondition {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 业务来源单号
     */
    private String bizNo;

    /**
     * 订单编号(模糊匹配)
     */
    private String orderNoFuzzy;

    /**
     * 订单状态10-待签约 ,字典ctmsOrderStatus
     */
    private Integer status;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 创建开始日期
     */
    private Date createDateStart;

    /**
     * 创建结束日期
     */
    private Date createDateEnd;


    /**
     * 结算状态[10-未结算20-结算中30-已结算40-无需结算]
     */
    private Integer settStatus;

    /**
     * 预付款状态[10-待付款20-待收款30-付款成功40付款失败]
     */
    private Integer prepaidStatus;

    /**
     * 尾款状态[10-待付款20-待收款30-付款成功40付款失败]
     */
    private Integer balancePaymentStatus;

    /**
     * 卖家公司名称（模糊）
     */
    private String sellerCompanyName;

    /**
     * 买家公司名称（模糊）
     */
    private String buyerCompanyName;

    /**
     * 卖家店铺名称（模糊）
     */
    private String sellerShopName;

}
