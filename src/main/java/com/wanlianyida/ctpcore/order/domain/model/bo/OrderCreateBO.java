package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 创建订单 BO
 */
@Slf4j
@Data
public class OrderCreateBO implements Serializable {
    private static final long serialVersionUID = -8783892834022377191L;

    /**
     * 直接创建、意向单、竞价单、投标单
     */
    private Integer orderSource;

    /**
     * 意向订单号
     */
    private String bizSourceCode;

    /**
     * 买家信息
     */
    private CompanyEntity buyer;

    /**
     * 卖家家信息
     */
    private CompanyEntity seller;

    /**
     * 商品信息
     */
    private List<OcOrderProductEntity> goodsList;

    /**
     * 交付信息
     */
    private OcOrderDeliveryEntity delivery;

    /**
     * 付款信息
     */
    private OcOrderPaymentEntity payment;

    /**
     * 收款方式信息
     */
    private List<OcOrderReceiveMethodEntity> receiveMethod;


    /**
     * 商品信息
     */
    private List<CtpProductSkuDetailBO> productSkuDetails;



    /**
     * 平台渠道 10:PC端 20:小程序
     */
    private Integer platformChannel;

    /**
     * 买家操作人id
     */
    private String buyerOperatorId;


    /**
     * 订单备注
     */
    private String orderRemark;

}
