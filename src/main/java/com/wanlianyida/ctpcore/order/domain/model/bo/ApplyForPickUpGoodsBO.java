package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderPickupCarEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipProductEntity;
import lombok.Data;

import java.util.List;

/**
 * 申请提货
 * <AUTHOR>
 * @since 2024/12/10/15:38
 */
@Data
public class ApplyForPickUpGoodsBO extends HandelStatusBO {

    public OcOrderShipEntity ocOrderShipEntity;

    public List<OcOrderPickupCarEntity> ocOrderPickupCarEntityList;

    public List<OcOrderShipProductEntity> ocOrderShipProductEntityList;
}
