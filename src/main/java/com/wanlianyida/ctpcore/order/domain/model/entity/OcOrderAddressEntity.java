package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class OcOrderAddressEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long id;

    /**
     * 类型 10地址 20仓库
     */
    private Integer type;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 联系人
     */
    private String linkName;

    /**
     * 联系人电话
     */
    private String linkTelephone;

    /**
     * 省名称
     */
    private String province;

    /**
     * 省code
     */
    private String provinceCode;

    /**
     * 市名称
     */
    private String city;

    /**
     * 市code
     */
    private String cityCode;

    /**
     * 区名称
     */
    private String area;

    /**
     * 区code
     */
    private String areaCode;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

}
