package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 发/提货单查询列表
 */
@Data
public class OrderShipListDetailResBO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货单号
     */
    private String shipNo;

    /**
     * 发货状态 10未发货 20部分发货 30已发货
     */
    private Integer shipStatus;

    /**
     * 收货状态 10未收货 20部分收货 30已收货
     */
    private Integer receiveStatus;

    /**
     * 物流公司
     */
    private String logistCompany;

    /**
     * 物流单号
     */
    private String logistNumber;
    /**
     * 收货人
     */
    private String receiveName;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 发货方式[10-企业自有车辆，20-物流公司承运]
     */
    private Integer shipType;

    /**
     * 综合状态
     */
    private Integer summaryStatus;

    /**
     * 指定提货人状态[100-已取消,110-待指定,120-已指定,130-无需指定]
     */
    private Integer pickupAssigneeStatus;

    /**
     * 买家名称
     */
    private String buyerCompanyName;

    /**
     * 卖家名称
     */
    private String sellerCompanyName;

    /**
     * 订单联系人
     */
    private String orderLinkName;

    /**
     * 订单联系人电话
     */
    private String orderLinkTelephone;

    /**
     * 询价状态[10-待报价,20-已报价,30-已成交,40-已拒绝,50-已撤销]
     */
    private Integer logisticsInquiryStatus;

    /**
     * 发货单列表详情
     */
    List<OrderShipListProductResBO> detailList;

}
