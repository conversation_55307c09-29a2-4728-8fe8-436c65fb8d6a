package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 付款与结算信息
 */
@Data
public class OrderPaymentInfoDetailBO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 结算状态
     */
    private Integer payStatus;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 商品总金额
     */
    private BigDecimal productAmount;

    /**
     * 交货方式10:自提20:物流配送
     */
    private Integer deliveryMode;

    /**
     * 运费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 其他费用
     */
    private BigDecimal otherAmount;

    /**
     * 已付款金额
     */
    private BigDecimal paidAmount;

    /**
     * 未付款金额
     */
    private BigDecimal unpaidAmount;

    /**
     * 订单结算
     */
    private OcSmentEntity sment;

    /**
     * 支付明细
     */
    private List<OcSmentOrderEntity> paidSmentOrderList;

    /**
     * 订单商品
     */
    private List<OcOrderProductEntity> productList;

    /**
     * 结算凭证
     */
    private List<OcOrderAttachmentEntity> attachmentList;

    /**
     * 是否需要结算标志
     */
    private Integer needSettlement;

}
