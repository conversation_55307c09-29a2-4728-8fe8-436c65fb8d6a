package com.wanlianyida.ctpcore.order.domain.service.strategy;

import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderCancelBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderCancelEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.domain.repository.attachment.OrderStatusChangeAssembler;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 订单取消
 */
@Slf4j
@Service("cancel")
public class CancelServiceImpl extends OrderStatusChangeService {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    /**
     * 订单取消校验
     * @param bo
     * @return
     */
    @Override
    protected ResultMode checkActionCondition(OrderStatusBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        Integer status = ocOrderInfo.getStatus();
        if(!OrderStatusEnum.canCancel(status)){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_20001.getCode(), CtpCoreOrderExceptionEnum.ERROR_20001.getMsg());
        }
        return ResultMode.success();
    }

    /**
     * 构建数据
     * @param bo
     * @return
     */
    @Override
    protected HandelStatusBO packActionData(OrderStatusBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        OrderCancelBO orderCancel = bo.getOrderCancel();
        String orderNo = ocOrderInfo.getOrderNo();
        HandelStatusBO handelStatusBO = new HandelStatusBO();

        OcOrderInfoEntity updateOrderInfo = new OcOrderInfoEntity();
        updateOrderInfo.setOrderNo(orderNo);
        updateOrderInfo.setStatus(OrderStatusEnum.STATUS_100.getStatus());
        handelStatusBO.setOrderInfoForUpdate(updateOrderInfo);

        OcOrderCancelEntity ocOrderCancelEntity = OrderStatusChangeAssembler.buildOcOrderCancel(orderNo,orderCancel);
        handelStatusBO.setOrderCancelEntityForInsert(ocOrderCancelEntity);
        return handelStatusBO;
    }

    /**
     * 处理
     * @param bo
     * @return
     */
    @Override
    protected int handleStatus(HandelStatusBO bo) {
        return orderStatusRepo.handleStatus(bo);
    }
}
