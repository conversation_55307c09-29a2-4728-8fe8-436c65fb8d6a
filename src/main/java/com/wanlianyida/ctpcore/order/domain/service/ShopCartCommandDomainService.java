package com.wanlianyida.ctpcore.order.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Functions;
import com.wanlianyida.ctpcore.order.domain.model.bo.AddShopCartBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.DelShopCartBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.DelShopCartNumBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.ShopCartEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.ShopProductEntity;
import com.wanlianyida.ctpcore.order.infrastructure.consts.CtpRedisConst;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.cache.lock.RedissonDistributedLocker;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 购物车操作聚合成
 */
@Slf4j
@Component
public class ShopCartCommandDomainService {

    @Resource
    private RedisService redisService;

    @Resource
    private RedissonDistributedLocker redissonDistributedLocker;

    /**
     * 添加购物车
     * @param shopCartBOS
     */
    public ResultMode addShopCart(AddShopCartBO shopCartBOS) {
        log.info("addShopCart#添加购物车请求参数:{}",JSONUtil.toJsonStr(shopCartBOS));
        String userId = JwtUtil.getTokenInfo().getUserBaseId();
        if(StrUtil.isBlank(userId)){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_USER_INFO.getMsg());
        }
        String shopCarLockKey = CtpRedisConst.SHOP_CAR_LOCK_PREFIX+ userId;
        boolean lock = redissonDistributedLocker.tryLock(shopCarLockKey, 1, 1,TimeUnit.SECONDS);
        if(!lock){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ADDING_CART.getMsg());
        }
        try {
            String companyId = shopCartBOS.getCompanyId();
            ShopCartEntity shopCart = shopCartBOS.getShopCart();
            String shopCarKey = CtpRedisConst.SHOP_CAR_PREFIX+ userId;
            String currentShopCarRedis = redisService.hGet(shopCarKey, companyId);
            if(StrUtil.isBlank(currentShopCarRedis)){
                //新增购物车
                redisService.hSet(shopCarKey,companyId, JSONUtil.toJsonStr(Arrays.asList(shopCart)));
            }else {
                //新增购物车
                List<ShopCartEntity> updateShopCarts = JSONUtil.toList(currentShopCarRedis, ShopCartEntity.class);
                if(CollUtil.isEmpty(updateShopCarts)) {
                    return ResultMode.fail(CtpCoreOrderExceptionEnum.FAILED_TO_UPDATE_SHOPPING_CART.getMsg());
                }
                Map<String, ShopCartEntity> groupBySku = updateShopCarts.stream().collect(Collectors.toMap(ShopCartEntity::getSkuCode, Functions.identity()));
                //判断是否是新品
                if(groupBySku.containsKey(shopCart.getSkuCode())) {
                    //老品
                    ShopCartEntity shopCartSuk = groupBySku.get(shopCart.getSkuCode());
                    shopCartSuk.setPurchaseQuantity(shopCartSuk.getPurchaseQuantity().add(shopCart.getPurchaseQuantity()));
                }else {
                    //新品
                    updateShopCarts.add(shopCart);
                }
                redisService.hSet(shopCarKey,companyId, JSONUtil.toJsonStr(updateShopCarts));
            }
        }catch (Exception e){
            log.error("addShopCart#添加购物车异常:",e);
            throw e;
        }finally {
            redissonDistributedLocker.unlock(shopCarLockKey);
        }
        return ResultMode.success();
    }

    /**
     * 删除购物车
     * @param shopCartBOS
     */
    public ResultMode delShopCart(List<DelShopCartBO> shopCartBOS) {
        log.info("delShopCart#删除购物车请求参数:{}",JSONUtil.toJsonStr(shopCartBOS));
        String userId = JwtUtil.getTokenInfo().getUserBaseId();
        String redisLockKey = CtpRedisConst.SHOP_CAR_LOCK_PREFIX + userId;
        boolean lock = redissonDistributedLocker.tryLock(redisLockKey, 1, TimeUnit.SECONDS);
        if(!lock){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.DETETING_CART.getMsg());
        }
        try {
            String redisShopCarKey = CtpRedisConst.SHOP_CAR_PREFIX + userId;
            for (DelShopCartBO shopCartBO : shopCartBOS) {
                String companyId = shopCartBO.getCompanyId();
                String currentShopCarRedis = redisService.hGet(redisShopCarKey, companyId);
                if(!StrUtil.isBlank(currentShopCarRedis)){
                    List<ShopProductEntity> shopProductEntities = JSONUtil.toList(currentShopCarRedis, ShopProductEntity.class);
                    List<ShopProductEntity> shopProducts = shopCartBO.getShopProducts();
                    //删除的商品
                    List<String> sukCodes = shopProducts.stream().map(sp -> sp.getSkuCode()).collect(Collectors.toList());
                    //从购物车查到要删除的商品
                    List<ShopProductEntity> cacheData = shopProductEntities.stream().filter(sp -> !sukCodes.contains(sp.getSkuCode())).collect(Collectors.toList());
                    if(!CollUtil.isEmpty(cacheData)){
                        redisService.hSet(redisShopCarKey,companyId,JSONUtil.toJsonStr(cacheData));
                    }else {
                        redisService.hDel(redisShopCarKey,companyId);
                    }
                }
            }
        }catch (Exception e){
            log.error("delShopCart#删除购物车异常:",e);
        }finally {
            redissonDistributedLocker.unlock(redisLockKey);
        }
        return ResultMode.success();
    }

    /**
     * 删除购物车数量
     * @param bo
     * @return
     */
    public ResultMode delShopCartNum(DelShopCartNumBO bo) {
        log.info("delShopCartNum#删除购物车数量请求参数:{}",JSONUtil.toJsonStr(bo));
        String userId = JwtUtil.getTokenInfo().getUserBaseId();
        String companyId = bo.getCompanyId();
        String skuCode = bo.getSkuCode();
        BigDecimal delQuantity = bo.getDelQuantity();
        String redisLockKey = CtpRedisConst.SHOP_CAR_NUM_DEL_PREFIX + userId;
        boolean lock = redissonDistributedLocker.tryLock(redisLockKey, 1, TimeUnit.SECONDS);
        if(!lock){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.DETETING_CART_NUM.getMsg());
        }
        try {
            String redisShopCarKey = CtpRedisConst.SHOP_CAR_PREFIX + userId;
            String currentShopCarRedis = redisService.hGet(redisShopCarKey, companyId);
            if(StrUtil.isBlank(currentShopCarRedis)){
                return ResultMode.success();
            }
            List<ShopProductEntity> shopProductEntities = JSONUtil.toList(currentShopCarRedis, ShopProductEntity.class);
            if(CollUtil.isEmpty(shopProductEntities)){
                return ResultMode.success();
            }
            Optional<ShopProductEntity> first = shopProductEntities.stream().filter(sp -> StrUtil.equals(sp.getSkuCode(), skuCode)).findFirst();
            if(first.isPresent()){
                ShopProductEntity shopProductEntity = first.get();
                shopProductEntity.setPurchaseQuantity(shopProductEntity.getPurchaseQuantity().subtract(delQuantity));
                List<ShopProductEntity> shopCars = shopProductEntities.stream().filter(isp -> ObjUtil.isNotNull(isp.getPurchaseQuantity()) && isp.getPurchaseQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if(CollUtil.isEmpty(shopCars)){
                    redisService.hDel(redisShopCarKey,companyId);
                }else {
                    redisService.hSet(redisShopCarKey,companyId,JSONUtil.toJsonStr(shopCars));
                }
            }
        }catch (Exception e){
            log.error("delShopCart#删除购物车数量异常:",e);
        }finally {
            redissonDistributedLocker.unlock(redisLockKey);
        }
        return ResultMode.success();
    }
}
