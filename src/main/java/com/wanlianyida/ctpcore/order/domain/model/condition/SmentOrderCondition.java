package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单付款明细查询
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmentOrderCondition {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 结算单号
     */
    private String settlementNo;

    /**
     * 结算状态:200待结算 210待付款 220待收款 230已付款
     */
    private Integer smentStatus;

    /**
     * 结算状态:200待结算 210待付款 220待收款 230已付款
     */
    private List<Integer> smentStatusList;

}
