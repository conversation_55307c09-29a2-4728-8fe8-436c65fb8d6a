package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 意向单地址信息
 */
@Data
public class OcPorderAddressEntity implements Serializable {
    private static final long serialVersionUID = -2566500271023016203L;

    /**
     * 意向单号
     */
    private String rfPorderNo;

    /**
     * 联系人
     */
    private String linkName;

    /**
     * 联系人电话
     */
    private String linkTelephone;

    /**
     * 省-
     */
    private String province;

    /**
     * 省-code
     */
    private String provinceCode;


    /**
     * 市
     */
    private String city;

    /**
     * 市-code
     */
    private String cityCode;


    /**
     * 区
     */
    private String area;

    /**
     * 区-code
     */
    private String areaCode;


    /**
     * 地址
     */
    private String detailAddress;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;


}
