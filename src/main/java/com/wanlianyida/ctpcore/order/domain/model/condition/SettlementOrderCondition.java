package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * 订单列表查询
 */
@Data
public class SettlementOrderCondition {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单编号(模糊匹配)
     */
    private String orderNoFuzzy;

    /**
     * 结算状态[10-未结算20-结算中30-已结算40-无需结算]
     */
    private Integer settStatus;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 买家公司id
     */
    @NotEmpty(message = "买家公司不能为空")
    private String buyerCompanyId;



    /**
     * 订单编号集合
     */
    private List<String> orderNoList;

}
