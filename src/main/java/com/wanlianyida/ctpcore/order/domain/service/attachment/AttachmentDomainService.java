package com.wanlianyida.ctpcore.order.domain.service.attachment;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.attach.AttachmentBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderAttachmentEntity;
import com.wanlianyida.ctpcore.order.domain.repository.attachment.AttachmentRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * 附件领域层
 */
@Slf4j
@Component
public class AttachmentDomainService {

    @Resource
    private AttachmentRepo attachmentRepo;

    /**
     * 处理附件事件
     * @param attachments
     */
    public void handleAttachment(List<AttachmentBO> attachments) {
        attachments.forEach(at ->{
            at.setAttachmentUrl(StrUtil.split(at.getAttachmentUrl(),"?").get(0));
        });
        attachmentRepo.insertAttachment(BeanUtil.copyToList(attachments, OcOrderAttachmentEntity.class));
    }
}
