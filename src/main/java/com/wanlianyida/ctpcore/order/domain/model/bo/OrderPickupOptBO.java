package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderAddressEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderPickupNoticeEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderProductEntity;
import lombok.Data;

import java.util.List;

/**
 * 买家申请提货
 */
@Data
public class OrderPickupOptBO {

    /**
     * 订单地址
     */
    private OcOrderAddressEntity address;

    /**
     * 通知提货信息
     */
    private OcOrderPickupNoticeEntity pickupNotice;

    /**
     * 可提货商品
     */
    private List<OcOrderProductEntity> productList;

}
