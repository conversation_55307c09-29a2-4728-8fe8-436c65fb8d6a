package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单履约节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Getter
@Setter
public class OcOrderNodeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 节点状态[枚举ORDER_STATUS_OPT_ENUM]
     */
    private Integer nodeStatus;

    /**
     * 操作时间
     */
    private Date optTime;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;


}
