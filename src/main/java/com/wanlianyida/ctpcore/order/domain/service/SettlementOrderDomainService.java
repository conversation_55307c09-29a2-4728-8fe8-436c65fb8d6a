package com.wanlianyida.ctpcore.order.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.order.domain.model.bo.OcSmentBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.SettlementOrderBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.SettlementOrderShipProductBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.OrderCondition;
import com.wanlianyida.ctpcore.order.domain.model.condition.SettlementOrderCondition;
import com.wanlianyida.ctpcore.order.domain.model.condition.SettlementOrderShipProductCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.repository.SettlementOrderRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderSettStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Component
public class SettlementOrderDomainService {

    @Resource
    private SettlementOrderRepo settlementOrderRepo;

    @Resource
    private UploadExchangeService uploadExchangeService;

    public PageInfo<SettlementOrderBO> pageList(PagingInfo<SettlementOrderCondition> pagingInfo) {
        OrderCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), OrderCondition.class);
        condition.setStatus(OrderStatusEnum.STATUS_60.getStatus());
        condition.setSettStatus(OrderSettStatusEnum.STATUS_10.getCode());
        Page<Object> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, true);
        PageHelper.orderBy("created_date desc, id desc");
        if (condition.getOrderNo() != null) {
            OcOrderInfoEntity ocOrderInfoEntity = settlementOrderRepo.getOcOrderInfo(condition.getOrderNo());
            if (!ocOrderInfoEntity.getSettStatus().equals(OrderSettStatusEnum.STATUS_10.getCode()))
                throw new RuntimeException("订单状态不是待结算，不能发起结算");
        }
        List<SettlementOrderBO> boList = settlementOrderRepo.queryList(condition);
        if (IterUtil.isEmpty(boList)) {
            return null;
        }
        //url转换
        boList.forEach(node -> {
            List<String> attachmentUrl = node.getAttachmentUrl();
            if (CollectionUtil.isNotEmpty(attachmentUrl)) {
                Map<String, String> stringStringMap = uploadExchangeService.convertUrls(attachmentUrl);
                node.setAttachmentUrl(stringStringMap.values().stream().collect(Collectors.toList()));
            }

        });
        PageInfo<SettlementOrderBO> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(boList);
        return pageInfo;
    }


    public List<SettlementOrderBO> settlementOrderDetailList(SettlementOrderCondition condition) {
        OrderCondition orderCondition = BeanUtil.toBean(condition, OrderCondition.class);
        return settlementOrderRepo.queryList(orderCondition);
    }


    public List<SettlementOrderShipProductBO> queryOrderSendOrPickUpProductList(SettlementOrderShipProductCondition condition) {
        return settlementOrderRepo.queryOrderSendOrPickUpProductList(condition);
    }


    public List<OcSmentBO> queryOldSettlementDetailList(SettlementOrderCondition condition) {
        return settlementOrderRepo.queryOldSettlementDetailList(condition);
    }
}
