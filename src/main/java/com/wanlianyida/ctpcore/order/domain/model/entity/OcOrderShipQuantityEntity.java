package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单发货数量表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OcOrderShipQuantityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单商品id
     */
    private Long orderProductId;

    /**
     * 订单数量
     */
    private BigDecimal orderQuantity;

    /**
     * 发货/提货数量
     */
    private BigDecimal shipPickupQuantity;

    /**
     * 收货数量
     */
    private BigDecimal receiveQuantity;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 总发货数量
     */
    private BigDecimal totalShipmentQuantity;

    /**
     * 实际收货数量
     */
    private BigDecimal actualReceiveQuantity;
}
