//package com.wanlianyida.ctpcore.order.domain.service.strategy.ship;
//
//import cn.hutool.core.util.ObjUtil;
//import com.google.common.collect.Lists;
//import com.wanlianyida.ctpcore.order.domain.assembler.OrderWaybillAssembler;
//import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
//import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
//import com.wanlianyida.ctpcore.order.domain.model.condition.OrderShipCondition;
//import com.wanlianyida.ctpcore.order.domain.model.entity.*;
//import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
//import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
//import com.wanlianyida.ctpcore.order.domain.service.strategy.OrderStatusChangeService;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipStatusEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipSummaryStatusEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
//import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
//import groovy.util.logging.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * 物流信息维护
// * <AUTHOR>
// * @since 2025/4/22
// */
//@Slf4j
//@Service("setLogisticsService")
//public class SetLogisticsServiceImpl extends OrderStatusChangeService {
//
//    @Resource
//    OrderStatusRepo orderStatusRepo;
//    @Resource
//    OrderRepo orderRepo;
//
//    @Override
//    protected ResultMode<?> checkActionCondition(OrderStatusBO bo) {
//        if (!OrderActionEnum.ACTION_230.getAction().equals(bo.getAction())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_NO_SEND_OUT_GOODS.getMsg());
//        }
//        OcOrderInfoEntity orderInfo = bo.getContextBO().getOrderInfoEntity();
//        // 校验交货方式！=物流运输，因为只有物流运输的订单才能手动发货
//        if (orderInfo.getDeliveryMode().equals(DeliveryModeEnum.TYPE_20.getType())){
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_TYPE_NOT_SEND_OUT.getMsg());
//        }
//        // 只有订单主状态为待发货时，才能手动发货
//        if (!orderInfo.getStatus().equals(OrderStatusEnum.STATUS_30.getStatus())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_SEND_OUT.getMsg());
//        }
//        List<OcOrderShipEntity> shipEntityList = orderRepo.queryOrderShipByCondition(OrderShipCondition.builder().shipmentNo(bo.getOrderShipNo()).build());
//        if (ObjUtil.isEmpty(shipEntityList)) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_SHIP_IS_NULL.getMsg());
//        }
//        bo.getContextBO().setOrderShipEntity(shipEntityList.get(0));
//        // 校验发货单状态，必须是待发货状态
//        if (!shipEntityList.get(0).getShipStatus().equals(ShipShipStatusEnum.STATUS_10.getCode())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_SHIP_STATUS_NOT_SEND_OUT.getMsg());
//        }
//        return ResultMode.success();
//    }
//
//    @Override
//    protected HandelStatusBO packActionData(OrderStatusBO bo) {
//        HandelStatusBO statusBO = new HandelStatusBO();
//        // 保存运单
//        List<OcOrderPickupCarEntity> wayBillList = OrderWaybillAssembler.buildManualShipList(bo.getContextBO(), bo.getPickUpCar());
//        statusBO.setPickUpCarListForInsert(wayBillList);
//
//        // 更新发货单
//        OcOrderShipEntity orderShipEntity = new OcOrderShipEntity();
//        orderShipEntity.setId(bo.getContextBO().getOrderShipEntity().getId());
//        // 发货方式
//        orderShipEntity.setShipType(bo.getOcOrderShip().getShipType());
//        // 已发货
//        orderShipEntity.setShipStatus(ShipShipStatusEnum.STATUS_20.getCode());
//        // 综合状态
//        orderShipEntity.setSummaryStatus(ShipSummaryStatusEnum.STATUS_40.getCode());
//        // 物流信息
//        if (ObjUtil.isNotEmpty(bo.getOcOrderShip().getLogistCompany())) {
//            orderShipEntity.setLogistCompany(bo.getOcOrderShip().getLogistCompany());
//        }
//        if (ObjUtil.isNotEmpty(bo.getOcOrderShip().getLogistNumber())) {
//            orderShipEntity.setLogistNumber(bo.getOcOrderShip().getLogistNumber());
//        }
//        if (ObjUtil.isNotEmpty(bo.getOcOrderShip().getLogistCompanyId())) {
//            orderShipEntity.setLogistCompanyId(bo.getOcOrderShip().getLogistCompanyId());
//        }
//        orderShipEntity.setUpdaterId(bo.getContextBO().getOperatorId());
//        statusBO.setOcOrderShipForUpdate(Lists.newArrayList(orderShipEntity));
//
//        // 更新发货单商品数量
//
//        return statusBO;
//    }
//
//    @Override
//    protected int handleStatus(HandelStatusBO bo) {
//        return orderStatusRepo.handleStatus(bo);
//    }
//
//}
