package com.wanlianyida.ctpcore.order.domain.service.ship.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.ctpcore.order.domain.model.bo.ShipDataBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.repository.LogisticsInquiryRepo;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.domain.service.ship.handler.support.ShipHandlerSupportService;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.*;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.ctpcore.order.infrastructure.factory.factory.ShipProcessor;
import com.wanlianyida.ctpcore.order.interfaces.facade.command.PickUpCarCommand;
import com.wanlianyida.ctpcore.order.interfaces.facade.command.ship.SetPickupPersonCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 指定提货人
 */
@Service
@Slf4j
public class SetPickUpPersonHandler implements ShipProcessor<SetPickupPersonCommand> {

    @Resource
    ShipHandlerSupportService shipHandlerSupportService;

    @Resource
    OrderStatusRepo orderStatusRepo;

    @Resource
    LogisticsInquiryRepo logisticsInquiryRepo;

    @Override
    public ResultMode<ShipDataBO> handleData(SetPickupPersonCommand command) {
        /**
         * 指定提货人
         */
        ContextBO context = new ContextBO();
        context.setCommand(command);
        // 校验
        ResultMode<?> validation = validation(context);
        if (!validation.isSucceed()) {
            return ResultMode.fail(validation.getMessage());
        }
        OcOrderInfoEntity orderInfo = context.getOrderInfo();
        // 查询发货商品数量信息
        List<OcOrderShipQuantityEntity> quantityEntityList = orderStatusRepo.queryShipQuantityOrderNo(orderInfo.getOrderNo());
        List<OcOrderShipProductEntity> shipProductEntityList = shipHandlerSupportService.queryOrderShipProductEntityList(orderInfo.getOrderNo(), command.getPickUpNo());
        ShipDataBO shipDataBO = ShipDataBO.builder()
                .orderNo(context.getOrderInfo().getOrderNo())
                .ocOrderShipQuantityEntityListForUpdate(buildUpdateQty(quantityEntityList, shipProductEntityList))
                .ocOrderShipProductEntityListForUpdate(buildUpdate(shipProductEntityList))
                .ocOrderShipEntityForUpdate(buildShipUpdate(context))
                .ocOrderPickupCarEntityListForInsert(buildWaybill(context))
                .build();

        log.info("发货单维护物流信息：单号={}", context.getOrderShipEntity().getShipNo());
        log.info("bo={}", JSONUtil.toJsonStr(shipDataBO));
        return ResultMode.success(shipDataBO);
    }

//    private OcOrderInfoEntity buildOrderUpdate(ContextBO context) {
//        OcOrderInfoEntity update = new OcOrderInfoEntity();
//        update.setStatus(OrderStatusEnum.STATUS_50.getStatus());
//        update.setSubStatus(OrderSubStatusEnum.SUB_STATUS_40.getSubStatus());
//        update.setId(context.getOrderInfo().getId());
//        return update;
//    }

    /**
     * 构建运单信息
     */
    private List<OcOrderPickupCarEntity> buildWaybill(ContextBO context) {
        SetPickupPersonCommand command = context.getCommand();
        List<OcOrderPickupCarEntity> waybillList = Lists.newArrayList();
        if (CollUtil.isEmpty(command.getPickUpCar())) {
            return waybillList;
        }
        for (PickUpCarCommand carCommand : command.getPickUpCar()) {
            OcOrderPickupCarEntity waybill = BeanUtil.toBean(carCommand, OcOrderPickupCarEntity.class);
            waybill.setBizType(OrderShipTypeEnum.TYPE_10.getType());
            waybillList.add(waybill);
        }
        return waybillList;
    }

    /**
     * 发货单状态更新对象
     */
    private OcOrderShipEntity buildShipUpdate(ContextBO contextBO) {
        OcOrderShipEntity upd = OcOrderShipEntity.builder()
                .pickupAssigneeStatus(ShipPickupAssigneeStatusEnum.STATUS_120.getCode())
                .shipType(contextBO.getCommand().getShipType())
                .entrustType(ShipShipEntrustTypeEnum.STATUS_20.getCode())
                .logistNumber(contextBO.getCommand().getLogistNumber())
                .logistCompany(contextBO.getCommand().getLogistCompany())
                .logistCompanyId(contextBO.getCommand().getLogistCompanyId())
                .id(contextBO.getOrderShipEntity().getId())
                .build();
        // 发货单进行指定提货人，提货方式选择电子提单交割，指定提货人状态应该更新为“无需指定”
        if (ObjUtil.equal(contextBO.getCommand().getShipType(), ShipShipTypeEnum.STATUS_30.getCode())) {
            upd.setPickupAssigneeStatus(ShipPickupAssigneeStatusEnum.STATUS_130.getCode());
        }
        return upd;
    }

    /**
     * qty数量更新
     */
    private List<OcOrderShipQuantityEntity> buildUpdateQty(List<OcOrderShipQuantityEntity> quantityEntityList, List<OcOrderShipProductEntity> shipProductEntityList) {
        List<OcOrderShipQuantityEntity> ansList = Lists.newArrayList();
        Map<Long, OcOrderShipQuantityEntity> collect = quantityEntityList.stream().collect(Collectors.toMap(OcOrderShipQuantityEntity::getOrderProductId, Function.identity()));
        for (OcOrderShipProductEntity entity : shipProductEntityList) {
            OcOrderShipQuantityEntity qtyEntity = collect.get(entity.getOrderProductId());
            OcOrderShipQuantityEntity qtyUpdate = OcOrderShipQuantityEntity.builder()
                    .id(qtyEntity.getId())
                    .shipPickupQuantity(qtyEntity.getShipPickupQuantity().add(entity.getTotalShipmentQuantity())) // 本单据发货的数量累加到发货数量
                    .build();
            ansList.add(qtyUpdate);
        }
        return ansList;
    }

    /**
     * product 数量更新
     */
    private List<OcOrderShipProductEntity> buildUpdate(List<OcOrderShipProductEntity> shipProductEntityList) {
        List<OcOrderShipProductEntity> ansList = Lists.newArrayList();
        // 当前单据下的所有商品发货
        for (OcOrderShipProductEntity entity : shipProductEntityList) {
            OcOrderShipProductEntity update = OcOrderShipProductEntity.builder()
                    .id(entity.getId())
                    .shipmentQuantity(entity.getTotalShipmentQuantity())
                    .build();
            ansList.add(update);
        }
        return ansList;
    }

    private ResultMode<?> validation(ContextBO context) {
        SetPickupPersonCommand command = context.getCommand();
        OcOrderInfoEntity orderInfo = shipHandlerSupportService.getOcOrderInfo(command.getOrderNo());
        // 订单不存在
        if (orderInfo == null) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_NO.getMsg());
        }
        context.setOrderInfo(orderInfo);
        // 校验交货方式
        if (ObjUtil.notEqual(orderInfo.getDeliveryMode(), DeliveryModeEnum.TYPE_10.getType())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_TYPE_NOT_SEND_OUT.getMsg());
        }
        OcOrderShipEntity ocOrderShip = shipHandlerSupportService.getOcOrderShip(command.getPickUpNo());
        if (ocOrderShip == null) {
            return ResultMode.fail("提货单号不正确");
        }
        context.setOrderShipEntity(ocOrderShip);
        log.info("{} {}", ShipPickupAssigneeStatusEnum.STATUS_110.getCode(),  ocOrderShip.getPickupAssigneeStatus());
        log.info("setLogistics查询信息,orderShip={}, flag={}", JSONUtil.toJsonStr(ocOrderShip), ObjUtil.notEqual(ShipPickupAssigneeStatusEnum.STATUS_110.getCode(), ocOrderShip.getPickupAssigneeStatus()));
        if (ObjUtil.notEqual(ShipPickupAssigneeStatusEnum.STATUS_110.getCode(), ocOrderShip.getPickupAssigneeStatus())) {
            log.info("校验失败,状态错误");
            return ResultMode.fail("提货单状态已变更，请刷新页面查看");
        }
        // 非电子提单交割必须有运单数据
        if (!ShipShipTypeEnum.STATUS_30.getCode().equals(command.getShipType())) {
            if (CollUtil.isEmpty(command.getPickUpCar())) {
                return ResultMode.fail("请至少填写一条提货人数据");
            }
        }
        boolean hasAvailableInquiry = logisticsInquiryRepo.hasAvailableInquiry(ocOrderShip.getShipNo());
        if (hasAvailableInquiry) {
            return ResultMode.fail("当前提货单已发起在线委托，无需人工维护物流信息。");
        }
        return ResultMode.success();
    }

    @Data
    private static class ContextBO {
        private SetPickupPersonCommand command;
        private OcOrderInfoEntity orderInfo;
        private OcOrderShipEntity orderShipEntity;

        private String userId;
        private String userName;
    }
    @Override
    public boolean handlerType(String type) {
        return BizOperateTypeEnum.SET_PICK_UP_PERSON.getAction().equals(type);
    }
}
