package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.Data;

/**
 * 意向订单查询
 */
@Data
public class PurOrderCondition {

    /**
     * id
     */
    private Long id;

    /**
     * 意向订单编号
     */
    private String pOrderNo;

    /**
     * 意向单状态 10-待确认 ,字典purposeOrderStatus
     */
    private Integer status;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    private String sellerCompanyName;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    private String buyerCompanyName;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 店铺名称
     */
    private String shopName;

}
