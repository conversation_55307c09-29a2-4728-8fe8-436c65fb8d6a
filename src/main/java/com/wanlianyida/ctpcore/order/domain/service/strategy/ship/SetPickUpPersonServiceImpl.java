//package com.wanlianyida.ctpcore.order.domain.service.strategy.ship;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.util.ObjUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.google.common.collect.Lists;
//import com.wanlianyida.ctpcore.order.domain.assembler.OrderWaybillAssembler;
//import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
//import com.wanlianyida.ctpcore.order.domain.model.bo.OrderShipProductBO;
//import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
//import com.wanlianyida.ctpcore.order.domain.model.condition.OrderShipCondition;
//import com.wanlianyida.ctpcore.order.domain.model.entity.*;
//import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
//import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
//import com.wanlianyida.ctpcore.order.domain.service.strategy.OrderStatusChangeService;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipPickupAssigneeStatusEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipStatusEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipSummaryStatusEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderException;
//import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
//import com.wanlianyida.ctpcore.order.interfaces.facade.command.OcOrderShipCommand;
//import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
//import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
//import com.wanlianyida.framework.ctpcommon.enums.NumberBizTypeEnum;
//import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
//import com.wanlianyida.framework.ctpcore.utils.NumberGenerator;
//import groovy.util.logging.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.Map;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * 指定提货人
// * <AUTHOR>
// * @since 2025/4/22
// */
//@Slf4j
//@Service("setPickUpPersonService")
//public class SetPickUpPersonServiceImpl extends OrderStatusChangeService {
//
//    @Resource
//    OrderStatusRepo orderStatusRepo;
//    @Resource
//    OrderRepo orderRepo;
//
//    @Override
//    protected ResultMode<?> checkActionCondition(OrderStatusBO bo) {
//        if (!OrderActionEnum.ACTION_220.getAction().equals(bo.getAction())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_NO_SEND_OUT_GOODS.getMsg());
//        }
//        OcOrderInfoEntity orderInfo = bo.getContextBO().getOrderInfoEntity();
//        // 校验交货方式！=自提，因为只有自提场景允许指定提货人
//        if (orderInfo.getDeliveryMode().equals(DeliveryModeEnum.TYPE_10.getType())){
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_TYPE_NOT_SEND_OUT.getMsg());
//        }
//        // 只有待提货状态的订单才允许指定提货人
//        if (!orderInfo.getStatus().equals(OrderStatusEnum.STATUS_50.getStatus())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_SEND_OUT.getMsg());
//        }
//        List<OcOrderShipEntity> shipEntityList = orderRepo.queryOrderShipByCondition(OrderShipCondition.builder().shipmentNo(bo.getOrderShipNo()).build());
//        if (ObjUtil.isEmpty(shipEntityList)) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_SHIP_IS_NULL.getMsg());
//        }
//        bo.getContextBO().setOrderShipEntity(shipEntityList.get(0));
//        // 校验提货单状态，必须是待提货状态
//        if (!shipEntityList.get(0).getShipStatus().equals(ShipShipStatusEnum.STATUS_10.getCode())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_SHIP_STATUS_NOT_SEND_OUT.getMsg());
//        }
//        return ResultMode.success();
//    }
//
//    @Override
//    protected HandelStatusBO packActionData(OrderStatusBO bo) {
//        HandelStatusBO statusBO = new HandelStatusBO();
//        // 保存运单
//        List<OcOrderPickupCarEntity> wayBillList = OrderWaybillAssembler.buildManualPickUpList(bo.getContextBO(), bo.getPickUpCar());
//        statusBO.setPickUpCarListForInsert(wayBillList);
//        // 更新提货单
//        OcOrderShipEntity orderShipEntity = new OcOrderShipEntity();
//        orderShipEntity.setId(bo.getContextBO().getOrderShipEntity().getId());
//        // 提货方式
//        orderShipEntity.setShipType(bo.getOcOrderShip().getShipType());
//        // 已提货
//        orderShipEntity.setShipStatus(ShipShipStatusEnum.STATUS_20.getCode());
//        // 指定提货人状态
//        orderShipEntity.setPickupAssigneeStatus(ShipPickupAssigneeStatusEnum.STATUS_120.getCode());
//        // 综合状态
//        orderShipEntity.setSummaryStatus(ShipSummaryStatusEnum.STATUS_40.getCode());
//        // 物流信息
//        if (ObjUtil.isNotEmpty(bo.getOcOrderShip().getLogistCompany())) {
//            orderShipEntity.setLogistCompany(bo.getOcOrderShip().getLogistCompany());
//        }
//        if (ObjUtil.isNotEmpty(bo.getOcOrderShip().getLogistNumber())) {
//            orderShipEntity.setLogistNumber(bo.getOcOrderShip().getLogistNumber());
//        }
//        if (ObjUtil.isNotEmpty(bo.getOcOrderShip().getLogistCompanyId())) {
//            orderShipEntity.setLogistCompanyId(bo.getOcOrderShip().getLogistCompanyId());
//        }
//        orderShipEntity.setUpdaterId(bo.getContextBO().getOperatorId());
//        statusBO.setOcOrderShipForUpdate(Lists.newArrayList(orderShipEntity));
//        return statusBO;
//    }
//
//    private static OcOrderShipCommand getShipCommand(OrderStatusBO bo, TokenInfo tokenInfo) {
//        OcOrderShipCommand ocOrderShip = bo.getOcOrderShip();
//        // 发货人id
//        ocOrderShip.setShipNameId(tokenInfo.getUserBaseId());
//        // 发货人名称
//        ocOrderShip.setShipName(tokenInfo.getUsername());
//        // 发货人账号
//        ocOrderShip.setShipAccount(tokenInfo.getLoginName());
//        return ocOrderShip;
//    }
//
//    @Override
//    protected int handleStatus(HandelStatusBO bo) {
//        return orderStatusRepo.handleStatus(bo);
//    }
//}
