package com.wanlianyida.ctpcore.order.domain.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 物流计划单地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "OcLogisticsInquiryAddressEntity对象", description = "物流计划单地址表")
public class LogisticsInquiryAddressEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("计划单号")
    private String inquiryNo;

    @ApiModelProperty("地址类型[10-发货人(卖家),20-收货人(买家)]")
    private Integer addressType;

    @ApiModelProperty("联系人名称")
    private String linkName;

    @ApiModelProperty("联系人电话")
    private String linkPhone;

    @ApiModelProperty("省名称")
    private String province;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("市名称")
    private String city;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("区名称")
    private String area;

    @ApiModelProperty("区编码")
    private String areaCode;

    @ApiModelProperty("'街道编码'")
    private String streetCode;

    @ApiModelProperty("'街道名称'")
    private String street;

    @ApiModelProperty("经纬度（英文逗号凭接）")
    private String lonLat;

    @ApiModelProperty("详细地址")
    private String detailAddress;

    @ApiModelProperty("创建用户id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    private Date createdDate;

    @ApiModelProperty("最后更新人id")
    private String updaterId;

    @ApiModelProperty("最后更新时间")
    private Date updatedDate;

    @ApiModelProperty("版本号")
    private Integer versionCode;

    @ApiModelProperty("是否删除标志[0-正常,1-删除]")
    private Integer delFlag;



}

