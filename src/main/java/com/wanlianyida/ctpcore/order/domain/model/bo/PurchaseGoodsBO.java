package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购商品信息
 */
@Data
public class PurchaseGoodsBO implements Serializable {
    private static final long serialVersionUID = -9056113081244405288L;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 商品数量
     */
    private BigDecimal productNum;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 单位转化
     */
    private BigDecimal unitCon;

    /**
     * 小计
     */
    private BigDecimal subtotal;


}
