package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.Data;

import java.util.Date;

@Data
public class LogisticsInquiryCondition {
    /**
     * 企业id
     */
    private String companyId;

    /**
     * 物流询价单号
     */
    private String inquiryNo;

    /**
     * 发货/提货号
     */
    private String shipNo;

    /**
     * 物流承运方名称
     */
    private String logisticsTransportName;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建开始日期
     */
    private Date createDateStart;

    /**
     * 创建结束日期
     */
    private Date createDateEnd;

    /**
     * 买家卖家类型[10-买家,20-卖家]
     */
    private String sellerBuyerType;
}
