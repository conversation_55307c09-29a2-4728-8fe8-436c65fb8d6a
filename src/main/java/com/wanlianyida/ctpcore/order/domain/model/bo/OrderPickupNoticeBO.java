package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/12/10/21:57
 */
@Data
public class OrderPickupNoticeBO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库地址
     */
    private String warehouseAddress;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 联系电话
     */
    private String linkmanPhone;

    /**
     * 提货开始时间
     */
    private Date pickUpStartDate;

    /**
     * 提货结束时间
     */
    private Date pickUpEndDate;
}
