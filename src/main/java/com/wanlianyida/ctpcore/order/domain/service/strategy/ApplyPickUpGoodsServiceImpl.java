package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.ctpcore.order.domain.assembler.OrderStatusAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.PickUpCarBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderPickupCarEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.DeliveryModeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;

import javax.annotation.Resource;

/**
 * 申请提货  不修改状态
 * <AUTHOR>
 * @since 2024/12/09/17:01
 */
@Service("applyPickUpGoodsService")
public class ApplyPickUpGoodsServiceImpl extends OrderStatusChangeService{

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private OrderShipBaseService orderShipBaseService;

    @Override
    protected ResultMode checkActionCondition(OrderStatusBO bo) {
        //不通知也可以申请
        //ACTION_80("80","申请提货","applyPickUpGoodsService"),
        //订单状态=待提货、部分提货时，买家端显示申请提货按钮
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        //主状态为 待提货 50 时可以申请提货
        HashSet<Integer> confirmReceiptStatus = OrderStatusAssembler.getCheckStatusCondition(bo.getAction());
        if(!confirmReceiptStatus.contains(ocOrderInfo.getStatus())){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_PICK_UP.getMsg());
        }
        if (!DeliveryModeEnum.TYPE_10.getType().equals(ocOrderInfo.getDeliveryMode())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_DELIVERY_MODE_INVALID.getMsg());
        }
        return ResultMode.success();
    }

    @Override
    protected HandelStatusBO packActionData(OrderStatusBO bo) {
        //基础数据设置
        HandelStatusBO handelStatusBO = orderShipBaseService.packActionData(bo);
        OcOrderShipEntity ocOrderShip = handelStatusBO.getOcOrderShipEntityForInsert();
        //提货单车辆信息
        List<PickUpCarBO> ocOrderPickupCarEntityList = bo.getPickUpCar();
        ocOrderPickupCarEntityList.forEach(e -> {
            e.setPickUpNo(ocOrderShip.getShipNo());
        });
        handelStatusBO.setPickUpCarListForInsert(BeanUtil.copyToList(ocOrderPickupCarEntityList, OcOrderPickupCarEntity.class));
        return handelStatusBO;
    }

    /**
     * 申请提货
     * @param bo
     * @return
     */
    @Override
    protected int handleStatus(HandelStatusBO bo) {
        //申请提货
        return orderStatusRepo.handleStatus(bo);
    }
}
