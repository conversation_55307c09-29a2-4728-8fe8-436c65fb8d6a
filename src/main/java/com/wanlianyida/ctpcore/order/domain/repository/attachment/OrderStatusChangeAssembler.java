package com.wanlianyida.ctpcore.order.domain.repository.attachment;

import com.wanlianyida.ctpcore.order.domain.model.bo.OrderCancelBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderCancelEntity;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;

import java.util.Date;

public class OrderStatusChangeAssembler {


    /**
     * 构建 OcOrderCancelEntity
     * @param orderNo
     * @param orderCancel
     * @return
     */
    public static OcOrderCancelEntity buildOcOrderCancel(String orderNo, OrderCancelBO orderCancel) {
        OcOrderCancelEntity ocOrderCancelEntity = new OcOrderCancelEntity();
        ocOrderCancelEntity.setOrderNo(orderNo);
        ocOrderCancelEntity.setCancelSource(orderCancel.getCancelSource());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        ocOrderCancelEntity.setCanceller(tokenInfo.getUsername());
        ocOrderCancelEntity.setCancellerAccount(tokenInfo.getLoginName());
        ocOrderCancelEntity.setCancelReason(orderCancel.getCancelReason());
        Date date = new Date();
        ocOrderCancelEntity.setCancelDate(date);
        String userBaseId = tokenInfo.getUserBaseId();
        ocOrderCancelEntity.setCreatorId(userBaseId);
        ocOrderCancelEntity.setUpdaterId(userBaseId);
        ocOrderCancelEntity.setCreatedDate(date);
        ocOrderCancelEntity.setUpdatedDate(date);
        return ocOrderCancelEntity;
    }
}
