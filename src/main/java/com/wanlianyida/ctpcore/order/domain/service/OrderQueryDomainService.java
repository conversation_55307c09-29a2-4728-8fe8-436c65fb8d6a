package com.wanlianyida.ctpcore.order.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.order.application.assembler.OrderAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.*;
import com.wanlianyida.ctpcore.order.domain.model.condition.*;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.repository.OrderInfoExtRepo;
import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.interfaces.facade.dto.SpuResponse;
import com.wanlianyida.ctpcore.order.interfaces.facade.query.OrderDealStatusQuery;
import com.wanlianyida.ctpcore.order.interfaces.facade.query.OrderSpuQuery;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 订单查询
 */
@Slf4j
@Service
public class OrderQueryDomainService {

    @Resource
    private OrderRepo orderRepo;

    @Resource
    private OrderInfoExtRepo  orderInfoExtRepo;

    /**
     * 订单列表查询（用户端）
     */
    public PageInfo<OrderListBO> pageList(PagingInfo<OrderCondition> pagingInfo) {
        OrderCondition condition = pagingInfo.getFilterModel();
        Page<Object> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, true);
        PageHelper.orderBy("created_date desc, id desc");

        List<OrderListBO> boList = orderRepo.queryList(condition);
        if (IterUtil.isEmpty(boList)) {
            return null;
        }

        PageInfo<OrderListBO> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(boList);
        return pageInfo;
    }

    /**
     * 订单列表查询（平台端）
     */
    public PageInfo<OcOrderInfoEntity> pageInfoList(PagingInfo<OrderCondition> pagingInfo) {
        OrderCondition condition = pagingInfo.getFilterModel();
        Page<Object> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, true);
        PageHelper.orderBy("created_date desc, id desc");

        List<OcOrderInfoEntity> list = orderRepo.queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        PageInfo<OcOrderInfoEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    /**
     * 订单基本信息详情查询
     */
    public OrderDetailBO queryBaseDetail(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        //订单信息
        OrderCondition condition = new OrderCondition();
        condition.setOrderNo(orderNo);
        List<OcOrderInfoEntity> list = orderRepo.queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        OcOrderInfoEntity orderInfo = IterUtil.getFirst(list);
        OrderDetailBO bo = new OrderDetailBO();
        bo.setOrderInfo(orderInfo);
        //状态信息
//        List<OcOrderStatusEntity> statusList = orderRepo.queryStatusByOrderNo(orderInfo.getOrderNo());
        List<OcOrderNodeEntity> orderNodeEntityList = orderRepo.queryNodeByOrderNo(orderInfo.getOrderNo());
        //付款与结算信息
        OcOrderPaymentEntity payment = orderRepo.queryPaymentByOrderNo(orderInfo.getOrderNo());
//        bo.setStatusList(assembleStatusList(statusList, orderInfo, payment));
        bo.setNodeEntityList(orderNodeEntityList);
        //商品信息
        List<OcOrderProductEntity> productList = orderRepo.queryProductByOrderNo(orderInfo.getOrderNo());
        //商品品类
        List<OcOrderProductSkuEntity> productSkuList =  orderRepo.queryProductSkuCondition(OrderAssembler.buildProductSkuCondition(orderInfo.getOrderNo()));
        if (IterUtil.isNotEmpty(productSkuList)){
            setProductCategoryNames(productList, productSkuList);
        }
        if (IterUtil.isNotEmpty(productList)) {
            bo.setProductList(productList);
            List<String> skuCodeList = productList.stream().map(OcOrderProductEntity::getSkuCode).collect(Collectors.toList());
            //商品规格
            List<OcOrderProductSkuAttrEntity> skuAttrList = orderRepo.queryProductSkuAttr(skuCodeList, orderInfo.getOrderNo());
            if (IterUtil.isNotEmpty(skuAttrList)) {
                bo.setSkuAttrMap(skuAttrList.stream().collect(Collectors.groupingBy(OcOrderProductSkuAttrEntity::getSkuCode)));
            }
        }
        //买卖家信息
        bo.setBuySellList(orderRepo.queryBuySellByOrderNo(orderInfo.getOrderNo()));
        //交货信息
        bo.setDelivery(orderRepo.queryDeliveryByOrderNo(orderInfo.getOrderNo()));
        //地址信息
        bo.setAddress(orderRepo.queryOrderAddressByOrderNo(orderInfo.getOrderNo()));
        //提货通知
        bo.setPickupNotice(orderRepo.queryPickupNoticeByOrderNo(orderInfo.getOrderNo()));
        if (ObjUtil.isNotNull(payment)) {
            bo.setPayment(payment);
            if (StrUtil.isNotBlank(payment.getPayMode())) {
                //付款方式
                bo.setPaymentWayList(Arrays.stream(StrUtil.splitToInt(payment.getPayMode(), ",")).boxed().collect(Collectors.toList()));
            }
        }
        //取消信息
        if (ObjUtil.equal(orderInfo.getStatus(), OrderStatusEnum.STATUS_100.getStatus())) {
            bo.setOrderCancel(orderRepo.queryOrderCancelByOrderNo(orderInfo.getOrderNo()));
        }
        List<OcOrderShipQuantityEntity> ocOrderShipQuantityList = orderRepo.queryShipQuantityOrderNo(orderNo);
        bo.setOrderShipQuantityEntityList(ocOrderShipQuantityList);
        if (IterUtil.isNotEmpty(ocOrderShipQuantityList)) {
            Map<String, BigDecimal> result = ocOrderShipQuantityList.stream() .collect(Collectors.groupingBy(OcOrderShipQuantityEntity::getOrderNo,
                    Collectors.reducing(BigDecimal.ZERO, entity -> entity.getOrderQuantity().subtract(entity.getShipPickupQuantity()), BigDecimal::add)
            ));
            bo.setAvailablePickupQuantity(result.getOrDefault(orderNo, BigDecimal.ZERO));
        }

        // 订单备注
        if (StrUtil.isNotBlank(orderNo)){
            OrderInfoExtEntity infoExtEntity = orderInfoExtRepo.getDetail(orderNo);
            bo.setOrderRemark(ObjUtil.isEmpty(infoExtEntity) ? "" : infoExtEntity.getOrderRemark());
        }
        return bo;
    }

    /**
     * 为商品列表设置三级品类名称
     *
     * @param productList 商品列表
     * @param productSkuList 商品SKU列表
     */
    private void setProductCategoryNames(List<OcOrderProductEntity> productList, List<OcOrderProductSkuEntity> productSkuList) {
        if (IterUtil.isEmpty(productList)){
            return;
        }
        //商品SkuMap
        Map<String, OcOrderProductSkuEntity> productSkuMap = productSkuList.stream()
                .collect(Collectors.toMap(OcOrderProductSkuEntity::getSkuCode, Function.identity()));
        //set 三级品类名称
        productList.forEach(product -> {
            OcOrderProductSkuEntity ocOrderProductSkuEntity = productSkuMap.get(product.getSkuCode());
            if (ObjUtil.isNotNull(ocOrderProductSkuEntity)) {
                product.setCategoryName3(ocOrderProductSkuEntity.getCategoryName3());
            }
        });
    }

    /**
     * 订单履约节点 处理
     */
    private List<OcOrderStatusEntity> assembleStatusList(List<OcOrderStatusEntity> statusList, OcOrderInfoEntity orderInfo, OcOrderPaymentEntity payment) {
        if (IterUtil.isEmpty(statusList)) {
            return null;
        }
        Integer orderStatus = orderInfo.getStatus();
        //订单已取消
        if (OrderStatusEnum.STATUS_100.getStatus().equals(orderStatus)) {
            return null;
        }

        Integer subStatus = orderInfo.getSubStatus();
        Integer deliveryMode = orderInfo.getDeliveryMode();
        Map<Integer, Date> statusMap = statusList.stream().collect(Collectors.toMap(OcOrderStatusEntity::getStatus, OcOrderStatusEntity::getOperDate));

        List<OcOrderStatusEntity> newStatusList = new ArrayList<>();
        List<Integer> statusValues = OrderStatusOptEnum.statusValues();
        for (Integer status : statusValues) {
            OcOrderStatusEntity entity = new OcOrderStatusEntity();
            //发货
            if (OrderStatusOptEnum.STATUS_40.getStatus().equals(status)) {
                //订单状态小于待发货
                if (orderStatus < OrderStatusEnum.STATUS_30.getStatus()) {
                    break;
                }
                //非物流配送
                if (!DeliveryModeEnum.TYPE_20.getType().equals(deliveryMode)) {
                    continue;
                }
                //待发货
                if (OrderSubStatusEnum.SUB_STATUS_30.getSubStatus().equals(subStatus)) {
                    break;
                }
                //取第一个发货单的发货时间
                List<OcOrderShipEntity> shipList = orderRepo.queryOrderShipByOrderNo(orderInfo.getOrderNo(), OrderShipTypeEnum.TYPE_20.getType());
                if (IterUtil.isEmpty(shipList)) {
                    continue;
                }
                List<Date> dateList = shipList.stream().map(OcOrderShipEntity::getShipDate).filter(Objects::nonNull).collect(Collectors.toList());
                Date shipDate = dateList.stream().sorted().findFirst().orElse(null);
                entity.setOperDate(shipDate);
            } else if (OrderStatusOptEnum.STATUS_30.getStatus().equals(status)
                    && ObjUtil.isNotNull(payment) && PaymentTermsEnum.TERMS_20.getTerms().equals(payment.getPaymentTerms())) {
                //先货后款无付款时间
                entity.setOperDate(null);
            } else if (OrderStatusOptEnum.STATUS_60.getStatus().equals(status)) {
                //确认结算时间
                entity.setOperDate(orderInfo.getSettConfirmTime());
            } else {
                List<Integer> valuelist = OrderStatusOptEnum.getOrderStatusList(status);
                Integer minStatus = valuelist.stream().sorted().findFirst().orElse(0);
                //订单状态小于履约状态
                if (orderStatus < minStatus) {
                    break;
                }
                for (Integer vStatus : valuelist) {
                    //第一个匹配则赋值退出
                    if (statusMap.get(vStatus) != null) {
                        entity.setOperDate(statusMap.get(vStatus));
                        break;
                    }
                }
            }

            newStatusList.add(entity);
            entity.setStatus(status);
        }

        return newStatusList;
    }

    /**
     * 是否需要结算
     */
    private boolean needSettlement(OcOrderPaymentEntity payment) {
        if (ObjUtil.isNull(payment)) {
            return false;
        }
        //先货后款
        if (PaymentTermsEnum.TERMS_20.getTerms().equals(payment.getPaymentTerms())) {
            return true;
        }
        //先款后货 & 预付不等于100%
        if (PaymentTermsEnum.TERMS_10.getTerms().equals(payment.getPaymentTerms())
                && BigDecimal.valueOf(100).compareTo(payment.getPrepayRatio()) != 0) {
            return true;
        }
        //结算数量=实际数量
        if (SettlementQuantityEnum.TERMS_20.getTerms().equals(payment.getSettlementQuantity())) {
            return true;
        }
        //结算金额=实际单价
        if (SettlementPriceEnum.TERMS_20.getTerms().equals(payment.getSettlementPrice())) {
            return true;
        }

        return false;
    }

    /**
     * 订单详情查询-付款与结算信息
     */
    public OrderPaymentInfoDetailBO queryPaymentDetail(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        //订单信息
        OrderCondition condition = new OrderCondition();
        condition.setOrderNo(orderNo);
        List<OcOrderInfoEntity> list = orderRepo.queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        OcOrderInfoEntity orderInfo = IterUtil.getFirst(list);
        Integer payStatus = orderInfo.getPayStatus();

        //订单商品
        List<OcOrderProductEntity> productList = orderRepo.queryProductByOrderNo(orderNo);
        //商品总金额
        BigDecimal productAmount = orderInfo.getProductTotalAmount();

        //运费
        BigDecimal expenseAmount = BigDecimal.ZERO;
        OcOrderDeliveryEntity delivery = orderRepo.queryDeliveryByOrderNo(orderNo);
        if (ObjUtil.isNotNull(delivery)) {
            expenseAmount = delivery.getExpenseAmount();
        }

        //订单付款信息
        OcOrderPaymentEntity payment = orderRepo.queryPaymentByOrderNo(orderNo);
        //是否需要结算
        boolean needSettlement = needSettlement(payment);

        //已付金额
        BigDecimal paidAmount = BigDecimal.ZERO;
        BigDecimal unpaidAmount = BigDecimal.ZERO;
        List<String> bizNoList = new ArrayList<>();
        //结算单：待收款、已付款
        List<OcSmentOrderEntity> paidSmentOrderList = new ArrayList<>();
        List<OcSmentOrderEntity> smentOrderList = orderRepo.querySmentOrderByCondition(
                SmentOrderCondition.builder().orderNo(orderNo).build());
        if (IterUtil.isNotEmpty(smentOrderList)) {
            for (OcSmentOrderEntity ocSmentOrderEntity : smentOrderList) {
                bizNoList.add(ocSmentOrderEntity.getSmentNo());
                //已付款
                if (SettlementStatusEnum.STATUS_230.getCode().equals(ocSmentOrderEntity.getSmentStatus())) {
                    paidAmount = paidAmount.add(Optional.ofNullable(ocSmentOrderEntity.getPaymentAmount()).orElse(BigDecimal.ZERO));
                }
                if (SettlementStatusEnum.STATUS_220.getCode().equals(ocSmentOrderEntity.getSmentStatus()) || SettlementStatusEnum.STATUS_230.getCode().equals(ocSmentOrderEntity.getSmentStatus())) {
                    paidSmentOrderList.add(ocSmentOrderEntity);
                }
            }
        }

        OcSmentEntity sment = new OcSmentEntity();
        if (orderInfo.getStatus() < OrderStatusEnum.STATUS_60.getStatus()
                || OrderStatusEnum.STATUS_100.getStatus().equals(orderInfo.getStatus())
                || (OrderStatusEnum.STATUS_60.getStatus().equals(orderInfo.getStatus()) && OrderPayStatusEnum.PAY_STATUS_100.getPayStatus().equals(payStatus))) {
            //未发起结算、待卖家结算
            sment.setShouldSmentAmount(BigDecimal.ZERO);
            sment.setOrderTotalAmount(BigDecimal.ZERO);
            sment.setProductTotalAmount(BigDecimal.ZERO);
            sment.setExpenseAmount(BigDecimal.ZERO);
            sment.setOtherExpenses(BigDecimal.ZERO);
            unpaidAmount = BigDecimal.ZERO;
            productList = null;
        } else if (!needSettlement) {
            //无需结算
            sment.setShouldSmentAmount(orderInfo.getOrderAmount());
            sment.setOrderTotalAmount(orderInfo.getOrderAmount());
            sment.setProductTotalAmount(productAmount);
            sment.setExpenseAmount(expenseAmount);
            sment.setOtherExpenses(BigDecimal.ZERO);
            unpaidAmount = orderInfo.getOrderAmount().subtract(paidAmount);
            productList.stream().forEach(prd -> {
                prd.setSmentQuantity(prd.getPurchaseQuantity());
                prd.setSmentPrice(prd.getPrice());
                prd.setSementSubtotal(prd.getSubtotal());
            });
        } else {
            //待买家付款、待卖家确认收款、已结算
            List<OcSmentEntity> smentList = orderRepo.querySmentByCondition(SmentCondition.builder().orderNo(orderNo).build());
            if (IterUtil.isNotEmpty(smentList)) {
                sment = IterUtil.getFirst(smentList);
//                unpaidAmount = sment.getSmentAmount().subtract(paidAmount);
                unpaidAmount = sment.getRemainAmount();
                paidAmount = sment.getSmentAmount();
            }
            //结算商品
            List<OcSmentProductEntity> smentProductList = orderRepo.querySmentProListByOrderNo(SmentPrdCondition.builder().orderNo(orderNo).build());
            if (IterUtil.isNotEmpty(smentProductList)) {
                Map<Long, OcSmentProductEntity> smentPrdMap = smentProductList.stream().collect(Collectors.toMap(OcSmentProductEntity::getOrderProductId, Function.identity()));
                productList.stream().forEach(prd -> {
                    OcSmentProductEntity ocSmentProductEntity = smentPrdMap.get(prd.getId());
                    prd.setSmentQuantity(ocSmentProductEntity.getSmentQuantity());
                    prd.setSmentPrice(ocSmentProductEntity.getSmentPrice());
                    prd.setSementSubtotal(ocSmentProductEntity.getSementSubtotal());
                });
            }
        }

        List<OcOrderAttachmentEntity> attachmentList = null;
        if (IterUtil.isNotEmpty(bizNoList)) {
            //结算凭证附件、付款凭证
            attachmentList = orderRepo.queryAttachmentByCondition(
                    OrderAttachmentCondition.builder().bizNoList(bizNoList)
                            .attachmentTypeList(Arrays.asList(AttachmentTypeEnum.TYPE_30.getType(), AttachmentTypeEnum.TYPE_40.getType()))
                            .build());
        }

        OrderPaymentInfoDetailBO bo = new OrderPaymentInfoDetailBO();
        bo.setOrderNo(orderNo);
        bo.setStatus(orderInfo.getStatus());
        bo.setPayStatus(payStatus);
        bo.setOrderAmount(orderInfo.getOrderAmount());
        bo.setProductAmount(productAmount);
        bo.setDeliveryMode(orderInfo.getDeliveryMode());
        bo.setExpenseAmount(expenseAmount);
        bo.setOtherAmount(BigDecimal.ZERO);
        bo.setPaidAmount(paidAmount);
        bo.setUnpaidAmount(unpaidAmount.compareTo(BigDecimal.ZERO)<=0?BigDecimal.ZERO:unpaidAmount);
        bo.setSment(sment);
        bo.setPaidSmentOrderList(paidSmentOrderList);
        bo.setProductList(productList);
        bo.setAttachmentList(attachmentList);
        bo.setNeedSettlement(needSettlement ? 1 : 0);

        return bo;
    }

    /**
     * 订单详情查询-提货记录
     */
    public List<OrderPickUpDetailBO> queryPickUpDetail(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }


        //提货单
        List<OcOrderShipEntity> pickUpList =  orderRepo.queryOrderShipByCondition(OrderShipCondition.builder().orderNo(orderNo).build());
        if (IterUtil.isEmpty(pickUpList)) {
            return null;
        }
        List<String> pickUpNoList = pickUpList.stream().map(OcOrderShipEntity::getShipNo).collect(Collectors.toList());
        //提货车辆信息
        Map<String, List<OcOrderPickupCarEntity>> pickupCarMap = null;
        List<OcOrderPickupCarEntity> pickUpCarList = orderRepo.queryOrderPickupCarByOrderNo(pickUpNoList);
        if (IterUtil.isNotEmpty(pickUpCarList)) {
            pickupCarMap = pickUpCarList.stream().collect(Collectors.groupingBy(OcOrderPickupCarEntity::getPickUpNo));
        }
        //提货商品
        Map<String, List<OcOrderShipProductEntity>> pickupPrdInfoMap = null;
        List<OcOrderShipProductEntity> pickupPrdInfoList = orderRepo.queryShipPrdListByOrderNo(pickUpNoList);
        if (IterUtil.isNotEmpty(pickupPrdInfoList)) {
            //完善商品信息
            this.assembleShipPrdList(pickupPrdInfoList);
            pickupPrdInfoMap = pickupPrdInfoList.stream().collect(Collectors.groupingBy(OcOrderShipProductEntity::getBizNo));
        }

        //提货凭证
        Map<String, List<OcOrderAttachmentEntity>> attachemntMap = null;
        List<OcOrderAttachmentEntity> attachmentList = orderRepo.queryAttachmentByCondition(
                OrderAttachmentCondition.builder().bizNoList(pickUpNoList)
                        .attachmentType(AttachmentTypeEnum.TYPE_10.getType()).build());
        if (IterUtil.isNotEmpty(attachmentList)) {
            attachemntMap = attachmentList.stream().collect(Collectors.groupingBy(OcOrderAttachmentEntity::getBizNo));
        }
        //订单信息
        OcOrderInfoEntity orderInfo = orderRepo.queryByOrderNo(orderNo);
        List<OrderPickUpDetailBO> boList = new ArrayList<>();
        for (OcOrderShipEntity pickUp : pickUpList) {
            OrderPickUpDetailBO bo = new OrderPickUpDetailBO();
            bo.setPickUp(pickUp);
            if (MapUtil.isNotEmpty(pickupCarMap)) {
                bo.setPickUpCarList(pickupCarMap.get(pickUp.getShipNo()));
            }
            if (MapUtil.isNotEmpty(pickupPrdInfoMap)) {
                bo.setProductList(pickupPrdInfoMap.get(pickUp.getShipNo()));
            }
            if (MapUtil.isNotEmpty(attachemntMap)) {
                bo.setAttachmentList(attachemntMap.get(pickUp.getShipNo()));
            }
            bo.setDeliveryMode(orderInfo.getDeliveryMode());
            boList.add(bo);
        }

        return boList;
    }

    /**
     * 订单详情查询-发货记录
     */
    public List<OrderShipmentsDetailBO> queryShipmentsDetail(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        //发货单
        List<OcOrderShipEntity> shipList = orderRepo.queryOrderShipByOrderNo(orderNo, OrderShipTypeEnum.TYPE_20.getType());
        if (IterUtil.isEmpty(shipList)) {
            return null;
        }
        List<String> shipNoList = shipList.stream().map(OcOrderShipEntity::getShipNo).collect(Collectors.toList());

        //发货商品
        Map<String, List<OcOrderShipProductEntity>> shipPrdInfoMap = null;
        List<OcOrderShipProductEntity> shipPrdInfoList = orderRepo.queryShipPrdListByOrderNo(shipNoList);
        if (IterUtil.isNotEmpty(shipPrdInfoList)) {
            //完善商品信息
            this.assembleShipPrdList(shipPrdInfoList);
            shipPrdInfoMap = shipPrdInfoList.stream().collect(Collectors.groupingBy(OcOrderShipProductEntity::getBizNo));
        }

        //发货凭证
        Map<String, List<OcOrderAttachmentEntity>> attachemntMap = null;
        List<OcOrderAttachmentEntity> attachmentList = orderRepo.queryAttachmentByCondition(
                OrderAttachmentCondition.builder().bizNoList(shipNoList)
                        .attachmentType(AttachmentTypeEnum.TYPE_20.getType()).build());
        if (IterUtil.isNotEmpty(attachmentList)) {
            attachemntMap = attachmentList.stream().collect(Collectors.groupingBy(OcOrderAttachmentEntity::getBizNo));
        }

        List<OrderShipmentsDetailBO> boList = new ArrayList<>();
        for (OcOrderShipEntity ship : shipList) {
            OrderShipmentsDetailBO bo = new OrderShipmentsDetailBO();
            bo.setShip(ship);
            if (MapUtil.isNotEmpty(shipPrdInfoMap)) {
                bo.setProductList(shipPrdInfoMap.get(ship.getShipNo()));
            }
            if (MapUtil.isNotEmpty(attachemntMap)) {
                bo.setAttachmentList(attachemntMap.get(ship.getShipNo()));
            }

            boList.add(bo);
        }
        return boList;
    }

    /**
     * 提货记录、发货记录 关联商品信息
     */
    private void assembleShipPrdList(List<OcOrderShipProductEntity> shipPrdInfoList) {
        //订单商品信息
        List<Long> productIdList = shipPrdInfoList.stream().map(OcOrderShipProductEntity::getOrderProductId).collect(Collectors.toList());
        List<OcOrderProductEntity> orderProductList = orderRepo.queryProductByIdList(productIdList);
        if (IterUtil.isNotEmpty(orderProductList)) {
            Map<Long, OcOrderProductEntity> orderPrdMap = orderProductList.stream().collect(Collectors.toMap(OcOrderProductEntity::getId, Function.identity()));
            for (OcOrderShipProductEntity prd : shipPrdInfoList) {
                OcOrderProductEntity orderPrd = orderPrdMap.get(prd.getOrderProductId());
                if (ObjUtil.isNotNull(orderPrd)) {
                    prd.setPicture(orderPrd.getPicture());
                    prd.setPrice(orderPrd.getPrice());
                    prd.setPriceUnit(orderPrd.getPriceUnit());
                    prd.setPurchaseQuantity(orderPrd.getPurchaseQuantity());
                    prd.setPurchaseQuantityUnit(orderPrd.getPurchaseQuantityUnit());
                }
            }
        }
    }

    /**
     * 提货、发货 关联商品信息
     */
    private List<OcOrderProductEntity> assembleShipPrdQuantityList(List<OcOrderProductEntity> productList, String orderNo) {
        if (IterUtil.isEmpty(productList) || StrUtil.isBlank(orderNo)) {
            return null;
        }

        //商品 已提货数量
        List<OcOrderShipQuantityEntity> shipQuantityList = orderRepo.queryShipQuantityOrderNo(orderNo);
        if (IterUtil.isNotEmpty(shipQuantityList)) {
            Map<Long, BigDecimal> shipPickUpMap = shipQuantityList.stream()
                    .collect(Collectors.toMap(OcOrderShipQuantityEntity::getOrderProductId, OcOrderShipQuantityEntity::getTotalShipmentQuantity)); // 改为使用已创建发货单的数量
            for (OcOrderProductEntity prd : productList) {
                BigDecimal shipPickUpQuantity = Optional.ofNullable(shipPickUpMap.get(prd.getId())).orElse(BigDecimal.ZERO);
                BigDecimal canShipPickUpQuantity = prd.getPurchaseQuantity().subtract(shipPickUpQuantity);

                prd.setShipPickupQuantity(shipPickUpQuantity);
                prd.setCanShipPickUpQuantity(canShipPickUpQuantity);
            }
        }

        return productList;
    }

    /**
     * 订单-买家申请提货
     */
    public OrderPickupOptBO applyPickUp(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        OrderPickupOptBO bo = new OrderPickupOptBO();
        //地址信息
        bo.setAddress(orderRepo.queryOrderAddressByOrderNo(orderNo));
        //通知提货信息
        bo.setPickupNotice(orderRepo.queryPickupNoticeByOrderNo(orderNo));
        //订单商品
        List<OcOrderProductEntity> productList = orderRepo.queryProductByOrderNo(orderNo);
        //商品 已提货数量
        bo.setProductList(this.assembleShipPrdQuantityList(productList, orderNo));

        return bo;
    }

    /**
     * 订单-买家付款（应付信息）
     */
    public OrderShouldPayBO orderShouldPayInfo(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        //订单信息
        OrderCondition condition = new OrderCondition();
        condition.setOrderNo(orderNo);
        List<OcOrderInfoEntity> list = orderRepo.queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        OcOrderInfoEntity orderInfo = IterUtil.getFirst(list);

        OrderShouldPayBO bo = new OrderShouldPayBO();
        bo.setOrderNo(orderInfo.getOrderNo());
        bo.setStatus(orderInfo.getStatus());
        bo.setSubStatus(orderInfo.getSubStatus());
        bo.setPayStatus(orderInfo.getPayStatus());
        bo.setOrderAmount(orderInfo.getOrderAmount());
        //付款信息
        OcOrderPaymentEntity payment = orderRepo.queryPaymentByOrderNo(orderNo);
        if (ObjUtil.isNotNull(payment)) {
            if (StrUtil.isNotBlank(payment.getPayMode())) {
                //付款方式
                bo.setPayModeList(Arrays.stream(StrUtil.splitToInt(payment.getPayMode(), ",")).boxed().collect(Collectors.toList()));
            }
        }

        //已收货待结算
        if (ObjUtil.equal(orderInfo.getStatus(), OrderStatusEnum.STATUS_60.getStatus())) {
            //结算单信息
            List<OcSmentOrderEntity> smentOrderList = orderRepo.querySmentOrderByCondition(SmentOrderCondition.builder()
                    .orderNo(orderNo).build());
            if (IterUtil.isNotEmpty(smentOrderList)) {
                OcSmentOrderEntity smentOrder = null;
                if (OrderPayStatusEnum.PAY_STATUS_110.getPayStatus().equals(orderInfo.getPayStatus())) {
                    //待付款
                    smentOrder = smentOrderList.stream()
                            .filter(s -> SettlementStatusEnum.STATUS_210.getCode().equals(s.getSmentStatus()))
                            .findFirst().orElse(null);
                } else if (OrderPayStatusEnum.PAY_STATUS_120.getPayStatus().equals(orderInfo.getPayStatus())) {
                    //待收款
                    smentOrder = smentOrderList.stream()
                            .filter(s -> SettlementStatusEnum.STATUS_220.getCode().equals(s.getSmentStatus()))
                            .findFirst().orElse(null);
                }
                if (ObjUtil.isNull(smentOrder)) {
                    return bo;
                }
                bo.setSmentOrder(smentOrder);
            }

            List<OcSmentEntity> smentList = orderRepo.querySmentByCondition(SmentCondition.builder().orderNo(orderNo).build());
            if (IterUtil.isNotEmpty(smentList)) {
                OcSmentEntity sment = IterUtil.getFirst(smentList);
                bo.setSment(sment);
                //需付款金额
                bo.setPayAmount(sment.getRemainAmount());
                //已付金额
                bo.setPaidAmount(sment.getSmentAmount());
            }

            return bo;
        }

        //运费
        BigDecimal expenseAmount = BigDecimal.ZERO;
        if (ObjUtil.equal(orderInfo.getDeliveryMode(), DeliveryModeEnum.TYPE_20.getType())) {
            OcOrderDeliveryEntity delivery = orderRepo.queryDeliveryByOrderNo(orderNo);
            if (ObjUtil.isNotNull(delivery)) {
                expenseAmount = delivery.getExpenseAmount();
            }
        }
        bo.setExpenseAmount(expenseAmount);

        //付款信息
        if (ObjUtil.isNotNull(payment)) {
            BigDecimal prepayRatio = payment.getPrepayRatio();
            bo.setPaymentTerms(payment.getPaymentTerms());
            bo.setPrepayRatio(prepayRatio);
            if (BigDecimal.ZERO.compareTo(prepayRatio) == 0) {
                //预付比例为0
                bo.setPayAmount(BigDecimal.ZERO);
            } else if (BigDecimal.valueOf(100).compareTo(prepayRatio) == 0) {
                //预付款比例=100%，需付款金额=订单总金额
                bo.setPayAmount(orderInfo.getOrderAmount());
            } else {
                //预付款比例≠100%，需付款金额=（订单总金额-运费金额）*预付款比例*0.01,保留小数点后2位，四舍五入
                BigDecimal subAmount = orderInfo.getOrderAmount().subtract(expenseAmount);
                BigDecimal payAmount = subAmount.multiply(prepayRatio).divide(BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP);
                bo.setPayAmount(payAmount);
            }
        }

        return bo;
    }

    /**
     * 订单-卖家确认收款
     */
    public OrderPaymentOptBO confirmReceipt(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        //应付信息
        OrderShouldPayBO orderShouldPayBO = orderShouldPayInfo(orderNo);
        if (ObjUtil.isNull(orderShouldPayBO)) {
            return null;
        }

        OrderPaymentOptBO bo = new OrderPaymentOptBO();
        bo.setShouldPayBO(orderShouldPayBO);

        //买家信息
        OrderBuySellCondition buySellCondition = new OrderBuySellCondition();
        buySellCondition.setOrderNo(orderNo);
        buySellCondition.setType(BuySelTypeEnum.TYPE_10.getType());
        List<OcOrderBuySellEntity> buyer = orderRepo.queryBuySellCondition(buySellCondition);
        if (IterUtil.isNotEmpty(buyer)) {
            OcOrderBuySellEntity buyerInfo = IterUtil.getFirst(buyer);
            bo.setPayerCompanyId(buyerInfo.getCompanyId());
            bo.setPayerCompany(buyerInfo.getCompanyName());
        }

        //付款信息
        OcSmentOrderEntity smentOrder = orderShouldPayBO.getSmentOrder();
        if (ObjUtil.isNull(smentOrder)) {
            List<OcSmentOrderEntity> smentOrderList = orderRepo.querySmentOrderByCondition(
                    SmentOrderCondition.builder().orderNo(orderNo).smentStatus(SettlementStatusEnum.STATUS_220.getCode()).build());
            smentOrder = IterUtil.getFirst(smentOrderList);
        }
        if (ObjUtil.isNotNull(smentOrder)) {
            bo.setPayMode(smentOrder.getPayMode());
            bo.setPaymentAmount(smentOrder.getPaymentAmount());
            bo.setPaymentDate(smentOrder.getPaymentDate());

            //付款凭证
            List<OcOrderAttachmentEntity> attachmentList = orderRepo.queryAttachmentByCondition(
                    OrderAttachmentCondition.builder().bizNo(smentOrder.getSmentNo())
                            .attachmentType(AttachmentTypeEnum.TYPE_30.getType()).build());
            bo.setAttachmentList(attachmentList);
        }


        return bo;
    }

    /**
     * 订单-卖家发货
     */
    public OrderShipmentsOptBO shipments(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        OrderShipmentsOptBO bo = new OrderShipmentsOptBO();
        //买家信息
        OrderBuySellCondition buySellCondition = new OrderBuySellCondition();
        buySellCondition.setOrderNo(orderNo);
        buySellCondition.setType(BuySelTypeEnum.TYPE_10.getType());
        List<OcOrderBuySellEntity> buyer = orderRepo.queryBuySellCondition(buySellCondition);
        if (IterUtil.isNotEmpty(buyer)) {
            bo.setBuyer(IterUtil.getFirst(buyer));
        }
        //地址信息
        bo.setAddress(orderRepo.queryOrderAddressByOrderNo(orderNo));
        //订单商品
        List<OcOrderProductEntity> productList = orderRepo.queryProductByOrderNo(orderNo);
        //商品 已发货数量
        bo.setProductList(this.assembleShipPrdQuantityList(productList, orderNo));

        return bo;
    }

    /**
     * 订单-卖家通知提货
     */
    public OrderPickupOptBO informPick(String orderNo) {
        OrderPickupOptBO bo = new OrderPickupOptBO();
        //地址信息
        bo.setAddress(orderRepo.queryOrderAddressByOrderNo(orderNo));

        return bo;
    }

    /**
     * 订单-卖家发起结算
     */
    public OrderSettlementBO settlement(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        //订单信息
        OrderCondition condition = new OrderCondition();
        condition.setOrderNo(orderNo);
        List<OcOrderInfoEntity> list = orderRepo.queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        OcOrderInfoEntity orderInfo = IterUtil.getFirst(list);

        //交付信息
        BigDecimal expenseAmount = BigDecimal.ZERO;
        Integer freightBearer = FreightBearerEnum.TYPE_30.getType();
        if (ObjUtil.equal(orderInfo.getDeliveryMode(), DeliveryModeEnum.TYPE_20.getType())) {
            OcOrderDeliveryEntity delivery = orderRepo.queryDeliveryByOrderNo(orderNo);
            if (ObjUtil.isNotNull(delivery)) {
                expenseAmount = delivery.getExpenseAmount();
                freightBearer = delivery.getFreightBearer();
            }
        }

        //已付款金额
        BigDecimal paidAmount = BigDecimal.ZERO;
        List<OcSmentOrderEntity> smentOrderList = orderRepo.querySmentOrderByCondition(SmentOrderCondition.builder()
                .orderNo(orderNo).smentStatus(SettlementStatusEnum.STATUS_230.getCode()).build());
        if (IterUtil.isNotEmpty(smentOrderList)) {
            paidAmount = smentOrderList.stream().map(OcSmentOrderEntity::getPaymentAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }

        //商品总金额
        BigDecimal productAmount = orderInfo.getProductTotalAmount();
        //订单商品
        List<OcOrderProductEntity> productList = orderRepo.queryProductByOrderNo(orderNo);
        if (IterUtil.isNotEmpty(productList)) {
            //提货商品、发货商品 数量
            this.assembleShipPrdQuantityList(productList, orderNo);
            //默认结算小计
            productList.forEach(prd -> prd.setSementSubtotal(prd.getSubtotal()));
        }

        OrderSettlementBO bo = new OrderSettlementBO();
        bo.setOrderNo(orderNo);
        bo.setOrderAmount(orderInfo.getOrderAmount());
        bo.setProductAmount(productAmount);
        bo.setDeliveryMode(orderInfo.getDeliveryMode());
        bo.setFreightBearer(freightBearer);
        bo.setExpenseAmount(expenseAmount);
        bo.setOtherAmount(BigDecimal.ZERO);
        bo.setPaidAmount(paidAmount);
        bo.setProductList(productList);
        bo.setPayment(orderRepo.queryPaymentByOrderNo(orderNo));

        return bo;
    }

    /**
     * 订单-查询提货单、发货单
     */
    public List<OrderShipmentsDetailBO> queryShipInfo(OrderShipCondition condition) {
        //提货单、发货单
        List<OcOrderShipEntity> shipList = orderRepo.queryOrderShipByCondition(condition);
        if (IterUtil.isEmpty(shipList)) {
            return null;
        }
        List<String> shipNoList = shipList.stream().map(OcOrderShipEntity::getShipNo).collect(Collectors.toList());

        Integer attachmentType;
        if (OrderShipTypeEnum.TYPE_10.getType().equals(condition.getType())) {
            attachmentType = AttachmentTypeEnum.TYPE_10.getType();
        } else {
            attachmentType = AttachmentTypeEnum.TYPE_20.getType();
        }

        //提货、收货凭证
        Map<String, List<OcOrderAttachmentEntity>> attachemntMap = null;
        List<OcOrderAttachmentEntity> attachmentList = orderRepo.queryAttachmentByCondition(
                OrderAttachmentCondition.builder().bizNoList(shipNoList)
                        .attachmentType(attachmentType).build());
        if (IterUtil.isNotEmpty(attachmentList)) {
            attachemntMap = attachmentList.stream().collect(Collectors.groupingBy(OcOrderAttachmentEntity::getBizNo));
        }

        List<OrderShipmentsDetailBO> boList = new ArrayList<>();
        for (OcOrderShipEntity ship : shipList) {
            OrderShipmentsDetailBO bo = new OrderShipmentsDetailBO();
            bo.setShip(ship);
            if (MapUtil.isNotEmpty(attachemntMap)) {
                bo.setAttachmentList(attachemntMap.get(ship.getShipNo()));
            }

            boList.add(bo);
        }

        return boList;
    }

    public List<OcOrderInfoEntity> batchOrderStatusQuery(List<String> pOrderNoList) {
        return orderRepo.batchOrderStatusQuery(pOrderNoList);
    }

    /**
     * 条件查询
     */
    public List<OcOrderInfoEntity> queryCondition(OrderCondition condition){
        return orderRepo.queryCondition(condition);
    }

    /**
     * 根据id查询结算单
     */
    public OcSmentOrderEntity queryOcSmentOrderById(Long id){
        return orderRepo.queryOcSmentOrderById(id);
    }

    public OrderPaymentDetailBO paymentItem(IdQuery query){
        OcSmentOrderEntity smentOrderEntity = orderRepo.queryOcSmentOrderById(query.getId());
        List<OcOrderAttachmentEntity> attachmentList = orderRepo.queryAttachmentByCondition(OrderAttachmentCondition.builder().bizNo(smentOrderEntity.getSmentNo())
                .attachmentType(AttachmentTypeEnum.TYPE_30.getType()).build());
        OrderPaymentDetailBO bo = new OrderPaymentDetailBO();
        bo.setOcSmentOrder(smentOrderEntity);
        bo.setAttachmentList(attachmentList);
        return bo;
    }

    /**
     * 获取订单的SPU信息列表
     *
     * @param query 订单SPU查询参数
     * @return SpuResponse 的列表，包含商品类目、名称、价格和数量
     */
    public List<SpuResponse> getOrderSpuInfo(OrderSpuQuery query) {
        // 构建查询条件，基于订单编号
        OrderProductSkuCondition condition = OrderProductSkuCondition.builder()
                .orderNo(query.getOrderNo())
                .build();

        // 查询订单对应的SKU实体列表
        List<OcOrderProductSkuEntity> skuEntities = orderRepo.queryProductSkuCondition(condition);

        // 将SKU实体转换为SPU响应对象列表
        return skuEntities.stream().map(skuEntity -> {
            return SpuResponse.builder()
                    .category(skuEntity.getCategoryName3()) // 商品类目（三级品类）
                    .name(skuEntity.getSkuName())           // 商品名称（品牌+三级品类+sku规格名称）
                    .price(skuEntity.getPriceFee())         // 商品价格（单价）
                    .quantity(skuEntity.getQuantity().setScale(0,  RoundingMode.DOWN))      // 商品数量保留整数位
                    .build();
        }).collect(Collectors.toList());
    }

    public Integer getOrderDealStatus(OrderDealStatusQuery dealStatusQuery) {
        OrderDealStatusCondition dealStatusCondition = BeanUtil.toBean(dealStatusQuery, OrderDealStatusCondition.class);
        return orderRepo.getOrderDealStatus(dealStatusCondition);
    }


    /**
     * 订单信息
     */
    public List<OrderItemsEntity> getOrderItemsList(List<String> orderIds) {
        return orderRepo.getOrderItemsList(orderIds);
    }

    /**
     * 商品信息
     */
    public List<OcOrderProductSkuEntity> getOrderProduct(List<String> orderIds) {
        OrderProductSkuCondition condition = new OrderProductSkuCondition();
        condition.setOrderNoList(orderIds);
        return orderRepo.queryProductSkuCondition(condition);
    }
}
