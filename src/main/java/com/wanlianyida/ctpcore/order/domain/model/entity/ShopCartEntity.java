package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 购物车实体
 */
@Data
public class ShopCartEntity implements Serializable {
    private static final long serialVersionUID = 7314524085040736537L;
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 商家公司id
     */
    private String companyId;

    /**
     * 商家公司
     */
    private String companyName;

    /**
     * spuCode
     */
    private String supCode;

    /**
     * skuCode
     */
    private String skuCode;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 是否选中
     */
    private boolean select;

    /**
     * 添加时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    
}
