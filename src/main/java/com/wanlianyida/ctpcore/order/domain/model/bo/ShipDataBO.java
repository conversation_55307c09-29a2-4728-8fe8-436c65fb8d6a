package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShipDataBO {
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 发货单号
     */
    private String shipNo;

    private OcOrderInfoEntity orderInfoForUpdate;

    /**
     * 发货单
     */
    private OcOrderShipEntity ocOrderShipEntityForInsert;

    private OcOrderShipEntity ocOrderShipEntityForUpdate;

    private List<OcOrderShipEntity> ocOrderShipEntityListForUpdate;

    /**
     * 发货单商品
     */
    private List<OcOrderShipProductEntity> ocOrderShipProductEntityListForInsert = new ArrayList<>();

    private List<OcOrderShipProductEntity> ocOrderShipProductEntityListForUpdate = new ArrayList<>();

    /**
     * 发货单商品数量
     */
    private List<OcOrderShipQuantityEntity> ocOrderShipQuantityEntityListForInsert = new ArrayList<>();

    private List<OcOrderShipQuantityEntity> ocOrderShipQuantityEntityListForUpdate = new ArrayList<>();

    /**
     * 提货单车辆
     */
    private List<OcOrderPickupCarEntity> ocOrderPickupCarEntityListForInsert = new ArrayList<>();

    private List<OcOrderPickupCarEntity> ocOrderPickupCarEntityListForUpdate = new ArrayList<>();

    /**
     * 附件
     */
    private List<OcOrderAttachmentEntity> ocOrderAttachmentEntityListForInsert = new ArrayList<>();
    private List<OcOrderAttachmentEntity> ocOrderAttachmentEntityListForUpdate = new ArrayList<>();


}
