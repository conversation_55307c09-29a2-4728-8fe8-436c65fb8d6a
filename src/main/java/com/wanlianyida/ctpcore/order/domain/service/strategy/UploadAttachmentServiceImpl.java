package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.ctpcore.order.domain.assembler.OrderStatusAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.attach.AttachmentBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.AttachmentEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.AttachmentTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderActionEnum;
import com.wanlianyida.ctpcore.order.infrastructure.event.AttachmentEvent;
import com.wanlianyida.ctpcore.order.infrastructure.event.EventPublisher;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;

import javax.annotation.Resource;

/**
 * 上传附件
 * <AUTHOR>
 * @since 2024/12/12/15:52
 */
@Service("uploadAttachmentService")
public class UploadAttachmentServiceImpl extends OrderStatusChangeService {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private EventPublisher eventPublisher;


    @Override
    protected ResultMode checkActionCondition(OrderStatusBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        //ACTION_60("60","上传收货凭证","uploadAttachmentService"),
        //订单状态=部分发货、待收货、部分收货时，卖家端均显示上传收货凭证按钮
        //ACTION_100("100","上传提货凭证","uploadAttachmentService"),
        //订单状态=待提货、部分提货时，卖家端均显示上传收货凭证按钮
        String action = bo.getAction();
        HashSet<Integer> confirmReceiptStatus = OrderStatusAssembler.getCheckStatusCondition(bo.getAction());
        if(!confirmReceiptStatus.contains(ocOrderInfo.getStatus())){
            return ResultMode.fail(OrderActionEnum.ACTION_60.getAction().equals(action) ?
                    CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_RECEIPT.getMsg()  : CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_PICK_UP.getMsg());
        }
        String shipNo = bo.getOcOrderShip().getShipNo();
        if (StringUtils.isBlank(shipNo)) {
            return ResultMode.fail(OrderActionEnum.ACTION_60.getAction().equals(action) ?
                    CtpCoreOrderExceptionEnum.ERROR_ORDER_SHIP_IS_NULL.getMsg() : CtpCoreOrderExceptionEnum.ERROR_ORDER_PICK_UP_IS_NULL.getMsg());
        }
        return ResultMode.success();
    }

    @Override
    protected HandelStatusBO packActionData(OrderStatusBO bo) {
        String action = bo.getAction();
        HandelStatusBO handelStatusBO = new HandelStatusBO();
        List<AttachmentBO> attachments = bo.getAttachments();
        String s = OrderActionEnum.ACTION_60.getAction().equals(action) ? AttachmentTypeEnum.TYPE_20.getType() + "" : AttachmentTypeEnum.TYPE_10.getType() + "";
        attachments.forEach(a ->
                a.setBizNo(bo.getOcOrderShip().getShipNo()).setAttachmentType(s)
        );
        handelStatusBO.setAttachment(BeanUtil.copyToList(attachments, AttachmentEntity.class));
        return handelStatusBO;
    }

    @Override
    protected void asynHandle(HandelStatusBO bo) {
        List<AttachmentEntity> attachment = bo.getAttachment();
        eventPublisher.attachmentEvent(BeanUtil.copyToList(attachment, AttachmentEvent.class));
    }

    @Override
    protected int handleStatus(HandelStatusBO bo) {
        return 1;
    }
}
