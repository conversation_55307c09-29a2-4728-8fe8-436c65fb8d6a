package com.wanlianyida.ctpcore.order.domain.model.message;

import com.wanlianyida.ctpcore.order.infrastructure.enums.pay.FinishTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Getter
@Setter
public class OrderFinishMessage implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 类型 10 付款
     * @see FinishTypeEnum
     */
    private Integer type;

    /**
     * 支付时间
     */
    private Date paymentDate;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人
     */
    private String operator;


}
