package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单结算商品
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmentPrdCondition {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 结算单id
     */
    private Long smentId;

    /**
     * 订单商品id
     */
    private Long productId;

}
