package com.wanlianyida.ctpcore.order.domain.model.condition;

import com.wanlianyida.ctpcore.order.infrastructure.enums.ActionRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 发货单查询
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderShipListCondition {

    /**
     * 操作来源[10-买家，20-卖家]
     *
     * @see ActionRoleEnum
     */
    private Integer actionRole;

    /**
     * 类型 10发货 20提货
     */
    private Integer type;

    /**
     * 综合状态[10-已取消,20-待发货,30-部分发货,40-已发货,50-部分收货,60-已收货]
     */
    private Integer summaryStatus;

    /**
     * 发货状态
     */
    private Integer shipStatus;

    /**
     * 指定提货人状态
     */
    private Integer pickupAssigneeStatus;

    /**
     * 确认收货状态
     */
    private Integer receiveStatus;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 发货单号
     */
    private String shipNo;

    /**
     * 发货单号列表
     */
    private List<String> shipNoList;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

}
