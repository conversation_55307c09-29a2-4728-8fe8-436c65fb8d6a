package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单发货单商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OcOrderShipProductEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货/提货单号
     */
    private String bizNo;

    /**
     * 订单商品id
     */
    private Long orderProductId;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 发货数量
     */
    private BigDecimal shipmentQuantity;

    /**
     * 收货数量
     */
    private BigDecimal receiceQuantity;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /* === 订单商品细信息 ===*/
    /**
     * 商品照片
     */
    private String picture;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 计价单位
     */
    private Integer priceUnit;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 数量单位
     */
    private Integer purchaseQuantityUnit;

    /**
     * 总发货数量
     */
    private BigDecimal totalShipmentQuantity;

    /**
     * 实际收货数量
     */
    private BigDecimal actualReceiveQuantity;
}
