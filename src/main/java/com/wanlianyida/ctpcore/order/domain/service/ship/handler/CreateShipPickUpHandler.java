package com.wanlianyida.ctpcore.order.domain.service.ship.handler;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.order.domain.assembler.ShipAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.ShipDataBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.domain.service.ship.handler.support.ShipHandlerSupportService;
import com.wanlianyida.ctpcore.order.infrastructure.enums.BizOperateTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.DeliveryModeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderShipTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.ctpcore.order.infrastructure.factory.factory.ShipProcessor;
import com.wanlianyida.ctpcore.order.interfaces.facade.command.ship.CreateShipPickupCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.enums.NumberBizTypeEnum;
import com.wanlianyida.framework.ctpcore.utils.NumberGenerator;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 创建提货单
 */
@Service
@Slf4j
public class CreateShipPickUpHandler implements ShipProcessor<CreateShipPickupCommand> {
    @Resource
    ShipHandlerSupportService shipHandlerSupportService;

    @Resource
    OrderStatusRepo orderStatusRepo;

    @Resource
    NumberGenerator numberGenerator;

    @Override
    public ResultMode<ShipDataBO> handleData(CreateShipPickupCommand command) {
        ContextBO context = new ContextBO();
        context.setCommand(command);
        // 校验
        ResultMode<?> validation = validation(context);
        if (!validation.isSucceed()) {
            return ResultMode.fail(validation.getMessage());
        }
        OcOrderInfoEntity orderInfo = context.getOrderInfo();
        // 查询发货商品数量
        List<OcOrderShipQuantityEntity> quantityEntityList = orderStatusRepo.queryShipQuantityOrderNo(orderInfo.getOrderNo());
        // 校验总发货数量，防止超发
        ResultMode<Void> quantityCheckRes = shipHandlerSupportService.checkTotalShipmentQuantity(command.getShipProductList(), quantityEntityList);
        if (!quantityCheckRes.isSucceed()) {
            return ResultMode.fail(quantityCheckRes.getMessage());
        }

        // 累加总发货数量
        List<OcOrderShipQuantityEntity> newQuantityList = shipHandlerSupportService.plusTotalShipmentQuantity(command.getShipProductList(), quantityEntityList);
        // 校验是否已全部发完(没用到)
        Boolean allProductsShipped = shipHandlerSupportService.validateTotalProductShipFinish(newQuantityList);

        // 查询订单下的商品信息
        List<OcOrderProductEntity> orderProductEntityList = shipHandlerSupportService.getOrderProductList(orderInfo.getOrderNo());
        context.setOrderProductEntityList(orderProductEntityList);

        /**
         * 1. 创建提货单
         * 2. 创建提货单商品数量统计
         * 3. 订单商品数量统计：更新总发货数量
         * ==处理完成==
         */
        // 提货单
        OcOrderShipEntity shipEntity = buildOrderShipEntity(context);
        // 提货单数量统计
        List<OcOrderShipProductEntity> shipProductEntityList = buildShipProductEntity(context, shipEntity);

        // 组装数据
        ShipDataBO shipDataBO = ShipDataBO.builder()
                .orderNo(context.getOrderInfo().getOrderNo())
                .shipNo(shipEntity.getShipNo())
                .ocOrderShipEntityForInsert(shipEntity)
                .ocOrderShipProductEntityListForInsert(shipProductEntityList)
                .ocOrderShipQuantityEntityListForUpdate(newQuantityList)
                .build();
        log.info("提货单创建：单号={}，是否全部发完={}", shipEntity.getShipNo(), allProductsShipped);
        log.info("bo={}", JSONUtil.toJsonStr(shipDataBO));
        return ResultMode.success(shipDataBO);
    }

    @Override
    public boolean handlerType(String type) {
        return BizOperateTypeEnum.CREATE_SHIP_PICK_UP.getAction().equals(type);
    }

    /**
     * 构建提货单
     */
    private OcOrderShipEntity buildOrderShipEntity(ContextBO contextBO) {
        String shipNo = numberGenerator.getBizId(NumberBizTypeEnum.PICK_UP.getCode());
        OcOrderShipEntity shipRecord = ShipAssembler.createShipRecord(OrderShipTypeEnum.TYPE_10.getType(), shipNo, contextBO.getOrderInfo(), contextBO.userId);
        shipRecord.setShipDate(contextBO.getCommand().getShipDate());
        return shipRecord;
    }

    /**
     * 创建提货单数量表信息
     */
    private List<OcOrderShipProductEntity> buildShipProductEntity(ContextBO contextBO, OcOrderShipEntity ship) {
        return ShipAssembler.buildShipProductEntityList(
                contextBO.getOrderProductEntityList(), contextBO.getCommand().getShipProductList(), contextBO.getOrderInfo(), ship, contextBO.getUserId());
    }

    private ResultMode<?> validation(ContextBO context) {
        CreateShipPickupCommand command = context.getCommand();
        OcOrderInfoEntity orderInfo = shipHandlerSupportService.getOcOrderInfo(command.getOrderNo());
        // 订单不存在
        if (orderInfo == null) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_NO.getMsg());
        }
        context.setOrderInfo(orderInfo);
        // 校验交货方式 自提场景
        if (ObjUtil.notEqual(orderInfo.getDeliveryMode(), DeliveryModeEnum.TYPE_10.getType())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_TYPE_NOT_SEND_OUT.getMsg());
        }
        // 只校验订单主状态
        if (ObjUtil.notEqual(OrderStatusEnum.STATUS_50.getStatus(), orderInfo.getStatus())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_50.getMsg());
        }
        return ResultMode.success();
    }

    @Data
    public static class ContextBO {
        private String orderNo;
        private OcOrderInfoEntity orderInfo;
        private CreateShipPickupCommand command;
        private List<OcOrderProductEntity> orderProductEntityList;

        private String userId;
        private String userName;
    }

}
