package com.wanlianyida.ctpcore.order.domain.model.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class StatisticsBusinessCondition {
    /**
     * 买家企业ID
     */
    @NotEmpty(message = "companyId 不能为空")
    String companyId;

    @ApiModelProperty("时间范围天数：7, 15, 30")
    Integer timePeriod;
    
    @ApiModelProperty("工作台类型、卖家seller、buyer买家")
    String workbenchesType;

}
