package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.order.domain.assembler.OrderStatusAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderShipProductBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipProductEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipQuantityEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipOptEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderException;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.enums.NumberBizTypeEnum;
import com.wanlianyida.framework.ctpcore.utils.NumberGenerator;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/12/15/19:33
 */
@Slf4j
@Service
public class OrderShipBaseService {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private NumberGenerator numberGenerator;


    public HandelStatusBO packActionData(OrderStatusBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        OcOrderShipEntity ocOrderShip = new OcOrderShipEntity();
        OrderStatusAssembler.buildOcOrderShipByInfo(ocOrderInfo, ocOrderShip, ShipOptEnum.STATUS1.getCode());
        ocOrderShip.setShipDate(bo.getShipDate());
        String orderNo = bo.getOrderNo();
        // 查询发货商品数量信息
        List<OcOrderShipQuantityEntity> quantityEntityList = orderStatusRepo.queryShipQuantityOrderNo(orderNo);
        // 获取表单中的发货商品信息
        List<OrderShipProductBO> orderShipProduct = bo.getOrderShipProduct();
        Map<Long, OrderShipProductBO> productBOMap = orderShipProduct.stream().collect(Collectors.toMap(OrderShipProductBO::getOrderProductId, Function.identity()));
        //判断是否超发
        BigDecimal totalShipQuantity = BigDecimal.ZERO;
        BigDecimal orderQuantity =  BigDecimal.ZERO;
        for (OcOrderShipQuantityEntity entity : quantityEntityList) {
            Long orderProductId = entity.getOrderProductId();
            OrderShipProductBO po = productBOMap.get(orderProductId);
            if (ObjUtil.isNull(po)) {
                continue;
            }
            // 获取表单中发货数量
            BigDecimal shipmentQuantity = po.getShipmentQuantity();
            // 获取订单中的购买数量
            orderQuantity = orderQuantity.add(entity.getOrderQuantity());
            // 获取订单中的已发货数量
            BigDecimal pickupQuantity = entity.getShipPickupQuantity();
            // 更新已发货数量
            entity.setShipPickupQuantity(pickupQuantity.add(shipmentQuantity));
            // 累加发货数量  当前 + 历史的
            totalShipQuantity = totalShipQuantity.add(shipmentQuantity).add(pickupQuantity);
            if (totalShipQuantity.compareTo(orderQuantity) > 0) {
                throw new CtpCoreOrderException(CtpCoreOrderExceptionEnum.ERROR_ORDER_GOODS_NUM_FULL.getMsg());
            }
        }
        // 处理订单状态  申请提货不改状态  确认提货改状态
        bo.setQuantityEntityList(quantityEntityList);
        // 设置单据类型
        ocOrderShip.setType(ocOrderInfo.getDeliveryMode());
        //  `ship_status` smallint(6) NOT NULL DEFAULT '10' COMMENT '发货/提货 状态 10 未发货 20已发货',
        ocOrderShip.setShipStatus(20);
        //提货单号：TH+日期+6位随机码。如TH 20241024 1234
        //发货单号：FH+日期+8位随机码。如FH202410241234
        // 生成发货单号
        String shipNo = numberGenerator.getBizId(NumberBizTypeEnum.PICK_UP.getCode());
        // 设置发货单号
        ocOrderShip.setShipNo(shipNo);
        bo.getOrderShipProduct().forEach(e -> e.setBizNo(shipNo));

        //设置参数
        HandelStatusBO handelStatusBO = new HandelStatusBO();
        handelStatusBO.setAction(bo.getAction());
        handelStatusBO.setOcOrderShipEntityForInsert(ocOrderShip);
        handelStatusBO.setPickupPrdInfoList(BeanUtil.copyToList(orderShipProduct, OcOrderShipProductEntity.class));
        handelStatusBO.setOrderInfoForUpdate(BeanUtil.copyProperties(ocOrderInfo, OcOrderInfoEntity.class));
        handelStatusBO.setOrderShipQuantityEntityListForUpdate(quantityEntityList);
        return handelStatusBO;
    }
}
