package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单交付信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Getter
@Setter
public class OcOrderDeliveryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 交货方式10:自提20:物流配送
     */
    private Integer deliveryMode;

    /**
     * 仓库id
     */
    private Long orderAddressId;

    /**
     * 订单地址
     */
    private OcOrderAddressEntity address;

    /**
     * 交货日期
     */
    private String deliveryDate;

    /**
     * 10买家承担20卖家承担30无运费
     */
    private Integer freightBearer;

    /**
     * 运费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 订单协议时间
     */
    private Date agreementTime;

}
