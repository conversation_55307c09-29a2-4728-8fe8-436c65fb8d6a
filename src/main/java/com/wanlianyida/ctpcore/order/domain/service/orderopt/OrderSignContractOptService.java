package com.wanlianyida.ctpcore.order.domain.service.orderopt;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.orderopt.OrderSignContractOptBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.OcOrderPaymentCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 合同签署
 */
@Slf4j
@Service("signContractOpt")
public class OrderSignContractOptService extends OrderOptCommonService<OrderSignContractOptBO> {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Override
    public Class<OrderSignContractOptBO> optParamObj() {
        return OrderSignContractOptBO.class;
    }

    /**
     * 数据校验
     */
    @Override
    protected ResultMode checkParam(OrderSignContractOptBO bo) {
        //订单状态校验
        OcOrderInfoEntity ocOrderInfo = bo.getOrderInfo();
        if (!ObjUtil.equals(ocOrderInfo.getStatus(), OrderStatusEnum.STATUS_10.getStatus())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_20002.getCode(), CtpCoreOrderExceptionEnum.ERROR_20002.getMsg());
        }
        if (StringUtils.isBlank(bo.getContractNo())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_00001.getCode(), CtpCoreOrderExceptionEnum.ERROR_00001.getMsg());
        }
        return ResultMode.success();
    }

    /**
     * 封装数据
     */
    @Override
    protected void assembleOrderOptData(OrderSignContractOptBO bo) {
        String orderNo = bo.getOrderNo();
        //判断付款条件
        OcOrderPaymentEntity ocOrderPayment = orderStatusRepo.getOcOrderPayment(new OcOrderPaymentCondition().setRfOrderNo(orderNo));
        OcOrderDeliveryEntity ocOrderDelivery = orderStatusRepo.getOcOrderDelivery(orderNo);
        bo.setOrderPayment(ocOrderPayment);

        Integer orderStatus = OrderStatusEnum.STATUS_20.getStatus();
        Integer orderSubStatus = 0;
        Integer payStatus = OrderPayStatusEnum.PAY_STATUS_110.getPayStatus();
        Integer prepaidStatus = OrderPrepaidStatusEnum.STATUS_110.getCode();

        if (ObjUtil.equal(ocOrderPayment.getPaymentTerms(), PaymentTermsEnum.TERMS_20.getTerms())) {
            orderStatus = OrderStatusEnum.STATUS_30.getStatus();
            orderSubStatus = OrderSubStatusEnum.SUB_STATUS_30.getSubStatus();
            payStatus = OrderPayStatusEnum.PAY_STATUS_100.getPayStatus();
            prepaidStatus = OrderPrepaidStatusEnum.STATUS_0.getCode();
            if (ObjUtil.equal(ocOrderDelivery.getDeliveryMode(), DeliveryModeEnum.TYPE_10.getType()) || ObjUtil.equal(ocOrderDelivery.getDeliveryMode(), DeliveryModeEnum.TYPE_30.getType())) {
                orderStatus = OrderStatusEnum.STATUS_50.getStatus();
                orderSubStatus = OrderSubStatusEnum.SUB_STATUS_50.getSubStatus();
            }
        }
        OcOrderInfoEntity updateOrderInfo = super.assembleOrderUpdCommon(bo);
        updateOrderInfo.setStatus(orderStatus);
        updateOrderInfo.setSubStatus(orderSubStatus);
        updateOrderInfo.setPayStatus(payStatus);
        updateOrderInfo.setPrepaidStatus(prepaidStatus);
        // 单笔合同编号
        updateOrderInfo.setContractNo(bo.getContractNo());
        if(!StringUtils.isBlank(bo.getLongtermContractNo())){
            // 长期合同编号
            updateOrderInfo.setLongtermContractNo(bo.getLongtermContractNo());
        }
        bo.setOrderInfoUpd(updateOrderInfo);
        //订单状态
        super.assembleOrderStatusInit(bo);
        //履约节点
        OcOrderNodeEntity ocOrderNodeEntity = super.assembleOrderNodeInit(bo);
        ocOrderNodeEntity.setNodeStatus(OrderNodeEnum.STATUS_20.getStatus());
        List<OcOrderNodeEntity> ocOrderNodeEntities = new ArrayList<>();
        ocOrderNodeEntities.add(ocOrderNodeEntity);
        bo.setOrderNodeInsert(ocOrderNodeEntities);
    }

}
