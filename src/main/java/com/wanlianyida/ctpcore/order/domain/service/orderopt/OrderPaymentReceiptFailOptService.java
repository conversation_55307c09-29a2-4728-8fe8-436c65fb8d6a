package com.wanlianyida.ctpcore.order.domain.service.orderopt;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.orderopt.OrderPaymentOptBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 买家付款-预付款、尾款  失败/驳回
 */
@Slf4j
@Service("paymentReceiptFailOpt")
public class OrderPaymentReceiptFailOptService extends OrderOptCommonService<OrderPaymentOptBO> {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private OrderRepo orderRepo;

    @Override
    public Class<OrderPaymentOptBO> optParamObj() {
        return OrderPaymentOptBO.class;
    }

    /**
     * 数据校验
     */
    @Override
    protected ResultMode checkParam(OrderPaymentOptBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOrderInfo();
        if (!ObjUtil.equal(ocOrderInfo.getPayStatus(), OrderPayStatusEnum.PAY_STATUS_120.getPayStatus())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_STATUS_ERROR_NOT_PENDING.getMsg());
        }
        //预付款状态校验
        if (StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_22.getOpt())
                && ObjUtil.equal(ocOrderInfo.getPrepaidStatus(), bo.getPaymentStatus()) ) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_STATUS_ERROR_NOT_PENDING.getMsg());
        }
        //尾款状态校验
        if (StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_62.getOpt())
                && ObjUtil.equal(ocOrderInfo.getPrepaidStatus(), bo.getPaymentStatus()))  {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_STATUS_ERROR_NOT_PENDING.getMsg());
        }
        return ResultMode.success();
    }

    /**
     * 封装数据
     */
    @Override
    protected void assembleOrderOptData(OrderPaymentOptBO bo) {
        OcOrderInfoEntity updateOrderInfo = super.assembleOrderUpdCommon(bo);
        OcOrderInfoEntity orderInfo = bo.getOrderInfo();
        if (ObjUtil.equal(orderInfo.getStatus(), OrderStatusEnum.STATUS_20.getStatus())) {
            //预付款
            updateOrderInfo.setPrepaidStatus(bo.getPaymentStatus());
        } else {
            //尾款
            updateOrderInfo.setBalancePaymentStatus(bo.getPaymentStatus());
        }
        updateOrderInfo.setPayStatus(OrderPayStatusEnum.PAY_STATUS_110.getPayStatus());
        bo.setOrderInfoUpd(updateOrderInfo);

    }

}
