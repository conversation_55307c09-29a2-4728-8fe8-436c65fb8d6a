package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单取消
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
public class OcOrderCancelEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 10买家取消20卖家取消
     */
    private Integer cancelSource;

    /**
     * 申请取消人
     */
    private String canceller;

    /**
     * 申请取消人账号
     */
    private String cancellerAccount;

    /**
     * 申请取消原因
     */
    private String cancelReason;

    /**
     * 申请取消时间
     */
    private Date cancelDate;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

}
