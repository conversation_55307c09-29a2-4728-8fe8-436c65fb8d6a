package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * sku表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
public class CtpProductSkuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SKU ID
     */
    private Long id;

    /**
     * 企业ID（店铺ID）
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 商品SPU ID
     */
    private Long relSpuId;

    /**
     * SKU编号
     */
    private String skuCode;

    /**
     * SKU规格名称(规格属性值value拼接)
     */
    private String skuSpecificationName;

    /**
     * SKU名称（品牌+三级品类+sku规格名称）
     */
    private String skuName;

    /**
     * 简单说明
     */
    private String skuDesc;

    /**
     * 上架状态（10未上架 20已上架 30违规下架）
     */
    private Integer onSaleStatus;

    /**
     * 上架时间
     */
    private Date onSaleDate;

    /**
     * 下架时间
     */
    private Date outSaleDate;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 单价
     */
    private BigDecimal priceFee;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单位转换 1吨=x吨度(计价单位转换为计量单位)
     */
    private BigDecimal unitTransfer;

    /**
     * 是否现货:1是 0否;
     */
    private Integer isSpotGoods;

    /**
     * 交货期限（天）
     */
    private String deliveryPeriod;

    /**
     * 计重方式（10理重 20过磅）
     */
    private String weightMeasurementType;

    /**
     * 商品照片
     */
    private String picture;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 更新人名字
     */
    private String updaterName;

    /**
     * 是否删除 1删除 0未删除
     */
    private Boolean deleted;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 计价单位
     */
    private BigDecimal priceUnit;


}
