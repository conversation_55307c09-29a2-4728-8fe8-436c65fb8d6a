package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单结算查询
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmentCondition {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 结算状态:10待结算 20 部分结算 20已结算
     */
    private Integer settlementStatus;

    /**
     * 结算状态:10待结算 20 部分结算 20已结算
     */
    private List<Integer> settlementStatusList;

}
