package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 卖家买家关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OcProductResellMerchantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SKU ID
     */
    private Long id;

    /**
     * 买家企业ID
     */
    private String buyerCompanyId;

    /**
     * 买家企业名称
     */
    private String buyerCompanyName;

    /**
     * 卖家企业ID
     */
    private String sellerCompanyId;

    /**
     * 卖家企业名
     */
    private String sellerCompanyName;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否删除 1删除 0未删除
     */
    private Integer deleted;
}
