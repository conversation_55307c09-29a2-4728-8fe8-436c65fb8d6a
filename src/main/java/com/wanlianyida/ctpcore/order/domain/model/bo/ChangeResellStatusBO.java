package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangeResellStatusBO {
    List<ItemBO> itemList;

    @Data
    public static class ItemBO {
        private String orderNo;
        private String orderSkuCode;
        Integer resellStatus;
        String resellSkuCode;;
    }
}
