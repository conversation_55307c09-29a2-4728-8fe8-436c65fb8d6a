package com.wanlianyida.ctpcore.order.domain.service.ship.handler;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.ctpcore.order.domain.model.bo.ShipDataBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.QuotationShipStatusCallbackCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.LogisticsInquiryEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderPickupCarEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderShipRepo;
import com.wanlianyida.ctpcore.order.domain.service.ship.handler.support.ShipHandlerSupportService;
import com.wanlianyida.ctpcore.order.infrastructure.enums.BizOperateTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderShipTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipAllShipEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.factory.factory.ShipProcessor;
import com.wanlianyida.ctpcore.order.infrastructure.util.BizMsgUtilService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import static com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipSignStatusEnum.STATUS_20;

@Service
@Slf4j
public class WaybillStatusReceiveHandler implements ShipProcessor<QuotationShipStatusCallbackCondition> {

    @Resource
    private ShipHandlerSupportService shipHandlerSupportService;

    @Resource
    private OrderShipRepo orderShipRepo;

    @Resource
    private BizMsgUtilService bizMsgUtilService;



    @Override
    public ResultMode<ShipDataBO> handleData(QuotationShipStatusCallbackCondition command) {
        log.info("运单状态回调数据:{}", command);
        ContextBO context = new ContextBO();
        context.setCommand(command);
        // 校验
        ResultMode<OcOrderShipEntity> validation = validation(context);
        log.info("运单状态回调数据校验结果：{}", validation);
        if (!validation.isSucceed()) {
            return ResultMode.fail(validation.getMessage());
        }
        OcOrderShipEntity orderShipEntity = context.getOrderShipEntity();
        // 单据更新
        OcOrderShipEntity shipUpdate = orderShipEntity.getType().equals(OrderShipTypeEnum.TYPE_10.getType())
                ? null // 提货不关注此状态
                : getShipStatusUpdateForShip(context);
        // 数据组装
        ShipDataBO shipDataBO = ShipDataBO.builder()
                .orderNo(context.getOrderInfo().getOrderNo())
                .shipNo(context.getOrderShipEntity().getShipNo())
                .build();
        if (shipUpdate != null) {
            shipDataBO.setOcOrderShipEntityForUpdate(shipUpdate);
        }
        log.info("运单状态回调数据组装结果：{}, command:{}", JSONUtil.toJsonStr(shipDataBO), JSONUtil.toJsonStr(command));
        return ResultMode.success(shipDataBO);
    }

    /**
     * 发货单状态变更
     */
    private OcOrderShipEntity getShipStatusUpdateForShip(ContextBO context) {
        boolean isAllShip = ObjUtil.equal(context.getCommand().getAllShippedStatusFlag(), ShipAllShipEnum.STATUS_1.getCode());
        boolean isAllSigned = ObjUtil.equal( context.getCommand().getAllSignedFlag(), ShipAllShipEnum.STATUS_1.getCode());;
        OcOrderShipEntity updated = new OcOrderShipEntity();
        updated.setId(context.getOrderShipEntity().getId());
        OcOrderShipEntity orderShipEntity = context.getOrderShipEntity();
        if (!isAllShip) {
            return null;
        }

        // 已取消、已送达 不需要处理
        if (ObjUtil.equal(orderShipEntity.getShipStatus(), ShipShipStatusEnum.STATUS_99.getCode())
                || ObjUtil.equal(orderShipEntity.getShipStatus(), ShipShipStatusEnum.STATUS_30.getCode())) {
            return null;
        }

        // 已发货、部分送达 -> 变更为已送达
        if (ObjUtil.equal(orderShipEntity.getShipStatus(), ShipShipStatusEnum.STATUS_25.getCode())
                || ObjUtil.equal(orderShipEntity.getShipStatus(), ShipShipStatusEnum.STATUS_20.getCode())) {
            if (isAllSigned) {
                updated.setShipStatus(ShipShipStatusEnum.STATUS_30.getCode());    // 设置为已送达
                updated.setSignStatus(STATUS_20.getCode());
                sendArriveMsg(context.getOrderInfo().getBuyerCompanyId(), context.getOrderInfo().getBuyerCompanyName(), context.getOrderInfo().getOrderNo());
                return updated;
            }
        }

        // 查询是否有已签收的运单
        Boolean exists = existsShipSigned(orderShipEntity.getShipNo());

        // 未发货、部分发货
        if (ObjUtil.equal(orderShipEntity.getShipStatus(), ShipShipStatusEnum.STATUS_10.getCode())
                || ObjUtil.equal(orderShipEntity.getShipStatus(), ShipShipStatusEnum.STATUS_11.getCode())) {
            if (!isAllSigned) {
                if (exists) {
                    updated.setShipStatus(ShipShipStatusEnum.STATUS_25.getCode()); // 部分送达
                } else {
                    updated.setShipStatus(ShipShipStatusEnum.STATUS_20.getCode()); // 已发货
                }
                return updated;
            } else {
                updated.setShipStatus(ShipShipStatusEnum.STATUS_30.getCode());     // 已送达
                updated.setSignStatus(STATUS_20.getCode());
                sendArriveMsg(context.getOrderInfo().getBuyerCompanyId(), context.getOrderInfo().getBuyerCompanyName(), context.getOrderInfo().getOrderNo());
                return updated;
            }
        }
        return null;
    }

    // 查询是否有已签收的运单
    private Boolean existsShipSigned(String shipNo) {
        //根据发货单号查询运单
        List<OcOrderPickupCarEntity> ocOrderPickupCarEntities = orderShipRepo.queryOrderPickupCarByShipNo(shipNo);
        if (ocOrderPickupCarEntities != null && !ocOrderPickupCarEntities.isEmpty()) {
            for (OcOrderPickupCarEntity ocOrderPickupCarEntity : ocOrderPickupCarEntities) {
                if (ObjUtil.equal(STATUS_20.getCode(), ocOrderPickupCarEntity.getSignStatus())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean handlerType(String type) {
        return BizOperateTypeEnum.WAY_BILL_STATUS_CHANGE.getAction().equals(type);
    }

    /**
     * 校验方法
     *
     * @param context 1
     * @return 2
     */
    private ResultMode<OcOrderShipEntity> validation(ContextBO context) {
        LogisticsInquiryEntity inquiry = shipHandlerSupportService.getInquiryByInquiryNo(context.getCommand().getInquiryOrderNo());
        if (inquiry == null) {
            return ResultMode.fail("询价单不存在");
        }
        context.setInquiryEntity(inquiry);
        OcOrderShipEntity ocOrderShip = shipHandlerSupportService.getOcOrderShip(inquiry.getShipNo());
        if (ocOrderShip == null) {
            return ResultMode.fail("发货单不存在");
        }
        context.setOrderShipEntity(ocOrderShip);
        OcOrderInfoEntity orderInfo = shipHandlerSupportService.getOcOrderInfo(ocOrderShip.getOrderNo());
        if (orderInfo == null) {
            return ResultMode.fail("订单不存在");
        }
        context.setOrderInfo(orderInfo);
        return ResultMode.success();
    }

    /**
     * 发送送达消息
     */
    private void sendArriveMsg(String companyId, String companyName, String orderNo) {
        bizMsgUtilService.sendOrderArriveMsg(companyId, companyName, orderNo, Lists.newArrayList());
        bizMsgUtilService.sendOrderArriveWxMsg(companyId, companyName, orderNo, Lists.newArrayList());
    }

    @Data
    public static class ContextBO {
        private QuotationShipStatusCallbackCondition command;
        private OcOrderInfoEntity orderInfo;
        private OcOrderShipEntity orderShipEntity;
        private LogisticsInquiryEntity inquiryEntity;

        private String userId;
        private String userName;
    }
}
