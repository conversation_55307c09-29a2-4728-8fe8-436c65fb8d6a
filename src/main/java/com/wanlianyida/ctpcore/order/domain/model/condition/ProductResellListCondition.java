package com.wanlianyida.ctpcore.order.domain.model.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ProductResellListCondition {
    @ApiModelProperty("商品sku编码")
    private String skuCode;

    @ApiModelProperty("3级品类名称")
    private String categoryName3;

    @ApiModelProperty("规格")
    private String skuSpecificationName;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("卖家名称")
    private String sellerCompanyName;

    @ApiModelProperty("卖家Id")
    private String sellerCompanyId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("采购日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDateStart;

    @ApiModelProperty("采购日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDateEnd;

    // 转卖状态（10未转卖 20已转卖）
    private Integer resellStatus;

    // 企业ID
    private String companyId;

    // 品类ID
    private String categoryId;
}
