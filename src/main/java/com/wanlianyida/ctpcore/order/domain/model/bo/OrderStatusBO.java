package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.bo.attach.AttachmentBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.status.OrderStartSmentBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipProductEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipQuantityEntity;
import com.wanlianyida.ctpcore.order.infrastructure.enums.ActionRoleEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderActionEnum;
import com.wanlianyida.ctpcore.order.interfaces.facade.command.OcOrderShipCommand;
import com.wanlianyida.ctpcore.order.interfaces.facade.command.ShipProductCommand;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单状态
 */
@Data
public class OrderStatusBO implements Serializable {
    private static final long serialVersionUID = -8294120282780885451L;

    /**
     * 操作来源： 买家/卖家
     *
     * @see ActionRoleEnum
     */
    private Integer actionRole;

    /**
     * 动作类型
     *
     * @see OrderActionEnum
     */
    private String action;

    /**
     * 提货时间
     */
    private Date shipDate;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货单号、提货单号(确认收货时，可选传参)
     */
    private List<String> bizNoList;

    /**
     * 发货提货单状态
     */
    private String orderShipNo;

    /**
     * 所有商品是否已全部发完
     */
    private Boolean allProductShip;

    /**
     * 订单信息
     */
    private OcOrderInfoEntity ocOrderInfo;


    /**
     * 付款信息
     */
    private OrderPayBO payInfo;

    /**
     * 上传附件附件
     */
    private List<AttachmentBO> attachments;


    /**
     * 取消信息
     */
    private OrderCancelBO orderCancel;

    /**
     * 订单结算
     */
    private OrderStartSmentBO orderSment;


    /**
     * 申请提货 商品
     */
    private List<OrderShipProductBO> orderShipProduct;

    /**
     * 申请提货 车辆
     */
    private List<PickUpCarBO> pickUpCar;

    /**
     * 通知提货
     */
    private OrderPickupNoticeBO orderPickupNotice;

    /**
     * 签署合同
     */
    private SignContractBO signContract;

    /**
     * 发货单表单数据
     */
    private OcOrderShipCommand ocOrderShip;

    /**
     * 发货单号
     */
    private List<String> shipNoList;

    /**
     * 发货单信息
     */
    private List<OcOrderShipEntity> ocOrderShipList;

    /**
     * 订单发货单商品表 数量更新
     */
    List<OcOrderShipProductEntity> pickupPrdInfoList;

    /**
     * 发货商品数量表 数据更新
     */
    List<OcOrderShipQuantityEntity> quantityEntityList;

    /**
     * 上下文信息
     */
    ContextBO contextBO;


    /**
     * 发货商品
     */
    private List<ShipProductCommand> shipProductList;
}
