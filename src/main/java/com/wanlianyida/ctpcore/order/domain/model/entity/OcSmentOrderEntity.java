package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 结算明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
public class OcSmentOrderEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 结算单号
     */
    private String smentNo;

    /**
     * 结算状态:200待结算 210待付款 220待收款 230已付款
     */
    private Integer smentStatus;

    /**
     * 是否预付:0-否,1-是
     */
    private Boolean prepayFlag;

    /**
     * 是否含税:0-否,1-是
     */
    private Boolean taxFlag;

    /**
     * 10电汇 20银行承诺 30商业承诺
     */
    private Integer payMode;

    /**
     * 结算日期
     */
    private Date smentDate;

    /**
     * 预付款比例
     */
    private BigDecimal prepayRatio;

    /**
     * 结算金额
     */
    private BigDecimal smentAmount;

    /**
     * 运费
     */
    private BigDecimal expenseAmount;

    /**
     * 其它费用
     */
    private BigDecimal otherExpenses;

    /**
     * 其它费用说明
     */
    private String otherExpensesRemark;

    /**
     * 支付方式 10线上 20线上
     */
    private Integer paymentMode;

    /**
     * 确认收款时间
     */
    private Date confirmReceiptDate;

    /**
     * 确认收款人
     */
    private String confirmPayee;

    /**
     * 确认收款人id
     */
    private String confirmPayeeId;


    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 支付时间
     */
    private Date paymentDate;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

}
