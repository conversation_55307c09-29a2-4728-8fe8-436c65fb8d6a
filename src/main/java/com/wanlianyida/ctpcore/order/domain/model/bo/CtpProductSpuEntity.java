package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品spu
 */
@Data
public class CtpProductSpuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SPU ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * spu编码
     */
    private String spuCode;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 品牌ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relBrandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 1级品类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId1;

    /**
     * 1级品类名称
     */
    private String categoryName1;

    /**
     * 2级品类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId2;

    /**
     * 2级品类名称
     */
    private String categoryName2;

    /**
     * 3级品类ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId3;

    /**
     * 3级品类名称
     */
    private String categoryName3;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 是否审核后立即上架 1是 0否
     */
    private Boolean onSaleAfterAudit;

    /**
     * 关联仓库ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relStockId;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 交货地省编号
     */
    private String deliveryAddrProvinceCode;

    /**
     * 交货地省名称
     */
    private String deliveryAddrCityCode;

    /**
     * 交货地城市编号
     */
    private String deliveryAddrProvinceName;

    /**
     * 交货地城市名称
     */
    private String deliveryAddrCityName;

    /**
     * 计价单位ID
     */
    private Integer relPricingUnitId;

    /**
     * 计量单位ID
     */
    private Integer relMeasurementUnitId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 更新人名字
     */
    private String updaterName;

}
