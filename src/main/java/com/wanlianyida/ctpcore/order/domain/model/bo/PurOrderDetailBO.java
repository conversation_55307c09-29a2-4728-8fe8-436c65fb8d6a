package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 意向订单详情
 */
@Data
public class PurOrderDetailBO {

    /**
     * 意向单信息
     */
    private OcPOrderInfoEntity purOrderInfo;

    /**
     * 意向单联系人
     */
    private OcPorderAddressEntity linkman;

    /**
     * 意向订单买卖信息
     */
    private List<OcPorderBuySellEntity> buySellList;

    /**
     * 意向单商品
     */
    private List<OcPorderProductEntity> productList;

    /**
     * 意向单商品规格
     */
    private Map<String, List<OcPorderProductSkuAttrEntity>> skuAttrMap;

    private String orderNo;

}
