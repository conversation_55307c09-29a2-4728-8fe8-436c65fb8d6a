package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品库存变更
 */
@Data
@Accessors(chain = true)
public class OrderStockChangeBO implements Serializable {
    private static final long serialVersionUID = -4696831158100042754L;

    /**
     * 订单（撮合）编号
     */
    private String orderNo;

    /**
     * 订单（撮合）状态
     */
    private String orderOpt;

    /**
     * 更新人id
     */
    private String updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 更新时间
     */
    private Date updatedDate;

    /**
     * 订单商品列表
     */
    private List<OrderProduct> orderProductList;

    @Data
    public static class OrderProduct implements Serializable{
        private static final long serialVersionUID = 6481497214505010341L;
        /**
         * 商品编号
         */
        private String skuCode;

        /**
         * 扣减库存
         */
        private BigDecimal quantity;
    }

}
