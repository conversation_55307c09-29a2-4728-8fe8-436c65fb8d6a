package com.wanlianyida.ctpcore.order.domain.model.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单列表
 */
@Data
public class SettlementOrderBO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 卖家店铺ID
     */
    private Long sellerShopId;

    /**
     * 卖家店铺名称
     */
    private String sellerShopName;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    private String sellerCompanyName;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    private String buyerCompanyName;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 提货、发货单号
     */
    private String shipNo;

    /**
     * 收货/提货状态 10未收货(未提货) 20已收货(已提货)
     */
    private Integer receiveStatus;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 提货、收货凭证附件
     */
    private List<String> attachmentUrl;
}
