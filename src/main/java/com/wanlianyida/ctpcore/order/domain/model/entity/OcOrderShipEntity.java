package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单发货单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OcOrderShipEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 类型 10 提货 2发货
     */
    private Integer type;

    /**
     * 交货方式10:自提20:物流配送
     */
    private Integer deliveryMode;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发/提货单号
     */
    private String shipNo;

    /**
     * 发/提货状态
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipStatusEnum
     */
    private Integer shipStatus;

    /**
     * 收货状态
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipReceiveStatusEnum
     */
    private Integer receiveStatus;

    /**
     * 综合状态
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipSummaryStatusEnum
     */
    private Integer summaryStatus;

    /**
     * 签收状态
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipSignStatusEnum
     */
    private Integer signStatus;

    /**
     * 指定提货人状态
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipPickupAssigneeStatusEnum
     */
    private Integer pickupAssigneeStatus;

    /**
     * 发货方式
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipTypeEnum
     */
    private String shipType;

    /**
     * 物流委托方式
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipEntrustTypeEnum
     */
    private String entrustType;

    /**
     * 卖家ID
     */
    private String sellerCompanyId;

    /**
     * 卖家名称
     */
    private String sellerCompanyName;

    /**
     * 买家id
     */
    private String buyerCompanyId;

    /**
     * 买家名称
     */
    private String buyerCompanyName;

    /**
     * 物流公司id
     */
    private String logistCompanyId;

    /**
     * 物流公司
     */
    private String logistCompany;

    /**
     * 物流单号
     */
    private String logistNumber;

    /**
     * 发货时间
     */
    private Date shipDate;

    /**
     * 收货时间
     */
    private Date receiveDate;

    /**
     * 发货人id
     */
    private String shipNameId;

    /**
     * 发货人
     */
    private String shipName;

    /**
     * 发货人账号
     */
    private String shipAccount;

    /**
     * 收货人id
     */
    private String receiveNameId;

    /**
     * 收货人
     */
    private String receiveName;

    /**
     * 收货人账号
     */
    private String receiveAccount;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;
}
