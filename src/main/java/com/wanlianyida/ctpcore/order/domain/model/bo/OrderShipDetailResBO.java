package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderAddressEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderAttachmentEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import lombok.Data;

import java.util.List;

/**
 * 发货订单明细
 * <AUTHOR>
 */
@Data
public class OrderShipDetailResBO {

    /**
     * 发货单
     */
    private OcOrderShipEntity ship;

    /**
     * 发货单商品
     */
    private List<OrderShipProductResBO> productList;

    /**
     * 发货凭证
     */
    private List<OcOrderAttachmentEntity> attachmentList;

    /**
     * 运单明细
     */
    private List<OrderShipPickupCarResBO> pickupCarList;

    /**
     * 订单地址
     */
    private OcOrderAddressEntity ocOrderAddress;

}
