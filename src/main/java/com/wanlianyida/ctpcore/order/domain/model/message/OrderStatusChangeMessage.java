package com.wanlianyida.ctpcore.order.domain.model.message;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Getter
@Setter
public class OrderStatusChangeMessage implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 意向单号
     */
    private String bizNo;

    /**
     * 订单状态10-待签约 ,字典ctmsOrderStatus
     */
    private Integer status;

    /**
     * 子状态
     */
    private Integer subStatus;

    /**
     * 待支付 100 枚举:payStatus
     */
    private Integer payStatus;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 优惠金额
     */
    private BigDecimal disAmount;

    /**
     * 商品总金额
     */
    private BigDecimal productTotalAmount;


    /**
     * 购买总数量
     */
    private BigDecimal purchaseTotalQuantity;

    /**
     * 商品总重量
     */
    private BigDecimal productTotalWeight;

    /**
     * 交货方式10:自提20:物流配送
     */
    private Integer deliveryMode;

    /**
     * 支付方式 10:电汇 20:银行承兑 30:银行承兑
     */
    private String payMode;

    /**
     * 支付时间
     */
    private Date payDate;

    /**
     * 订单来源 10:意向 20:求购
     */
    private Integer orderSource;

    /**
     * 下单时间
     */
    private Date placeOrderDate;

    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    private String sellerCompanyName;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    private String buyerCompanyName;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人
     */
    private String operator;


}
