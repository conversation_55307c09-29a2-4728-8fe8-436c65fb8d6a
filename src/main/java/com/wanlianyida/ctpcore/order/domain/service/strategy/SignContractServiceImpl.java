package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderShipProductBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.SignContractBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.OcOrderPaymentCondition;
import com.wanlianyida.ctpcore.order.domain.model.condition.OrderShipCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderPayStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.OrderStatusEnum;
import com.wanlianyida.ctpcore.order.infrastructure.enums.PaymentTermsEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * 订单状态变更（签署合同）
 */
@Slf4j
@Service("signContract")
public class SignContractServiceImpl extends OrderStatusChangeService{

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private OrderRepo orderRepo;

    @Resource
    private OrderShipBaseService orderShipBaseService;

    @Resource
    private MqEventPublisher mqEventPublisher;

    /**
     * 数据校验
     * @param bo
     * @return
     */
    @Override
    protected ResultMode checkActionCondition(OrderStatusBO bo) {
        //校验订单是否可以签署合同
        SignContractBO signContract = bo.getSignContract();
        if(StringUtils.isEmpty(signContract.getContractNo())){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_00001.getCode(), CtpCoreOrderExceptionEnum.ERROR_00001.getMsg());
        }
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        if(!ObjUtil.equals(ocOrderInfo.getStatus(), OrderStatusEnum.STATUS_10.getStatus())){
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_20002.getCode(), CtpCoreOrderExceptionEnum.ERROR_20002.getMsg());
        }
        return ResultMode.success();
    }

    /**
     * 封装数据
     * @param bo
     * @return
     */
    @Override
    protected HandelStatusBO packActionData(OrderStatusBO bo) {
        String orderNo = bo.getOrderNo();
        HandelStatusBO handelStatusBO = new HandelStatusBO();
        OcOrderInfoEntity updateOrderInfo = new OcOrderInfoEntity();
        SignContractBO signContract = bo.getSignContract();

        //判断付款条件
        OcOrderPaymentCondition paymentCondition = new OcOrderPaymentCondition().setRfOrderNo(orderNo);
        OcOrderPaymentEntity ocOrderPayment = orderStatusRepo.getOcOrderPayment(paymentCondition);
        OcOrderDeliveryEntity ocOrderDelivery = orderStatusRepo.getOcOrderDelivery(orderNo);

        Integer orderStatus = OrderStatusEnum.STATUS_20.getStatus();
        Integer orderSubStatus = 0;
        Integer payStatus = OrderPayStatusEnum.PAY_STATUS_110.getPayStatus();

        if(ObjUtil.equal(ocOrderPayment.getPaymentTerms(), PaymentTermsEnum.TERMS_20.getTerms())) {
            payStatus = OrderPayStatusEnum.PAY_STATUS_100.getPayStatus();
            orderStatus = OrderStatusEnum.STATUS_30.getStatus();
            orderSubStatus = OrderSubStatusEnum.SUB_STATUS_30.getSubStatus();
            if(ObjUtil.equal(ocOrderDelivery.getDeliveryMode(),DeliveryModeEnum.TYPE_10.getType()) || ObjUtil.equal(ocOrderDelivery.getDeliveryMode(), DeliveryModeEnum.TYPE_30.getType())){
                orderStatus = OrderStatusEnum.STATUS_50.getStatus();
                orderSubStatus = OrderSubStatusEnum.SUB_STATUS_50.getSubStatus();
            }

        }
        updateOrderInfo.setStatus(orderStatus);
        updateOrderInfo.setSubStatus(orderSubStatus);
        updateOrderInfo.setPayStatus(payStatus);
        // 单笔合同
        updateOrderInfo.setContractNo(signContract.getContractNo());
        // 长期合同
        if(!StringUtils.isBlank(signContract.getLongtermContractNo())){
            updateOrderInfo.setLongtermContractNo(signContract.getLongtermContractNo());
        }
        updateOrderInfo.setOrderNo(orderNo);
        handelStatusBO.setOrderInfoForUpdate(updateOrderInfo);

        if (ObjUtil.equal(ocOrderDelivery.getDeliveryMode(), DeliveryModeEnum.TYPE_30.getType())) {
            OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
            handelStatusBO.setAction(bo.getAction());
            this.buildShipInfo(handelStatusBO, ocOrderInfo);
        }
        handelStatusBO.setOcOrderInfo(bo.getOcOrderInfo());
        return handelStatusBO;
    }

    private void buildShipInfo(HandelStatusBO handelStatusBO,  OcOrderInfoEntity ocOrderInfo) {
        String orderNo = ocOrderInfo.getOrderNo();
        List<OcOrderShipEntity> ocOrderShipEntities = orderRepo.queryOrderShipByCondition(OrderShipCondition.builder().orderNo(orderNo).build());
        if (ObjUtil.isNotEmpty(ocOrderShipEntities)) {
            return;
        }
        OrderStatusBO orderShipBo = new OrderStatusBO();
        orderShipBo.setOrderNo(orderNo);
        orderShipBo.setShipDate(new Date());
        List<OrderShipProductBO> orderShipProduct = new ArrayList<>();
        List<OcOrderProductEntity> ocOrderProductEntities = orderRepo.queryProductByOrderNo(orderNo);
        ocOrderProductEntities.forEach(ocOrderProductEntity -> {
            OrderShipProductBO orderShipProductBO = new OrderShipProductBO();
            orderShipProductBO.setOrderNo(orderNo);
            orderShipProductBO.setOrderProductId(ocOrderProductEntity.getId());
            orderShipProductBO.setSkuCode(ocOrderProductEntity.getSkuCode());
            orderShipProductBO.setSkuName(ocOrderProductEntity.getSkuName());
            orderShipProductBO.setShipmentQuantity(ocOrderProductEntity.getPurchaseQuantity());
            orderShipProduct.add(orderShipProductBO);
        });
        orderShipBo.setOrderShipProduct(orderShipProduct);
        orderShipBo.setOcOrderInfo(ocOrderInfo);
        //无需运输数据设置
        HandelStatusBO handel = orderShipBaseService.packActionData(orderShipBo);
        handelStatusBO.setOcOrderShipEntityForInsert(handel.getOcOrderShipEntityForInsert());
        handelStatusBO.setOrderShipQuantityEntityListForUpdate(handel.getOrderShipQuantityEntityListForUpdate());
        handelStatusBO.setPickupPrdInfoList(handel.getPickupPrdInfoList());
    }


    /**
     * 入库
     * @param bo
     * @return
     */
    @Override
    protected int handleStatus(HandelStatusBO bo) {
       return orderStatusRepo.handleStatus(bo);
    }

}
