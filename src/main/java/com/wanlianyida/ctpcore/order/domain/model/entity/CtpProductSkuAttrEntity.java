package com.wanlianyida.ctpcore.order.domain.model.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商品属性值表sku
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
public class CtpProductSkuAttrEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 属性值ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 属性项ID（规格ID）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relAttributeId;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 属性值id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String valueId;

    /**
     * 属性值文本
     */
    private String valueText;

    /**
     * 属性类型（10通用参数 20特殊参数） 标志是否为销售属性
     */
    private String attributeType;
}
