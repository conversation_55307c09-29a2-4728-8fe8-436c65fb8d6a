package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderAddressEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderBuySellEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderProductEntity;
import lombok.Data;

import java.util.List;

/**
 * 卖家发货
 */
@Data
public class OrderShipmentsOptBO {

    /**
     * 买家信息
     */
    private OcOrderBuySellEntity buyer;

    /**
     * 订单地址
     */
    private OcOrderAddressEntity address;

    /**
     * 可提货商品
     */
    private List<OcOrderProductEntity> productList;

}
