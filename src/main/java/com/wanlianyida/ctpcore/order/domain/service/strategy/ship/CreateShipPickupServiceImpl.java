//package com.wanlianyida.ctpcore.order.domain.service.strategy.ship;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.util.ObjUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
//import com.wanlianyida.ctpcore.order.domain.model.bo.OrderShipProductBO;
//import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
//import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
//import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
//import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipProductEntity;
//import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipQuantityEntity;
//import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
//import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
//import com.wanlianyida.ctpcore.order.domain.service.strategy.OrderStatusChangeService;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipStatusEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipTypeEnum;
//import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderException;
//import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
//import com.wanlianyida.ctpcore.order.interfaces.facade.command.OcOrderShipCommand;
//import com.wanlianyida.ctpcore.order.interfaces.facade.command.ShipProductCommand;
//import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
//import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
//import com.wanlianyida.framework.ctpcommon.enums.NumberBizTypeEnum;
//import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
//import com.wanlianyida.framework.ctpcore.utils.NumberGenerator;
//import groovy.util.logging.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.Map;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * 生成发货单
// * <AUTHOR>
// * @since 2025/4/22
// */
//@Slf4j
//@Service("createShipPickupService")
//public class CreateShipPickupServiceImpl extends OrderStatusChangeService {
//
//    @Resource
//    private OrderStatusRepo orderStatusRepo;
//
//    @Resource
//    private NumberGenerator numberGenerator;
//
//    @Resource
//    private OrderRepo orderRepo;
//
//    @Override
//    protected ResultMode<?> checkActionCondition(OrderStatusBO bo) {
//
//        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
//        OcOrderShipCommand ocOrderShip = bo.getOcOrderShip();
//        String orderNo = bo.getOrderNo();
//
//        // 校验交货方式 自提场景
//        Integer deliveryMode = ocOrderInfo.getDeliveryMode();
//        if (deliveryMode.equals(DeliveryModeEnum.TYPE_20.getType())){
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_TYPE_NOT_SEND_OUT.getMsg());
//        }
//
//        // 校验操作动作
//        String action = bo.getAction();
//        if (!OrderActionEnum.ACTION_210.getAction().equals(action)) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_NO_SEND_OUT_GOODS.getMsg());
//        }
//
//        // 只校验订单主状态
//        Integer status = ocOrderInfo.getStatus();
//        if (!status.equals(OrderStatusEnum.STATUS_30.getStatus())) {
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_SEND_OUT.getMsg());
//        }
//        // 校验发货人信息
//        Boolean validated = validateCompanyInfo(ocOrderShip);
//        if (!validated){
//            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_SEND_OUT_USER_VAILD.getMsg());
//        }
//
//        // 查询发货商品数量信息
//        List<OcOrderShipQuantityEntity> quantityEntityList = orderStatusRepo.queryShipQuantityOrderNo(orderNo);
//        // 订单商品转map
//        Map<Long, OcOrderShipQuantityEntity> productBOMapOld = quantityEntityList.stream()
//                .collect(Collectors.toMap(OcOrderShipQuantityEntity::getOrderProductId, Function.identity()));
//
//        // 处理发货商品数据
//        for (ShipProductCommand productBO : bo.getShipProductList()) {
//            updateShipmentQuantity(productBO, productBOMapOld);
//        }
//
//        // 校验是否已全部发完
//        Boolean allProductsShipped = validateAllProductShip(quantityEntityList);
//
//        // 处理订单状态
//        if (!allProductsShipped) {
//            ocOrderInfo.setSubStatus(OrderSubStatusEnum.SUB_STATUS_31.getSubStatus());
//        } else {
//            //判断之前是否已经收过货
//            if (validateAllProductShipReceiveStatus(quantityEntityList)) {
//                ocOrderInfo.setSubStatus(OrderSubStatusEnum.SUB_STATUS_41.getSubStatus());
//            } else {
//                ocOrderInfo.setSubStatus(OrderSubStatusEnum.SUB_STATUS_40.getSubStatus());
//            }
//            ocOrderInfo.setStatus(OrderStatusEnum.STATUS_40.getStatus());
//        }
//
//        bo.setAllProductShip(allProductsShipped);
//        bo.setQuantityEntityList(quantityEntityList);
//
//        // 生成发货单号
//        String shipNo = numberGenerator.getBizId(NumberBizTypeEnum.SHIP.getCode());
//        // 设置发货单号
//        ocOrderShip.setShipNo(shipNo);
//        ocOrderShip.setShipStatus(ShipShipStatusEnum.STATUS_10.getCode());
//        ocOrderShip.setType(OrderShipTypeEnum.TYPE_10.getType());
//        bo.getOrderShipProduct().forEach(e -> e.setBizNo(shipNo));
//        return ResultMode.success();
//    }
//
//    @Override
//    protected HandelStatusBO packActionData(OrderStatusBO bo) {
//        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
//        // 订单信息
//        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
//        OcOrderShipCommand ocOrderShip = getShipCommand(bo, tokenInfo);
//        // 发货商品信息
//        List<OrderShipProductBO> orderShipProduct = bo.getOrderShipProduct();
//        // 遍历表单中的发货商品
//        List<OcOrderShipProductEntity> ocOrderShipProductEntities = BeanUtil.copyToList(orderShipProduct, OcOrderShipProductEntity.class);
//        ocOrderShipProductEntities.forEach(e -> e.setCreatorId(tokenInfo.getUserBaseId()));
//
//        OcOrderShipEntity shipEntity = BeanUtil.copyProperties(ocOrderShip, OcOrderShipEntity.class);
//        shipEntity.setCreatorId(tokenInfo.getUserBaseId());
//        shipEntity.setBuyerCompanyId(ocOrderInfo.getBuyerCompanyId());
//        shipEntity.setBuyerCompanyName(ocOrderInfo.getBuyerCompanyName());
//        shipEntity.setSellerCompanyId(ocOrderInfo.getSellerCompanyId());
//        shipEntity.setSellerCompanyName(ocOrderInfo.getSellerCompanyName());
//        shipEntity.setOrderNo(ocOrderInfo.getOrderNo());
//        // 发货类型
//        ocOrderShip.setType(OrderShipTypeEnum.TYPE_20.getType());
//        ocOrderShip.setShipDate(null);
//        ocOrderShip.setShipStatus(ShipShipStatusEnum.STATUS_10.getCode());
//        HandelStatusBO handelStatusBO = new HandelStatusBO();
//        handelStatusBO.setAction(bo.getAction());
//        handelStatusBO.setOcOrderShipEntityForInsert(shipEntity);
//        return handelStatusBO;
//    }
//
//    private static OcOrderShipCommand getShipCommand(OrderStatusBO bo, TokenInfo tokenInfo) {
//        OcOrderShipCommand ocOrderShip = bo.getOcOrderShip();
//        // 发货人id
//        ocOrderShip.setShipNameId(tokenInfo.getUserBaseId());
//        // 发货人名称
//        ocOrderShip.setShipName(tokenInfo.getUsername());
//        // 发货人账号
//        ocOrderShip.setShipAccount(tokenInfo.getLoginName());
//        return ocOrderShip;
//    }
//
//    @Override
//    protected int handleStatus(HandelStatusBO bo) {
//        return orderStatusRepo.handleStatus(bo);
//    }
//
//    // 封装的方法
//    private void updateShipmentQuantity(ShipProductCommand shipProductCommand, Map<Long, OcOrderShipQuantityEntity> productBOMapOld) {
//        Long orderProductId = shipProductCommand.getOrderProductId();
//        if (ObjectUtil.isEmpty(orderProductId)){
//            throw new CtpCoreOrderException(CtpCoreOrderExceptionEnum.ERROR_ORDER_GOODS_IS_NULL.getMsg());
//        }
//
//        OcOrderShipQuantityEntity entity = productBOMapOld.get(orderProductId);
//        if (ObjUtil.isNull(entity)){
//            throw new CtpCoreOrderException(CtpCoreOrderExceptionEnum.ERROR_ORDER_GOODS_IS_EMPTY.getMsg());
//        }
//        // 获取表单中发货数量
//        BigDecimal shipmentQuantity = shipProductCommand.getShipmentQuantity();
//
//        // 获取订单中的购买数量
//        BigDecimal orderQuantity = entity.getOrderQuantity();
//
//        // 获取订单中的已发货数量
//        BigDecimal pickupQuantity = entity.getShipPickupQuantity();
//
//        // 累加发货数量
//        BigDecimal totalShipQuantity = pickupQuantity.add(shipmentQuantity);
//
//        if (totalShipQuantity.compareTo(orderQuantity) > 0) {
//            throw new CtpCoreOrderException(CtpCoreOrderExceptionEnum.ERROR_ORDER_GOODS_NUM_FULL.getMsg());
//        }
//
//        // 更新已发货数量
//        entity.setShipPickupQuantity(totalShipQuantity);
//    }
//
//    /**
//     * 验证发货人信息
//     *
//     * @param ocOrderShip OC 订单发货
//     * @return {@link Boolean }
//     */
//    private Boolean validateCompanyInfo(OcOrderShipCommand ocOrderShip) {
//        // 验证发货人信息
//        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
//        String userBaseId = tokenInfo.getUserBaseId();
//        String username = tokenInfo.getUsername();
//        String loginName = tokenInfo.getLoginName();
//
//        if (StrUtil.isBlank(userBaseId)|| StrUtil.isBlank(username) || StrUtil.isBlank(loginName)){
//            return false;
//        }
//        // 发货人id
//        ocOrderShip.setShipNameId(userBaseId);
//        // 发货人名称
//        ocOrderShip.setShipName(username);
//        // 发货人账号
//        ocOrderShip.setShipAccount(loginName);
//
//        return true;
//    }
//
//    /**
//     * 校验是否已全部发完
//     *
//     * @param quantityEntityList 数量实体列表
//     * @return {@link Boolean }
//     */
//    private Boolean validateAllProductShip(List<OcOrderShipQuantityEntity> quantityEntityList) {
//        // 标记是否所有商品都已全部发完
//        boolean allProductsShipped = true;
//
//        // 校验是否已经全部发完
//        for (OcOrderShipQuantityEntity oldEntity : quantityEntityList) {
//            // 获取购买数量
//            BigDecimal orderQuantity = oldEntity.getOrderQuantity();
//            // 获取已发货数量
//            BigDecimal shipPickupQuantity = oldEntity.getShipPickupQuantity();
//            if (shipPickupQuantity.compareTo(orderQuantity) != 0){
//                allProductsShipped = false;
//                break;
//            }
//        }
//
//        return allProductsShipped;
//    }
//
//    /**
//     * 校验是否已经有收货的发货单
//     *
//     * @param quantityEntityList 数量实体列表
//     * @return {@link Boolean }
//     */
//    private Boolean validateAllProductShipReceiveStatus(List<OcOrderShipQuantityEntity> quantityEntityList) {
//        // 校验是否已经全部发完
//        for (OcOrderShipQuantityEntity oldEntity : quantityEntityList) {
//            // 获取收货数量
//            if (oldEntity.getReceiveQuantity().compareTo(BigDecimal.ZERO) > 0){
//                return true;
//            }
//        }
//        return false;
//    }
//}
