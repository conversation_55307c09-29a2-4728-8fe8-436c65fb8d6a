package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 物流接口日志表
 *
 * <AUTHOR>
 */
@Data
public class OcLogisticsInterfaceLogEntity implements Serializable {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 业务类型[10-运单信息、20-运单状态]
     */
    private String businessType;
    /**
     * 业务类型名称
     */
    private String businessTypeName;
    /**
     * 接口名称
     */
    private String interfaceName;
    /**
     * 接口URL
     */
    private String interfaceUrl;
    /**
     * 调用参数（JSON格式存储）
     */
    private String requestParams;
    /**
     * 返回结果（JSON格式存储）
     */
    private String responseResult;
    /**
     * 创建用户id
     */
    private String creatorId;
    /**
     * 创建时间
     */
    private Date createdDate;
    /**
     * 最后更新人id
     */
    private String updaterId;
    /**
     * 最后更新时间
     */
    private Date updatedDate;
    /**
     * 版本号
     */
    private Integer versionCode;

}
