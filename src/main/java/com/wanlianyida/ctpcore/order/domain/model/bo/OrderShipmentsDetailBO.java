package com.wanlianyida.ctpcore.order.domain.model.bo;

import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderAttachmentEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderShipProductEntity;
import lombok.Data;

import java.util.List;

/**
 * 发货记录
 */
@Data
public class OrderShipmentsDetailBO {

    /**
     * 发货单
     */
    private OcOrderShipEntity ship;

    /**
     * 发货单商品
     */
    private List<OcOrderShipProductEntity> productList;

    /**
     * 发货凭证
     */
    private List<OcOrderAttachmentEntity> attachmentList;

}
