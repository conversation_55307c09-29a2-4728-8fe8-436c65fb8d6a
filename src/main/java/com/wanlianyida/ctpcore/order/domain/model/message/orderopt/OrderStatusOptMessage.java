package com.wanlianyida.ctpcore.order.domain.model.message.orderopt;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单状态变更 接收消息
 */
@Data
public class OrderStatusOptMessage extends OrderStatusCommonMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     *  10 买家取消 20 卖家取消
     */
    private Integer cancelSource;

    /**
     * 支付时间
     */
    private Date payDate;

    /**
     * 付款状态
     */
    private Integer paymentStatus;

    /**
     * 结算金额
     */
    private BigDecimal smentAmount;

    /**
     * 实际支付方式
     */
    private String actualPayMode;

}
