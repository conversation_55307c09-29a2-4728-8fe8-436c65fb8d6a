package com.wanlianyida.ctpcore.order.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商品属性值表sku
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
public class OcOrderProductSkuAttrEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * sku_code
     */
    private String skuCode;

    private String orderNo;

    /**
     * 属性项ID（规格ID）
     */
    private Long relAttributeId;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 属性值文本
     */
    private String valueText;

    /**
     * 是否是销售规格(0不是 1是)
     */
    private Boolean attributeType;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 更新人名字
     */
    private String updaterName;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 是否删除 1删除 0未删除
     */
    private Boolean deleted;

}
