package com.wanlianyida.ctpcore.order.domain.service.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.order.domain.assembler.OrderStatusAssembler;
import com.wanlianyida.ctpcore.order.domain.model.bo.HandelStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderShipProductBO;
import com.wanlianyida.ctpcore.order.domain.model.bo.OrderStatusBO;
import com.wanlianyida.ctpcore.order.domain.model.condition.OcOrderPaymentCondition;
import com.wanlianyida.ctpcore.order.domain.model.condition.OrderAttachmentCondition;
import com.wanlianyida.ctpcore.order.domain.model.condition.OrderShipCondition;
import com.wanlianyida.ctpcore.order.domain.model.entity.*;
import com.wanlianyida.ctpcore.order.domain.model.message.OrderFinishMessage;
import com.wanlianyida.ctpcore.order.domain.repository.OrderRepo;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.enums.pay.FinishTypeEnum;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.ctpcore.order.interfaces.facade.command.OcOrderShipCommand;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 确认收货/提货 逻辑
 * <AUTHOR>
 * @since 2024/12/09/13:38
 */
@Slf4j
@Service("confirmReceipt")
public class ConfirmReceiptServiceImpl extends OrderStatusChangeService{

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Resource
    private OrderRepo orderRepo;

    @Resource
    private MqEventPublisher mqEventPublisher;

    /**
     *
     * @see OrderStatusEnum
     * @see OrderSubStatusEnum
     * @param bo
     * @return
     */
    @Override
    protected ResultMode checkActionCondition(OrderStatusBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        //原型：订单状态=部分发货、待收货、部分收货时，买家/卖家端均显示确认收货
        //判断主状态为  待收货  待提货 40 or 50
        // ACTION_50("50","确认收货","confirmReceipt"),
        // ACTION_90("90","确认提货","confirmReceipt"),
        String action = bo.getAction();
        HashSet<Integer> checkStatusCondition = OrderStatusAssembler.getCheckStatusCondition(bo.getAction());
        if(!checkStatusCondition.contains(ocOrderInfo.getStatus())){
            //提货 or 收货 errorMsg
            return ResultMode.fail(OrderActionEnum.ACTION_50.getAction().equals(action) ?
                    CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_NOT_RECEIPT.getMsg() : CtpCoreOrderExceptionEnum.ERROR_ORDER_STATUS_PICK_UP.getMsg());
        }
        Integer actionRole = bo.getActionRole();
        //买家确认收货: 校验该订单下是否存在未收货的发货单
        //卖家确认收货: 1 校验该订单下是否存在未收货的发货单 2 继续校验该订单下全部未收货的发货单是否均已上传收货凭证
        String shipNo = "";
        OcOrderShipCommand ocOrderShip = bo.getOcOrderShip();
        if (ObjUtil.isNotNull(ocOrderShip)) {
            shipNo = ocOrderShip.getShipNo();
        }
        List<OcOrderShipEntity> ocOrderShipList = orderRepo.queryOrderShipByCondition(OrderShipCondition.builder().type(ocOrderInfo.getDeliveryMode())
                .orderNo(bo.getOrderNo()).receiveStatus(OrderShipReceiveStatusEnum.RECEIVE_STATUS_10.getReceiveStatus()).shipmentNo(shipNo).build());
        if (ocOrderShipList.isEmpty()) {
            return ResultMode.fail(OrderActionEnum.ACTION_50.getAction().equals(action) ?
                    CtpCoreOrderExceptionEnum.ERROR_ORDER_RECEIPT_IS_NULL.getMsg() : CtpCoreOrderExceptionEnum.ERROR_ORDER_PICKUP_IS_NULL.getMsg());
        }

        //未收货的发货单 发货单号去重列表
        List<String> shipNoList = ocOrderShipList.stream().map(OcOrderShipEntity::getShipNo).distinct().collect(Collectors.toList());
        if (ActionRoleEnum.ACTIION_SOURCE_20.getActionSource().equals(actionRole)) {
            //收货凭证 发货单号去重列表
            List<OcOrderAttachmentEntity> attachmentList = orderRepo.queryAttachmentByCondition(
                    OrderAttachmentCondition.builder().bizNoList(shipNoList)
                            .attachmentType(OrderActionEnum.ACTION_50.getAction().equals(action) ? AttachmentTypeEnum.TYPE_20.getType() : AttachmentTypeEnum.TYPE_10.getType())
                            .build());
            List<String> collect = attachmentList.stream().map(OcOrderAttachmentEntity::getBizNo).distinct().collect(Collectors.toList());
            //单号没有关联附件
            if (shipNoList.size() != collect.size()) {
                return ResultMode.fail(OrderActionEnum.ACTION_50.getAction().equals(action) ?
                        CtpCoreOrderExceptionEnum.ERROR_ORDER_UPLOAD_RECEIPT_ATTACH.getMsg() : CtpCoreOrderExceptionEnum.ERROR_ORDER_UPLOAD_PICKUP_ATTACH.getMsg());
            }
        }

        // 确认数量信息校验
        List<OrderShipProductBO> orderShipProduct = bo.getOrderShipProduct();
        if (CollUtil.isEmpty(orderShipProduct)) {
            return ResultMode.fail("确认信息不能为空");
        }
        Map<String, OrderShipProductBO> map = new HashMap<>();
        orderShipProduct.forEach(e ->  map.put(buildMapKey(e), e));
        if (CollUtil.isNotEmpty(bo.getBizNoList())) {
            shipNoList = (List<String>) CollUtil.intersection(shipNoList, bo.getBizNoList());
        }
        List<OcOrderShipProductEntity> ocOrderShipProductEntityList = orderRepo.queryShipPrdListByOrderNo(shipNoList);
        for (OcOrderShipProductEntity e : ocOrderShipProductEntityList) {
            OrderShipProductBO shipBO = map.get(buildMapKey(e));
            if (shipBO == null || shipBO.getReceiceQuantity() == null) {
                return ResultMode.fail("确认数量不能为空,请刷新页面重试");
            }
            if (e.getShipmentQuantity().compareTo(shipBO.getReceiceQuantity()) < 0) {
                return ResultMode.fail("确认数量不能大于发货数量或提货数量,请刷新页面重试");
            }
        }

        // 需要更新的发货单列表
        bo.setOcOrderShipList(ocOrderShipList);
        bo.setShipNoList(shipNoList);
        return ResultMode.success();
    }

    @Override
    protected HandelStatusBO packActionData(OrderStatusBO bo) {
        HandelStatusBO handelStatusBO = new HandelStatusBO();
        //（1）变更该订单下未收货的发货单状态=已收货，同时更新其实际收货数量=本次收货数量(20250504更新)
        //（2）根据订单状态变更规则，变更当前订单状态
        OcOrderInfoEntity ocOrderInfo = bo.getOcOrderInfo();
        //1.更新发货单表
        List<OcOrderShipEntity> ocOrderShipList = bo.getOcOrderShipList();
        ocOrderShipList.forEach(e -> {
            //已收货/已提货
            e.setReceiveStatus(OrderShipReceiveStatusEnum.RECEIVE_STATUS_20.getReceiveStatus());
            OrderStatusAssembler.buildOcOrderShipByInfo(ocOrderInfo, e, 2);
        });
        handelStatusBO.setOcOrderShipForUpdate(ocOrderShipList);
        // 2.更新发货单关联商品表 的收货商品数量  订单  + 商品 + 发货单 维度
        List<OrderShipProductBO> orderShipProduct = bo.getOrderShipProduct();
        // 构建map
        Map<String, OrderShipProductBO> map = new HashMap<>();
        orderShipProduct.forEach(e ->  map.put(buildMapKey(e), e));
        List<String> shipNoList = bo.getShipNoList();
        List<OcOrderShipProductEntity> ocOrderShipProductEntityList = orderRepo.queryShipPrdListByOrderNo(shipNoList);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String userBaseId;
        if (ObjUtil.isNotNull(tokenInfo) && StringUtils.isNotBlank(tokenInfo.getUserBaseId())) {
            userBaseId = tokenInfo.getUserBaseId();
        } else {
            userBaseId = "";
        }
        // 处理确认数量
        ocOrderShipProductEntityList.forEach(e -> {
            OrderShipProductBO shipBO = map.get(buildMapKey(e));
            if (shipBO != null) {
                e.setReceiceQuantity(shipBO.getReceiceQuantity());
            } else {
                e.setReceiceQuantity(e.getShipmentQuantity());
            }
            e.setUpdaterId(userBaseId);
        });
        handelStatusBO.setPickupPrdInfoList(ocOrderShipProductEntityList);
        // 3.更新  订单发货数量表的收货数量  基于上订单+商品维度
        //商品 已提货数量
        List<OcOrderShipQuantityEntity> ocOrderShipQuantityEntityList = orderRepo.queryShipQuantityOrderNo(bo.getOrderNo());

        OrderStatusAssembler.getPickUpPrdNum(ocOrderShipProductEntityList, ocOrderShipQuantityEntityList);

        handelStatusBO.setOrderShipQuantityEntityListForUpdate(ocOrderShipQuantityEntityList);
        // 4.更新订单主表  主状态
        BigDecimal sum = ocOrderShipQuantityEntityList.stream().map(OcOrderShipQuantityEntity::getReceiveQuantity).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //订单状态
        OrderStatusAssembler.getOrderStatusByShipStatus(ocOrderInfo, sum, bo.getAction());
        handelStatusBO.setOrderInfoForUpdate(ocOrderInfo);
        //将异步处理支付状态改为同步
        this.synHandle(handelStatusBO);
        log.info("确认收货/提货订单状态:{}", JSONUtil.toJsonStr(ocOrderInfo));
        return handelStatusBO;
    }

    private String buildMapKey(OrderShipProductBO e) {
        return e.getOrderNo() + e.getBizNo() + e.getOrderProductId();
    }
    private String buildMapKey(OcOrderShipProductEntity e) {
        return e.getOrderNo() + e.getBizNo() + e.getOrderProductId();
    }

    protected void synHandle(HandelStatusBO bo) {
        //支付状态更新前---状态快照
        super.asynHandle(bo);
        //提货/收货完成---订单状态变更--支付状态与结算状态  同步处理
        OcOrderInfoEntity updateOrderInfo = bo.getOrderInfoForUpdate();
        String orderNo = updateOrderInfo.getOrderNo();
        if(ObjUtil.equal(updateOrderInfo.getSubStatus(),OrderSubStatusEnum.SUB_STATUS_42.getSubStatus())
                || ObjUtil.equal(updateOrderInfo.getSubStatus(),OrderSubStatusEnum.SUB_STATUS_52.getSubStatus())){
            //提货/收货完成消息
            OcOrderPaymentCondition ocOrderPaymentCondition = new OcOrderPaymentCondition();
            ocOrderPaymentCondition.setRfOrderNo(orderNo);
            OcOrderPaymentEntity ocOrderPayment = orderStatusRepo.getOcOrderPayment(ocOrderPaymentCondition);
            //付款条件10先款后货 20先货后款
            Integer paymentTerms = ocOrderPayment.getPaymentTerms();
            //预付款比例
            BigDecimal prepayRatio = ocOrderPayment.getPrepayRatio();
            //结算单价10合同单价 20实际单价
            Integer settlementPrice = ocOrderPayment.getSettlementPrice();
            //结算数量10合同数量 20实际数量
            Integer settlementQuantity = ocOrderPayment.getSettlementQuantity();
            //全部收货
            OcSmentEntity updateOcSment = null;
            if (ObjUtil.equal(PaymentTermsEnum.TERMS_10.getTerms(),paymentTerms)
                    && prepayRatio.compareTo(new BigDecimal("100")) >= 0
                    && ObjUtil.equal(SettlementQuantityEnum.TERMS_10.getTerms(),settlementQuantity)
                    && ObjUtil.equal(SettlementPriceEnum.TERMS_10.getTerms(),settlementPrice)) {
                updateOrderInfo.setStatus(OrderStatusEnum.STATUS_70.getStatus());
                updateOrderInfo.setPayStatus(OrderPayStatusEnum.PAY_STATUS_130.getPayStatus());

                updateOcSment = new OcSmentEntity();
                updateOcSment.setOrderNo(orderNo);
                updateOcSment.setSmentStatus(SmentStatusEnum.STATUS_30.getCode());

                bo.setOcSmentEntityForUpdate(updateOcSment);
                bo.setOrderInfoForUpdate(updateOrderInfo);
            }
        }
    }

    @Override
    protected int handleStatus(HandelStatusBO bo) {
        return orderStatusRepo.handleStatus(bo);
    }

    @Override
    protected void asynHandle(HandelStatusBO bo) {
        log.info("收货完成消息stat:{}", JSONUtil.toJsonStr(bo));
        super.asynHandle(bo);
        OcOrderInfoEntity updateOrderInfo = bo.getOrderInfoForUpdate();
        if(ObjUtil.equal(updateOrderInfo.getSubStatus(),OrderSubStatusEnum.SUB_STATUS_42.getSubStatus())
                || ObjUtil.equal(updateOrderInfo.getSubStatus(),OrderSubStatusEnum.SUB_STATUS_52.getSubStatus())){
            //提货/收货完成消息
            OrderFinishMessage orderFinishMessage = new OrderFinishMessage();
            orderFinishMessage.setOrderNo(updateOrderInfo.getOrderNo());
            orderFinishMessage.setType(FinishTypeEnum.TYPE_30.getType());
            orderFinishMessage.setOperator(JwtUtil.getTokenInfo().getUsername());
            orderFinishMessage.setOperatorId(JwtUtil.getTokenInfo().getUserBaseId());
            log.info("提货/收货完成消息:{}", JSONUtil.toJsonStr(orderFinishMessage));
//            mqEventPublisher.syncSendFifoMessage(MqEventMessage.buildEventMessage(CtpKafkaConst.CTP_ORDER_PAY_FINISH_TOPIC, orderFinishMessage),
//                    CtpKafkaConst.CTP_ORDER_FINISH_GROUP);
        }
    }
}
