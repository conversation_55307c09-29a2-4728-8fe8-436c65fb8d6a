package com.wanlianyida.ctpcore.order.domain.model.condition;

import lombok.Data;

import java.util.List;

@Data
public class BatchOrderProductCondition {
    /**
     * 查询参数列表
     */
    List<QueryItem> queryList;

    @Data
    public static class QueryItem {
        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 订单skuCode
         */
        private String orderSkuCode;
    }
}
