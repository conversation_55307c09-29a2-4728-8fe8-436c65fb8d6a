package com.wanlianyida.ctpcore.order.domain.service.orderopt;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.order.domain.model.bo.orderopt.OrderPaymentOptBO;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderDeliveryEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderInfoEntity;
import com.wanlianyida.ctpcore.order.domain.model.entity.OcOrderNodeEntity;
import com.wanlianyida.ctpcore.order.domain.repository.OrderStatusRepo;
import com.wanlianyida.ctpcore.order.infrastructure.enums.*;
import com.wanlianyida.ctpcore.order.infrastructure.exception.CtpCoreOrderExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * 卖家确认收款-预付款、尾款
 */
@Slf4j
@Service("paymentReceiptOpt")
public class OrderPaymentReceiptOptService extends OrderOptCommonService<OrderPaymentOptBO> {

    @Resource
    private OrderStatusRepo orderStatusRepo;

    @Override
    public Class<OrderPaymentOptBO> optParamObj() {
        return OrderPaymentOptBO.class;
    }

    /**
     * 数据校验
     */
    @Override
    protected ResultMode checkParam(OrderPaymentOptBO bo) {
        OcOrderInfoEntity ocOrderInfo = bo.getOrderInfo();
        if (!ObjUtil.equals(ocOrderInfo.getPayStatus(), OrderPayStatusEnum.PAY_STATUS_120.getPayStatus())
                || !ObjUtil.equal(bo.getPaymentStatus(), OrderPrepaidStatusEnum.STATUS_130.getCode())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_STATUS_ERROR_NOT_PENDING.getMsg());
        }
        //预付款状态校验
        if (StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_21.getOpt())
                &&  ObjUtil.equal(ocOrderInfo.getPrepaidStatus(), bo.getPaymentStatus())) {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_STATUS_ERROR_NOT_PENDING.getMsg());
        }
        //尾款状态校验
        if (StrUtil.equals(bo.getOrderOpt(), OrderStatusBussOptEnum.OPT_61.getOpt())
                && ObjUtil.equal(ocOrderInfo.getBalancePaymentStatus(), bo.getPaymentStatus()))  {
            return ResultMode.fail(CtpCoreOrderExceptionEnum.ORDER_STATUS_ERROR_NOT_PENDING.getMsg());
        }
        return ResultMode.success();
    }

    /**
     * 封装数据
     */
    @Override
    protected void assembleOrderOptData(OrderPaymentOptBO bo) {
        OcOrderInfoEntity orderInfo = bo.getOrderInfo();
        if (ObjUtil.equal(orderInfo.getStatus(), OrderStatusEnum.STATUS_60.getStatus())) {
            //尾款
            super.assembleOrderUpdSuccess(bo);
            //履约节点
            List<OcOrderNodeEntity> orderNodeInsert = Optional.ofNullable(bo.getOrderNodeInsert())
                    .orElseGet(ArrayList::new);
            OcOrderNodeEntity ocOrderNodeEntity = super.assembleOrderNodeInit(bo);
            ocOrderNodeEntity.setNodeStatus(OrderNodeEnum.STATUS_80.getStatus());
            ocOrderNodeEntity.setOptTime(bo.getPayDate());
            orderNodeInsert.add(0, ocOrderNodeEntity);
            bo.setOrderNodeInsert(orderNodeInsert);
            return;
        }

        //预付款
        OcOrderInfoEntity updateOrderInfo = super.assembleOrderUpdCommon(bo);
        updateOrderInfo.setPayStatus(OrderPayStatusEnum.PAY_STATUS_100.getPayStatus());
        updateOrderInfo.setPrepaidStatus(OrderPrepaidStatusEnum.STATUS_130.getCode());
        updateOrderInfo.setStatus(OrderStatusEnum.STATUS_50.getStatus());
        updateOrderInfo.setSubStatus(OrderStatusEnum.STATUS_50.getStatus());
        bo.setOrderInfoUpd(updateOrderInfo);

        OcOrderDeliveryEntity ocOrderDelivery = orderStatusRepo.getOcOrderDelivery(bo.getOrderNo());
        if (ObjUtil.equal(ocOrderDelivery.getDeliveryMode(), DeliveryModeEnum.TYPE_20.getType())) {
            updateOrderInfo.setStatus(OrderStatusEnum.STATUS_30.getStatus());
            updateOrderInfo.setSubStatus(OrderStatusEnum.STATUS_30.getStatus());
        }

        super.assembleOrderStatusInit(bo);

        //履约节点
        OcOrderNodeEntity ocOrderNodeEntity = super.assembleOrderNodeInit(bo);
        ocOrderNodeEntity.setNodeStatus(OrderNodeEnum.STATUS_30.getStatus());
        ocOrderNodeEntity.setOptTime(bo.getPayDate());
        List<OcOrderNodeEntity> ocOrderNodeEntities = new ArrayList<>();
        ocOrderNodeEntities.add(ocOrderNodeEntity);
        bo.setOrderNodeInsert(ocOrderNodeEntities);
    }

}
