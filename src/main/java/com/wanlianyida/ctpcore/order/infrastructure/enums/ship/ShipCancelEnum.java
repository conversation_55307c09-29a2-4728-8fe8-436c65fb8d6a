package com.wanlianyida.ctpcore.order.infrastructure.enums.ship;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 发货取消状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ShipCancelEnum {
    STATUS_10(10, "取消成功"),
    STATUS_20(20, "发货单不存在"),
    STATUS_30(30, "已有进行中的物流询价单，不可取消"),
    STATUS_40(40, "发货单状态已变更，请刷新页面查看"),

    ;

    private final Integer code;

    public final String desc;


    /**
     * 根据code获取枚举实例（避免NPE）
     */
    public static ShipCancelEnum getByCode(Integer code) {
        if (code == null) return null;
        for (ShipCancelEnum status : values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 校验状态是否有效
     */
    public static boolean isValidStatus(Integer code) {
        return getByCode(code) != null;
    }
}
