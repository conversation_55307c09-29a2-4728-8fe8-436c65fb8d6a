package com.wanlianyida.ctpcore.order.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.ctpcore.order.domain.model.bo.*;
import com.wanlianyida.ctpcore.order.domain.model.condition.OrderCondition;
import com.wanlianyida.ctpcore.order.domain.model.condition.StatisticsBusinessCondition;
import com.wanlianyida.ctpcore.order.infrastructure.repository.po.OcOrderInfoPO;

import java.util.List;

/**
 * <p>
 * 订单信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
public interface OcOrderInfoMapper extends BaseMapper<OcOrderInfoPO> {

    /**
     * 订单列表查询
     */
    List<OrderListBO> queryList(OrderCondition condition);

    /**
     * 订单统计
     */
    StatisticsOrderBO statisticsOrderQuery(String companyId);

    /**
     * 订单经营数据
     */
    StatisticsOrdersBusinessBO statisticsBusinessQuery(StatisticsBusinessCondition condition);

    /**
     * 按天成交金额统计（带企业ID过滤）
     */
    List<StatisticsOrdersBusinessBO> totalAmountQuery(StatisticsBusinessCondition condition);

    /**
     * 按天成交单数统计（带企业ID过滤）
     */
    List<StatisticsOrdersBusinessBO> totalOrdersQuery(StatisticsBusinessCondition condition);

    /**
     * 按天客单价统计（带企业ID过滤）
     */
    List<StatisticsOrdersBusinessBO> avgPriceQuery(StatisticsBusinessCondition condition);

    /**
     * 买家订单
     */
    StatisticsBuyerOrderBO statisticsBuyerOrderQuery(String companyId);

    /**
     * 店铺采购情况-金额
     */
    List<StatisticsShopOrderBO> statisticsShopOrderAmountQuery(String companyId);

    /**
     * 店铺采购情况-数量
     */
    List<StatisticsShopOrderBO> statisticsShopOrderCountQuery(String companyId);
}
