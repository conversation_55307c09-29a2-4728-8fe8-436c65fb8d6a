package com.wanlianyida.ctpcore.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商品库存流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Getter
@Setter
@TableName("oc_product_inventory_flow")
public class OcProductInventoryFlowPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品sku_code
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 企业id(店铺id)
     */
    @TableField("company_id")
    private String companyId;

    /**
     * 买家公司id
     */
    @TableField("buyer_company_id")
    private String buyerCompanyId;

    /**
     * 业务编号(订单号)
     */
    @TableField("bus_num")
    private String busNum;

    /**
     * 库存变化数量
     */
    @TableField("change_quantity")
    private BigDecimal changeQuantity;

    /**
     * 业务动作:10-下单扣减,20-订单取消
     */
    @TableField("bus_action")
    private String busAction;

    /**
     * 操作动作:10-增加,20-减少
     */
    @TableField("operation_action")
    private String operationAction;

    /**
     * 操作时间
     */
    @TableField("operation_date")
    private Date operationDate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;


}
