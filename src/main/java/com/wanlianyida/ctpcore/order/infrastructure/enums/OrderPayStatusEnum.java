package com.wanlianyida.ctpcore.order.infrastructure.enums;

import lombok.Getter;

@Getter
public enum OrderPayStatusEnum {
    PAY_STATUS_100(100,"待结算"),
    PAY_STATUS_110(110,"待付款"),
    PAY_STATUS_120(120,"待收款"),
    PAY_STATUS_130(130,"已收款"),
    ;

    private Integer payStatus;

    private String desc;

    OrderPayStatusEnum(Integer payStatus, String desc) {
        this.payStatus = payStatus;
        this.desc = desc;
    }
}
