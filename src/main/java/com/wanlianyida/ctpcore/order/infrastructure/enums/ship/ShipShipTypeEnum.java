package com.wanlianyida.ctpcore.order.infrastructure.enums.ship;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 发货方式
 */
@Getter
@AllArgsConstructor
public enum ShipShipTypeEnum {
    /**
     * 企业自有车辆
     */
    STATUS_10("10","企业自有车辆"),
    /**
     *物流公司承运
     */
    STATUS_20("20","物流公司承运"),
    /**
     * 电子提单交割
     */
    STATUS_30("30","电子提单交割（仅提货有）"),
    ;

    private final String code;

    public final String desc;


    /**
     * 根据code获取枚举实例（避免NPE）
     */
    public static ShipShipTypeEnum getByCode(String code) {
        if (code == null) return null;
        for (ShipShipTypeEnum status : values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 校验状态是否有效
     */
    public static boolean isValidStatus(String code) {
        return getByCode(code) != null;
    }
}
