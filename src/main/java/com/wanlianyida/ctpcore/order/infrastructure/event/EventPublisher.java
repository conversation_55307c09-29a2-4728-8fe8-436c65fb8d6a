package com.wanlianyida.ctpcore.order.infrastructure.event;

import com.google.common.eventbus.AsyncEventBus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 事件发布器
 */

@Slf4j
@Component
public class EventPublisher {

    private final AsyncEventBus asyncEventBus;

    public EventPublisher(AsyncEventBus asyncEventBus) {
        this.asyncEventBus = asyncEventBus;
    }

    public void attachmentEvent(List<AttachmentEvent> events){
        asyncEventBus.post(events);
    }
}
