package com.wanlianyida.ctpcore.order.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 订单收款方式表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("oc_order_receive_method")
public class OcOrderReceiveMethodPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单付款id
     */
    @TableField("order_payment_id")
    private Long orderPaymentId;

    /**
     * 收款id
     */
    @TableField("receive_id")
    private Long receiveId;

    /**
     * 收款方式[20-银行承兑30-商业承兑40-电汇(电子钱包)50-电汇(银行账户)]
     */
    @TableField("receive_method")
    private Short receiveMethod;

    /**
     * 开户名称
     */
    @TableField("receiver_account_name")
    private String accountName;

    /**
     * 银行联行号
     */
    @TableField("receiver_bank_union_no")
    private String bankUnionNumber;

    /**
     * 银行名称
     */
    @TableField("receiver_bank_name")
    private String bankName;

    /**
     * 银行账号
     */
    @TableField("receiver_bank_account_no")
    private String bankAccountNumber;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;
}
