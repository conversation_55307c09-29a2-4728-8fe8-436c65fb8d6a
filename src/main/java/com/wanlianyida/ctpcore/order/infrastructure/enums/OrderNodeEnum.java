package com.wanlianyida.ctpcore.order.infrastructure.enums;

import lombok.Getter;

/**
 * 订单履约节点
 */
@Getter
public enum OrderNodeEnum {
    STATUS_10(10,"提交订单","订单创建时间"),
    STATUS_20(20,"签约","订单状态从待签约变更成下一状态时间"),
    STATUS_30(30,"支付预付款","订单关联预付款类型的应付单状态变为付款成功的时间"),
    STATUS_40(40,"发货","订单状态首次变为部分发货/已发货的时间"),
    STATUS_90(90,"申请提货","订单首个提货单的创建时间"),
    STATUS_50(50,"确认收货","订单最后一个发货单/提货单的确认收货时间"),
    STATUS_60(60,"结算","订单关联的结算单状态变为已确认时，对应的时间"),
    STATUS_80(80,"支付尾款","订单关联尾款类型的应付单状态变为付款成功的时间"),
    STATUS_70(70,"交易完成","订单状态变为交易完成的时间"),
    ;

    private Integer status;

    private String desc;

    private String remark;

    OrderNodeEnum(Integer status, String desc, String remark) {
        this.status = status;
        this.desc = desc;
        this.remark = remark;
    }
}
