package com.wanlianyida.ctpcore.order.interfaces.facade.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 付款信息
 */
@Data
public class OrderPaymentDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 付款条件
     */
    private Integer paymentTerms;

    /**
     * 预付款比例
     */
    private BigDecimal prepayRatio;

    /**
     * 付款方式
     */
    private String payMode;

    /**
     * 结算数量
     */
    private Integer settlementQuantity;

    /**
     * 结算单价
     */
    private Integer settlementPrice;

}
