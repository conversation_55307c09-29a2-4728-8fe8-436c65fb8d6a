package com.wanlianyida.ctpcore.order.interfaces.facade.command.ship;

import com.wanlianyida.ctpcore.order.interfaces.facade.command.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 订单状态
 */
@Data
public class SetLogisticsCommand implements Serializable {
    private static final long serialVersionUID = -8294120282780885451L;
    /**
     * 订单号
     */
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 发货单号
     */
    @NotEmpty(message = "发货单号不能为空")
    private String pickUpNo;

    /**
     * 发货方式 10-企业自有车辆 20-物流公司承运
     * @see com.wanlianyida.ctpcore.order.infrastructure.enums.ship.ShipShipTypeEnum
     */
    @NotEmpty(message = "发货方式不能为空")
    private String shipType;

    /**
     * 物流公司id（物流公司承运时必传）
     */
    private String logistCompanyId;

    /**
     * 物流公司（物流公司承运时必传）
     */
    private String logistCompany;

    /**
     * 物流单号（物流公司承运时必传）
     */
    private String logistNumber;

    /**
     * 车辆信息
     */
    @NotNull(message = "车辆信息不能为空")
    @Valid
    private List<PickUpCarCommand> pickUpCar;
}
