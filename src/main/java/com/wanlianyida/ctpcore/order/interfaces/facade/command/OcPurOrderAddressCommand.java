package com.wanlianyida.ctpcore.order.interfaces.facade.command;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

/**
 * 意向单联系人
 */
@Data
public class OcPurOrderAddressCommand implements Serializable {
    private static final long serialVersionUID = -2566500271023016203L;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人为空")
    private String linkName;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话为空")
    private String linkTelephone;

    /**
     * 省-
     */
    @NotBlank(message = "地址省为空")
    private String province;

    /**
     * 省-code
     */
    @NotBlank(message = "地址省编码为空")
    private String provinceCode;


    /**
     * 市
     */
    @NotBlank(message = "地址市为空")
    private String city;

    /**
     * 市-code
     */
    @NotBlank(message = "地址市编码空")
    private String cityCode;


    /**
     * 区
     */
    @NotBlank(message = "地址区空")
    private String area;

    /**
     * 区-code
     */
    @NotBlank(message = "地址区编码空")
    private String areaCode;


    /**
     * 地址
     */
    @NotBlank(message = "详细地址为空")
    private String detailAddress;


}
