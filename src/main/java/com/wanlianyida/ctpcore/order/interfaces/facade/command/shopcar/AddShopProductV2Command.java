package com.wanlianyida.ctpcore.order.interfaces.facade.command.shopcar;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 更新购物车
 */
@Data
public class AddShopProductV2Command implements Serializable {

    /**
     * 商家id
     */
    @NotEmpty(message = "商家id为空")
    private String companyId;

    /**
     * 商家名称
     */
    @NotEmpty(message = "商家名称为空")
    private String companyName;

    /**
     * 店铺id
     */
    @NotNull(message = "shopId为空")
    private Long shopId;

    /**
     * 店铺名称
     */
    @NotEmpty(message = "shopName为空")
    private String shopName;

    /**
     * spuCode
     */
    @NotEmpty(message = "spuCode为空")
    private String spuCode;

    /**
     * sukCode
     */
    @NotEmpty(message = "sukCode为空")
    private String skuCode;

    /**
     * 购买数量
     */
    @NotNull(message = "购买数量为空")
    private BigDecimal purchaseQuantity;

    /**
     * 单价
     */
    @NotNull(message = "单价为空")
    private BigDecimal price;

    private String userId;
}
