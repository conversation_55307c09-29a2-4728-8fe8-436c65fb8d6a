package com.wanlianyida.ctpcore.order.interfaces.facade.command;

import com.wanlianyida.ctpcore.order.interfaces.facade.command.attachment.AttachmentCommand;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单状态
 */
@Data
public class OrderStartSmentCommand implements Serializable {
    private static final long serialVersionUID = -8294120282780885451L;


    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 商品总金额
     */
    private BigDecimal productTotalAmount;

    /**
     * 运费
     */
    private BigDecimal expenseAmount;

    /**
     * 其它费用
     */
    private BigDecimal otherExpenses;


    /**
     * 其它费用
     */
    private String otherExpensesRemark;

    /**
     * 已结算金额
     */
    private BigDecimal smentAmount;

    /**
     * 剩余结算金额
     */
    private BigDecimal remainAmount;

    /**
     * 结算商品
     */
    private List<Product> products;

    /**
     * 附件
     */
    private List<AttachmentCommand> attachments;

    /**
     * 结算商品
     */
    @Data
    public static class Product implements Serializable{

        private static final long serialVersionUID = 4241577382637455331L;

        /**
         * 订单商品id
         */
        private String orderProductId;

        /**
         * 商品编号
         */
        private String skuCode;

        /**
         * sku名称
         */
        private String skuName;

        /**
         * 结算数量
         */
        private BigDecimal smentQuantity;

        /**
         * 结算单价
         */
        private BigDecimal smentPrice;

        /**
         * 结算小计
         */
        private BigDecimal sementSubtotal;

        /**
         * 单位转换
         */
        private BigDecimal unitCon;
    }


}
