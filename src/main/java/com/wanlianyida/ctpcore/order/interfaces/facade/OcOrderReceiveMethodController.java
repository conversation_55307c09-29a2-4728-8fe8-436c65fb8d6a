package com.wanlianyida.ctpcore.order.interfaces.facade;

import com.wanlianyida.ctpcore.order.application.service.OcOrderReceiveMethodAppService;
import com.wanlianyida.ctpcore.order.interfaces.facade.dto.OrderReceiveMethodDTO;
import com.wanlianyida.ctpcore.order.interfaces.facade.query.OrderReceiveMethodQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * <p>
 * 订单收款方式表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@RestController
@RequestMapping("/ocOrderReceiveMethod")
@Validated
public class OcOrderReceiveMethodController {

    @Resource
    private OcOrderReceiveMethodAppService ocOrderReceiveMethodAppService;

    /**
     * 买家-我的订单-付款-付款方式查询(非电子钱包)
     */
    @PostMapping("/queryList")
    public ResultMode<List<OrderReceiveMethodDTO>> queryList(@RequestBody @Validated OrderReceiveMethodQuery query) {
        return ocOrderReceiveMethodAppService.queryList(query);
    }



}
