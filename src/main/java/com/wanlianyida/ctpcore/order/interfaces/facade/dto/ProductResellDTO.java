package com.wanlianyida.ctpcore.order.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel("转卖列表返回值")
@Data
public class ProductResellDTO implements Serializable {
    private static final long serialVersionUID = -1532516454863003879L;

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("sku编号")
    private String skuCode;

    @ApiModelProperty("spu编号")
    private String spuCode;

    @ApiModelProperty("sku名称")
    private String skuName;

    @ApiModelProperty("卖家ID")
    private String sellerCompanyId;

    @ApiModelProperty("卖家名称")
    private String sellerCompanyName;

    @ApiModelProperty("卖家店铺ID")
    private Long sellerShopId;

    @ApiModelProperty("卖家店铺名称")
    private String sellerShopName;

    @ApiModelProperty("sku id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relSkuId;

    @ApiModelProperty("品牌ID")
    private Long relBrandId;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("3级品类ID")
    private Long categoryId3;

    @ApiModelProperty("3级品类名称")
    private String categoryName3;

    @ApiModelProperty("SKU规格名称")
    private String skuSpecificationName;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("采购日期")
    private Date orderDate;

    @ApiModelProperty("采购数量")
    private BigDecimal purchaseTotalQuantity;

    @ApiModelProperty("采购单价")
    private BigDecimal purchasePriceFee;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("转卖时间")
    private Date resellDate;

    @ApiModelProperty("转卖商品编号")
    private String resellSkuCode;

    @ApiModelProperty("转卖状态（10未转卖 20已转卖）")
    private Integer resellStatus;

    @ApiModelProperty("图片")
    private String picUrl;

    @ApiModelProperty("计量单位id")
    private Integer relMeasurementUnitId;

    @ApiModelProperty("计价单位id")
    private Integer relPricingUnitId;
}
