package com.wanlianyida.ctpcore.order.interfaces.facade.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应付信息：买家付款
 */
@Data
public class OrderShouldPayDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单子状态
     */
    private Integer subStatus;

    /**
     * 待支付 100 枚举:payStatus
     */
    private Integer payStatus;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 运费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 付款条件
     */
    private Integer paymentTerms;

    /**
     * 预付款比例
     */
    private BigDecimal prepayRatio;

    /**
     * 需付款金额
     */
    private BigDecimal payAmount;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 已付款金额
     */
    private BigDecimal paidAmount = BigDecimal.ZERO;

    /**
     * 付款方式
     */
    private List<Integer> payModeList;

}
