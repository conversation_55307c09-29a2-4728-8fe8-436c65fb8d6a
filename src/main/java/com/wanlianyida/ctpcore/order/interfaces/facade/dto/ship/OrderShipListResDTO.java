package com.wanlianyida.ctpcore.order.interfaces.facade.dto.ship;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 创建发/提货单list返回值
 */
@Data
public class OrderShipListResDTO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货单号
     */
    private String shipNo;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 买家名称
     */
    private String buyerCompanyName;

    /**
     * 卖家名称
     */
    private String sellerCompanyName;

    /**
     * 询价状态[10-待报价,20-已报价,30-已成交,40-已拒绝,50-已撤销]
     */
    private Integer logisticsInquiryStatus;

    /**
     * 发货单列表详情
     */
    List<OrderShipListDetailResDTO> detailList;
}
