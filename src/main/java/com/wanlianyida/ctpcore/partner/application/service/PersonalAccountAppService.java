package com.wanlianyida.ctpcore.partner.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.application.assembler.UmMemberAssemble;
import com.wanlianyida.ctpcore.partner.domain.model.condition.QueryBoundInfoCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.SwitchCompanyCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmEmployeeBindingRecordCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.BoundCompanyEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmEmployeeBindingRecordEntity;
import com.wanlianyida.ctpcore.partner.domain.service.EnterpriseAccountDomainService;
import com.wanlianyida.ctpcore.partner.domain.service.PersonalAccountDomainService;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.util.GetUserIdUtil;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.*;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.AuthenticateRealNameResultDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.PersonalInfoDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.SwitchCompanyDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmEmployeeBindingRecordDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmAccreditAuditQuery;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmEmployeeBindingRecordQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.partner.api.model.dto.BindCompanyDTO;
import com.wanlianyida.partner.api.model.query.QueryBoundInfoQuery;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Service
public class PersonalAccountAppService {
    @Resource
    private PersonalAccountDomainService domainService;

    @Resource
    private EnterpriseAccountDomainService enterpriseAccountDomainService;
    @Resource
    private GetUserIdUtil getUserIdUtil;

    public Map<String,String> register(CmdUserInfoCommand command) {
        String passwordStrength = command.getPasswordStrength();
        if (StrUtil.isEmpty(passwordStrength)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_PASSWORD_STRENGTH_NOT_EMPTY);
        }

        return domainService.register(command);
    }

    public void updateUserPhone(CmdUserInfoCommand command) {
        domainService.updateUserPhone(command);
    }

    public void setUserEmailAddress(CmdUserInfoCommand command) {
        domainService.setUserEmailAddress(command);
    }


    public AuthenticateRealNameResultDTO authenticateRealName(AuthenticateRealNameCommand command) {
        String frontUrl = command.getFrontUrl();
        String backUrl = command.getBehindUrl();
        if (StrUtil.isEmpty(frontUrl) || StrUtil.isEmpty(backUrl)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_ID_CARD_NOT_EMPTY);
        }

        return domainService.authenticateRealName(command);
    }

    public void updateUserBasicTwoFactors(AuthenticateRealNameCommand command) {
        String idCard = command.getIdCardNo();
        String name = command.getUserName();
        String frontUrl = command.getFrontUrl();
        String backUrl = command.getBehindUrl();

        if (ObjectUtil.isEmpty(name) || ObjectUtil.isEmpty(idCard) || StrUtil.isEmpty(frontUrl) || StrUtil.isEmpty(backUrl)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_COMPLETE_ID_CARD_INFO);
        }
        domainService.updateUserBasicTwoFactors(command);

    }

    public void applyToBeAdministrator(AdministratorCommand command) {
        String companyId = command.getCompanyId();
        String fileUrl = command.getFileUrl();
        String phone = command.getPhone();
        if (StrUtil.isEmpty(companyId) || StrUtil.isEmpty(fileUrl) ||
                StrUtil.isEmpty(phone) || StrUtil.isEmpty(fileUrl)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_COMPLETE);
        }

        domainService.applyToBeAdministrator(command);

    }

    public void handOverAdministrator(AdministratorCommand command) {
        String fileUrl = command.getFileUrl();
        String phone = command.getPhone();
        String newPhone = command.getNewPhone();
        if (StrUtil.isEmpty(fileUrl) || StrUtil.isEmpty(phone) || StrUtil.isEmpty(newPhone)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_COMPLETE);
        }
        domainService.handOverAdministrator(command);

    }

    /**
     * 分页查询绑定记录
     */
    public ResultMode<List<UmEmployeeBindingRecordDTO>> pageBindingRecordCondition(PagingInfo<UmEmployeeBindingRecordQuery> pageQuery) {
        Page<UmAccreditAuditQuery> page = PageHelper.startPage(pageQuery.currentPage, pageQuery.pageLength, pageQuery.getCountTotal());
        page.setOrderBy("updated_date desc");
        UmEmployeeBindingRecordCondition condition = BeanUtil.toBean(pageQuery.getFilterModel(), UmEmployeeBindingRecordCondition.class);
        List<UmEmployeeBindingRecordEntity> list = enterpriseAccountDomainService.queryBindingHistoryCondition(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, UmEmployeeBindingRecordDTO.class), (int) page.getTotal());
    }

    public ResultMode<Integer> countPendingInvitedEmployees(UmEmployeeBindingRecordQuery query){
        return ResultMode.success(enterpriseAccountDomainService.countPendingInvitedEmployees(query));
    }


    /**
     * 统计绑定记录数量
     */
    public ResultMode<Integer> countBindingHistoryCondition(UmEmployeeBindingRecordQuery query) {
        Integer count = enterpriseAccountDomainService.countBindingHistoryCondition(BeanUtil.toBean(query, UmEmployeeBindingRecordCondition.class));
        return ResultMode.success(count);
    }

    public PersonalInfoDTO getPersonalDetailPage() {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)){
            throw new CtpCorePartnerException("用户未登录");
        }
        String userId = getUserIdUtil.getUserId();
        PersonalInfoDTO dto = new PersonalInfoDTO();
        if (StrUtil.isNotEmpty(userId)){
            dto = domainService.getPersonalDetailPage(userId);
        }
        return  dto;
    }

    public void employeeInviteProcess(HandleInvitationCommand command) {
        domainService.employeeInviteProcess(command);
    }

    public void setDefaultCompany(SetDefaultCompanyCommand command) {
        domainService.setDefaultCompany(command);
    }

    public PageInfo<BindCompanyDTO> queryBoundCompanyInfo(PagingInfo<QueryBoundInfoQuery> pagingInfo) {
        // req 转condition
        QueryBoundInfoCondition condition = BeanUtil.copyProperties(pagingInfo.getFilterModel(), QueryBoundInfoCondition.class);
        PagingInfo<QueryBoundInfoCondition> conditionPage = new PagingInfo<>(condition,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        PageInfo<BoundCompanyEntity> list = domainService.queryBoundCompanyInfo(conditionPage);
        return UmMemberAssemble.getInstance().queryBoundCompanyInfo(list);
    }

    public SwitchCompanyDTO switchCompany(SwitchCompanyCommand command) {
        // req 转condition
        SwitchCompanyCondition condition = BeanUtil.copyProperties(command, SwitchCompanyCondition.class);
        return domainService.switchCompany(condition);
    }

    public void queryUserRealNameStatus(String phone) {
        if (StrUtil.isEmpty(phone)){
            throw new CtpCorePartnerException("指定管理员手机号码不能为空");
        }
        if (!PhoneUtil.isPhone(phone)){
            throw new CtpCorePartnerException("指定管理员手机号码格式不正确");
        }
        domainService.queryUserRealNameStatus(phone);

    }
}



