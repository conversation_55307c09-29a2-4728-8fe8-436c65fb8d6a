package com.wanlianyida.ctpcore.partner.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.application.assembler.UmMemberAssemble;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmMemberCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmMemberEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmUserCacheEntity;
import com.wanlianyida.ctpcore.partner.domain.service.UmMemberDomainService;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmMemberAddCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmMemberDeleteCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmMemberUpdateCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.MemberConditionDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmMemberDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmUserCacheDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmMemberListQuery;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmMemberQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

@Service
public class UmMemberAppService {
    @Resource
    private UmMemberDomainService domainService;

    public Boolean add(UmMemberAddCommand command) {
        // req 转entity
        UmMemberEntity umMemberEntity = BeanUtil.copyProperties(command, UmMemberEntity.class);
        umMemberEntity.setPostId(command.getPost());
        umMemberEntity.setCompanyId(command.getCompanyId());
        return domainService.add(umMemberEntity);
    }

    public Boolean update(UmMemberUpdateCommand command) {
        // req 转entity
        UmMemberEntity umMemberEntity = BeanUtil.copyProperties(command, UmMemberEntity.class);
        umMemberEntity.setPostId(command.getPost());
        umMemberEntity.setUserStatus(command.getStatus());
        return domainService.update(umMemberEntity);
    }

    public Boolean delete(UmMemberDeleteCommand command) {
        // req 转entity
        UmMemberEntity umMemberEntity = BeanUtil.copyProperties(command, UmMemberEntity.class);
        return domainService.delete(umMemberEntity);
    }


    public UmMemberDTO queryDetail(UmMemberListQuery query) {
        // req 转condition
        UmMemberCondition condition = BeanUtil.copyProperties(query, UmMemberCondition.class);
        UmMemberEntity entity = domainService.queryDetail(condition);
        if (ObjectUtil.isNotEmpty(entity)) {
            return BeanUtil.copyProperties(entity, UmMemberDTO.class);
        }
        return null;
    }

    public List<MemberConditionDTO> queryByCondition(UmMemberQuery query) {
        // req 转condition
        UmMemberCondition condition = BeanUtil.copyProperties(query, UmMemberCondition.class);
        List<UmMemberEntity> entities = domainService.queryByCondition(condition);
        if (CollUtil.isNotEmpty(entities)) {
            return BeanUtil.copyToList(entities, MemberConditionDTO.class);
        }
        return Collections.emptyList();
    }

    public MemberConditionDTO queryCompanyInfoByUserId(String userBaseId) {
        return domainService.queryCompanyInfoByUserId(userBaseId);
    }

    public PageInfo<UmMemberDTO> queryPage(PagingInfo<UmMemberListQuery> pagingInfo) {
        // req 转condition
        UmMemberCondition condition = BeanUtil.copyProperties(pagingInfo.getFilterModel(), UmMemberCondition.class);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }
        String companyId = condition.getCompanyId();
        if (StrUtil.isEmpty(companyId)){
            condition.setCompanyId(tokenInfo.getCompanyId());
        }
        PagingInfo<UmMemberCondition> conditionPage = new PagingInfo<>(condition,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        PageInfo<UmMemberEntity> list = domainService.queryPage(conditionPage);
        return UmMemberAssemble.getInstance().queryPage(list);
    }

    public List<UmUserCacheDTO> updateCache() {
        List<UmUserCacheEntity> list =domainService.queryAllUserInfo();
        if (CollUtil.isNotEmpty(list)){
            return BeanUtil.copyToList(list, UmUserCacheDTO.class);
        }
        return Collections.emptyList();
    }

    public List<UmMemberDTO> queryListByCondition(UmMemberListQuery query) {
        // req 转condition
        UmMemberCondition condition = BeanUtil.copyProperties(query, UmMemberCondition.class);
        List<UmMemberEntity> entities = domainService.queryListByCondition(condition);
        if (CollUtil.isNotEmpty(entities)) {
            return BeanUtil.copyToList(entities, UmMemberDTO.class);
        }
        return Collections.emptyList();
    }

    public void batchDeleteByOperatorIds(List<String> operatorIds) {
        domainService.batchDeleteByOperatorIds(operatorIds);
    }

    public UmMemberDTO queryByOperatorId(UmMemberQuery query) {
        // req 转condition
        UmMemberCondition condition = BeanUtil.copyProperties(query, UmMemberCondition.class);
        UmMemberEntity entity = domainService.queryByOperatorId(condition);
        if (ObjectUtil.isNotEmpty(entity)) {
            return BeanUtil.copyProperties(entity, UmMemberDTO.class);
        }
        return null;
    }

    public List<MemberConditionDTO> queryByCompanyIds(UmMemberQuery query) {
        // req 转condition
        UmMemberCondition condition = BeanUtil.copyProperties(query, UmMemberCondition.class);
        List<UmMemberEntity> entities = domainService.queryByCompanyIds(condition);
        if (CollUtil.isNotEmpty(entities)) {
            return BeanUtil.copyToList(entities, MemberConditionDTO.class);
        }
        return Collections.emptyList();
    }
}
