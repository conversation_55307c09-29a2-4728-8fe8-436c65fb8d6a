package com.wanlianyida.ctpcore.partner.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.bo.UmShopAddAndUpdateBO;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopAreaEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopCategoryEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopEntity;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmShopCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopAreaDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopCategoryDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopDTO;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UmShopAssemble {
    /**
     * 构建添加和更新bo
     *
     * @param command 命令
     * @return {@code UmShopAddAndUpdateBO }
     */
    public static UmShopAddAndUpdateBO buildAddAndUpdateBo(UmShopCommand command, String type) {
        UmShopAddAndUpdateBO bo = new UmShopAddAndUpdateBO();
        bo.setUmShopEntity(BeanUtil.copyProperties(command, UmShopEntity.class));
        bo.setUmShopCategoryEntity(BeanUtil.copyToList(command.getShopCategoryCommandList(), UmShopCategoryEntity.class));
        bo.setUmShopAreaEntity(BeanUtil.copyToList(command.getShopAreaCommandList(), UmShopAreaEntity.class));
        // 设置用户信息
        setUserInfo(bo, type);
        return bo;
    }

    /**
     * 设置用户信息
     *
     * @param bo   bo
     * @param type 类型
     */
    private static void setUserInfo(UmShopAddAndUpdateBO bo, String type) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String userBaseId = tokenInfo.getUserBaseId();
        String username = tokenInfo.getUsername();

        UmShopEntity umShopEntity = bo.getUmShopEntity();
        List<UmShopCategoryEntity> umShopCategoryEntity = bo.getUmShopCategoryEntity();
        List<UmShopAreaEntity> umShopAreaEntity = bo.getUmShopAreaEntity();
        switch (type) {
            case "add":
                umShopEntity.setId(null);
                umShopEntity.setCreatedDate(new Date());
                umShopEntity.setCreatorId(StrUtil.isNotEmpty(userBaseId) ? userBaseId : "");
                umShopEntity.setCreatorName(StrUtil.isNotEmpty(username) ? username : "");
                break;
            case "update":
                umShopEntity.setUpdatedDate(new Date());
                umShopEntity.setUpdaterId(StrUtil.isNotEmpty(userBaseId) ? userBaseId : "");
                umShopEntity.setUpdaterName(StrUtil.isNotEmpty(username) ? username : "");
                umShopCategoryEntity.forEach(item -> {
                    item.setCreatedDate(new Date());
                    item.setCreatorId(StrUtil.isNotEmpty(userBaseId) ? userBaseId : "");
                    item.setCreatorName(StrUtil.isNotEmpty(username) ? username : "");
                    item.setUpdatedDate(new Date());
                    item.setUpdaterId(StrUtil.isNotEmpty(userBaseId) ? userBaseId : "");
                    item.setUpdaterName(StrUtil.isNotEmpty(username) ? username : "");
                });

                umShopAreaEntity.forEach(item -> {
                    item.setCreatedDate(new Date());
                    item.setCreatorId(StrUtil.isNotEmpty(userBaseId) ? userBaseId : "");
                    item.setCreatorName(StrUtil.isNotEmpty(username) ? username : "");
                    item.setUpdatedDate(new Date());
                    item.setUpdaterId(StrUtil.isNotEmpty(userBaseId) ? userBaseId : "");
                    item.setUpdaterName(StrUtil.isNotEmpty(username) ? username : "");
                });
                break;
                default:
                    throw new RuntimeException("类型错误");
        }
    }

    /**
     * 构建店铺dto
     *
     * @param umShopEntity             um商店实体
     * @param umShopAreaEntityList     um店铺区域实体列表
     * @param umShopCategoryEntityList um店铺类别实体列表
     * @return {@code UmShopDTO }
     */
    public static UmShopDTO buildShopDTO(UmShopEntity umShopEntity, List<UmShopAreaEntity> umShopAreaEntityList, List<UmShopCategoryEntity> umShopCategoryEntityList) {
        UmShopDTO umShopDTO = BeanUtil.copyProperties(umShopEntity, UmShopDTO.class);
        umShopDTO.setUmShopAreaDTOS(BeanUtil.copyToList(umShopAreaEntityList, UmShopAreaDTO.class));
        umShopDTO.setUmShopCategoryDTOS(BeanUtil.copyToList(umShopCategoryEntityList, UmShopCategoryDTO.class));
        return umShopDTO;
    }

    /**
     * 构建店铺dtoList
     *
     * @param shopEntities             商店实体
     * @param umShopAreaEntityList     um店铺区域实体列表
     * @param umShopCategoryEntityList um店铺类别实体列表
     * @return {@code List<UmShopDTO> }
     */
    public static List<UmShopDTO> buildShopDTOList(List<UmShopEntity> shopEntities, List<UmShopAreaEntity> umShopAreaEntityList, List<UmShopCategoryEntity> umShopCategoryEntityList) {
        List<UmShopDTO> umShopDTOList = BeanUtil.copyToList(shopEntities, UmShopDTO.class);
        // 店铺主营品类根据店铺id分组
        Map<Long, List<UmShopCategoryEntity>> shopCategoryMap = new HashMap<>();
        // 店铺主营区域根据店铺id分组
        Map<Long, List<UmShopAreaEntity>> shopAreaMap = new HashMap<>();
        if (CollUtil.isNotEmpty(umShopAreaEntityList)){
            // 店铺主营区域根据店铺id分组
            shopAreaMap = umShopAreaEntityList.stream().collect(Collectors.groupingBy(UmShopAreaEntity::getShopId));
        }
        if (CollUtil.isNotEmpty(umShopCategoryEntityList)){
            // 店铺主营品类根据店铺id分组
            shopCategoryMap = umShopCategoryEntityList.stream().collect(Collectors.groupingBy(UmShopCategoryEntity::getShopId));
        }

        for (UmShopDTO umShopDTO : umShopDTOList) {
            Long id = umShopDTO.getId();
            List<UmShopAreaEntity> umShopAreaEntities = shopAreaMap.get(id);
            if (umShopAreaEntities != null){
                umShopDTO.setUmShopAreaDTOS(BeanUtil.copyToList(umShopAreaEntities, UmShopAreaDTO.class));
            }
            List<UmShopCategoryEntity> umShopCategoryEntities = shopCategoryMap.get(id);
            if (umShopCategoryEntities != null){
                umShopDTO.setUmShopCategoryDTOS(BeanUtil.copyToList(umShopCategoryEntities, UmShopCategoryDTO.class));
            }
        }
        return umShopDTOList;
    }

    /**
     * 分页查询
     *
     * @param pageInfo 页面信息
     * @return {@code PageInfo<UmShopDTO> }
     */
    public static PageInfo<UmShopDTO> queryPage(PageInfo<UmShopEntity> pageInfo) {
        if (ObjUtil.isNull(pageInfo) || IterUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        PageInfo<UmShopDTO> page = new PageInfo();
        page.setTotal(pageInfo.getTotal());
        page.setList(BeanUtil.copyToList(pageInfo.getList(), UmShopDTO.class));
        return page;
    }
}
