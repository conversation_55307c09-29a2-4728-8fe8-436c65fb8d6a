package com.wanlianyida.ctpcore.partner.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmOrgCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmOrgEntity;
import com.wanlianyida.ctpcore.partner.domain.service.UmOrgDomainService;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmOrgCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmOrgDeleteCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmOrgDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmOrgTreeQuery;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UmOrgAppService {
    @Resource
    private UmOrgDomainService umOrgDomainService;

    public Boolean add(UmOrgCommand command) {
        // req 转entity
        UmOrgEntity umOrgEntity = BeanUtil.copyProperties(command, UmOrgEntity.class);
        return umOrgDomainService.add(umOrgEntity);
    }

    public Boolean update(UmOrgCommand command) {
        // req 转entity
        UmOrgEntity umOrgEntity = BeanUtil.copyProperties(command, UmOrgEntity.class);
        return umOrgDomainService.update(umOrgEntity);
    }

    public UmOrgDTO delete(UmOrgDeleteCommand command) {
        // req 转entity
        UmOrgEntity umOrgEntity = BeanUtil.copyProperties(command, UmOrgEntity.class);
        UmOrgEntity umOrg = umOrgDomainService.delete(umOrgEntity);
        if (ObjectUtil.isNotEmpty(umOrg)){
            return BeanUtil.copyProperties(umOrg, UmOrgDTO.class);
        }
        return null;
    }

    public UmOrgDTO buildOrganizationTree(UmOrgTreeQuery query) {
        // req 转condition
        UmOrgCondition condition = new UmOrgCondition();
        condition.setPlatformType(query.getPlatformType());
        String licenseNo = query.getLicenseNo();
        if (StrUtil.isEmpty(licenseNo)){
            licenseNo = JwtUtil.getTokenInfo().getLicenseNo();
        }
        condition.setSocialCreditCode(licenseNo);

        UmOrgEntity umOrgEntity = umOrgDomainService.buildOrganizationTree(condition);
        if (umOrgEntity != null){
            return BeanUtil.copyProperties(umOrgEntity, UmOrgDTO.class);
        }
        return null;
    }

    public UmOrgDTO validatedParams(UmOrgCommand command) {
        // req 转entity
        UmOrgEntity umOrgEntity = BeanUtil.copyProperties(command, UmOrgEntity.class);
        UmOrgEntity orgEntity = umOrgDomainService.validatedParams(umOrgEntity);
        if (ObjectUtil.isNotEmpty(orgEntity)){
            return BeanUtil.copyProperties(orgEntity, UmOrgDTO.class);
        }
        return null;
    }
}
