package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import lombok.Data;

import java.util.List;

@Data
public class UmCompanyPageMiniQuery {

    /**
     * 公司ID
     */
    private String id;

    /**
     * 公司ID List
     */
    private List<String> companyIdList;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司类型
     */
    private String BizType;

    private List<String> idList;
}
