package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 员工DT
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
public class UmMemberDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 企业信息ID
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 用户等级:1管理员,2普通员工
     */
    private String levelType;

    /**
     * 用户ID
     */
    private String userBaseId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 部门id
     */
    private String department;

    /**
     * 部门信息集合
     */
    private List<UmDeptDTO> deptList;

    /**
     * 岗位ID
     */
    private String postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 状态:11有效,21无效
     */
    private String userStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 邀请处理状态 10-未处理 20-已绑定 30-已拒绝
     */
    private String auditStatus;

    /**
     * 10-正常 20-存在管理员过户
     */
    private String companyStatus = "10";
}
