package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.wanlianyida.ctpcore.partner.infrastructure.config.ValidPlatformType;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * 员工更新
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
public class UmMemberUpdateCommand {
    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private String id;


    /**
     * 用户等级:1管理员,2普通员工
     */
    private String levelType;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private String userBaseId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 部门id集合
     */
    private List<String> deptIds;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 状态:11有效,21无效
     */
    @NotNull(message = "是否启用不能为空")
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @ValidPlatformType
    @NotNull(message = "平台类型不能为空")
    private String platformType;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 10-新增 20-更新 30-删除 40-绑定 50-管理员操作解绑  60-用户主动解绑
     */
    private String bizScene;

    /**
     * 是否默认公司 0-否 1-是
     */
    private Integer defaultCompanyFlag;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 选择的角色ids
     */
    private List<Long> selPermissionIdList;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 企业id
     */
    private String companyId;


}
