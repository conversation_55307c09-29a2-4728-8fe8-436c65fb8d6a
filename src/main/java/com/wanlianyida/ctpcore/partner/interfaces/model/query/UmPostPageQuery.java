package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import com.wanlianyida.ctpcore.partner.infrastructure.config.ValidPlatformType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 岗位分页查询
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@Data
public class UmPostPageQuery {
    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位级别
     */
    private String postGrade;


    /**
     * 平台类型 10-用户端 20-平台端
     */
    @ValidPlatformType
    @NotNull(message = "平台类型不能为空")
    private String platformType;

}
