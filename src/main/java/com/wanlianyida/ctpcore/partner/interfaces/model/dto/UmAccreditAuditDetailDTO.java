package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:19
 */
@Data
@ApiModel("授权委托书审核")
public class UmAccreditAuditDetailDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("过户用户id")
    private String transferUserId;

    @ApiModelProperty("过户用户编号")
    private String transferUserCode;

    @ApiModelProperty("过户用户姓名")
    private String transferUserName;

    @ApiModelProperty("过户用户身份证号")
    private String transferIdCardNo;

    @ApiModelProperty("过户用户手机号")
    private String transferMobile;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业编码")
    private String companyCode;

    @ApiModelProperty("统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("授权委托书文件地址")
    private String fileUrl;

    @ApiModelProperty("来源类型[10-创建，20-申请，30-过户]")
    private Integer sourceType;

    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty("法定代表人")
    private String legalPerson;

    @ApiModelProperty("营业执照照片url")
    private String licenseFileUrl;

    @ApiModelProperty("营业执照审核状态")
    private Integer licenseAuditStatus;

    @ApiModelProperty("法定代表人姓名")
    private String legalPersonName;

    @ApiModelProperty("法定代表人身份证号")
    private String legalPersonIdCardNo;

    @ApiModelProperty("法定代表人审核状态")
    private Integer legalPersonAuditStatus;

    @ApiModelProperty("法定代表人人像面")
    private String legalPersonBehindUrl;

    /**
     * 最新数据标志 [0-否，1-是]
     */
    private Integer latestDataFlag;

    /**
     * 业务类型 [10-创建,20-变更]
     */
    private Integer bizType;
}
