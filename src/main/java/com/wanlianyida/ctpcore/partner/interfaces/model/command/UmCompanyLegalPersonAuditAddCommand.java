package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 10:13
 */
@Data
@ApiModel("公司法定代表人审核表")
public class UmCompanyLegalPersonAuditAddCommand {


    @ApiModelProperty("姓名")
    @NotNull(message = "姓名不能为空")
    private String userName;

    @ApiModelProperty("用户手机号")
    @NotNull(message = "用户手机号不能为空")
    private String userMobile;

    @ApiModelProperty("用户邮箱")
    @NotNull(message = "用户邮箱不能为空")
    private String userEmail;

    @ApiModelProperty("身份证号")
    @NotNull(message = "身份证号不能为空")
    private String idCardNo;

    @ApiModelProperty("身份证国徽面地址")
    @NotNull(message = "身份证国徽面地址不能为空")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    @NotNull(message = "身份证人像面地址不能为空")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty("性别[1-男,2-女]")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countyCode;

    @ApiModelProperty("县名称")
    private String countyName;

    @ApiModelProperty("详细地址")
    @NotNull(message = "详细地址不能为空")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issueAgency;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty("截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date untilDate;

    @ApiModelProperty("证件长期有效标志[11-是,21-否]")
    private Integer licenseLongValidFlag;

    @ApiModelProperty("企业信用代码")
    @NotNull(message = "企业信用代码不能为空")
    private String licenseNo;
}
