package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 计价类型配置
 */
@Data
public class BdPricingTypeConfigQuery {

    /**
     * 平台编号
     */
    @NotBlank(message = "平台编号不允许为空")
    @ApiModelProperty("平台编号")
    private String platformCode;

    /**
     * 启用状态：10启用 20停用
     */
    @ApiModelProperty("启用状态：10启用 20停用")
    private String enableStatus;

    /**
     * 计价类型名称
     */
    @ApiModelProperty("计价类型名称")
    private String pricingTypeName;

    /**
     * 计量单位 0否 1是
     */
    @ApiModelProperty("计量单位 0否 1是")
    private Integer measureUnit;

    /**
     * 计价单位 0否1是
     */
    @ApiModelProperty("计价单位 0否1是")
    private Integer pricingUnit;

}
