package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import lombok.Data;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;

/**
 * 计价类型配置
 */
@Data
public class BdPricingTypeConfigAddCommand {

    /**
     * 平台编号
     */
    @NotBlank(message = "平台编号不允许为空")
    private String platformCode;

    /**
     * 计价类型
     */
    private Integer pricingType;

    /**
     * 计价类型名称
     */
    private String pricingTypeName;

    /**
     * 单位类型 10计价单位 20计量单位 30计价+计量
     */
    private String unitType;

    /**
     * 最小值
     */
    private BigDecimal minValue;

    /**
     * 最大值
     */
    private BigDecimal maxValue;

    /**
     * 排序序号
     */
    private Integer sortNum;

    /**
     * 启用状态:10启用 20停用
     */
    private String enableStatus;

    /**
     * 小数点前位数
     */
    private Integer beforePoint;

    /**
     * 小数点后位数
     */
    private Integer afterPoint;

    /**
     * 计价方式：10输入值 20固定值
     */
    private String pricingMethod;

    /**
     * 数量取值方式：10取重量 20取固定值 30取输入值
     */
    private String quantityValueMethod;

    /**
     * 固定值
     */
    private String fixedValue;

    /**
     * 删除标记0正常1删除
     */
    private Integer deleted;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 计量单位 0否 1是
     */
    private Integer measureUnit;

    /**
     * 计价单位 0否1是
     */
    private Integer pricingUnit;

}
