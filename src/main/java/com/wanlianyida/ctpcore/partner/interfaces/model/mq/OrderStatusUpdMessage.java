package com.wanlianyida.ctpcore.partner.interfaces.model.mq;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单状态变更 发送消息
 */
@Data
public class OrderStatusUpdMessage  extends  OrderStatusCommonMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 优惠金额
     */
    private BigDecimal disAmount;

    /**
     * 商品总金额
     */
    private BigDecimal productTotalAmount;

    /**
     * 是否预付:0-否,1-是
     */
    private Boolean prepayFlag;

    /**
     * 预付款比例
     */
    private BigDecimal prepayRatio;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    private String buyerCompanyName;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    private String sellerCompanyName;

    /**
     * 运费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 10买家承担20卖家承担30无运费
     */
    private Integer freightBearer;

}
