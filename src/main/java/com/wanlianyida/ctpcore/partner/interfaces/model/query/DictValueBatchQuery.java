package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年09月06 17:14
 */
@Data
public class DictValueBatchQuery {

    @Size(min = 1, message = "字典编码不能为空")
    @ApiModelProperty("字典编码")
    private List<String> dictCodeList;
}
