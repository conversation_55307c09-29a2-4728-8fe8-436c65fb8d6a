package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 企业业务配置信息表 DTO
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
public class UmCompanyConfigDTO {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 企业ID
	 */
	private String companyId;

	/**
	 * 企业配置key
	 */
	private String configKey;

	/**
	 * 企业配置值
	 */
	private String configValue;


}
