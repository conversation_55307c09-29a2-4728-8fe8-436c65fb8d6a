package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import lombok.Data;

import java.util.List;

/**
 * 员工DT
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
public class UmMemberQuery {

    /**
     * ID
     */
    private Long id;

    /**
     * 企业信息ID
     */
    private String companyId;

    /**
     * 用户等级:1管理员,2普通员工
     */
    private String levelType;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 部门id
     */
    private String department;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 状态:11有效,21无效
     */
    private String status;


    /**
     * 企业信息IDs
     */
    private List<String> companyIds;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    private String platformType;

    /**
     * 公共参数
     */
    private String commonParams;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 操作员id集合
     */
    private List<String> operatorIds;

}
