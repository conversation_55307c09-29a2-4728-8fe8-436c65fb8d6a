package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 物流公司管理
 *
 * <AUTHOR>
 * @date 2024/12/05
 */
@Data
public class BdExpressDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 简介
     */
    private String companyProfile;

    /**
     * 状态(0停用 1启用)
     */
    private Integer enableStatus;


    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
