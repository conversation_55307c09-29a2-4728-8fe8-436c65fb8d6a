package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.wanlianyida.ctpcore.partner.infrastructure.config.ValidPlatformType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 岗位批量删除
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@Data
public class UmPostDeleteCommand {
    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @ValidPlatformType
    @NotNull(message = "平台类型不能为空")
    private String platformType;


}
