package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:05
 */
@Data
@ApiModel("企业员工绑定记录")
public class UmEmployeeBindingRecordDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("员工id")
    private String employeeId;

    @ApiModelProperty("员工编码")
    private String employeeCode;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业编码")
    private String companyCode;

    @ApiModelProperty("员工类型[10-:管理员,20-普通员工]")
    private Integer employeeType;

    @ApiModelProperty("绑定来源[10-邀请,11-注册,20-创建企业,21-过户,22-申请]")
    private Integer bindingSource;

    @ApiModelProperty("申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @ApiModelProperty("绑定状态：10 待审核（待确认）；20 已同意；90：已拒绝")
    private Integer bindingStatus;

    @ApiModelProperty("操作员id")
    private String operatorId;
}
