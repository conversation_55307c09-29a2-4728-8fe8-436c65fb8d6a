package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年05月09日 15:35
 */
@Data
@ApiModel("企业收款信息")
public class UmCompanyReceiptQuery {

    private Long id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业编码")
    private String companyIdentityCode;

    @ApiModelProperty("收款银行联行号")
    private String unionBankNo;

    @ApiModelProperty("开户行编码")
    private String bankCode;

    @ApiModelProperty("收款银行账号")
    private String bankAccount;

    @ApiModelProperty("默认标识[1-默认,0-非默认]")
    private Integer defaultFlag;
}
