package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门DTO
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class UmDeptDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 上级部门id
     */
    private Long parentId;

    /**
     * 上级部门名称
     */
    private String parentName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * dept 属性
     */
    private String deptProperty;

    /**
     * 描述
     */
    private String remark;

    private List<UmDeptDTO> children = new ArrayList<>();

}
