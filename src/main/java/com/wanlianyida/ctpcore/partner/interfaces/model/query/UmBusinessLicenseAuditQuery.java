package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:19
 */
@Data
@ApiModel("营业执照审核")
public class UmBusinessLicenseAuditQuery {

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private Integer auditStatus;
}
