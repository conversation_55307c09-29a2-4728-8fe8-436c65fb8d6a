package com.wanlianyida.ctpcore.partner.interfaces.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.partner.application.service.UmCompanyAppService;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.SetCompanyOfficialFlagCommand;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Component
public class CompanyTask {
    @Resource
    private UmCompanyAppService umCompanyAppService;

    /**
     * 同步店铺数据到es
     */
    @XxlJob("batchSetCompanyOfficialFlag")
    public void batchSetCompanyOfficialFlag() {
        log.info("batchSetCompanyOfficialFlag执行批量设置企业标志开始...");

        String jobParam = XxlJobHelper.getJobParam();
        log.info("原始jobParam: {}", jobParam);

        if (StrUtil.isEmpty(jobParam)) {
            log.warn("jobParam为空，无法执行批量设置企业标志操作");
            return;
        }

        // 去除参数首尾空格
        jobParam = jobParam.trim();
        log.info("去除首尾空格后的jobParam: {}", jobParam);

        SetCompanyOfficialFlagCommand command = new SetCompanyOfficialFlagCommand();
        // 分割参数并过滤空值
        List<String> params = StrUtil.split(jobParam, ',')
                .stream()
                .map(String::trim)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());

        log.info("分割后的参数列表: {}", params);

        if (params.size() < 2) {
            log.warn("参数数量不足，需要至少一个licenseNo和一个officialFlag，实际参数数量: {}", params.size());
            return;
        }

        // 解析officialFlag
        String flagStr = params.get(params.size() - 1);
        Integer officialFlag = Integer.parseInt(flagStr);

        // 校验officialFlag
        if (officialFlag != 0 && officialFlag != 1) {
            log.warn("officialFlag必须是0或1，实际值: {}", officialFlag);
            throw new CtpCorePartnerException("officialFlag 必须是 0 或 1");
        }

        // 提取licenseNoList
        List<String> licenseNoList = params.subList(0, params.size() - 1);

        log.info("解析结果 - licenseNoList: {}, officialFlag: {}", licenseNoList, officialFlag);

        if (CollectionUtil.isEmpty(licenseNoList)) {
            log.warn("licenseNoList为空，无法执行批量设置企业标志操作");
            return;
        }

        command.setLicenseNoList(licenseNoList);
        command.setOfficialFlag(officialFlag);

        log.info("SetCompanyOfficialFlagCommand对象内容为: {}", JSONUtil.toJsonStr(command));

        try {
            umCompanyAppService.batchSetCompanyOfficialFlag(command);
        } catch (Exception e) {
            log.error("批量设置企业标志发生异常：", e);
        }
    }

}
