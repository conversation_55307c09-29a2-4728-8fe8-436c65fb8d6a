package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ActivityUserAddCommand {

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 登录账号
     */
    @NotBlank(message = "登录账号不能为空")
    private String loginAccount;

    /**
     * 录入公司名称
     */
    private String recordCompanyName;

    /**
     * 业务方向dict=biz_direction
     */
    private String bizDirection;

    /**
     * 参会目的[10-采购,20-销售]
     */
    private String attendancePurpose;

}
