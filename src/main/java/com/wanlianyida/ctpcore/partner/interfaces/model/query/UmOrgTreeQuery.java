package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import com.wanlianyida.ctpcore.partner.infrastructure.config.ValidPlatformType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 组织结构树查询
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class UmOrgTreeQuery {
    /**
     * 平台类型 21-大宗管理端 22-大宗用户端
     */
    @ValidPlatformType
    @NotNull(message = "平台类型不能为空")
    private String platformType;

    /**
     * 企业信用编码
     */
    private String licenseNo;
}
