package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.wanlianyida.ctpcore.partner.infrastructure.config.IsMobile;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Data
public class UserInfoCommand {
    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    @Length(min = 11, max = 11, message = "手机号长度为11位")
    @IsMobile
    private String phone;

    /**
     * 验证码
     */
    @NotNull(message = "验证码不能为空")
    private String code;

    /**
     * 密码
     */
    @NotNull(message = "密码不能为空")
    @Length(min = 6, max = 16, message = "密码长度为6-16位")
    private String password;

    /**
     * 密码强度
     */
    @NotNull(message = "密码强度不能为空")
    private String passwordStrength;

    /**
     * 是否同意平台协议
     */
    private String isPlatformAgreementAgreed;

    /**
     * 邮箱地址
     */
    private String emailAddress;
}
