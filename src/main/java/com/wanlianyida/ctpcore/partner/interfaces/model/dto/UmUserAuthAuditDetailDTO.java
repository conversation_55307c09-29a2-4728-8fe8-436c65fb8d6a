package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:18
 */
@Data
@ApiModel("用户实名认证审核")
public class UmUserAuthAuditDetailDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("登录名")
    private String loginName;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty("性别[1-男,2-女]")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countyCode;

    @ApiModelProperty("县名称")
    private String countyName;

    @ApiModelProperty("详细地址")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issueAgency;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date untilDate;

    @ApiModelProperty("是否长期有效，1是0否")
    private Integer validFlag;

    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;
}
