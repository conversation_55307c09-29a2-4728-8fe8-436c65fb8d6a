package com.wanlianyida.ctpcore.partner.interfaces.facade;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.application.service.PersonalAccountAppService;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.*;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.*;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmEmployeeBindingRecordQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.dto.BindCompanyDTO;
import com.wanlianyida.partner.api.model.query.QueryBoundInfoQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 个人账号管理
 * <AUTHOR>
 * @date 2025/03/28
 */
@RestController
@RequestMapping("/um/personal/account")
public class PersonalAccountController {
    @Resource
    private PersonalAccountAppService appService;

    /**
     * 用户注册
     *
     * @param command
     * @return
     */
    @PostMapping("/register")
    public ResultMode<Map<String,String>> register(@RequestBody CmdUserInfoCommand command) {
        return ResultMode.success(appService.register(command));
    }

    /**
     * 更换手机号
     *
     * @param command
     * @return
     */
    @PostMapping("/updateUserPhone")
    public ResultMode<Void> updateUserPhone(@RequestBody CmdUserInfoCommand command) {
        appService.updateUserPhone(command);
        return ResultMode.success();
    }

    /**
     * 绑定/更新用户邮箱
     *
     * @param command
     * @return
     */
    @PostMapping("/setUserEmailAddress")
    public ResultMode<Void> setUserEmailAddress(@RequestBody CmdUserInfoCommand command) {
        appService.setUserEmailAddress(command);
        return ResultMode.success();
    }

    /**
     *
     * 实名认证
     * @param command
     * @return
     */
    @PostMapping("/authenticateRealName")
    public ResultMode<AuthenticateRealNameResultDTO> authenticateRealName(@RequestBody AuthenticateRealNameCommand command) {
        return ResultMode.success(appService.authenticateRealName(command));
    }

    /**
     *  更新用户二要素信息（姓名和身份证号）
     * @param command
     * @return
     */
    @PostMapping("/updateUserBasicTwoFactors")
    public ResultMode<Void> updateUserBasicTwoFactors(@RequestBody AuthenticateRealNameCommand command) {
        appService.updateUserBasicTwoFactors(command);
        return ResultMode.success();
    }

    /**
     * 申请成为管理员
     * @param command
     * @return
     */
    @PostMapping("/applyToBeAdministrator")
    public ResultMode<Void> applyToBeAdministrator(@RequestBody AdministratorCommand command) {
        appService.applyToBeAdministrator(command);
        return ResultMode.success();
    }

    /**
     * 管理员过户
     * @param command
     * @return
     */
    @PostMapping("/handOverAdministrator")
    public ResultMode<Void> handOverAdministrator(@RequestBody AdministratorCommand command) {
        appService.handOverAdministrator(command);
        return ResultMode.success();
    }

    @ApiOperation("分页查询绑定记录")
    @PostMapping("/pageBindingRecordCondition")
    public ResultMode<List<UmEmployeeBindingRecordDTO>> pageBindingRecordCondition(@RequestBody @Validated PagingInfo<UmEmployeeBindingRecordQuery> pageQuery) {
        return appService.pageBindingRecordCondition(pageQuery);
    }

    @ApiOperation("待处理的邀请员工数量")
    @PostMapping("/countPendingInvitedEmployees")
    public ResultMode<Integer> countPendingInvitedEmployees(@RequestBody @Validated UmEmployeeBindingRecordQuery pageQuery) {
        return appService.countPendingInvitedEmployees(pageQuery);
    }

    @ApiOperation("统计绑定记录数量")
    @PostMapping("/countBindingHistoryCondition")
    public ResultMode<Integer> countBindingHistoryCondition(@RequestBody UmEmployeeBindingRecordQuery query) {
        return appService.countBindingHistoryCondition(query);
    }

    /**
     * 个人详情页查询
     * @return
     */
    @PostMapping("/getPersonalDetailPage")
    public ResultMode<PersonalInfoDTO> getPersonalDetailPage() {
        PersonalInfoDTO dto = appService.getPersonalDetailPage();
        return ResultMode.success(dto);
    }

    /**
     * 员工处理邀请
     * @param command
     * @return
     */
    @PostMapping("/employeeInviteProcess")
    public ResultMode<Void> employeeInviteProcess(@RequestBody @Validated HandleInvitationCommand command) {
        appService.employeeInviteProcess(command);
        return ResultMode.success();
    }

    /**
     * 设置默认企业
     * @param command
     * @return
     */
    @PostMapping("/setDefaultCompany")
    public ResultMode<Void> setDefaultCompany(@RequestBody @Validated SetDefaultCompanyCommand command) {
        appService.setDefaultCompany(command);
        return ResultMode.success();
    }

    /**
     * 分页查询已绑定的企业信息
     * @param pagingInfo
     * @return
     */
    @PostMapping("/queryBoundCompanyInfo")
    public ResultMode<List<BindCompanyDTO>> queryBoundCompanyInfo(@RequestBody PagingInfo<QueryBoundInfoQuery> pagingInfo) {
        PageInfo<BindCompanyDTO> pageInfo = appService.queryBoundCompanyInfo(pagingInfo);
        return ResultMode.successPageList(pageInfo.getList(), (int) pageInfo.getTotal());
    }

    /**
     * 切换企业
     * @param command
     * @return 10-当前用户未绑定企业 20-切换企业成功 30-切换企业失败
     */
    @PostMapping("/switchCompany")
    public ResultMode<SwitchCompanyDTO> switchCompany(@RequestBody @Validated SwitchCompanyCommand command) {
         return ResultMode.success(appService.switchCompany(command));
    }

    /**
     * 根据手机号查询用户是否实名认证
     * @param phone
     * @return
     */
    @GetMapping("/queryUserRealNameStatus/{phone}")
    public ResultMode<Void> queryUserRealNameStatus(@PathVariable String phone) {
        appService.queryUserRealNameStatus(phone);
        return ResultMode.success();
    }
}
