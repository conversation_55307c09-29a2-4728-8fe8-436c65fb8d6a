package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门组织结构树
 *
 * <AUTHOR>
 * @date 2024/12/09
 */
@Data
public class UmOrgAndDeptTreeDTO {

    /**
     * 组织id
     */
    private String orgId;
    /**
     * 上级组织id
     */
    private String parentOrgId;

    /**
     * 上级组织名称
     */
    private String parentOrgName;

    /**
     * 组织名称
     */
    private String orgName;

    private List<UmOrgAndDeptTreeDTO> orgChildren = new ArrayList<>();

    private List<UmDeptDTO> deptList = new ArrayList<>();
}
