package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 账户证书审核DTO
 */
@Data
public class UmAcctCertApplyDetailDTO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 账户证书id
     */
    private Long certId;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业社会信用代码
     */
    private String socialCreditCode;

    /**
     * 委托书文件url
     */
    private String poaFileUrl;

    /**
     * 委托书文件名称
     */
    private String poaFileName;

    /**
     * 审核状态[1-待审核,2-审核未通过,3-审核通过]
     */
    private Integer auditStatus;

    /**
     * 审核人id
     */
    private String auditorId;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 审核人账号
     */
    private String auditorAccount;

    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}