package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月17日 19:01
 */
@Data
public class UmShopCollectUserQuery {

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userBaseId;

    /**
     * 店铺id
     */
    @NotBlank(message = "店铺id不能为空")
    private String shopId;

    /**
     * 店铺列表
     */
    private List<String> shopIdList;
}
