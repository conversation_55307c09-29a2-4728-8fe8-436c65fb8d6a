package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BdSensitiveWordsDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 敏感词
     */
    private String senWord;

    /**
     * 平台编号 20商贸门户
     */
    private String platformCode;

    /**
     * 状态(0停用 1启用)
     */
    private Short enableStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
