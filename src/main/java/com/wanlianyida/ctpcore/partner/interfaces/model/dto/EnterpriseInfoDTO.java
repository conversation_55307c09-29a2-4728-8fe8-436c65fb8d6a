package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EnterpriseInfoDTO {
    /**
     * 公司ID
     */
    private String companyId;
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 授权委托书是否存在 true-存在,false-不存在
     * 这个字段用于标识授权委托书是否有审核通过的记录
     */
    private boolean hasPoA;

    /**
     * 企业资质是否存在（营业执照 + 法人）true-存在,false-不存在
     * 这个字段用于标识营业执照 + 法人是否有审核通过的记录
     */
    private boolean hasQualification;
    /**
     * 邀请码信息
     */
    private UmInviteCodeDTO umInviteCodeDTO;
    /**
     * 工商信息
     */
    private BusinessLicenseInfoDTO businessLicenseInfo;
    /**
     * 授权委托书信息
     */
    private EnterpriseAuthorizationInfoDTO powerOfAttorneyInfo;

    @ApiModelProperty("法定代表人信息")
    private UmCompanyLegalPersonAuditDTO legalPersonInfo;

    /**
     * 平台角色信息
     */
    private List<PlatformRoleDTO> platformRoleList;
}
