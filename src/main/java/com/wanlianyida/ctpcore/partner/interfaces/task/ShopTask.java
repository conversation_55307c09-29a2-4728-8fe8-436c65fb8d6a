package com.wanlianyida.ctpcore.partner.interfaces.task;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.partner.domain.service.UmShopDomainService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ShopTask {
    @Resource
    private UmShopDomainService umShopDomainService;

    /**
     * 同步店铺数据到es
     */
    @XxlJob("syncShopData")
    public void syncShopData(){
        log.info("syncShopData执行同步店铺数据到es任务...");
        String timeValue = "10";
        String timeUnit = "minutes";

        String jobParam = XxlJobHelper.getJobParam();

        // 解析 jobParam
        if (StrUtil.isNotEmpty(jobParam)) {
            String[] parts = jobParam.split(",");
            if (parts.length != 2) {
                log.error("jobParam 格式不正确，无法解析。预期格式: timeValue,timeUnit");
                return;
            }
            timeValue = parts[0].trim();
            timeUnit = parts[1].trim();
        }
        if (umShopDomainService == null){
            log.error("umShopDomainService为空");
        }
        try {
            umShopDomainService.syncShopData(timeValue,timeUnit);
        } catch (Exception e) {
            log.error("同步店铺数据时发生异常：", e);
        }

    }

}
