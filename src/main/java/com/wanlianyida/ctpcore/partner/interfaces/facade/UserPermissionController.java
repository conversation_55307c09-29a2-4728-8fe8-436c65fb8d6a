//package com.wanlianyida.ctpcore.partner.interfaces.facade;
//
//import com.isoftstone.hig.common.model.PagingInfo;
//import com.isoftstone.hig.common.model.ResultMode;
//import com.wanlianyida.fssbase.userauth.application.service.UserPermissionService;
//import com.wanlianyida.fssuserauth.api.inter.UserPermissionInter;
//import com.wanlianyida.fssuserauth.api.model.command.UserPermissionCommand;
//import com.wanlianyida.fssuserauth.api.model.dto.UserPermissionDTO;
//import com.wanlianyida.fssuserauth.api.model.query.UserPermissionQuery;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
//@RestController
//public class UserPermissionController implements UserPermissionInter {
//
//    @Resource
//    UserPermissionService userPermissionService;
//
//    @Override
//    public ResultMode addUserPermission(UserPermissionCommand userPermissionCommand) {
//        userPermissionService.addUserPermission(userPermissionCommand);
//        return ResultMode.success();
//    }
//
//    @Override
//    public ResultMode updateUserPermission(UserPermissionCommand userPermissionCommand) {
//        userPermissionService.updateUserPermission(userPermissionCommand);
//        return ResultMode.success();
//    }
//
//    @Override
//    public ResultMode delUserPermission(UserPermissionCommand userPermissionCommand) {
//        userPermissionService.deleteUserPermission(userPermissionCommand);
//        return ResultMode.success();
//    }
//
//    @Override
//    public ResultMode<UserPermissionDTO> queryList(PagingInfo<UserPermissionQuery> pagingInfo) {
//        return userPermissionService.queryUserPermission(pagingInfo);
//    }
//}
