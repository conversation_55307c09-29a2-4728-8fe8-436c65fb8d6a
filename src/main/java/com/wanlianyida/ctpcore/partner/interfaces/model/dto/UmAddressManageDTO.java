package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import lombok.Data;

@Data
public class UmAddressManageDTO {
    /**
     *  主键
     */
    private String id;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 类型 10买家收货地址 20卖家发货地址 30买家仓库地址 40卖家仓库地址
     */
    private String type;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 编码（省/直辖市）
     */
    private String provinceCode;

    /**
     * 编码（市）
     */
    private String cityCode;

    /**
     * 编码（县）
     */
    private String areaCode;

    /**
     * 中文（省/直辖市）
     */
    private String province;

    /**
     * 中文（市）
     */
    private String city;

    /**
     * 中文（区）
     */
    private String area;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 简称（仓库名称）
     */
    private String shortName;

    /**
     * 是否默认收货地址(0否 1是)
     */
    private Integer defaultStatus;
}
