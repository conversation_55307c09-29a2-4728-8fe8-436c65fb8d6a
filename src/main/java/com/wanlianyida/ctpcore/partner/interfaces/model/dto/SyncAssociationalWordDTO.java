package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import lombok.Data;

@Data
public class SyncAssociationalWordDTO {
    /**
     * 品牌id/品类id/店铺id
     */
    private String bizId;
    /**
     * 联想词类型：10 品牌 ;20 品类 ;30 店铺
     */
    private Integer wordType;
    /**
     * 品类级别：1、2、3
     */
    private Integer categoryLevel;
    /**
     * 联想词内容
     */
    private String wordContent;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer deleted;
}
