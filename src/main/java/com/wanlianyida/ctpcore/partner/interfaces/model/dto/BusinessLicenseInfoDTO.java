package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工商信息
 * <AUTHOR>
 * @date 2025/03/29
 */
@Data
public class BusinessLicenseInfoDTO {
    private Long id;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("法定代表人")
    private String legalPerson;

    @ApiModelProperty("照片url")
    private String fileUrl;

    @ApiModelProperty("状态:1待审核,2审核不通过,3审核通过")
    private String aptitudeStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    /**
     * 主管税务机关
     */
    private String taxAuthority;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     * 成立日期
     */
    private String foundDate;

    /**
     * 资质(营业执照)有效期起日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseStartDate;

    /**
     * 资质(营业执照)有效期止日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseEndDate;


    /**
     * 资质(营业执照)是否为长期有效【select:11-是,21-否】当是时清空license_end_date
     */
    private String licenseValidIsLong;

    /**
     * 经营范围
     */
    private String manageScope;

    /**
     * 企业类型(这个是营业执照里面的文本)
     */
    private String companyType;

    /**
     * 企业地址（省Code）
     */
    private String province;

    /**
     * 企业地址（省中文）
     */
    private String provinceName;

    /**
     * 企业地址（市Code）
     */
    private String city;

    /**
     * 企业地址（市中文）
     */
    private String cityName;

    /**
     * 企业地址（区/县Code）
     */
    private String area;

    /**
     * 企业地址（区/县中文）
     */
    private String areaName;

    /**
     * 企业地址（镇/街道Code）
     */
    private String street;

    /**
     * 企业地址（镇/街道中文）
     */
    private String streetName;

    /**
     * 企业地址（详细地址）
     */
    private String addressDetail;

    /**
     * 企业信用编码
     */
    private String licenseNo;
    /**
     * 组织机构代码
     */
    private String orgCode;

    /**
     * 组织机构代码证书开始日期'
     */
    private Date orgCodeCertStartDate;

    /**
     * 组织机构代码有效期证书结束日期
     */
    private Date orgCodeCertEndDate;

    /**
     * 组织机构文件url
     */
    private String orgFileUrl;

    /**
     * 组织机构长期有效标志[0-否,1-是]
     */
    private String orgLongTermFlag;

    /**
     * 税务号
     */
    private String taxNo;

    /**
     * 税务文件url
     */
    private String taxFileUrl;

    /**
     * 证照(资质)类型:1000企营,1100个体,2100道运,1900其他
     */
    private String certificateType;

    /**
     * 企业资质是否存在（营业执照 + 法人）true-存在,false-不存在
     * 这个字段用于标识营业执照 + 法人是否有审核通过的记录
     */
    private boolean hasQualification;
}
