package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 岗位管理DTO
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@Data
public class UmPostDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 企业ID
     */
    private String relCompanyId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位级别
     */
    private String postGrade;

    /**
     * 状态(0停用 1启用)
     */
    private Short enableStatus;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

}
