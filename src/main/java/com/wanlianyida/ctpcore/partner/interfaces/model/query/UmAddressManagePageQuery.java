package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class UmAddressManagePageQuery {
    /**
     * 类型 10买家收货地址 20卖家发货地址 30买家仓库地址 40卖家仓库地址
     */
    @Length(max = 2, message = "类型长度超过限制")
    private String type;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 简称（仓库名称）
     */
    private String shortName;
}
