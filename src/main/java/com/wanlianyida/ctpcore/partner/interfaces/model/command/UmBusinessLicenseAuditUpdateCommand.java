package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:19
 */
@Data
@ApiModel("营业执照审核")
public class UmBusinessLicenseAuditUpdateCommand {

    @NotNull(message = "审核id不能为空")
    private Long id;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业类型")
    private String companyType;

    @ApiModelProperty("法定代表人")
    private String legalPerson;

    @ApiModelProperty("注册资本（万元）")
    private String registeredCapital;

    @ApiModelProperty("成立日期")
    private String foundDate;

    @ApiModelProperty("营业期限截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireDate;

    @ApiModelProperty("是否为长期有效:11是,21否")
    private String validFlag;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countryCode;

    @ApiModelProperty("县")
    private String countryName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("主管税务机关")
    private String taxAuthority;

    @ApiModelProperty("经营范围")
    private String manageScope;

    @NotNull(message = "审核状态不能为空")
    @ApiModelProperty("审核状态:1待审核,2审核不通过,3审核通过")
    private String auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("法定代表人信息")
    private UmCompanyLegalPersonAuditUpdateCommand legalPersonInfo;
}
