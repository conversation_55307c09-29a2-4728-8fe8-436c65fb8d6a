package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 补充资料 Query
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ApiModel("补充资料审核")
public class UmQualSuppPageQuery {

	@ApiModelProperty("企业名称")
	private String companyName;

	@ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
	private Integer auditStatus;


}
