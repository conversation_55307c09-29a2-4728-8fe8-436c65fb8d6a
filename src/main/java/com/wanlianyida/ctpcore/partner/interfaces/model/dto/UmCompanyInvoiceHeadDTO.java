package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 企业发票抬头信息表 DTO
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
public class UmCompanyInvoiceHeadDTO {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 企业信息ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private String companyId;

	/**
	 * 默认开票信息:1是,0否
	 */
	private String isDefault;

	/**
	 * 类型:10买家,20卖家
	 */
	private String headType;

	/**
	 * 抬头名称
	 */
	private String headName;

	/**
	 * 统一社会信用代码/税号
	 */
	private String socialCreditCode;

	/**
	 * 银行名称
	 */
	private String bankName;

	/**
	 * 银行卡号
	 */
	private String bankCard;

	/**
	 * 企业地址
	 */
	private String addressDetail;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 备注
	 */
	private String invoiceRemark;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;


}
