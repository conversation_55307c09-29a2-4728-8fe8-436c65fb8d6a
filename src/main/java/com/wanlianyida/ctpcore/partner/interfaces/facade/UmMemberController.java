package com.wanlianyida.ctpcore.partner.interfaces.facade;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.application.service.UmMemberAppService;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmMemberAddCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmMemberDeleteCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmMemberUpdateCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.MemberConditionDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmMemberDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmUserCacheDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmMemberListQuery;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmMemberQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 员工管理
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RestController
@RequestMapping("/um/member")
public class UmMemberController {
    @Resource
    private UmMemberAppService appService;

    /**
     * 员工新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/add")
    public ResultMode<Boolean> add(@RequestBody UmMemberAddCommand command){
        return ResultMode.success(appService.add(command));
    }

    /**
     * 更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/update")
    public ResultMode<Boolean> update(@RequestBody UmMemberUpdateCommand command){
        return ResultMode.success(appService.update(command));
    }

    /**
     * 删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    @PostMapping("/delete")
    public ResultMode<Boolean> delete(@RequestBody UmMemberDeleteCommand command){
        return ResultMode.success(appService.delete(command));
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    @PostMapping("/queryList")
    public ResultMode<List<UmMemberDTO>> queryList(@RequestBody PagingInfo<UmMemberListQuery> pagingInfo) {
        PageInfo<UmMemberDTO> pageInfo = appService.queryPage(pagingInfo);
        return ResultMode.successPageList(pageInfo.getList(), (int) pageInfo.getTotal());
    }

    /**
     * 查询详情
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link UmMemberDTO }>
     */
    @PostMapping("/queryDetail")
    public ResultMode<UmMemberDTO> queryDetail (@RequestBody UmMemberListQuery query){
        return ResultMode.success(appService.queryDetail(query));
    }

    /**
     * 员工条件查询
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link UmMemberDTO }>
     */
    @PostMapping("/queryByCondition")
    public ResultMode<List<MemberConditionDTO>>queryByCondition(@RequestBody UmMemberQuery query){
        return ResultMode.success(appService.queryByCondition(query));
    }

    /**
     * 员工条件查询
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link UmMemberDTO }>
     */
    @PostMapping("/query-list-by-companyIds")
    public ResultMode<List<MemberConditionDTO>>queryByCompanyIds(@RequestBody UmMemberQuery query){
        return ResultMode.success(appService.queryByCompanyIds(query));
    }

    /**
     * 根据用户id查询公司信息
     *
     * @param userBaseId 用户 ID
     * @return {@link ResultMode }<{@link UmMemberDTO }>
     */
    @PostMapping("/queryCompanyInfoByUserId")
    public ResultMode<MemberConditionDTO> queryCompanyInfoByUserId(@RequestParam("userBaseId") String userBaseId){
        return ResultMode.success(appService.queryCompanyInfoByUserId(userBaseId));
    }

    /**
     * 更新缓存(作废)
     *
     * @return {@code ResultMode<List<UmUserCacheDTO>> }
     */
    @Deprecated
    @PostMapping("/updateCache")
    public ResultMode<List<UmUserCacheDTO>> updateCache(){
        return ResultMode.success(appService.updateCache());
    }

    /**
     * 查询列表
     * @param query
     * @return
     */
    @PostMapping("/queryListByCondition")
    public ResultMode<List<UmMemberDTO>> queryListByCondition(@RequestBody UmMemberListQuery query){
        return ResultMode.success(appService.queryListByCondition(query));
    }

    /**
     * 根据操作员id批量删除
     * @param operatorIds
     * @return
     */
    @PostMapping("batchDeleteByOperatorIds")
    public ResultMode<Void> batchDeleteByOperatorIds(@RequestBody List<String> operatorIds){
        appService.batchDeleteByOperatorIds(operatorIds);
        return ResultMode.success();
    }

    /**
     * 根据操作员id查询
     * @param query
     * @return
     */
    @PostMapping("/queryByOperatorId")
    public ResultMode<UmMemberDTO> queryByOperatorId(@RequestBody UmMemberQuery query) {
        UmMemberDTO umMemberDTO  =appService.queryByOperatorId(query);
        return ResultMode.success(umMemberDTO);
    }

}
