package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName: UmShareCodeRelationDTO
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2025年07月16日
 * @version: 1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "UmInviteCodeRelationDTO对象", description = "分享码关系表")
public class UmInviteCodeRelationDTO {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("发起邀请企业的分享码ID")
    private Long inviteId;

    @ApiModelProperty("发起邀请企业的邀请码")
    private String inviteCode;

    @ApiModelProperty("发起邀请的企业ID")
    private String inviteCompanyId;

    @ApiModelProperty("发起邀请的企业名称")
    private String inviteCompanyName;

    @ApiModelProperty("被邀请者用户ID")
    private String inviteeUserId;

    @ApiModelProperty("被邀请者用户名称")
    private String inviteeUserName;

    @ApiModelProperty("被邀请者企业ID")
    private String inviteeCompanyId;

    @ApiModelProperty("被邀请者企业名称")
    private String inviteeCompanyName;

    @ApiModelProperty("被邀请者类型[10-用户,20-企业]")
    private String inviteeType;

    @ApiModelProperty("渠道[10-链接,20-二维码,30-手动输入]")
    private String channelType;

    @ApiModelProperty("创建用户id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后更新人id")
    private String lastUpdaterId;

    @ApiModelProperty("最后更新时间")
    private Date lastUpdatedTime;

    @ApiModelProperty("是否删除标志[0-正常,1-删除]")
    private Integer delFlag;
}
