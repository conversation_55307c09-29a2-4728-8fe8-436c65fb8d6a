package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 组织DTO
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class UmOrgDTO {

    /**
     * 主键
     */
    private String id;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 企业联系人，发送验证码用
     */
    private String phone;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 上级组织id
     */
    private String parentOrgId;

    /**
     * 上级组织名称
     */
    private String parentOrgName;

    /**
     * 父级信用代码
     */
    private String parentSocialCreditCode;


    /**
     * 管理员账号
     */
    private String adminAccount;


    /**
     * 描述
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    private List<UmOrgDTO> children = new ArrayList<>();
}
