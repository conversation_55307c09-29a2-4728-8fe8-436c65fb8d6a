package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.wanlianyida.ctpcore.partner.infrastructure.config.ValidPlatformType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 岗位新增
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@Data
public class UmPostAddCommand {
    /**
     * 岗位名称
     */
    @NotNull(message = "岗位名称不能为空")
    private String postName;

    /**
     * 岗位级别
     */
    @NotNull(message = "岗位级别不能为空")
    private String postGrade;

    /**
     * 描述
     */
    private String remark;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @ValidPlatformType
    @NotNull(message = "平台类型不能为空")
    private String platformType;

}
