package com.wanlianyida.ctpcore.partner.interfaces.facade;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.application.service.UmShopAppService;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmShopCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmCompanyIdAndShopIdListDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopCompanyDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopContactsDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmShopPageQuery;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.queryShopHallByPageQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * 店铺管理
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@RestController
@RequestMapping("/um/shop")
public class UmShopController {
    @Resource
    private UmShopAppService appService;

    /**
     * 添加店铺
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    @PostMapping("/addShop")
    public ResultMode<Boolean> addShop(@RequestBody @Validated UmShopCommand command) {
        return ResultMode.success(appService.addShop(command));
    }

    /**
     * 更新店铺
     *
     * @param command 命令
     * @return {@code ResultMode<Boolean> }
     */
    @PostMapping("/updateShop")
    public ResultMode<Boolean> updateShop(@RequestBody @Validated UmShopCommand command) {
        return ResultMode.success(appService.updateShop(command));
    }

    /**
     * 更新店铺分类导航
     *
     * @param id      身份证件
     * @param navType 没有类型
     * @return {@code ResultMode<Boolean> }
     */
    @PostMapping("/updateCategoriesForShopNav")
    public ResultMode<Boolean> updateCategoriesForShopNav(@RequestParam(value = "id") Long id,
                                                          @RequestParam(value = "navType") String navType) {
        return ResultMode.success(appService.updateCategoriesForShopNav(id, navType));
    }

    /**
     * 根据店铺id/公司id 查询店铺信息
     *
     * @param shopId 店铺id
     * @return {@code ResultMode<UmShopDTO> }
     */
    @PostMapping("/shopDetailByShopIdOrCompanyId")
    public ResultMode<UmShopDTO> shopDetailByShopIdOrCompanyId(@RequestParam(value = "shopId", required = false) Long shopId,
                                                    @RequestParam(value = "companyId", required = false) String companyId) {
        return ResultMode.success(appService.shopDetailByShopIdOrCompanyId(shopId,companyId));
    }

    /**
     * 按店铺ID批量查询列表
     *
     * @param shopIds 店铺ID
     * @return {@code ResultMode<List<UmShopDTO>> }
     */
    @PostMapping("/batchQueryListByShopIds")
    public ResultMode<List<UmShopDTO>> batchQueryListByShopIds(@RequestBody List<Long> shopIds) {
        return ResultMode.success(appService.batchQueryListByShopIds(shopIds));
    }

    /**
     * 批量获取店铺联系方式
     *
     * @param shopIds 店铺id列表
     * @return {@code ResultMode<String> }
     */
    @PostMapping("/getShopContactsList")
    public ResultMode<List<UmShopContactsDTO>> getShopContactsList(@RequestParam(value = "shopIds") List<Long> shopIds) {
        return ResultMode.success(appService.getShopContactsList(shopIds));
    }

    /**
     * 获取登录用户公司id和店铺id列表
     *
     * @return {@code ResultMode<UmCompanyIdAndShopIdListDTO> }
     */
    @PostMapping("/getLoginUserCompanyIdAndShopIds")
    public ResultMode<UmCompanyIdAndShopIdListDTO> getLoginUserCompanyIdAndShopIds(){
        return ResultMode.success(appService.getLoginUserCompanyIdAndShopIds());
    }

    /**
     * 分页查询
     *
     * @param pagingInfo 寻呼信息
     * @return {@code ResultMode }
     */
    @PostMapping("/queryPage")
    public ResultMode queryPage(@RequestBody PagingInfo<UmShopPageQuery> pagingInfo){
        PageInfo<UmShopDTO> pageInfo =appService.queryPage(pagingInfo);
        return ResultMode.successPageList(pageInfo.getList(),(int)pageInfo.getTotal());
    }

    /**
     * 同步店铺数据
     * @return
     */
    @PostMapping(value = {"/syncShopData"})
    public ResultMode<Void> syncShopData(@RequestParam String timeValue, @RequestParam String timeUnit) {
        appService.syncShopData(timeValue,timeUnit);
        return ResultMode.success();
    }

    /**
     * 店铺大厅分页查询
     * @param pagingInfo
     * @return
     */
    @PostMapping("/queryShopHallByPage")
    public ResultMode queryShopHallByPage(@RequestBody @Validated PagingInfo<queryShopHallByPageQuery> pagingInfo){
        PageInfo<UmShopDTO> pageInfo = appService.queryShopHallByPage(pagingInfo);
        if (pageInfo == null || pageInfo.getList() == null || pageInfo.getTotal() == 0) {
            return ResultMode.successPageList(Collections.emptyList(), 0);
        }
        return ResultMode.successPageList(pageInfo.getList(),(int)pageInfo.getTotal());

    }


    /**
     * 根据公司ID批量获取店铺联系方式
     *
     */
    @PostMapping("/queryShopCompanyIdList")
    public ResultMode<List<UmShopCompanyDTO>> queryShopCompanyIdList(@RequestParam(value = "companyIdList") List<String> companyIdList) {
        return ResultMode.success(appService.queryShopCompanyIdList(companyIdList));
    }
}
