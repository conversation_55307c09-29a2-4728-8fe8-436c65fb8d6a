package com.wanlianyida.ctpcore.partner.interfaces.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:18
 */
@Data
@ApiModel("用户实名认证审核")
public class UmUserAuthAuditQuery {

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private Integer auditStatus;
}
