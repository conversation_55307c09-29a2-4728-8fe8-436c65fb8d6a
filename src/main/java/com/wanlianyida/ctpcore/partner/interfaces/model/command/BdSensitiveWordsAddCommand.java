package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

@Data
public class BdSensitiveWordsAddCommand implements Serializable {
    private static final long serialVersionUID = -7121321413085959522L;
    /**
     * 敏感词集合，按逗号分隔
     */
    @NotNull(message = "敏感词不能为空")
    private String senWords;

    /**
     * 平台编号 20商贸门户
     */
    private String platformCode;

    /**
     * 状态(0停用 1启用)
     */
    @NotNull(message = "状态不能为空")
    private Short enableStatus;

    /**
     * 备注
     */
    private String remark;
}
