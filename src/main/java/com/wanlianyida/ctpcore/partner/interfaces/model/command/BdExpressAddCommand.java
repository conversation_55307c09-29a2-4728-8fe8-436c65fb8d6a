package com.wanlianyida.ctpcore.partner.interfaces.model.command;

import com.wanlianyida.ctpcore.partner.infrastructure.config.IsMobile;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * 物流公司新增
 *
 * <AUTHOR>
 * @date 2024/12/05
 */
@Data
public class BdExpressAddCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物流公司名称
     */
    @NotNull(message = "物流公司名称不能为空")
    private String companyName;

    /**
     * 联系人姓名
     */
    @NotNull(message = "联系人姓名不能为空")
    private String contactName;

    /**
     * 联系人电话
     */
    @IsMobile
    @NotNull(message = "联系人电话不能为空")
    private String contactPhone;

    /**
     * 状态(0停用 1启用)
     */
    @NotNull(message = "状态不能为空")
    private Integer enableStatus;

    /**
     * 简介
     */
    private String companyProfile;

}
