package com.wanlianyida.ctpcore.partner.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月28日 15:08
 */
@Data
@ApiModel("营业执照审核")
public class UmBusinessLicenseAuditListDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("企业类型")
    private String companyType;

    @ApiModelProperty("法定代表人")
    private String legalPerson;

    @ApiModelProperty("注册资本（万元）")
    private String registeredCapital;

    @ApiModelProperty("申请人用户名")
    private String applyLoginName;

    @ApiModelProperty("申请人姓名")
    private String applyUsername;

    @ApiModelProperty("营业期限截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireDate;

    @ApiModelProperty("是否为长期有效:11是,21否")
    private String validFlag;

    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private String auditStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;
    /**
     * 申请来源:1大宗平台,2物流平台
     */
    private String applySource;
}
