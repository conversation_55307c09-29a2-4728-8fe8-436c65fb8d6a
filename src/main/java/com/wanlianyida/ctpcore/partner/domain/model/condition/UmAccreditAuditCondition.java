package com.wanlianyida.ctpcore.partner.domain.model.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:19
 */
@Data
@ApiModel("授权委托书审核")
public class UmAccreditAuditCondition {

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("过户用户id")
    private String transferUserId;

    @ApiModelProperty("过户用户编号")
    private String transferUserCode;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("来源类型[10-创建，20-申请，30-过户,40-变更]")
    private Integer sourceType;

    @ApiModelProperty("审核状态[1-待审核，2-不通过 3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("企业ID集合")
    private List<String> companyIdList;
}
