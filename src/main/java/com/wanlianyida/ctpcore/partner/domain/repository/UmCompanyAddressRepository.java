package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmAddressManageCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmAddressManageEntity;

import java.util.List;

public interface UmCompanyAddressRepository {

    /**
     * 新增地址
     *
     * @param umAddressManageEntity BI 地址管理实体
     * @return {@link Boolean }
     */
    Boolean add(UmAddressManageEntity umAddressManageEntity);

    /**
     * 查询默认地址
     *
     * @param entity 实体
     * @return {@link UmAddressManageEntity }
     */
    UmAddressManageEntity selectByStatus(UmAddressManageEntity entity);

    /**
     * 更新默认收货地址
     *
     * @param id     身份证
     * @param status 地位
     * @return {@link Boolean }
     */
    Boolean updateDefaultStatus(Long id, Integer status);

    /**
     * 更新信息
     *
     * @param entity 实体
     * @return {@link Boolean }
     */
    Boolean updateInfo(UmAddressManageEntity entity);

    /**
     * 删除
     *
     * @param entity 实体
     * @return {@link Boolean }
     */
    Boolean delete(UmAddressManageEntity entity);

    /**
     * 查询列表
     *
     * @param condition 条件
     * @return {@link List }<{@link UmAddressManageEntity }>
     */
    List<UmAddressManageEntity> queryList(UmAddressManageCondition condition);

    /**
     * 详情查询
     *
     * @param id 身份证
     * @return {@link UmAddressManageEntity }
     */
    UmAddressManageEntity detail(Long id,String companyId);

    /**
     * 分页查询
     *
     * @param condition 条件
     * @return {@link List }<{@link UmAddressManageEntity }>
     */
    List<UmAddressManageEntity> queryPage(UmAddressManageCondition condition);

    /**
     * 查询地址数量
     *
     * @param condition 条件
     * @return {@link Long }
     */
    Long queryCount(UmAddressManageCondition condition);

    /**
     * 按地址查询计数
     *
     * @param condition 条件
     * @return {@link Long }
     */
    Long queryCountByAddress(UmAddressManageCondition condition);
}
