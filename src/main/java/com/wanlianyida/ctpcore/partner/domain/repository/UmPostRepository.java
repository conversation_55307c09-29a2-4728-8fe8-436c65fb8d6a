package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmPostCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmPostEntity;

import java.util.List;

public interface UmPostRepository {
    /**
     * 岗位新增
     *
     * @param umPostEntity um邮政实体
     * @return {@code Boolean }
     */
    Boolean add(UmPostEntity umPostEntity);

    /**
     *  岗位通用查询
     *
     * @param condition 条件
     * @return {@code Long }
     */
    UmPostEntity selectInfo(UmPostCondition condition);

    /**
     * 岗位更新
     *
     * @param umPostEntity um邮政实体
     * @return {@code Boolean }
     */
    Boolean update(UmPostEntity umPostEntity);

    /**
     * 岗位删除
     *
     * @param umPostEntity um后状态
     * @return {@code Boolean }
     */
    Boolean delete(UmPostEntity umPostEntity);

    /**
     * 查询页面
     *
     * @param condition 条件
     * @return {@code List<UmPostEntity> }
     */
    List<UmPostEntity> queryPage(UmPostCondition condition);

    /**
     * 查询岗位是否被使用
     *
     * @param condition 条件
     * @return {@code Integer }
     */
    Integer validatePostIsUsed(UmPostCondition condition);
}
