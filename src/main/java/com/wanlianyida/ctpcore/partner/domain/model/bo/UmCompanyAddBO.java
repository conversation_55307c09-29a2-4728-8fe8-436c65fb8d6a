package com.wanlianyida.ctpcore.partner.domain.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 企业信息表 AddBO
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
public class UmCompanyAddBO {
    /**
	 * ID
	 */
	private Long id;

    /**
     * (增加)时间
     */
    @ApiModelProperty(value = "(增加)时间", name = "addTime",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date addTime;
    /**
     * (增加)类型
     */
    @ApiModelProperty(value = "(增加)类型", name = "addType")
    private int addType;
    /**
     * 现居住地址
     */
    @ApiModelProperty(value = "现居住地址", name = "address")
    private String address;
    /**
     * 地址编码
     */
    @ApiModelProperty(value = "地址编码", name = "addressCode")
    private String addressCode;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", name = "age")
    private int age;
    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", name = "dateOfBirth")
    private String dateOfBirth;
    /**
     * 0-不删除，1-删除
     */
    @ApiModelProperty(value = "21-不删除，11-删除", name = "deleted")
    private String deleted;
    /**
     * 教育程度（参照GB/T 4658。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类）
     */
    @ApiModelProperty(value = "教育程度（参照GB/T 4658。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类）", name = "educationalStatus")
    private String educationalStatus;
    /**
     * 邮箱地址
     */
    @ApiModelProperty(value = "邮箱地址", name = "email")
    private String email;
    /**
     * 紧急联系人姓名
     */
    @ApiModelProperty(value = "紧急联系人姓名", name = "emergencyContact")
    private String emergencyContact;
    /**
     * 紧急联系人关系（即与户主关系)采用GB/T 4761二位数字代码表。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类。
     */
    @ApiModelProperty(value = "紧急联系人关系（即与户主关系)采用GB/T 4761二位数字代码表。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类。", name = "emergencyRelation")
    private String emergencyRelation;
    /**
     * 紧急联系人手机号
     */
    @ApiModelProperty(value = "紧急联系人手机号", name = "emergencyTelephone",example = "13411221345")
    private String emergencyTelephone;
    /**
     * 身份证有效期
     */
    @ApiModelProperty(value = "身份证有效期", name = "idCardDeadline",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date idCardDeadline;

    /**
     * 身份证ID唯一键，如果小孩或外国人没有身份证号就生成一个GUID来标识先
     */
    @ApiModelProperty(value = "身份证ID唯一键，如果小孩或外国人没有身份证号就生成一个GUID来标识先 ", name = "idNumber",example = "362030199811110001")
    private String idNumber;
    /**
     * 用户登录ID（关联UM_LoginInfo)
     */
    @ApiModelProperty(value = "用户登录ID（关联UM_LoginInfo)", name = "loginId")
    private String loginId;
    /**
     * 身份证是否长期有效(1:是, 2:否)
     */
    @ApiModelProperty(value = "身份证是否长期有效(11:是, 21:否)", name = "longTermEffective")
    private String longTermEffective;
    /**
     * 婚姻状态（GB/T 2261.2   --10未婚，20已婚 , 21初婚 , 22再婚 , 23复婚 , 30丧偶 , 40离婚 , 90未说明的婚姻状况......)
     */
    @ApiModelProperty(value = "婚姻状态（GB/T 2261.2   --10未婚，20已婚 , 21初婚 , 22再婚 , 23复婚 , 30丧偶 , 40离婚 , 90未说明的婚姻状况......)", name = "maritalStatus")
    private String maritalStatus;
    /**
     * 民族（GB-3304-91）
     */
    @ApiModelProperty(value = "民族（GB-3304-91）", name = "nation")
    private String nation;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码", name = "postalCode")
    private String postalCode;
    /**
     * 相片
     */
    @ApiModelProperty(value = "相片", name = "profilePhoto")
    private String profilePhoto;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;
    /**
     * 户口所在地(通过数据字典获得如431026000000  汝城县 湖南省郴州市汝城县)
     */
    @ApiModelProperty(value = "户口所在地(通过数据字典获得如431026000000  汝城县 湖南省郴州市汝城县) ", name = "residentCity")
    private String residentCity;
    /**
     * 户口类型(GB/T 17538   1-非农业户口(城镇居民）2--农业户口（农村居民）9-其他)
     */
    @ApiModelProperty(value = "户口类型(GB/T 17538   1-非农业户口(城镇居民）2--农业户口（农村居民）9-其他)", name = "residentType")
    private String residentType;
    /**
     * 性别（0未知性别，1男，2女，9未说明性别, GB/T 2261.1。编码方法：采用顺序码，用1位数字表示）
     */
    @ApiModelProperty(value = "性别（0未知性别，1男，2女，9未说明性别, GB/T 2261.1。编码方法：采用顺序码，用1位数字表示）", name = "sex")
    private String sex;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", name = "telephone")
    private String telephone;

    @ApiModelProperty(value = "原始手机号", name = "telephone")
    private String originTelephone;

    @ApiModelProperty(value = "资质状态【select:1-待审核,2-审核不通过,3-审核通过,4-临时通过】", name = "telephone")
    private String exAptitudeStatus;
    /**
     * 用户基本信息表
     */
    @ApiModelProperty(value = "用户基本信息表 ", name = "userBaseId")
    private String userBaseId;
    /**
     * 用户信息状态(0:无效 , 1:有效)
     */
    @ApiModelProperty(value = "用户信息状态(11:有效, 21:无效)", name = "userStatus")
    private String userStatus;
    /**
     * 学生姓名
     * 姓名
     */
    @ApiModelProperty(value = "学生姓名姓名", name = "username")
    private String username;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号", name = "wechat")
    private String wechat;

    /**
     * 省Code
     */
    @ApiModelProperty(value = "省Code", name = "province")
    private String province;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称", name = "provinceName")
    private String provinceName;

    /**
     * 市Code
     */
    @ApiModelProperty(value = "市Code", name = "city")
    private String city;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称", name = "cityName")
    private String cityName;

    /**
     * 区/县Code
     */
    @ApiModelProperty(value = "区/县Code", name = "area")
    private String area;

    /**
     * 区/县名称
     */
    @ApiModelProperty(value = "区/县名称", name = "areaName")
    private String areaName;

    /**
     * 镇/街道Code
     */
    @ApiModelProperty(value = "镇/街道Code", name = "street")
    private String street;

    /**
     * 镇/街道名称
     */
    @ApiModelProperty(value = "镇/街道名称", name = "streetName")
    private String streetName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", name = "addressDetail")
    private String addressDetail;

    /**
     * companyId
     */
    private String companyId;


    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本", name = "registeredCapital")
    private BigDecimal registeredCapital;


    /**
     * 成立日期
     */
    @ApiModelProperty(value = "成立日期", name = "foundDate")
    private String foundDate;

    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item1")
    private String item1;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item2")
    private String item2;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item3")
    private String item3;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item4")
    private String item4;
    /**
     * E签宝个人账号id
     */
    @ApiModelProperty(value = "E签宝个人账号id", name = "accountId")
    private String accountId;

    private String fuzzyFileds;

    @ApiModelProperty(value = "创建时间开始", name = "createDateFrom")
    private Date createDateFrom;

    @ApiModelProperty(value = "创建时间结束", name = "createDateTo")
    private Date createDateTo;

    /**
     * 登录名称以手机号码或者邮箱在库中唯一
     */
    @ApiModelProperty(value = "登录名称,库中唯一", name = "loginName")
    private String loginName;

    /**
     * 登录密码
     */
    @ApiModelProperty(value = "登录密码", name = "password")
    private String password;

    /**
     * 确认新密码
     */
    @ApiModelProperty(value = "登录密码", name = "password")
    private String confirmNewPassword;

    /**
     * 图形验证码
     */
    @ApiModelProperty(value = "图形验证码", name = "graphValidateCode")
    private String graphValidateCode;
    /**
     * 短信验证码
     */
    @ApiModelProperty(value = "短信验证码", name = "messageValidateCode")
    private String messageValidateCode;

    /**
     * 证件有效期
     */
    @ApiModelProperty(value = "证件有效期", name = "deadline",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deadline;

    /**
     * 正面照
     */
    @ApiModelProperty(value = "正面照", name = "frontPhoto")
    private String frontPhoto;

    /**
     * 反面照
     */
    @ApiModelProperty(value = "反面照", name = "backPhoto")
    private String backPhoto;

    /**
     * 执照类型(1:企业营业执照，2:个体工商户执照)
     */
    @ApiModelProperty(value = "执照类型(1:企业营业执照，2:个体工商户执照)", name = "licenseType",example = "1")
    private String licenseType;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码", name = "socialCreditCode")
    private String socialCreditCode;

    /**
     * 企业全称
     */
    @ApiModelProperty(value = "企业全称", name = "companyName")
    private String companyName;

    /**
     * 企业简称
     */
    @ApiModelProperty(value = "企业简称", name = "companyShortName")
    private String companyShortName;

    /**
     * 公司法人
     */
    @ApiModelProperty(value = "公司法人", name = "legalPerson")
    private String legalPerson;

    /**
    * 法人身份证号
    */
    @ApiModelProperty(value = "公司法人身份证号", name = "legalPersonIdCard")
    private String legalPersonIdCard;

    /**
     * 创建来源
     */
    @ApiModelProperty(value = "创建来源", name = "exCreateSourse")
    private String exCreateSourse;

    /**
     * 新密码
     */
    @ApiModelProperty(value = "新密码", name = "newPassword")
    private String newPassword;

    /**
     * 司机id
     */
    @ApiModelProperty(value = "司机id", name = "exDriverId")
    private String exDriverId;

    /**
     * 权限id集合
     */
    @ApiModelProperty(value = "权限id集合", name = "exFuncIdList")
    private List<Long> exFuncIdList;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围", name = "manageScope")
    private String manageScope;

    @ApiModelProperty(value = "公司类型", name = "companyType")
    private String  companyType;

    @ApiModelProperty(value = "法人代表证明及授权书url", name = "legalPersonAuthUrl")
    private String legalPersonAuthUrl;

    /**
     * 主管税务机关
     */
    @ApiModelProperty(value = "主管税务机关", name = "taxAuthority")
    private String taxAuthority;

    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段", name = "ex")
    private String ex;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

    /**
     * 申请来源:1大宗平台,2物流平台
     */
    private String applySource;

    /**
     * 实名认证状态:1未认证,2认证通过
     */
    private String realnameStatus;

    /**
     * 会员类型:10项目会员,20一般会员
     */
    private String memberType;

    /**
     * 贸易业务归属企业id
     */
    private String bizCompanyId;

    /**
     * 商家类型:10自营,20直营,30贸易商
     */
    private String bizType;

    /**
     * 入驻申请人
     */
    private String bizContacts;

    /**
     * 入驻申请人电话
     */
    private String bizPhone;

    /**
     * 主营品类
     */
    private List<Long> bizCategoryList;

    /**
     * 主营品牌
     */
    private List<Long> bizBrandList;

}
