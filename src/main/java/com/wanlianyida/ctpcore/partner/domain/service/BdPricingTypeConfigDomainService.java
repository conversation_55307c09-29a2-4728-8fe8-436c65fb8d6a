package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.condition.BdPricingTypeConfigCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.BdPricingTypeConfigEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.BdPricingTypeConfigRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.RedisConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.EnableStatusEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.UnitTypeEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.util.BigDecimalUtil;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 计价类型配置枚举
 *
 * <AUTHOR>
 * @since 2024/10/9 14:09
 */
@Service
public class BdPricingTypeConfigDomainService {

    @Resource
    private BdPricingTypeConfigRepository pricingTypeConfigRepository;

    @Resource
    private RedisService redisService;

    public void add(BdPricingTypeConfigEntity entity) {
        BdPricingTypeConfigCondition condition = new BdPricingTypeConfigCondition();
        condition.setPlatformCode(entity.getPlatformCode());
        List<BdPricingTypeConfigEntity> list = pricingTypeConfigRepository.queryByCondition(condition);
        if (IterUtil.isEmpty(list)) {
            entity.setPricingType(1000);
        } else {
            Integer max = list.stream().map(BdPricingTypeConfigEntity::getPricingType).max(Comparator.naturalOrder()).orElse(0);
            if (max < 1000) {
                entity.setPricingType(1000);
            } else {
                entity.setPricingType(max + 1);
            }
        }
        entity.setSortNum(entity.getPricingType());

        int add = pricingTypeConfigRepository.add(entity);
        if (add == 0) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UNIT_CONFIG_ADD_FAILED);
        }
        //清空redis
        clearRedis(entity.getPlatformCode());
    }

    public void update(BdPricingTypeConfigEntity entity) {
        BdPricingTypeConfigCondition condition = new BdPricingTypeConfigCondition();
        condition.setId(entity.getId());
        List<BdPricingTypeConfigEntity> list = pricingTypeConfigRepository.queryByCondition(condition);
        if (IterUtil.isEmpty(list)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UNIT_CONFIG_NOT_EXISTS);
        }

        BdPricingTypeConfigEntity pricingTypeConfig = IterUtil.getFirst(list);
        Integer afterPoint = Optional.ofNullable(entity.getAfterPoint()).orElse(pricingTypeConfig.getAfterPoint());
        Integer beforePoint = Optional.ofNullable(entity.getBeforePoint()).orElse(pricingTypeConfig.getBeforePoint());
        //最小值
        entity.setMinValue(BigDecimalUtil.generateMin(afterPoint));
        //最大值
        entity.setMaxValue(BigDecimalUtil.generateMax(beforePoint, afterPoint));

        int update = pricingTypeConfigRepository.update(entity);
        if (update == 0) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UNIT_CONFIG_UPDATE_FAILED);
        }

        //清空redis
        clearRedis(IterUtil.getFirst(list).getPlatformCode());
    }

    public PageInfo<BdPricingTypeConfigEntity> queryPage(PagingInfo<BdPricingTypeConfigCondition> pagingInfo) {
        BdPricingTypeConfigCondition condition = pagingInfo.getFilterModel();
        Page<Object> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, true);
//        PageHelper.orderBy("created_date desc");

        condition.setDeleted(0);
        List<BdPricingTypeConfigEntity> list = pricingTypeConfigRepository.queryByCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        PageInfo<BdPricingTypeConfigEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    public void delete(Long id) {
        BdPricingTypeConfigCondition condition = new BdPricingTypeConfigCondition();
        condition.setId(id);
        List<BdPricingTypeConfigEntity> list = pricingTypeConfigRepository.queryByCondition(condition);
        if (IterUtil.isEmpty(list)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UNIT_CONFIG_NOT_EXISTS);
        }

        int delete = pricingTypeConfigRepository.delete(id);
        if (delete == 0) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UNIT_CONFIG_DELETE_FAILED);
        }
        //清空redis
        clearRedis(IterUtil.getFirst(list).getPlatformCode());
    }

    /**
     * 查询计价配置枚举
     */
    public List<BdPricingTypeConfigEntity> query(BdPricingTypeConfigCondition condition) {
        String key = StrUtil.format(RedisConstants.PRICING_TYPE_CONFIG_PREFIX, condition.getPlatformCode());
        String redisKey;
        if (StrUtil.equals(condition.getEnableStatus(), EnableStatusEnum.DEFAULT.getCode())) {
            redisKey = key + EnableStatusEnum.DEFAULT.getCode();
        }else if (ObjUtil.equal(condition.getMeasureUnit(), 1)) {
            //计量单位
            redisKey = key + UnitTypeEnum.MEASURE.getRedisPrefix();
        } else if (ObjUtil.equal(condition.getPricingUnit(), 1)) {
            //计价单位
            redisKey = key + UnitTypeEnum.PRICING.getRedisPrefix();
        } else {
            //全部
            redisKey = key + UnitTypeEnum.ALL.getRedisPrefix();
        }

        String pricingTypeConfigRedisData = redisService.get(redisKey);
        if (StrUtil.isNotEmpty(pricingTypeConfigRedisData)) {
            return JSONUtil.toList(pricingTypeConfigRedisData, BdPricingTypeConfigEntity.class);
        }

        //加载配置
        cachePricingTypeConfig(condition.getPlatformCode());

        List<BdPricingTypeConfigEntity> list = pricingTypeConfigRepository.queryByCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    private void cachePricingTypeConfig(String platformCode) {
        String key = StrUtil.format(RedisConstants.PRICING_TYPE_CONFIG_PREFIX, platformCode);
        //删除key
        clearRedis(platformCode);

        BdPricingTypeConfigCondition condition = new BdPricingTypeConfigCondition();
        condition.setDeleted(0);
        List<BdPricingTypeConfigEntity> list = pricingTypeConfigRepository.queryByCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return;
        }

        //每天晚上23点过期
        String endDate = DateUtil.formatDate(new Date()) + " 23:00:00";
        int expirationTime = (int) DateUtil.between(new Date(), DateUtil.parseDateTime(endDate), DateUnit.SECOND);

        List<BdPricingTypeConfigEntity> pricingUnitList = list.stream().filter(c -> ObjUtil.equal(c.getPricingUnit(), 1)).collect(Collectors.toList());
        if (IterUtil.isNotEmpty(pricingUnitList)) {
            String redisKey = key + UnitTypeEnum.PRICING.getRedisPrefix();
            redisService.set(redisKey, JSONUtil.toJsonStr(pricingUnitList), expirationTime, TimeUnit.SECONDS);
        }
        List<BdPricingTypeConfigEntity> measureUnitList = list.stream().filter(c -> ObjUtil.equal(c.getMeasureUnit(), 1)).collect(Collectors.toList());
        if (IterUtil.isNotEmpty(measureUnitList)) {
            String redisKey = key + UnitTypeEnum.MEASURE.getRedisPrefix();
            redisService.set(redisKey, JSONUtil.toJsonStr(measureUnitList), expirationTime, TimeUnit.SECONDS);
        }

        //保存所有配置
        String redisKey = key+ UnitTypeEnum.ALL.getRedisPrefix();
        redisService.set(redisKey, JSONUtil.toJsonStr(list), expirationTime, TimeUnit.SECONDS);
    }

    /**
     * 清空redis
     */
    private void clearRedis(String platformCode) {
        String key = StrUtil.format(RedisConstants.PRICING_TYPE_CONFIG_PREFIX, platformCode);
        UnitTypeEnum.redisPrefixValues().forEach(redisPrefix -> {
            redisService.remove(key + redisPrefix);
        });
    }

}
