package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmCompanyLegalPersonAuditCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyLegalPersonAuditEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 11:53
 */
public interface UmCompanyLegalPersonAuditRepository {

    UmCompanyLegalPersonAuditEntity queryById(Long id);

    UmCompanyLegalPersonAuditEntity queryBySocialCreditCode(String socialCreditCode);

    List<UmCompanyLegalPersonAuditEntity> queryByCondition(UmCompanyLegalPersonAuditCondition condition);

    boolean insert(UmCompanyLegalPersonAuditEntity entity);

    void updateById(UmCompanyLegalPersonAuditEntity entity);

    void deleteBySocialCreditCode(String socialCreditCode);
}
