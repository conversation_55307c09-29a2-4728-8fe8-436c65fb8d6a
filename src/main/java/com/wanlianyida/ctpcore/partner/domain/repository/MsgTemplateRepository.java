package com.wanlianyida.ctpcore.partner.domain.repository;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.bo.MsgTemplateBO;
import com.wanlianyida.ctpcore.partner.domain.model.bo.MsgTemplateCategoryBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.MsgTemplateCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.MsgTemplateEntity;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.MsgTemplateDTO;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;

import java.util.List;

/**
 * 消息模板表 Service
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
public interface MsgTemplateRepository {

	/**
	 * 分页查询
	 * @param pagingInfo 分页参数
	 * @return {@link PageInfo}<{@link MsgTemplateDTO}>
	 */
	PageInfo<MsgTemplateDTO> queryPage(PagingInfo<MsgTemplateCondition> pagingInfo);

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link MsgTemplateEntity}>
	 */
	List<MsgTemplateDTO> queryList(MsgTemplateCondition condition);

	/**
     * 关联新增
     *
     * @param bo
     */
	Long insert(MsgTemplateBO bo);

	/**
	 * 关联修改
	 *
	 * @param bo
	 */
	Long update(MsgTemplateBO bo);

	/**
	 * 分类设置
	 * @param bo
	 * @return Long
	 */
	Long updateCategory(MsgTemplateCategoryBO bo);

	/**
	 * 分类启停
	 * @param bo
	 * @return Long
	 */
	Long switchCategory(MsgTemplateCategoryBO bo);

	/**
	 * 逻辑删除
	 *
	 * @param id
	 */
	Long delete(Long id);

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link MsgTemplateDTO}
	 */
	MsgTemplateDTO selectByPrimaryKey(Long id);
	
}
