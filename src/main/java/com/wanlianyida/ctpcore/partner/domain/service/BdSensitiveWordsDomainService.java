package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.condition.BdSensitiveWordsCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.BdSensitiveWordsEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.BdSensitiveWordsRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.config.SensitiveWordsInitializer;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.util.SetBaseInfo;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.enums.PlatfromCodeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
@Slf4j
public class BdSensitiveWordsDomainService {
    @Resource
    private BdSensitiveWordsRepository repository;
    @Resource
    private SensitiveWordsInitializer wordsInitializer;

    public void batchInsert(BdSensitiveWordsEntity entity) {
        // 敏感词截取并去重
        List<String> senWordsList = Arrays.stream(entity.getSenWords().split(","))
                .distinct()
                .collect(Collectors.toList());

        // 去重校验
        validateDuplicateSensitiveWords(senWordsList);

        SetBaseInfo.setCreatorInfo(entity, JwtUtil.getTokenInfo());

        if (CollUtil.isNotEmpty(senWordsList)) {
            List<BdSensitiveWordsEntity> wordsEntities = senWordsList.stream().map(senWord -> {
                BdSensitiveWordsEntity bdSensitiveWordsEntity = BeanUtil.copyProperties(entity, BdSensitiveWordsEntity.class);
                bdSensitiveWordsEntity.setSenWord(senWord);
                bdSensitiveWordsEntity.setPlatformCode(StrUtil.isNotEmpty(entity.getPlatformCode())
                        ? entity.getPlatformCode() : PlatfromCodeEnum.CTP.getCode());
                return bdSensitiveWordsEntity;
            }).collect(Collectors.toList());

            // 执行批量操作
            repository.batchInsert(wordsEntities);
            // 重新构建敏感词
            wordsInitializer.initSensitiveWordsOnStartup();
        }
    }

    public Boolean update(BdSensitiveWordsEntity entity) {
        // 单查
        BdSensitiveWordsEntity bdSensitiveWordsEntity = repository.selectById(entity.getId());
        if (ObjectUtil.isEmpty(bdSensitiveWordsEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UPDATE_DATA_NOT_EXISTS);
        }

        if (StrUtil.isNotEmpty(entity.getSenWord()) &&
                !entity.getSenWord().equals(bdSensitiveWordsEntity.getSenWord())) {

            List<String> senWordsList = new ArrayList<>();
            senWordsList.add(entity.getSenWord());

            // 去重校验
            validateDuplicateSensitiveWords(senWordsList);
        }

        SetBaseInfo.setUpdaterInfo(entity, JwtUtil.getTokenInfo());

        Boolean result = repository.updateInfo(entity);
        // 重新构建敏感词
        wordsInitializer.initSensitiveWordsOnStartup();

        return result;
    }

    private void validateDuplicateSensitiveWords(List<String> senWordsList) {
        // 获取所有已存在的敏感词
        List<String> existingSenWords = repository.selectAll().stream()
                .map(BdSensitiveWordsEntity::getSenWord)
                .collect(Collectors.toList());

        // 去重校验
        List<String> duplicates = senWordsList.stream()
                .filter(existingSenWords::contains)
                .collect(Collectors.toList());
        if (!duplicates.isEmpty()) {
            String join = String.join(",", duplicates);
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.SENSITIVE_WORD_REPEAT);
        }
    }

    public Boolean batchDelete(BdSensitiveWordsEntity entity) {
        SetBaseInfo.setUpdaterInfo(entity, JwtUtil.getTokenInfo());
        Boolean result = repository.batchDelete(entity);
        // 重新构建敏感词
        wordsInitializer.initSensitiveWordsOnStartup();

        return result;
    }

    public PageInfo<BdSensitiveWordsEntity> queryPage(PagingInfo<BdSensitiveWordsCondition> conditionPage) {
        BdSensitiveWordsCondition condition = conditionPage.getFilterModel();
        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        List<BdSensitiveWordsEntity> list = repository.queryPage(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        PageInfo<BdSensitiveWordsEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }


}
