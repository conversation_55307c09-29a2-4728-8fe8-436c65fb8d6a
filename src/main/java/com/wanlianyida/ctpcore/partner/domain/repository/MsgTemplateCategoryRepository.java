package com.wanlianyida.ctpcore.partner.domain.repository;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.bo.MsgTemplateCategoryBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.MsgTemplateCategoryCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.MsgTemplateCategoryEntity;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.MsgTemplateCategoryDTO;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;

import java.util.List;

/**
 * 消息模板分类表 Service
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
public interface MsgTemplateCategoryRepository {

	/**
	 * 分页查询
	 * @param pagingInfo 分页参数
	 * @return {@link PageInfo}<{@link MsgTemplateCategoryDTO}>
	 */
	PageInfo<MsgTemplateCategoryDTO> queryPage(PagingInfo<MsgTemplateCategoryCondition> pagingInfo);

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link MsgTemplateCategoryEntity}>
	 */
	List<MsgTemplateCategoryEntity> queryList(MsgTemplateCategoryCondition condition);

	/**
     * 关联新增
     *
     * @param bo
     */
	Long insert(MsgTemplateCategoryBO bo);

	/**
	 * 关联修改
	 *
	 * @param bo
	 */
	Long update(MsgTemplateCategoryBO bo);

    /**
     * 单表新增
     *
     * @param record
     * @return int
     */
    Long insertSelective(MsgTemplateCategoryEntity record);

    /**
     * 单表修改
     *
     * @param record
     * @return int
     */
    Long updateByPrimaryKeySelective(MsgTemplateCategoryEntity record);

	/**
	 * 逻辑删除
	 *
	 * @param id
	 */
	Long delete(Long id);

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link MsgTemplateCategoryEntity}
	 */
	MsgTemplateCategoryEntity selectByPrimaryKey(Long id);
	
}
