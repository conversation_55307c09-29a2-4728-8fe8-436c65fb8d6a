package com.wanlianyida.ctpcore.partner.domain.model.condition;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年09月06 17:14
 */
@Data
public class UmShopCollectUserCondition {

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userBaseId;


    /**
     * 店铺id
     */
    @NotBlank(message = "店铺id不能为空")
    private String shopId;

    /**
     * 店铺id列表
     */
    private List<String> shopIdList;
}
