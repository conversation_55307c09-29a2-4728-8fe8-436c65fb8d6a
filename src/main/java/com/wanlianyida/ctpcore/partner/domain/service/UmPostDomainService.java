package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.condition.DictValueCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmPostCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.DictValue;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmPostEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.IDictValueRepository;
import com.wanlianyida.ctpcore.partner.domain.repository.UmPostRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.PermissionConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.EnableStatusFlagEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.util.SetBaseInfo;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

import static com.wanlianyida.ctpcore.partner.infrastructure.util.SetBaseInfo.getAndValidateCompanyId;

@Service
public class UmPostDomainService {
    @Resource
    private UmPostRepository repository;

    @Resource
    private IDictValueRepository dictValueRepository;

    public Boolean add(UmPostEntity umPostEntity) {
        // 验证企业ID
        String companyId = getAndValidateCompanyId(umPostEntity.getRelCompanyId(), JwtUtil.getTokenInfo());
        umPostEntity.setRelCompanyId(companyId);

        // 验证岗位等级是否存在
        validatePostGrade(umPostEntity.getPostGrade());

        // 验证同一平台同一公司同一岗位级别下岗位名称是否重复
        validateUniquePostName(umPostEntity, umPostEntity.getRelCompanyId());

        SetBaseInfo.setCreatorInfo(umPostEntity, JwtUtil.getTokenInfo());

        return repository.add(umPostEntity);
    }

    public Boolean update(UmPostEntity umPostEntity) {
        // 验证企业ID
        String companyId = getAndValidateCompanyId(umPostEntity.getRelCompanyId(),JwtUtil.getTokenInfo());
        umPostEntity.setRelCompanyId(companyId);

        // 验证岗位是否存在
        UmPostEntity existingPost = fetchExistingPost(umPostEntity.getId(), umPostEntity.getPlatformType());
        if (ObjectUtil.isEmpty(existingPost)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UPDATE_INFO_NOT_EXISTS);
        }

        // 验证岗位等级是否存在
        validatePostGrade(umPostEntity.getPostGrade());

        // 验证岗位名称或等级是否变更
        if (isPostNameOrGradeChanged(existingPost, umPostEntity)) {
            // 验证同一平台同一公司同一岗位级别下岗位名称是否重复
            validateUniquePostName(umPostEntity, companyId);
        }

        SetBaseInfo.setUpdaterInfo(umPostEntity, JwtUtil.getTokenInfo());

        return repository.update(umPostEntity);
    }

    public Boolean delete(UmPostEntity umPostEntity) {
        // 验证企业ID
        String companyId = getAndValidateCompanyId(umPostEntity.getRelCompanyId(),JwtUtil.getTokenInfo());
        umPostEntity.setRelCompanyId(companyId);

        // 校验是有成员在使用此岗位
        Integer validatePostIsUsed = repository.validatePostIsUsed(BeanUtil.copyProperties(umPostEntity, UmPostCondition.class));
        if (validatePostIsUsed > 0) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.JOB_IN_USE);
        }
        SetBaseInfo.setUpdaterInfo(umPostEntity, JwtUtil.getTokenInfo());
        return repository.delete(umPostEntity);
    }

    public PageInfo<UmPostEntity> queryPage(PagingInfo<UmPostCondition> conditionPage) {
        UmPostCondition condition = conditionPage.getFilterModel();
        // 验证企业ID
        String companyId = getAndValidateCompanyId(condition.getRelCompanyId(),JwtUtil.getTokenInfo());
        condition.setRelCompanyId(companyId);

        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        List<UmPostEntity> list = repository.queryPage(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        PageInfo<UmPostEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    /**
     * 单查
     *
     * @param postId         帖子id
     * @param permissionType 权限类型
     * @return {@code UmPostEntity }
     */
    private UmPostEntity fetchExistingPost(Long postId, String permissionType) {
        // 构件查询条件
        UmPostCondition condition = createUmPostConditionWithId(postId, permissionType);
        return repository.selectInfo(condition);
    }

    /**
     * 岗位级别或岗位名称有变更
     *
     * @param existingPost 现有职位
     * @param updatedPost  更新帖子
     * @return boolean
     */
    private boolean isPostNameOrGradeChanged(UmPostEntity existingPost, UmPostEntity updatedPost) {
        return !existingPost.getPostName().equals(updatedPost.getPostName())
                || !existingPost.getPostGrade().equals(updatedPost.getPostGrade());
    }

    /**
     * 验证岗位等级是否存在
     *
     * @param postGrade 职位等级
     */
    private void validatePostGrade(String postGrade) {
        DictValueCondition condition = new DictValueCondition();
        condition.setDictCode(PermissionConstants.POST_GRADE_CODE);
        condition.setDictStatus(EnableStatusFlagEnum.ENABLE.getCode());

        List<DictValue> dictValues = dictValueRepository.queryCondition(condition);
        if (CollUtil.isEmpty(dictValues) || !dictValues.stream().anyMatch(dictValue -> dictValue.getDictValue().equals(postGrade))) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.JOB_LEVEL_NOT_EXISTS);
        }
    }

    /**
     * 验证同一平台同一公司同一岗位级别下岗位名称是否重复
     *
     * @param umPostEntity 实体
     * @param companyId    公司id
     */
    private void validateUniquePostName(UmPostEntity umPostEntity, String companyId) {
        UmPostCondition condition = createUmPostConditionForUniqueCheck(umPostEntity, companyId);
        UmPostEntity existingPost = repository.selectInfo(condition);
        if (ObjectUtil.isNotEmpty(existingPost)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.JOB_ALREADY_EXISTS);
        }
    }


    /**
     * 构件查询条件
     *
     * @param postId         帖子id
     * @param permissionType 权限类型
     * @return {@code UmPostCondition }
     */
    private UmPostCondition createUmPostConditionWithId(Long postId, String permissionType) {
        UmPostCondition condition = new UmPostCondition();
        condition.setId(postId);
        condition.setPlatformType(permissionType);
        return condition;
    }

    /**
     * 为唯一检查创建um-post条件
     *
     * @param umPostEntity um邮政实体
     * @param companyId    公司id
     * @return {@code UmPostCondition }
     */
    private UmPostCondition createUmPostConditionForUniqueCheck(UmPostEntity umPostEntity, String companyId) {
        UmPostCondition condition = new UmPostCondition();
        condition.setPlatformType(umPostEntity.getPlatformType());
        condition.setPostName(umPostEntity.getPostName());
        condition.setPostGrade(umPostEntity.getPostGrade());
        condition.setRelCompanyId(companyId);
        return condition;
    }
}
