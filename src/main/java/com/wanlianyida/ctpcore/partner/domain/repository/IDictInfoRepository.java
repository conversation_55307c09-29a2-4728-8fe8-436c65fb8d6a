package com.wanlianyida.ctpcore.partner.domain.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wanlianyida.ctpcore.partner.domain.model.condition.DictCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.DictInfo;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.po.DictInfoPO;

import java.util.List;

/**
 * 字典表(DictInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:17
 */
public interface IDictInfoRepository extends IService<DictInfoPO> {

    List<DictInfo> queryCondition(DictCondition condition);
}

