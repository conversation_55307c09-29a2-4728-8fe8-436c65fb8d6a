package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.basemdm.api.inter.MdmCompanyApplyInter;
import com.wanlianyida.basemdm.api.inter.MdmCompanyInfoInter;
import com.wanlianyida.basemdm.api.inter.MdmUserInfoInter;
import com.wanlianyida.basemdm.api.model.command.MdmCompanyApplyCommand;
import com.wanlianyida.basemdm.api.model.command.MdmCompanyBeneficiaryCommand;
import com.wanlianyida.basemdm.api.model.command.MdmCompanyInfoCommand;
import com.wanlianyida.basemdm.api.model.dto.AuditStatisticsDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmCompanyApplyDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmCompanyInfoDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmCompanyApplyQuery;
import com.wanlianyida.basemdm.api.model.query.MdmCompanyInfoQuery;
import com.wanlianyida.basemdm.api.model.query.MdmGetUserListQuery;
import com.wanlianyida.ctpcore.partner.application.assembler.UmBusinessLicenseAssembler;
import com.wanlianyida.ctpcore.partner.domain.model.bo.CompanyBusinessLicenseAuditBO;
import com.wanlianyida.ctpcore.partner.domain.model.bo.OperatorBO;
import com.wanlianyida.ctpcore.partner.domain.model.entity.QualSuppApplyMqEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyBusinessLicenseEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyLegalPersonAuditEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.UmCompanyLegalPersonAuditRepository;
import com.wanlianyida.ctpcore.partner.domain.repository.UmCompanyRepository;
import com.wanlianyida.ctpcore.partner.domain.repository.UmMemberRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.CommonConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.KafkaConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.CompanyAuditStatusEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.BizLogUtilService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.BizMsgUtilService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.CommonUtil;
import com.wanlianyida.ctpcore.partner.infrastructure.util.PartnerException;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.support.api.model.command.MsgCommand;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;
import com.wanlianyida.support.api.model.enums.MsgTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.ZoneOffset;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月28日 15:59
 */
@Slf4j
@Service
public class UmBusinessLicenseAuditDomainService {

    @Resource
    private MdmCompanyApplyInter companyApplyInter;
    @Resource
    private UmCompanyRepository umCompanyRepository;
    @Resource
    private MdmCompanyInfoInter cmdCompanyInfoInter;
    @Resource
    private MqEventPublisher mqEventPublisher;
    @Resource
    private UmCompanyLegalPersonAuditRepository companyLegalPersonAuditRepository;
    @Resource
    private MdmExchangeService mdmExchangeService;
    @Autowired
    private BizMsgUtilService bizMsgUtilService;

    @Resource
    private UmMemberRepository umMemberRepository;

    @Resource
    private MdmUserInfoInter mdmUserInfoInter;

    @Transactional(rollbackFor = Exception.class)
    public void audit(MdmCompanyApplyDTO query, CompanyBusinessLicenseAuditBO bo) {
        log.info("【营业执照审核】审核信息：{}", bo);
        MdmCompanyApplyCommand command = bo.getApplyCommand();
        String aptitudeStatus = command.getAptitudeStatus();
        ResponseMessage<Long> responseMessage = null;
        if (aptitudeStatus != null && ObjectUtil.equal(CompanyAuditStatusEnum.PASS.getCode(), aptitudeStatus)){
            responseMessage = companyApplyInter.update(command);
        } else if (aptitudeStatus != null && ObjectUtil.equal(CompanyAuditStatusEnum.REJECT.getCode(), aptitudeStatus)){
            MdmCompanyApplyCommand applyCommand = new MdmCompanyApplyCommand();
            applyCommand.setId(command.getId());
            applyCommand.setAptitudeStatus(aptitudeStatus);
            applyCommand.setReviewReason(command.getReviewReason());
            responseMessage = companyApplyInter.update(applyCommand);
        }

        if (!responseMessage.isSucceed()) {
            throw new PartnerException(CtpCorePartnerExceptionEnum.AUDIT_RECORD_FAIL);
        }
        // 审核法定代表人
        UmCompanyLegalPersonAuditEntity legalPersonAuditEntity = bo.getLegalPersonAuditEntity();

        if (legalPersonAuditEntity != null) {
            if (aptitudeStatus != null && ObjectUtil.equal(CompanyAuditStatusEnum.PASS.getCode(), aptitudeStatus)){
                companyLegalPersonAuditRepository.updateById(legalPersonAuditEntity);
            } else if (aptitudeStatus != null && ObjectUtil.equal(CompanyAuditStatusEnum.REJECT.getCode(), aptitudeStatus)) {
                UmCompanyLegalPersonAuditEntity personAuditEntity = new UmCompanyLegalPersonAuditEntity();
                personAuditEntity.setId(legalPersonAuditEntity.getId());
                personAuditEntity.setAuditStatus(Integer.valueOf(aptitudeStatus));
                personAuditEntity.setRejectReason(legalPersonAuditEntity.getRejectReason());
                personAuditEntity.setAuditTime(new Date());
                companyLegalPersonAuditRepository.updateById(personAuditEntity);
            }
        }
        // 同步数据
        if (CompanyAuditStatusEnum.PASS.getCode().equals(aptitudeStatus)) {
            // 更新本地数据库数据
            UmCompanyEntity entity = UmBusinessLicenseAssembler.createUmCompany(query, command);
            umCompanyRepository.updateBySocialCreditCode(entity);
            log.info("【营业执照审核】本地企业信息：{}", entity);
            // 主数据域
            MdmCompanyInfoCommand mdmCompany = UmBusinessLicenseAssembler.createMdmCompany(query, bo);
            log.info("【营业执照审核】同步企业信息：{}", mdmCompany);
            ResponseMessage<?> companyResponseMessage = cmdCompanyInfoInter.syncCompanyInfoToCmd(mdmCompany);
            if (!companyResponseMessage.isSucceed()) {
                throw new PartnerException(CtpCorePartnerExceptionEnum.AUDIT_RECORD_FAIL_SYNC);
            }
            // 主数据企业id
            Object companyIdForCmd = companyResponseMessage.getModel();

            // 推送消息
            OperatorBO operatorBO = UmBusinessLicenseAssembler.createCompanyAuditMessage(String.valueOf(companyIdForCmd), query, command);
            log.info("【营业执照审核】推送消息：{}", operatorBO);
            MqEventMessage.EventMessage<OperatorBO> message = MqEventMessage.buildEventMessage(KafkaConstants.CTP_COMPANY_STATUS_CHANGE_NOTICE, operatorBO);
            mqEventPublisher.syncSendNormalMessage(message);

            // 新增企业受益人
            processCompanyBeneficiary(query, legalPersonAuditEntity, companyIdForCmd, command);

            // 同步企业资质数据
            syncQualificationExpiryAfterAudit(bo);
        } else if (CompanyAuditStatusEnum.REJECT.getCode().equals(aptitudeStatus)) {
            if (isCreateCompanyAuditScene(query.getLicenseNo())) {
                sendMessage(query, MsgTemplateEnum.COMPANY_CREATE_FAIL.getId());
            }
        }

        // 异步更新本地企业资质审核状态
        UmCompanyBusinessLicenseEntity umLicense = UmBusinessLicenseAssembler.createUmLicense(query, command);
        MqEventMessage.EventMessage<UmCompanyBusinessLicenseEntity> eventMessage = MqEventMessage.buildEventMessage(KafkaConstants.CTP_BUSINESS_LICENSE_AUDIT_NOTICE, umLicense);
        mqEventPublisher.syncSendNormalMessage(eventMessage);

        command.setLicenseNo(query.getLicenseNo());
        // 发送审核短信/站内信
        sendBusinessLicenseReviewMessage(query, aptitudeStatus);
        // 记录日志
        BizLogUtilService.sendLogBizMsg(command.getLicenseNo(), command.getUpdatedDate(),Integer.valueOf(command.getAptitudeStatus()),
                command.getReviewReason(), LogOperationRecordEnum.CTP_PARTNER_LICENSE_AUDIT);
    }
    /**
     * 审核发送站内信和短信
     * @param applyDTO
     */
    private void sendBusinessLicenseReviewMessage(MdmCompanyApplyDTO applyDTO,String aptitudeStatus) {
        // 获取企业信息
        UmCompanyEntity umCompanyEntity = umCompanyRepository.queryBySocialCreditCode(applyDTO.getLicenseNo());
        if (umCompanyEntity == null || umCompanyEntity.getId() == null){
            log.error("[补充资料审核]审核通过发送站内信和短信失败,未找到企业信息,统一信用代码：{}", applyDTO.getLicenseNo());
            return;
        }
        String creatorId = applyDTO.getCreatorId();
        if (StrUtil.isEmpty(creatorId)){
            log.error("创建用户id为空");
            return;
        }
        if (this.isUserBaseId(creatorId)){
            MdmGetUserListQuery mdmGetUserListQuery = new MdmGetUserListQuery();
            mdmGetUserListQuery.setUserBaseIdList(Collections.singletonList(creatorId));
            ResponseMessage<List<MdmUserInfoDTO>> listResponseMessage = mdmUserInfoInter.batchQueryUserList(mdmGetUserListQuery);
            if (!listResponseMessage.isSucceed() || CollUtil.isEmpty(listResponseMessage.getModel())){
                log.error("未查到对应的userid，查询主数据失败，creatorId：{}",creatorId);
                return;
            }
            List<MdmUserInfoDTO> model = listResponseMessage.getModel();
            MdmUserInfoDTO first = CollUtil.getFirst(model);
            creatorId = String.valueOf(first.getId());
        }

        bizMsgUtilService.sendBusinessLicenseReviewMsg(umCompanyEntity, aptitudeStatus, Collections.singletonList(creatorId));
    }
    public boolean isUserBaseId(String str) {
        return str.matches(".*[a-zA-Z].*");
    }
    /**
     * 发送消息
     */
    private void sendMessage(MdmCompanyApplyDTO query, String templateId) {
        try {
            // 组装消息
            MsgCommand msgCommand = buildMessageBody(query, templateId);
            if (ObjectUtil.isNotEmpty(msgCommand)) {
                MqEventMessage.EventMessage<MsgCommand> message = MqEventMessage.buildEventMessage(KafkaConstants.CTP_SEND_MSG_TOPIC, msgCommand);
                mqEventPublisher.syncSendNormalMessage(message);
                log.info("【营业执照审核】，开始发送消息：{}", JSONUtil.toJsonStr(msgCommand));
            }
        } catch (Exception e) {
            log.warn("【营业执照审核】发送消息异常：{}", e);
        }
    }

    /**
     * 组装消息
     */
    /**
     * 组装消息
     */
    private MsgCommand buildMessageBody(MdmCompanyApplyDTO query, String templateId) {
        try {
            MsgCommand msgCommand = new MsgCommand();
            String creatorId = query.getCreatorId();

            // 创建人ID为空
            if (StrUtil.isEmpty(creatorId)) {
                log.info("【营业执照审核】创建人id为空,发送消息流程终止");
                return null;
            }

            // 设置基础参数
            msgCommand.setSceneType("10");
            msgCommand.setTemplateId(templateId);
            Map<String, String> paramMap = Collections.singletonMap("companyName", query.getLicenseName());
            msgCommand.setParamMap(paramMap);

            // 查询接收人信息
            List<MdmUserInfoDTO> userInfoDTOS = Optional.ofNullable(queryReceivers(creatorId))
                    .orElse(Collections.emptyList());

            if (userInfoDTOS.isEmpty()) {
                log.info("【营业执照审核】未查询到创建人信息，发送消息流程终止");
                return null;
            }

            // 收集用户ID与手机号
            List<String> userIds = new ArrayList<>();
            List<String> mobileList = new ArrayList<>();

            for (MdmUserInfoDTO dto : userInfoDTOS) {
                if (dto.getId() != null) {
                    userIds.add(String.valueOf(dto.getId()));
                }
                if (StrUtil.isNotBlank(dto.getMobile())) {
                    mobileList.add(dto.getMobile());
                }
            }

            msgCommand.setTargetUserIdList(userIds);
            msgCommand.setTargetPhoneList(mobileList);

            return msgCommand;

        } catch (Exception e) {
            log.warn("【营业执照审核】发送消息异常,发送消息流程终止", e);
            return null;
        }
    }


    /**
     * 企业资质 & 法人资质审核后同步更新资质到期表
     *
     * @param bo 审核业务对象
     */
    private void syncQualificationExpiryAfterAudit(CompanyBusinessLicenseAuditBO bo) {
       try{
           MdmCompanyApplyCommand applyCommand = bo.getApplyCommand();
           UmCompanyLegalPersonAuditEntity legalPersonAuditEntity = bo.getLegalPersonAuditEntity();

           // 同步企业资质
           if (ObjectUtil.isNotEmpty(applyCommand)) {
               QualSuppApplyMqEntity entity = buildQualificationEntityFromCompany(applyCommand);
               sendQualificationMessage(entity);
           }

           // 同步法人资质
           if (ObjectUtil.isNotEmpty(legalPersonAuditEntity)) {
               QualSuppApplyMqEntity entity = buildQualificationEntityFromLegalPerson(legalPersonAuditEntity, applyCommand);
               sendQualificationMessage(entity);
           }
       }catch (Exception e){
           log.warn("【资质消息提醒】发送消息异常：{}", e);
       }
    }

    /**
     * 构造企业资质实体
     */
    private QualSuppApplyMqEntity buildQualificationEntityFromCompany(MdmCompanyApplyCommand command) {
        QualSuppApplyMqEntity entity = new QualSuppApplyMqEntity();
        entity.setApplySource("20");
        entity.setCompanyName(command.getLicenseName());
        entity.setQualType("10");
        entity.setSocialCreditCode(command.getLicenseNo());

        String licenseValidIsLong = command.getLicenseValidIsLong();
        Date licenseEndDate = command.getLicenseEndDate();
        if (StrUtil.isEmpty(licenseValidIsLong) && ObjectUtil.isEmpty(licenseEndDate)) return null;
        if ("11".equals(licenseValidIsLong)) {
            entity.setValidEndDate(Date.from(CommonConstants.LONG_TERM_EXPIRE_DATE.atStartOfDay(ZoneOffset.systemDefault()).toInstant()));
        } else {
            entity.setValidEndDate(licenseEndDate);
        }

        return entity;
    }

    /**
     * 构造法人资质实体
     */
    private QualSuppApplyMqEntity buildQualificationEntityFromLegalPerson(UmCompanyLegalPersonAuditEntity legalPerson, MdmCompanyApplyCommand command) {
        QualSuppApplyMqEntity entity = new QualSuppApplyMqEntity();
        entity.setApplySource("20");
        entity.setCompanyName(command.getLicenseName());
        entity.setSocialCreditCode(command.getLicenseNo());
        entity.setLegalPersonName(legalPerson.getUserName());
        entity.setQualType("20");

        Integer licenseLongValidFlag = legalPerson.getLicenseLongValidFlag();

        String validFlag = licenseLongValidFlag.toString();
        Date untilDate = legalPerson.getUntilDate();
        if (StrUtil.isEmpty(validFlag) && ObjectUtil.isEmpty(untilDate)) return null;
        if ("11".equals(validFlag)) {
            entity.setValidEndDate(Date.from(CommonConstants.LONG_TERM_EXPIRE_DATE.atStartOfDay(ZoneOffset.systemDefault()).toInstant()));
        } else  {
            entity.setValidEndDate(untilDate);
        }

        return entity;
    }

    /**
     * 发送 MQ 消息
     */
    private void sendQualificationMessage(QualSuppApplyMqEntity entity) {
        if (entity == null) return;

        try {
            MqEventMessage.EventMessage<QualSuppApplyMqEntity> message =
                    MqEventMessage.buildEventMessage(KafkaConstants.CTP_RMS_SUPP_QUAL_TOPIC, entity);
            mqEventPublisher.syncSendNormalMessage(message);
            log.info("【资质同步】发送MQ消息成功: {}", JSONUtil.toJsonStr(entity));
        } catch (Exception e) {
            log.error("【资质同步】发送MQ消息失败", e);
        }
    }


    /**
     * 是否为创建企业审核的场景
     * 创建企业审核【主数据companyInfo表没有数据】
     */
    private boolean isCreateCompanyAuditScene(String licenseNo) {
        try {
            MdmCompanyInfoQuery query = new MdmCompanyInfoQuery();
            query.setLicenseNo(licenseNo);
            query.setApplySource("1");
            ResultMode<List<MdmCompanyInfoDTO>> companyListResultModel = mdmExchangeService.getCompanyList(query);
            if (!companyListResultModel.isSucceed()) {
                log.error("【营业执照审核】查询主数据companyInfo表失败,licenseNo:{}", licenseNo);
            }
            List<MdmCompanyInfoDTO> companyInfoDTOList = companyListResultModel.getModel();
            if (CollUtil.isEmpty(companyInfoDTOList)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("【营业执照审核】查询主数据companyInfo表异常,licenseNo:{}", licenseNo, e);
            return false;
        }
    }

    /**
     * 查询接收人信息
     */
    private List<MdmUserInfoDTO> queryReceivers(String creatorId) {
        if (StrUtil.isEmpty(creatorId)) {
            return Collections.emptyList();
        }

        MdmGetUserListQuery mdmGetUserListQuery = new MdmGetUserListQuery();

        if (CommonUtil.isAlphaNumeric(creatorId)) {
            mdmGetUserListQuery.setUserBaseIdList(Collections.singletonList(creatorId));
        } else if (CommonUtil.isPureNumeric(creatorId)) {
            mdmGetUserListQuery.setUserIdList(Collections.singletonList(creatorId));
        } else {
            log.warn("【营业执照审核】creatorId 格式不符合要求，既不是纯数字也不是字母数字组合：{}", creatorId);
            return Collections.emptyList();
        }

        ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.batchQueryUserList(mdmGetUserListQuery);
        if (!resultMode.isSucceed()) {
            log.error("【营业执照审核】查询接收人信息失败, creatorId: {}", creatorId);
            return Collections.emptyList();
        }

        return Optional.ofNullable(resultMode.getModel()).orElse(Collections.emptyList());
    }

    /**
     * 处理企业受益人
     *
     * @param query
     * @param legalPersonAuditEntity
     * @param companyIdForCmd
     * @param command
     */
    private void processCompanyBeneficiary(MdmCompanyApplyDTO query, UmCompanyLegalPersonAuditEntity legalPersonAuditEntity, Object companyIdForCmd, MdmCompanyApplyCommand command) {
        try {
            // cmd 企业受益人表新增默认数据
            String name = legalPersonAuditEntity.getUserName();
            String idCardNo = legalPersonAuditEntity.getIdCardNo();
            String socialCreditCode = query.getLicenseNo();
            MdmCompanyBeneficiaryCommand mdmCompanyBeneficiaryCommand = new MdmCompanyBeneficiaryCommand();
            mdmCompanyBeneficiaryCommand.setCompanyId(String.valueOf(companyIdForCmd));
            mdmCompanyBeneficiaryCommand.setCompanyName(query.getLicenseName());
            mdmCompanyBeneficiaryCommand.setSocialCreditCode(socialCreditCode);
            mdmCompanyBeneficiaryCommand.setBeneficiaryName(name);
            mdmCompanyBeneficiaryCommand.setBeneficiaryCertType("1");
            mdmCompanyBeneficiaryCommand.setBeneficiaryCertNumber(idCardNo);
            mdmCompanyBeneficiaryCommand.setLegalPersonFlag(1);
            mdmCompanyBeneficiaryCommand.setStartDate(legalPersonAuditEntity.getStartDate());
            mdmCompanyBeneficiaryCommand.setUntilDate(legalPersonAuditEntity.getUntilDate());
            Integer licenseLongValidFlag = legalPersonAuditEntity.getLicenseLongValidFlag();
            if (ObjectUtil.isNotEmpty(licenseLongValidFlag)) {
                if (ObjectUtil.equal(licenseLongValidFlag, 11)) {
                    mdmCompanyBeneficiaryCommand.setLongTermValidFlag(1);
                } else if (ObjectUtil.equal(licenseLongValidFlag, 21)) {
                    mdmCompanyBeneficiaryCommand.setLongTermValidFlag(0);
                }
            }
            mdmCompanyBeneficiaryCommand.setCreatorId(ObjectUtil.isNotEmpty(command.getCreatorId()) ? command.getCreatorId() : "");
            mdmCompanyBeneficiaryCommand.setUpdaterId(ObjectUtil.isNotEmpty(command.getUpdaterId()) ? command.getCreatorId() : "");
            ResultMode<String> stringResultMode = mdmExchangeService.addCompanyBeneficiary(mdmCompanyBeneficiaryCommand);
            if (!stringResultMode.isSucceed()) {
                log.info("cmd企业受益人表新增失败：{}", stringResultMode.getMessage());
            }
        } catch (Exception e) {
            log.info("cmd企业受益人表新增失败：{}", e.getMessage());
        }
    }

    public ResponseMessage<List<MdmCompanyApplyDTO>> queryCondition(PagingInfo<MdmCompanyApplyQuery> query) {
        return companyApplyInter.pageList(query);
    }

    public MdmCompanyApplyDTO queryById(Long id) {
        ResponseMessage<MdmCompanyApplyDTO> responseMessage = companyApplyInter.getById(id);
        if (!responseMessage.isSucceed() || responseMessage.getModel() == null) {
            return null;
        }
        return responseMessage.getModel();
    }

    public AuditStatisticsDTO statistics() {
        MdmCompanyApplyQuery query = new MdmCompanyApplyQuery();
        query.setApplySource("1");
        ResponseMessage<AuditStatisticsDTO> responseMessage = companyApplyInter.statistics(query);
        if (!responseMessage.isSucceed() || responseMessage.getModel() == null) {
            return new AuditStatisticsDTO();
        }
        return responseMessage.getModel();
    }
}
