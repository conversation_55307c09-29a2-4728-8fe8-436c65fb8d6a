package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.entity.UmQualSuppApplyEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmQualSuppInfoEntity;

public interface UmQualSuppCommandRepository {

    /**
     * 审核更新申请表
     * @param umQualSuppApplyEntity
     */
    void updateSuppApply(UmQualSuppApplyEntity umQualSuppApplyEntity);

    /**
     * 新增补充资料表
     * @param umQualSuppInfoEntity
     */
    void insertSuppInfo(UmQualSuppInfoEntity umQualSuppInfoEntity);

    /**
     * 新增补充资料申请
     * @param entity
     */
    void insertSuppApply(UmQualSuppApplyEntity entity);

    /**
     * 删除所有过期的申请
     */
    void deleteAllForExpire();
}
