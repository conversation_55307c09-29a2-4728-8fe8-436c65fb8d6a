package com.wanlianyida.ctpcore.partner.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息模板表 entity
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Data
public class MsgTemplateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	//@JsonSerialize(using = ToStringSerializer.class) 此行挪到vo中
	private Long id;

	/**
	 * 模板名称
	 */
	private String templateName;

	/**
	 * 接收类型:10买家,20卖家
	 */
	private String receviceType;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;


}
