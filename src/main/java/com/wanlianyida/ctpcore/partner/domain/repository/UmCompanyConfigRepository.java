package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmCompanyConfigCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyConfigEntity;

import java.util.List;

/**
 * 企业业务配置信息表 Service
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface UmCompanyConfigRepository {

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link UmCompanyConfigEntity}>
	 */
	List<UmCompanyConfigEntity> queryList(UmCompanyConfigCondition condition) ;

	/**
     * 新增
     *
     * @param record
     * @return int
     */
	int insertSelective(UmCompanyConfigEntity record) ;

	/**
	 * 根据主键修改
	 *
	 * @param record
	 * @return int
	 */
	int updateByPrimaryKeySelective(UmCompanyConfigEntity record) ;

	/**
	 * 逻辑删除
	 *
	 * @param id
	 * @return int
	 */
	int deleteByPrimaryKey(Long id) ;

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link UmCompanyConfigEntity}
	 */
	UmCompanyConfigEntity selectByPrimaryKey(Long id) ;

	/**
	 * 物理删除
	 * @param companyId
	 */
	void deleteByCompanyId(String companyId);

	Boolean insertOrUpdate(UmCompanyConfigEntity record);



}
