package com.wanlianyida.ctpcore.partner.domain.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 组织DTO
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UmOrgEntity extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 管理员手机号
     */
    private String phone;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 上级组织id
     */
    private String parentOrgId;

    /**
     * 上级组织名称
     */
    private String parentOrgName;

    /**
     * 上级组织信用代码
     */
    private String parentSocialCreditCode;


    /**
     * 管理员账号
     */
    private String adminAccount;

    /**
     * 操作类型 10-组织新增 20-组织更新 30-组织删除
     */
    private String operationType;


    /**
     * 描述
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    private String platformType;

    private List<UmOrgEntity> children = new ArrayList<>();

    public void addChild(UmOrgEntity child) {
        this.children.add(child);
    }
}
