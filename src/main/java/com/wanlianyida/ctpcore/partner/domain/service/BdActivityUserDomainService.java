package com.wanlianyida.ctpcore.partner.domain.service;

import com.wanlianyida.ctpcore.partner.domain.model.bo.ActivityUserAddBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.ActivityUserPageCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.ActivityUserDetailEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.ActivityUserPageEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.BdActivityUserRepository;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class BdActivityUserDomainService {

    @Resource
    private BdActivityUserRepository activityUserRepository;



    public List<ActivityUserPageEntity> pageList(ActivityUserPageCondition condition) {
        return activityUserRepository.pageList(condition);
    }


    public ActivityUserDetailEntity userDetail(Long id) {
        return activityUserRepository.userDetail(id);
    }


    public void addUser(ActivityUserAddBO userAddBO) {
        activityUserRepository.addUser(userAddBO);
    }
}
