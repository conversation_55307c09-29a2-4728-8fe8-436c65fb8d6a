package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmCompanyBusinessLicenseCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyBusinessLicenseEntity;

import java.util.List;

/**
 * 企业营业执照信息表 Repository
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
public interface UmCompanyBusinessLicenseRepository {

	/**
	 * 列表查询
	 *
	 * @param filter
	 * @return {@link List}<{@link UmCompanyBusinessLicenseEntity}>
	 */
	List<UmCompanyBusinessLicenseEntity> queryList(UmCompanyBusinessLicenseCondition filter) ;

	/**
     * 新增
     *
     * @param record
     * @return int
     */
	int insertSelective(UmCompanyBusinessLicenseEntity record) ;

	/**
	 * 根据主键修改
	 *
	 * @param record
	 * @return int
	 */
	int updateByPrimaryKeySelective(UmCompanyBusinessLicenseEntity record) ;

	/**
	 * 逻辑删除
	 *
	 * @param id
	 * @return int
	 */
	int deleteByPrimaryKey(Long id) ;

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link UmCompanyBusinessLicenseEntity}
	 */
	UmCompanyBusinessLicenseEntity selectByPrimaryKey(Long id) ;

    boolean updateByLicenseNo(UmCompanyBusinessLicenseEntity entity);

	UmCompanyBusinessLicenseEntity queryByLicenseNo(String licenseNo);

	/**
	 * 更新法人授权状态
	 * @param companyId
	 * @param legalPersonAuthStatus
	 * @return int
	 */
	int updateLegalPersonAuthStatus(String companyId, Integer legalPersonAuthStatus);

    UmCompanyBusinessLicenseEntity queryByCompanyId(String companyId);
}
