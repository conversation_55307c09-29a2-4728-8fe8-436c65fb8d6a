package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.partner.domain.model.bo.UmShopCollectBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmShopCollectCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmShopCollectUserCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopCollectEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.UmShopCollectRepository;
import com.wanlianyida.ctpcore.partner.domain.repository.UmShopRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <p>
 * 用户收藏商品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Service
public class UmShopCollectDomainService {


    @Resource
    private UmShopCollectRepository umShopCollectRepository;

    @Resource
    private UmShopRepository umShopRepository;

    /**
     * 新增商品收藏
     *
     * @param umShopCollectEntity
     * @return {@link Boolean }
     */
    public Boolean addShopCollect(UmShopCollectEntity umShopCollectEntity) {
        return umShopCollectRepository.insertOnUpdate(umShopCollectEntity);
    }


    /**
     * 取消收藏
     *
     * @param productCollectEntity
     * @return {@link Boolean }
     */
    public Boolean cancelCollection(UmShopCollectEntity productCollectEntity){
        return umShopCollectRepository.cancelCollection(productCollectEntity);
    }

    /**
     * 查询收藏条件
     *
     * @param condition 条件
     * @return {@code Boolean }
     */
    public Boolean queryCollectCondition(UmShopCollectUserCondition condition){
        return umShopCollectRepository.queryCollectCondition(condition);
    }

    /**
     * 分页查询
     *
     * @param condition 条件
     * @return {@code List<ProductCollectEntity> }
     */
    public List<UmShopCollectBO> queryCollectListPage(UmShopCollectCondition condition) {
        List<UmShopCollectBO> umShopCollectBOS = umShopCollectRepository.queryCollectListPage(condition);
        if (ObjUtil.isEmpty(umShopCollectBOS)) {
            return umShopCollectBOS;
        }
        List<Long> shopIds = umShopCollectBOS.stream().map(UmShopCollectBO::getShopId).collect(Collectors.toList());
        List<UmShopEntity> umShopEntities = umShopRepository.batchQueryListByShopIds(shopIds);
        Map<Long, UmShopEntity> umShopEntityMap = umShopEntities.stream().collect(Collectors.toMap(UmShopEntity::getId, UmShopEntity -> UmShopEntity));
        umShopCollectBOS.forEach(umShopCollectBO -> {
            UmShopEntity umShopEntity = umShopEntityMap.get(umShopCollectBO.getShopId());
            if (ObjUtil.isNotEmpty(umShopEntity)) {
                umShopCollectBO.setShopName(umShopEntity.getShopName());
                umShopCollectBO.setShopLogoUrl(umShopEntity.getShopLogoUrl());
            }
        });
        return umShopCollectBOS;
    }

    /**
     * 查询总数
     *
     * @param condition 条件
     * @return {@code int }
     */
    public int selectCount(UmShopCollectCondition condition) {
        return umShopCollectRepository.selectCount(condition);
    }

    /**
     * 批量查询店铺是否收藏
     * @param umShopCollectUserCondition
     * @return
     */
    public Map<String, Boolean> batchQueryCollectCondition(UmShopCollectUserCondition umShopCollectUserCondition) {
        List<UmShopCollectEntity> umShopCollectEntities = umShopCollectRepository.batchQueryCollectCondition(umShopCollectUserCondition);
        if (CollUtil.isEmpty(umShopCollectEntities)) {
            // 如果 umShopCollectEntities 为空，初始化 booleanMap 为 false
            return umShopCollectUserCondition.getShopIdList().stream()
                    .collect(Collectors.toMap(shopId -> shopId, shopId -> false));
        }

        Set<Long> collectedShopIds = umShopCollectEntities.stream()
                .map(UmShopCollectEntity::getShopId)
                .collect(Collectors.toSet());

        // 根据 collectedShopIds 构建 booleanMap
        Map<String, Boolean> booleanMap = umShopCollectUserCondition.getShopIdList().stream()
                .collect(Collectors.toMap(
                        shopId -> shopId,
                        shopId -> collectedShopIds.contains(Long.parseLong(shopId))
                ));

        return booleanMap;
    }
}
