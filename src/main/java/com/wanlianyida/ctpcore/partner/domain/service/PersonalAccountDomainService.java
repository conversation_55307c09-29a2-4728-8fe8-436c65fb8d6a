package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.api.model.command.MdmOperatorCommand;
import com.wanlianyida.basemdm.api.model.command.MdmUserContactCommand;
import com.wanlianyida.basemdm.api.model.command.MdmUserInfoCommand;
import com.wanlianyida.basemdm.api.model.dto.MdmCompanyApplyDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmOperatorDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmUserContactDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmCompanyApplyQuery;
import com.wanlianyida.basemdm.api.model.query.MdmOperatorQuery;
import com.wanlianyida.basemdm.api.model.query.MdmUserContactQuery;
import com.wanlianyida.basemdm.api.model.query.MdmUserInfoQuery;
import com.wanlianyida.baseots.api.model.dto.OtsAddressDetailDTO;
import com.wanlianyida.baseots.api.model.dto.OtsIdentityCardOcrDTO;
import com.wanlianyida.baseots.api.model.dto.TwoFactorsDTO;
import com.wanlianyida.baseots.api.model.query.Certification2FactorsQuery;
import com.wanlianyida.baseots.api.model.query.DirectionOcrQuery;
import com.wanlianyida.baseots.api.model.query.OtsEnums;
import com.wanlianyida.ctpcore.partner.application.assembler.LogCompanyLoginAssembler;
import com.wanlianyida.ctpcore.partner.domain.model.bo.UserInfoBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.*;
import com.wanlianyida.ctpcore.partner.domain.model.entity.*;
import com.wanlianyida.ctpcore.partner.domain.repository.*;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.KafkaConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.ServiceNameConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.*;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.LogExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.OtsExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.*;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.*;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.AuthenticateRealNameDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.AuthenticateRealNameResultDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.PersonalInfoDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.SwitchCompanyDTO;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.constant.CommonRedisConstants;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.IdUtil;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssbaselog.api.model.command.LogOperationRecordCommand;
import com.wanlianyida.fssuserauth.api.enums.UserAuthEnums;
import com.wanlianyida.fssuserauth.api.model.command.UserSystemPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.dto.UserSystemDTO;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PersonalAccountDomainService {

    @Resource
    private MdmExchangeService mdmExchangeService;

    @Resource
    private OtsExchangeService otsExchangeService;

    @Resource
    private UmUserAuthAuditRepository umUserAuthAuditRepository;

    @Resource
    private UmEmployeeBindingRecordRepository umEmployeeBindingRecordRepository;

    @Resource
    private IdUtil idUtil;

    @Resource
    private UmCompanyMemberRepository umCompanyMemberRepository;

    @Resource
    MqEventPublisher mqEventPublisher;

    @Resource
    private RedisService redisService;

    @Resource
    private UploadExchangeService uploadExchangeService;

    @Resource
    private UmAccreditAuditRepository umAccreditAuditRepository;

    @Resource
    private UmOrgRepository umOrgRepository;

    @Resource
    private LogExchangeService logExchangeService;

    @Resource
    private UmCompanyRepository umCompanyRepository;

    @Resource
    private GetUserIdUtil getUserIdUtil;

    @Resource
    private OtsUtil otsUtil;

    @Resource
    private UmInviteCodeDomainService umInviteCodeDomainService;

    @Resource
    private UmCompanyLegalPersonAuditRepository umCompanyLegalPersonAuditRepository;

    private String generateAndValidateUserCode() {
        String loginName;
        MdmUserInfoDTO dto = null;
        do {
            loginName = CommonUtil.generateUserId("U", 8);
            dto = mdmExchangeService.queryUserInfoByLoginName(loginName);
        } while (ObjectUtil.isNotEmpty(dto));
        return loginName;
    }


    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> register(CmdUserInfoCommand command) {
        Map<String, String> resultMap = new HashMap<>();
        // 1、生成loginName
        String loginName = generateAndValidateUserCode();

        // 2、保存用户信息
        MdmUserInfoCommand userInfoCommand = new MdmUserInfoCommand();
        userInfoCommand.setLoginName(loginName);
        userInfoCommand.setMobile(command.getPhone());
        userInfoCommand.setEnableFlag("1");
        userInfoCommand.setId(idUtil.generateId(ServiceNameConstants.MDM_SERVICE));
        Long userId = mdmExchangeService.CmdUserInfoAdd(userInfoCommand);

        // 3、保存用户联系方式
        MdmUserContactCommand userContactCommand = new MdmUserContactCommand();
        userContactCommand.setUserId(userId);
        userContactCommand.setContactInfo(command.getPhone());
        userContactCommand.setCategory(ContactTypeEnum.PHONE.getCode());
        userContactCommand.setEnableFlag("1");
        ResultMode<?> userContactAdd = mdmExchangeService.CmdUserContactAdd(userContactCommand);
        if (!userContactAdd.isSucceed()) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_SAVE_USER_CONTACT_FAILED);
        }

        // 4、发送mq 保存用户密码及密码强度
        try {
            UserInfoBO event = new UserInfoBO();
            event.setId(userId);
            event.setLoginName(loginName);
            event.setMobile(command.getPhone());
            event.setPassword(command.getPassword());
            event.setPwdStrength(command.getPasswordStrength());
            // 生成指定长度的uuid
            String simpledUUID = cn.hutool.core.util.IdUtil.simpleUUID();
            // 截取
            String singleKey = simpledUUID.substring(0, 15);
            event.setSingleKey(singleKey);
            event.setScene(UserAuthEnums.PermissionChangeSceneEnum.REGISTER.getCode());
            MqEventMessage.EventMessage<UserInfoBO> operatorBOEventMessage =
                    MqEventMessage.buildEventMessage(KafkaConstants.CTP_USER_STATUS_CHANGE_NOTICE, event);
            mqEventPublisher.publishNormalMessage(KafkaConstants.CTP_USER_STATUS_CHANGE_NOTICE, operatorBOEventMessage);
            log.info("发送mq消息成功:{},消息场景：{}，", operatorBOEventMessage, UserAuthEnums.PermissionChangeSceneEnum.REGISTER.getDesc());
        } catch (Exception e) {
            log.error("发送mq消息失败", e);
        }
        umInviteCodeDomainService.registerAddUserInviteCode(command.getInviteCode(),command.getChannelType(),userId);

        // 处理物流用户在商贸注册的场景 暂不处理
        //handleLogisticsUserRegistration(command, userId);

        resultMap.put("loginName", loginName);
        resultMap.put("userId", String.valueOf(userId));
        return resultMap;
    }

    /**
     * 处理物流用户在商贸注册的场景
     *
     * @param command
     */
    public void handleLogisticsUserRegistration(CmdUserInfoCommand command, Long userId) {
        if (ObjectUtil.isEmpty(command) || ObjectUtil.isEmpty(userId)) {
            return;
        }

        String userBaseId = command.getUserBaseId();
        if (StrUtil.isEmpty(userBaseId)) {
            return;
        }

        try {
            // 物流用户在商贸注册，要携带token请求用户注册接口
            TokenInfo tokenInfo = JwtUtil.getTokenInfo();
            String userBaseIdForToken = tokenInfo.getUserBaseId();
            if (StrUtil.isEmpty(userBaseId)) {
                log.info("物流用户在商贸注册，token信息为空");
                return;
            }

            if (!StrUtil.equals(userBaseId, userBaseIdForToken)) {
                log.info("物流用户在商贸注册，token信息不匹配");
                return;
            }

            // 1、主数据operator表新增数据，关联userBaseId
            MdmOperatorCommand operatorCommand = new MdmOperatorCommand();
            operatorCommand.setUserId(String.valueOf(userId));
            operatorCommand.setOperatorCode(userBaseId);
            operatorCommand.setOperatorType("10");
            operatorCommand.setPlatformCode("2");
            operatorCommand.setOperatorAccount(tokenInfo.getLoginName());
            operatorCommand.setOperatorName(tokenInfo.getUsername());
            mdmExchangeService.mdmOperatorInsertForLogistics(operatorCommand);
            log.info("主数据operator表新增物流操作员成功：{}", operatorCommand);

            // 2、物流 platform_user_system_rel 表新增数据
            ResultMode<List<UserSystemDTO>> listResultMode = mdmExchangeService.queryRelByUserBaseId(userBaseId);
            if (listResultMode.isSucceed()) {
                List<UserSystemDTO> model = listResultMode.getModel();
                if (CollUtil.isNotEmpty(model)) {
                    for (UserSystemDTO userSystemDTO : model) {
                        UserSystemPlatformCommand userSystemPlatformCommand = BeanUtil.copyProperties(userSystemDTO, UserSystemPlatformCommand.class);
                        Long generateId = idUtil.generateId(ServiceNameConstants.PLATFORM_SERVICE);
                        userSystemPlatformCommand.setId(generateId);
                        userSystemPlatformCommand.setUserBaseId(String.valueOf(userId));
                        userSystemPlatformCommand.setTenantId(String.valueOf(userId));
                        userSystemPlatformCommand.setTenantCategory("10");
                        userSystemPlatformCommand.setDefaultFlag("20");
                        ResultMode<String> stringResultMode = mdmExchangeService.insertPlatformUserSystemRel(userSystemPlatformCommand);
                        if (!stringResultMode.isSucceed()) {
                            throw new CtpCorePartnerException(stringResultMode.getCode(), stringResultMode.getMessage());
                        }
                    }
                }
            }

            // 3、新增商贸用户关联
            UserSystemPlatformCommand userSystemPlatformCommand = new UserSystemPlatformCommand();
            Long generateId = idUtil.generateId(ServiceNameConstants.PLATFORM_SERVICE);
            userSystemPlatformCommand.setId(generateId);
            userSystemPlatformCommand.setUserBaseId(userBaseId);
            userSystemPlatformCommand.setSystemCode("22");
            userSystemPlatformCommand.setDefaultFlag("20");
            ;
            userSystemPlatformCommand.setCreatorId(String.valueOf(userId));
            userSystemPlatformCommand.setUpdaterId(String.valueOf(userId));
            ResultMode<String> stringResultMode = mdmExchangeService.insertPlatformUserSystemRel(userSystemPlatformCommand);
        } catch (Exception e) {
            log.error("处理物流用户在商贸注册的场景失败", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserPhone(CmdUserInfoCommand command) {
        String userId = getUserIdUtil.getUserId();

        String newPhone = command.getNewPhone();

        // 1、更新用户信息
        MdmUserInfoCommand userInfoCommand = new MdmUserInfoCommand();
        userInfoCommand.setId(Long.valueOf(userId));
        userInfoCommand.setMobile(newPhone);
        userInfoCommand.setUpdaterId(userId);
        ResultMode<?> updateUserInfo = mdmExchangeService.updateUserInfo(userInfoCommand);
        if (!updateUserInfo.isSucceed()) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_UPDATE_USER_INFO_FAILED);
        }

        // 2、更新用户联系方式
        MdmUserContactCommand userContactCommand = new MdmUserContactCommand();
        userContactCommand.setUserId(Long.valueOf(userId));
        userContactCommand.setContactInfo(newPhone);
        userContactCommand.setCategory(ContactTypeEnum.PHONE.getCode());
        userContactCommand.setUpdaterId(userId);
        ResultMode<?> updateUserContact = mdmExchangeService.updateUserContact(userContactCommand);
        if (!updateUserContact.isSucceed()) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_UPDATE_USER_CONTACT_FAILED);
        }

        // 3、更新cmd 操作员表
        mdmExchangeService.updateOperatorPhone(userId, newPhone);

        // 4、更新ctp员工表
        UmCompanyMemberEntity umCompanyMemberEntity = new UmCompanyMemberEntity();
        umCompanyMemberEntity.setUserId(userId);
        umCompanyMemberEntity.setUserMobile(newPhone);
        umCompanyMemberRepository.updateByUserId(umCompanyMemberEntity);

        // 根据userId查询已绑定的公司信息
        QueryBoundInfoCondition condition = new QueryBoundInfoCondition();
        condition.setUserId(userId);
        List<BoundCompanyEntity> boundCompanyEntities = umCompanyMemberRepository.queryBoundCompanyInfo(condition);
        if (CollUtil.isNotEmpty(boundCompanyEntities)) {
            List<BoundCompanyEntity> companyEntityList = boundCompanyEntities.stream().filter(obj -> StrUtil.equals(obj.getLevelType(), "1")).collect(Collectors.toList());
            // 获取公司id
            List<String> companyIdList = companyEntityList.stream().map(BoundCompanyEntity::getCompanyId).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(companyIdList)) {
                // 5、批量更新ctp企业信息表、企业资质表联系人手机号
                umCompanyRepository.batchUpdatePhoneByCompanyIds(companyIdList, newPhone);
            }
        }
    }

    public void setUserEmailAddress(CmdUserInfoCommand command) {
        String userId = getUserIdUtil.getUserId();
        // 1、邮箱去重校验
        MdmUserContactQuery query = new MdmUserContactQuery();
        query.setContactInfo(command.getEmailAddress());
        query.setCategory(ContactTypeEnum.EMAIL.getCode());
        query.setEnableFlag("1");
        List<MdmUserContactDTO> mdmUserContactDTOS = mdmExchangeService.queryUserContactList(query);
        if (CollUtil.isNotEmpty(mdmUserContactDTOS)) {
            MdmUserContactDTO mdmUserContactDTO = IterUtil.getFirst(mdmUserContactDTOS);
            if (ObjectUtil.isNotEmpty(mdmUserContactDTO)) {
                String id = String.valueOf(mdmUserContactDTO.getUserId());
                if (!StrUtil.equals(userId, id)) {
                    throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_EMAIL_ALREADY_EXISTS);
                }
            }
        }

        MdmUserContactQuery contactQuery = new MdmUserContactQuery();
        contactQuery.setCategory(ContactTypeEnum.EMAIL.getCode());
        contactQuery.setEnableFlag("1");
        contactQuery.setUserId(Long.valueOf(userId));
        List<MdmUserContactDTO> model = mdmExchangeService.queryUserContactList(contactQuery);
        if (CollUtil.isEmpty(model)) {
            // 新增
            MdmUserContactCommand userContactCommand = new MdmUserContactCommand();
            userContactCommand.setId(idUtil.generateId(ServiceNameConstants.CORE_PARTNER));
            userContactCommand.setUserId(Long.valueOf(userId));
            userContactCommand.setContactInfo(command.getEmailAddress());
            userContactCommand.setCategory(ContactTypeEnum.EMAIL.getCode());
            userContactCommand.setEnableFlag("1");
            userContactCommand.setCreatorId(userId);
            userContactCommand.setUpdaterId(userId);
            ResultMode<?> userContactAdd = mdmExchangeService.CmdUserContactAdd(userContactCommand);
            if (!userContactAdd.isSucceed()) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_UPDATE_USER_CONTACT_FAILED);
            }
        } else {
            // 2、更新用户联系方式
            MdmUserContactCommand userContactCommand = new MdmUserContactCommand();
            userContactCommand.setUserId(Long.valueOf(Long.valueOf(userId)));
            userContactCommand.setContactInfo(command.getEmailAddress());
            userContactCommand.setCategory(ContactTypeEnum.EMAIL.getCode());
            userContactCommand.setUpdaterId(JwtUtil.getTokenInfo().getUserBaseId());
            ResultMode<?> updateUserContact = mdmExchangeService.updateUserContact(userContactCommand);
            if (!updateUserContact.isSucceed()) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_UPDATE_USER_CONTACT_FAILED);
            }
        }

    }

    public AuthenticateRealNameResultDTO authenticateRealName(AuthenticateRealNameCommand command) {
        String userId = getUserIdUtil.getUserId();
        // 1. 验证用户登录状态
        validateUserLoginStatus();

        // 2. 检查用户是否已有待审核的认证请求
        checkPendingAuditRequests(userId);

        // 3. 创建基础审核实体
        UmUserAuthAuditEntity entity = createBaseAuditEntity(userId);

        // 4. 身份证国徽页识别
        if (!processFrontIdCard(command, entity)) {
            return createFailureResult(command);
        }

        // 5. 身份证人像页识别
        if (!processBackIdCard(command, entity)) {
            return createFailureResult(command);
        }

        // 6. 验证身份证信息
        validateIdCardInfo(entity, userId);

        // 7. 二要素认证
        if (!performTwoFactorsCheck(entity)) {
            return createFailureResult(command);
        }

        // 8. 创建审核记录
        createUserAuthAudit(entity, isAutoAudit(entity));

        // 9. 记录操作日志
        logOperation(entity, userId);

        return createSuccessResult(command);
    }

    /**
     * 是否自动审核
     * @param entity
     * @return boolean
     */
    private boolean isAutoAudit(UmUserAuthAuditEntity entity) {
        return ObjectUtil.isNotEmpty(entity.getStartDate())
                && ObjectUtil.isNotEmpty(entity.getUntilDate())
                && ObjectUtil.isNotEmpty(entity.getIssueAgency());
    }
    /**
     * 记录操作日志
     *
     * @param entity
     * @param userId
     */
    private void logOperation(UmUserAuthAuditEntity entity, String userId) {
        String name = entity.getUserName();
        String id = JwtUtil.getTokenInfo().getUserId();
        // 查询该用户是首次认证还是二次认证
        String operateType = "新增实名认证";
        // 查询审核状态 有审核驳回状态为二次认证
        UmUserAuthAuditEntity checkAuditEntity = umUserAuthAuditRepository.queryByUserId(userId, CommonAuditStatusEnum.REJECT.getCode());
        if (ObjectUtil.isNotEmpty(checkAuditEntity)) {
            operateType = "重新认证";
        }
        if (ObjectUtil.isNotEmpty(id)) {
            logExchangeService.add(createUserAuthAuditLog(id, name, operateType, "新增实名认证"));
        }
    }

    /**
     * 验证用户登录状态
     */
    private void validateUserLoginStatus() {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (StrUtil.isEmpty(tokenInfo.getLoginName())) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }
    }

    /**
     * 检查用户是否已有待审核的认证请求
     *
     * @param userId
     */
    private void checkPendingAuditRequests(String userId) {
        // 查询审核状态 审核中不可发起
        UmUserAuthAuditEntity auditEntity = umUserAuthAuditRepository.queryByUserId(userId, CommonAuditStatusEnum.WAITING.getCode());
        if (ObjectUtil.isNotEmpty(auditEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_AUTH_AUDIT_WAITING);
        }
    }

    /**
     * 创建基础审核实体
     *
     * @param userId
     * @return
     */
    private UmUserAuthAuditEntity createBaseAuditEntity(String userId) {
        UmUserAuthAuditEntity entity = new UmUserAuthAuditEntity();
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        entity.setLoginName(tokenInfo.getLoginName());
        entity.setUserId(userId);

        MdmUserInfoDTO userInfo = getUserInfo(userId);
        if (ObjectUtil.isNotEmpty(userInfo)) {
            entity.setUserMobile(userInfo.getMobile());
        }

        return entity;
    }

    /**
     * 身份证国徽页识别
     *
     * @param command
     * @param entity
     * @return boolean
     */
    private boolean processFrontIdCard(AuthenticateRealNameCommand command, UmUserAuthAuditEntity entity) {
        DirectionOcrQuery directionOcrQuery = new DirectionOcrQuery();
        directionOcrQuery.setImageUrl(command.getFrontUrl());
        directionOcrQuery.setDirection(OtsEnums.OcrDirection.FRONT);

        log.info("实名认证身份证国徽页ocr识别,入参:{}", directionOcrQuery);
        ResultMode<OtsIdentityCardOcrDTO> frontResultModel = otsExchangeService.identityCardOcr(directionOcrQuery);

        if (!frontResultModel.isSucceed() || ObjectUtil.isEmpty(frontResultModel.getModel())) {
            return false;
        }
        log.info("身份证正面(国徽页)调用结果：{},识别结果：{}", frontResultModel.getModel(), frontResultModel);

        OtsIdentityCardOcrDTO frontModel = frontResultModel.getModel();
        entity.setFrontUrl(StrUtil.subBefore(command.getFrontUrl(), "?", true));
        // 发证机关
        entity.setIssueAgency(frontModel.getAuthority());
        // 身份证有效期-开始日期
        entity.setStartDate(CommonUtil.safeParseForDate(frontModel.getIssueDate()));
        // 身份证有效期-截止日期
        entity.setUntilDate(CommonUtil.safeParseForDate(frontModel.getTimeLimit()));
        return true;
    }

    /**
     * 身份证人像页识别
     *
     * @param command
     * @param entity
     * @return boolean
     */
    private boolean processBackIdCard(AuthenticateRealNameCommand command, UmUserAuthAuditEntity entity) {
        DirectionOcrQuery directionOcrQuery = new DirectionOcrQuery();
        directionOcrQuery.setImageUrl(command.getBehindUrl());
        directionOcrQuery.setDirection(OtsEnums.OcrDirection.BACK);

        log.info("身份证反面（人像页）ocr识别,入参:{}", directionOcrQuery);
        ResultMode<OtsIdentityCardOcrDTO> backResultModel = otsExchangeService.identityCardOcr(directionOcrQuery);

        if (!backResultModel.isSucceed() || ObjectUtil.isEmpty(backResultModel.getModel())) {
            return false;
        }
        log.info("身份证反面（人像页）调用结果：{},识别结果：{}", backResultModel, backResultModel.getModel());
        OtsIdentityCardOcrDTO backModel = backResultModel.getModel();
        // 截取地址，第一个问号后边全部截掉
        entity.setBehindUrl(StrUtil.subBefore(command.getBehindUrl(), "?", false));
        entity.setUserName(backModel.getName());
        entity.setIdCardNo(backModel.getIdCard());
        // 处理地址、性别、民族、出生日期等信息
        processBackIdCardDetails(backModel, entity);

        return true;
    }

    /**
     * 处理身份证反面识别的详细信息
     *
     * @param backModel OCR识别结果
     * @param entity    用户认证审核实体
     */
    private void processBackIdCardDetails(OtsIdentityCardOcrDTO backModel, UmUserAuthAuditEntity entity) {
        // 处理地址信息
        String address = backModel.getAddress();
        if (StrUtil.isNotEmpty(address)) {
            entity.setAddrDetail(address);
            // 地址解析
            getAddressDetail(address, entity);
        }

        // 处理性别信息
        String sex = backModel.getSex();
        if (StrUtil.isNotEmpty(sex)) {
            entity.setGender("男".equals(sex) ? 1 : 2);
        }

        // 民族
        entity.setNation(backModel.getNation());
        // 出生日期
        entity.setBirthday(CommonUtil.safeParseForDate(backModel.getBirthday()));
    }

    /**
     * 二要素认证
     *
     * @param entity
     * @return boolean
     */
    private boolean performTwoFactorsCheck(UmUserAuthAuditEntity entity) {
        if (StrUtil.isEmpty(entity.getUserName()) || StrUtil.isEmpty(entity.getIdCardNo())) {
            log.warn("二要素识别，用户名或身份证号为空，用户名：{}，身份证号：{}",
                    entity.getUserName(), entity.getIdCardNo());
            return false;
        }

        Certification2FactorsQuery query = new Certification2FactorsQuery();
        query.setName(entity.getUserName());
        query.setIdcard(entity.getIdCardNo());

        ResultMode<TwoFactorsDTO> result = otsExchangeService.twoFactorsCheck(query);
        TwoFactorsDTO model = result.getModel();
        log.info("二要素识别调用结果：{}，识别结果：{}", result, model);
        return result.isSucceed() && ObjectUtil.isNotEmpty(model) && "1".equals(model.getResult());
    }

    private AuthenticateRealNameResultDTO createFailureResult(AuthenticateRealNameCommand command) {
        AuthenticateRealNameResultDTO dto = new AuthenticateRealNameResultDTO();
        dto.setFrontUrl(command.getFrontUrl());
        dto.setBehindUrl(command.getBehindUrl());
        dto.setRecognitionResult(false);
        return dto;
    }

    private AuthenticateRealNameResultDTO createSuccessResult(AuthenticateRealNameCommand command) {
        AuthenticateRealNameResultDTO dto = createFailureResult(command);
        dto.setRecognitionResult(true);
        return dto;
    }

    /**
     * 验证身份证信息
     *
     * @param entity
     * @param userId
     */
    private void validateIdCardInfo(UmUserAuthAuditEntity entity, String userId) {
        String idCardNo = entity.getIdCardNo();
        // 根据身份证号查询是否存在且未过期
        validateIdentityInfoExists(idCardNo, userId);

        // 校验身份证信息是否与历史信息一致
        validateIdCardInfoConsistency(idCardNo, userId);


        // 校验是否过期
        Date untilDate = entity.getUntilDate();
        if (ObjectUtil.isNotEmpty(untilDate)) {
            if (DateUtil.compare(untilDate, new Date()) < 0) {
                throw new CtpCorePartnerException("该身份证信息已失效");
            }
        }
    }

    /**
     * 地址解析
     */
    public void getAddressDetail(String address, UmUserAuthAuditEntity umUserAuthAuditEntity) {
        OtsAddressDetailDTO otsAddressDetailDTO = otsUtil.addressParse(address);
        if (ObjectUtil.isEmpty(otsAddressDetailDTO)) {
            return;
        }
        // 省
        umUserAuthAuditEntity.setProvinceCode(otsAddressDetailDTO.getProvinceCode());
        umUserAuthAuditEntity.setProvinceName(otsAddressDetailDTO.getProvince());
        // 市
        umUserAuthAuditEntity.setCityCode(otsAddressDetailDTO.getCityCode());
        umUserAuthAuditEntity.setCityName(otsAddressDetailDTO.getCity());
        // 区
        umUserAuthAuditEntity.setCountyCode(otsAddressDetailDTO.getCountyCode());
        umUserAuthAuditEntity.setCountyName(otsAddressDetailDTO.getCounty());
    }

    public void updateUserBasicTwoFactors(AuthenticateRealNameCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        String userId = getUserIdUtil.getUserId();
        // 查询审核状态 审核中不可发起
        UmUserAuthAuditEntity auditEntity = umUserAuthAuditRepository.queryByUserId(userId, CommonAuditStatusEnum.WAITING.getCode());
        if (ObjectUtil.isNotEmpty(auditEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_AUTH_AUDIT_WAITING);
        }
        // 查询审核状态 有驳回状态日志为重新认证
        UmUserAuthAuditEntity checkAuditEntity = umUserAuthAuditRepository.queryByUserId(userId, CommonAuditStatusEnum.REJECT.getCode());

        // 根据身份证号查询是否存在且未过期
        validateIdentityInfoExists(command.getIdCardNo(), userId);

        // 校验身份证信息是否与历史信息一致
        validateIdCardInfoConsistency(command.getIdCardNo(), userId);

        UmUserAuthAuditEntity entity = BeanUtil.copyProperties(command, UmUserAuthAuditEntity.class);

        entity.setUserId(userId);
        entity.setCreatorId(userId);
        entity.setUpdaterId(userId);
        entity.setLoginName(tokenInfo.getLoginName());
        entity.setFrontUrl(StrUtil.subBefore(command.getFrontUrl(), "?", true));
        entity.setBehindUrl(StrUtil.subBefore(command.getBehindUrl(), "?", true));

        // 查询用户信息
        MdmUserInfoDTO userInfo = getUserInfo(userId);
        if (ObjectUtil.isNotEmpty(userInfo)) {
            entity.setUserMobile(userInfo.getMobile());
        }

        // 创建用户实名认证审核记录
        createUserAuthAudit(entity, false);

        // 4、记录操作日志
        String operateType = "二要素认证";
        if (ObjectUtil.isNotEmpty(checkAuditEntity)) {
            operateType = "重新认证";
        }


        if (ObjectUtil.isNotEmpty(userId)) {
            logExchangeService.add(createUserAuthAuditLog(userId, entity.getUserName(), operateType, "二要素认证"));
        }
    }

    /**
     * 根据用户id查询用户信息
     */
    public MdmUserInfoDTO getUserInfo(String userId) {
        MdmUserInfoDTO userInfoDTO = mdmExchangeService.queryUserInfo(Long.valueOf(userId));
        return userInfoDTO;
    }


    /**
     * 校验身份证信息是否存在
     *
     * @param idCardNo
     */
    public void validateIdentityInfoExists(String idCardNo, String userId) {
        if (StrUtil.isEmpty(idCardNo) || StrUtil.isEmpty(userId)) {
            return;
        }
        MdmUserInfoDTO userInfoDTO = null;

        // 根据身份证号查询是否注册过
        if (StrUtil.isNotEmpty(idCardNo)) {
            MdmUserInfoQuery idCardNoQuery = new MdmUserInfoQuery();
            idCardNoQuery.setIdCardNo(idCardNo);
            ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.userInfoQueryList(idCardNoQuery);
            if (!resultMode.isSucceed()) {
                throw new CtpCorePartnerException(resultMode.getCode(), resultMode.getMessage());
            }

            List<MdmUserInfoDTO> mdmUserInfoDTOS = resultMode.getModel();
            if (CollUtil.isNotEmpty(mdmUserInfoDTOS)) {
                userInfoDTO = IterUtil.getFirst(mdmUserInfoDTOS);
            }
        }
        if (ObjectUtil.isNotEmpty(userInfoDTO)) {
            // 获取用户id
            Long id = userInfoDTO.getId();
            if (!StrUtil.equals(userId, id.toString())) {
                throw new CtpCorePartnerException("该身份证已实名认证，无法重复认证");
            }
        }
    }

    /**
     * 校验身份证信息是否一致
     *
     * @param idCardNo
     * @param userId
     */ //
    public void validateIdCardInfoConsistency(String idCardNo, String userId) {
        if (StrUtil.isEmpty(idCardNo) || StrUtil.isEmpty(userId)) {
            return;
        }
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.queryUserInfo(Long.valueOf(userId));
        if (ObjectUtil.isNotEmpty(mdmUserInfoDTO)) {
            String idCardNoOld = mdmUserInfoDTO.getIdCardNo();
            if (StrUtil.isNotEmpty(idCardNoOld) && !StrUtil.equals(idCardNo, idCardNoOld)) {
                throw new CtpCorePartnerException("当前认证信息与历史认证信息不一致，无法提交");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void applyToBeAdministrator(AdministratorCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        // 校验营业执照/法人是否审核通过、是否存在，是否存在待审核的记录
        validateCompanyAptitudeAndLegalPerson(command.getLicenseNo(),CtpCorePartnerExceptionEnum.COMPANY_NOT_PASS_APPLY);

        // 校验当前公司是否已存在管理员
        UmCompanyMemberEntity umCompanyMemberEntity = umCompanyMemberRepository.queryManagerByCompanyId(command.getCompanyId());
        if (ObjectUtil.isNotEmpty(umCompanyMemberEntity)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.COMPANY_HAS_MANAGER);
        }

        String userId = getUserIdUtil.getUserId();

        String companyId = command.getCompanyId();

        UmAccreditAuditEntity umAccreditAuditEntity = new UmAccreditAuditEntity();
        // 1、根据手机号查询并设置用户信息
        setUserInfo(command.getPhone(), umAccreditAuditEntity, false);

        // 2、查询当前企业是否存在审核中或审核通过的记录
        checkIfCompanyHasPendingAudit(companyId,CtpCorePartnerExceptionEnum.AUDIT_ACCREDIT_EXISTS3);

        // 3、删除历史审核记录
        updateHistoryAccreditAudit(companyId);

        // 4、新增授权委托书审核记录
        umAccreditAuditEntity.setCompanyId(companyId);
        umAccreditAuditEntity.setCompanyName(command.getCompanyName());
        umAccreditAuditEntity.setUserId(userId);
        umAccreditAuditEntity.setUserName(tokenInfo.getUsername());
        umAccreditAuditEntity.setSourceType(20);
        umAccreditAuditEntity.setBizType(AcreditBizTypeEnum.ADD.getCode());
        umAccreditAuditEntity.setLatestDataFlag(1);
        // 截取文件路径
        umAccreditAuditEntity.setFileUrl(StrUtil.subBefore(command.getFileUrl(), "?", true));
        umAccreditAuditEntity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        umAccreditAuditEntity.setSocialCreditCode(command.getLicenseNo());
        umAccreditAuditEntity.setCreatorId(userId);
        umAccreditAuditEntity.setUpdaterId(userId);
        umAccreditAuditRepository.insert(umAccreditAuditEntity);


        // 5、新增企业员工绑定历史
        addEmployeeBindingHistory(tokenInfo, command, 22);

        // 根据企业id查询企业信息
        String socialCreditCode = "";
        UmCompanyEntity umCompanyEntity = umCompanyRepository.queryById(companyId);
        if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
            socialCreditCode = umCompanyEntity.getSocialCreditCode();
        }

        logExchangeService.add(createCompanyManagerLog(socialCreditCode, 20, "申请成为管理员"));


    }

    @Transactional(rollbackFor = Exception.class)
    public void handOverAdministrator(AdministratorCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        String userId = getUserIdUtil.getUserId();

        String companyId = command.getCompanyId();
        if (StrUtil.isEmpty(companyId)) {
            companyId = tokenInfo.getCompanyId();
            command.setCompanyId(companyId);
        }
        if (StrUtil.isEmpty(companyId)){
            log.info("管理员过户,用户token中未查询到companyId信息：{}", tokenInfo);
            throw new CtpCorePartnerException("用户token异常,请联系管理员");
        }
        String licenseNo = command.getLicenseNo();
        if (StrUtil.isEmpty(licenseNo)){
            UmCompanyEntity company = umCompanyRepository.queryById(command.getCompanyId());
            if (ObjectUtil.isEmpty(company)){
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_INFO_NOT_FOUND);
            }
            licenseNo = company.getSocialCreditCode();
        }

        // 校验营业执照/法人是否审核通过、是否存在，是否存在待审核的记录
        validateCompanyAptitudeAndLegalPerson(licenseNo,CtpCorePartnerExceptionEnum.COMPANY_NOT_PASS_TRANSFER);

        UmAccreditAuditEntity umAccreditAuditEntity = new UmAccreditAuditEntity();

        // 1、根据手机号查询并设置用户信息
        setUserInfo(command.getNewPhone(), umAccreditAuditEntity, true);

        // 根据企业id查询企业信息
        String socialCreditCode = null;
        UmCompanyEntity umCompanyEntity = umCompanyRepository.queryById(companyId);
        if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
            socialCreditCode = umCompanyEntity.getSocialCreditCode();
        }
        // 2、查询当前企业是否存在审核中或审核通过的记录
        checkIfCompanyHasPendingAudit(companyId,CtpCorePartnerExceptionEnum.AUDIT_ACCREDIT_EXISTS2);

        // 3、删除历史审核记录
        updateHistoryAccreditAudit(companyId);

        // 4、新增授权委托书审核记录
        umAccreditAuditEntity.setCompanyId(companyId);
        umAccreditAuditEntity.setSocialCreditCode(socialCreditCode);
        umAccreditAuditEntity.setCompanyName(command.getCompanyName());
        umAccreditAuditEntity.setUserId(userId);
        umAccreditAuditEntity.setUserName(tokenInfo.getUsername());
        umAccreditAuditEntity.setSourceType(30);
        umAccreditAuditEntity.setLatestDataFlag(1);
        umAccreditAuditEntity.setBizType(AcreditBizTypeEnum.ADD.getCode());
        // 截取文件路径
        umAccreditAuditEntity.setFileUrl(StrUtil.subBefore(command.getFileUrl(), "?", true));
        umAccreditAuditEntity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        umAccreditAuditEntity.setCreatorId(userId);
        umAccreditAuditEntity.setUpdaterId(userId);


        umAccreditAuditRepository.insert(umAccreditAuditEntity);

        // 5、新增企业员工绑定历史
        addEmployeeBindingHistory(tokenInfo, command, 21);

        if (ObjectUtil.isNotEmpty(socialCreditCode)) {
            logExchangeService.add(createCompanyManagerLog(socialCreditCode, 30, "过户给【账号：" + umAccreditAuditEntity.getTransferAccount() + " 姓名：" + umAccreditAuditEntity.getTransferUserName() + "】"));
        }

    }

    /**
     * 校验企业资质审核状态和法人信息审核状态是否通过
     * @param licenseNo 营业执照编号
     */
    private void validateCompanyAptitudeAndLegalPerson(String licenseNo,CtpCorePartnerExceptionEnum exceptionEnum) {
        if (StrUtil.isEmpty(licenseNo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.LICENSE_NO_NOT_EMPTY);
        }
        // 查询企业资质审核状态
        MdmCompanyApplyQuery mdmCompanyApplyQuery = new MdmCompanyApplyQuery();
        mdmCompanyApplyQuery.setLicenseNo(licenseNo);
        ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(mdmCompanyApplyQuery);

        if (!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            throw new CtpCorePartnerException("未查询到营业执照审核信息");
        }

        MdmCompanyApplyDTO mdmCompanyApply = IterUtil.getFirst(resultMode.getModel());
        String aptitudeStatus = ObjectUtil.isNotEmpty(mdmCompanyApply) ?
                mdmCompanyApply.getAptitudeStatus() : null;

        // 查询法人信息审核状态
        UmCompanyLegalPersonAuditEntity personAuditEntity =
                umCompanyLegalPersonAuditRepository.queryBySocialCreditCode(licenseNo);

        if (personAuditEntity == null) {
            throw new CtpCorePartnerException(
                    CtpCorePartnerExceptionEnum.COMPANY_LEGAL_PERSON_NOT_EXIT);
        }

        Integer auditStatus = personAuditEntity.getAuditStatus();

        // 校验审核状态是否通过
        if (auditStatus.equals(CommonAuditStatusEnum.WAITING.getCode()) ||
                StrUtil.equals(aptitudeStatus, CompanyAuditStatusEnum.WAITING.getCode())) {
            throw new CtpCorePartnerException(exceptionEnum);
        }
    }
    /**
     * 更新企业授权委托书历史审核记录为已删除
     *
     * @param companyId
     */
    private void updateHistoryAccreditAudit(String companyId) {
        if (StrUtil.isEmpty(companyId)) {
            return;
        }
        UmAccreditAuditEntity entity = new UmAccreditAuditEntity();
        entity.setDelFlag(DeletedFlagEnum.DELETED.getCode());
        entity.setLatestDataFlag(0);
        entity.setCompanyId(companyId);
        Integer count = umAccreditAuditRepository.update(entity);
        log.info("更新历史授权审核记录成功，更新条数：{}", count);
    }

    /**
     * 新增员工绑定记录
     *
     * @param tokenInfo
     * @param command
     */
    public void addEmployeeBindingHistory(TokenInfo tokenInfo, AdministratorCommand command, Integer bindingSource) {
        UmEmployeeBindingRecordEntity umEmployeeBindingRecordEntity = new UmEmployeeBindingRecordEntity();
        umEmployeeBindingRecordEntity.setUserId(tokenInfo.getUserId());
        umEmployeeBindingRecordEntity.setCompanyId(command.getCompanyId());
        umEmployeeBindingRecordEntity.setCompanyName(command.getCompanyName());
        umEmployeeBindingRecordEntity.setUserMobile(command.getPhone());
        umEmployeeBindingRecordEntity.setEmployeeType(10);
        umEmployeeBindingRecordEntity.setBindingSource(bindingSource);
        umEmployeeBindingRecordEntity.setBindingStatus(10);
        umEmployeeBindingRecordEntity.setApplyTime(DateUtil.date());
        umEmployeeBindingRecordEntity.setCreatorId(tokenInfo.getUserId());
        umEmployeeBindingRecordEntity.setUpdaterId(tokenInfo.getUserId());
        boolean result = umEmployeeBindingRecordRepository.insert(umEmployeeBindingRecordEntity);
        if (!result) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_ADD_BUSINESS_AUDIT_FAILED);
        }
    }

    /**
     * 查询当前企业是否存在待审核的授权委托书记录
     *
     * @param companyId
     */
    public void checkIfCompanyHasPendingAudit(String companyId,CtpCorePartnerExceptionEnum exceptionEnum) {
        UmAccreditAuditCondition umAccreditAuditCondition = new UmAccreditAuditCondition();
        umAccreditAuditCondition.setCompanyId(companyId);
        umAccreditAuditCondition.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(umAccreditAuditCondition);
        if (CollUtil.isNotEmpty(umAccreditAuditEntities)) {
            throw new CtpCorePartnerException(exceptionEnum);
        }
    }

    /**
     * 查询当前企业是否存在审核驳回的授权委托书记录
     *
     * @param licenseNo
     */
    public List<UmAccreditAuditEntity> checkIfCompanyHasRejectAudit(String licenseNo, Integer sourceType) {
        UmAccreditAuditCondition umAccreditAuditCondition = new UmAccreditAuditCondition();
        umAccreditAuditCondition.setSocialCreditCode(licenseNo);
        umAccreditAuditCondition.setSourceType(sourceType);
        umAccreditAuditCondition.setAuditStatus(CommonAuditStatusEnum.REJECT.getCode());
        List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(umAccreditAuditCondition);
        return umAccreditAuditEntities;
    }


    private void setUserInfo(String phone, UmAccreditAuditEntity umAccreditAuditEntity, boolean isCheck) {
        // 1、根据手机号查询用户信息
        MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
        mdmUserInfoQuery.setMobile(phone);
        ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.userInfoQueryList(mdmUserInfoQuery);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(resultMode.getCode(), resultMode.getMessage());
        }
        List<MdmUserInfoDTO> resultModeModel = resultMode.getModel();
        if (CollUtil.isNotEmpty(resultModeModel)) {
            MdmUserInfoDTO userInfoDTO = IterUtil.getFirst(resultModeModel);
            if (isCheck) {
                // 不能过户给自己
                TokenInfo tokenInfo = JwtUtil.getTokenInfo();
                if (ObjectUtil.isNotEmpty(tokenInfo) && StrUtil.equals(tokenInfo.getUserId(), String.valueOf(userInfoDTO.getId()))) {
                    throw new CtpCorePartnerException("不能过户给自己");
                }
            }

            // 被委托人账号
            umAccreditAuditEntity.setTransferAccount(userInfoDTO.getLoginName());
            // 被委托人手机号
            umAccreditAuditEntity.setTransferMobile(userInfoDTO.getMobile());
            // 被委托人身份证号
            umAccreditAuditEntity.setTransferIdCardNo(userInfoDTO.getIdCardNo());
            // 被委托人姓名
            umAccreditAuditEntity.setTransferUserName(userInfoDTO.getUserName());
            // 被委托人用户id
            umAccreditAuditEntity.setTransferUserId(String.valueOf(userInfoDTO.getId()));
        }
    }

    public PersonalInfoDTO getPersonalDetailPage(String userId) {
        PersonalInfoDTO dto = new PersonalInfoDTO();
        AuthenticateRealNameDTO authenticateRealNameDTO = new AuthenticateRealNameDTO();
        // 1、查询主数据获取用户基本信息
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.queryUserInfo(Long.valueOf(userId));
        if (ObjectUtil.isNotEmpty(mdmUserInfoDTO)) {
            dto.setUserId(userId);
            dto.setUserName(mdmUserInfoDTO.getUserName());
            dto.setLoginName(mdmUserInfoDTO.getLoginName());
        }

        // 2、查询主数据获取用户联系方式
        MdmUserContactQuery query = new MdmUserContactQuery();
        query.setEnableFlag("1");
        query.setUserId(Long.valueOf(userId));
        List<MdmUserContactDTO> mdmUserContactDTOS = mdmExchangeService.queryUserContactList(query);
        if (CollUtil.isNotEmpty(mdmUserContactDTOS)) {
            for (MdmUserContactDTO mdmUserContactDTO : mdmUserContactDTOS) {
                if (mdmUserContactDTO.getCategory().equals("10")) {
                    dto.setUserPhone(mdmUserContactDTO.getContactInfo());
                } else if (mdmUserContactDTO.getCategory().equals("20")) {
                    dto.setUserEmail(mdmUserContactDTO.getContactInfo());
                }
            }
        }

        // 3、查询实名认证审核记录
        UmUserAuthAuditEntity umUserApproveAuditEntity = umUserAuthAuditRepository.queryByUserId(userId, null);
        if (ObjectUtil.isEmpty(umUserApproveAuditEntity)) {
            authenticateRealNameDTO.setAuditStatus(0);
            return dto;
        }

        authenticateRealNameDTO = BeanUtil.copyProperties(umUserApproveAuditEntity, AuthenticateRealNameDTO.class);
        // 处理身份证路径
        String frontUrl = umUserApproveAuditEntity.getFrontUrl();
        String behindUrl = umUserApproveAuditEntity.getBehindUrl();
        if (StrUtil.isNotEmpty(frontUrl)) {
            authenticateRealNameDTO.setFrontUrl(uploadExchangeService.getUrl(frontUrl));
        }
        if (StrUtil.isNotEmpty(behindUrl)) {
            authenticateRealNameDTO.setBehindUrl(uploadExchangeService.getUrl(behindUrl));
        }
        dto.setAuthenticateRealNameDTO(authenticateRealNameDTO);

        // 4、处理用户身份证是否过期
        handleIdCardExpiration(dto, umUserApproveAuditEntity);

        // 是否长期有效
        authenticateRealNameDTO.setValidFlag(transferIsValidLongTime(umUserApproveAuditEntity.getUntilDate()));
        return dto;
    }

    /**
     * 处理用户身份证是否过期
     *
     * @param dto
     * @param umUserApproveAuditEntity
     */
    private void handleIdCardExpiration(PersonalInfoDTO dto, UmUserAuthAuditEntity umUserApproveAuditEntity) {
        Date untilDate = umUserApproveAuditEntity.getUntilDate();
        int expiredStatus;

        if (untilDate == null) {
            // 身份证有效期为空
            expiredStatus = 30;
        } else if (DateUtil.compare(untilDate, DateUtil.date()) < 0) {
            // 身份证已过期
            expiredStatus = 20;
        } else {
            // 身份证未过期
            expiredStatus = 10;
        }

        dto.getAuthenticateRealNameDTO().setIsExpired(expiredStatus);
    }


    /**
     * 新增用户实名认证审核记录
     *
     * @param command
     */
    public void createUserAuthAudit(UmUserAuthAuditEntity command, boolean isAutoAudit) {
        // 更新历史实名认证审核记录为已删除
        String userId = command.getUserId();
        if (StrUtil.isNotEmpty(userId)) {
            UmUserAuthAuditEntity entity = new UmUserAuthAuditEntity();
            entity.setUserId(userId);
            entity.setDelFlag(DeletedFlagEnum.DELETED.getCode());
            umUserAuthAuditRepository.update(entity);
        }
        command.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());

        // 新增用户实名认证审核记录
        boolean result = umUserAuthAuditRepository.insert(command);
        if (!result) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_ADD_USER_AUTH_AUDIT_FAILED);
        }
        // 获取主键id
        Long id = command.getId();
        if (ObjectUtil.isNotEmpty(id) && isAutoAudit) {
            // 发送mq,处理实名认证审核
            sendMessage(id);
        }
    }

    /**
     * 发送mq消息
     *
     * @param id
     */
    private void sendMessage(Long id) {
        try {
            UmUserAuthAuditEntity event = new UmUserAuthAuditEntity();
            event.setId(id);
            MqEventMessage.EventMessage<UmUserAuthAuditEntity> authAuditEntityEventMessage =
                    MqEventMessage.buildEventMessage(KafkaConstants.CTP_REAL_NAME_AUTO_AUDIT_TOPIC, event);
            mqEventPublisher.publishNormalMessage(KafkaConstants.CTP_REAL_NAME_AUTO_AUDIT_TOPIC + ":" + KafkaConstants.REAL_NAME_AUTO_AUDIT_TAG, authAuditEntityEventMessage);
        } catch (Exception e) {
            log.error("实名认证，自动审核发送mq消息失败", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void employeeInviteProcess(HandleInvitationCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        String userId = getUserIdUtil.getUserId();
        // 1、查询邀请记录是否存在
        UmEmployeeBindingRecordCondition umEmployeeBindingRecordCondition = new UmEmployeeBindingRecordCondition();
        umEmployeeBindingRecordCondition.setId(Long.valueOf(command.getId()));
        umEmployeeBindingRecordCondition.setEmployeeId(userId);
        UmEmployeeBindingRecordEntity umEmployeeBindingRecord = umEmployeeBindingRecordRepository.queryById(Long.valueOf(command.getId()));

        if (ObjectUtil.isEmpty(umEmployeeBindingRecord)) {
            throw new CtpCorePartnerException("邀请记录不存在");
        }

        // 获取被邀请员工 用户id
        String employeeId = umEmployeeBindingRecord.getEmployeeId();
        // 被邀请用户手机号
        String userMobile = umEmployeeBindingRecord.getUserMobile();
        // 获取操作员id
        String operatorId = umEmployeeBindingRecord.getOperatorId();
        // 企业id
        String companyId = umEmployeeBindingRecord.getCompanyId();

        // 2、查询被邀请员工已经绑定的公司信息
        Integer defaultCompanyFlag = 0;
        QueryBoundInfoCondition condition = new QueryBoundInfoCondition();
        condition.setUserId(employeeId);
        List<BoundCompanyEntity> boundCompanyEntities = umCompanyMemberRepository.queryBoundCompanyInfo(condition);
        if (CollUtil.isEmpty(boundCompanyEntities)) {
            // 被邀请员工未绑定任何企业,则指定企业为默认企业
            defaultCompanyFlag = 1;
        }
        BoundCompanyEntity boundCompanyEntityOld = boundCompanyEntities.stream().filter
                (boundCompanyEntity -> ObjectUtil.equals(boundCompanyEntity.getDefaultCompanyFlag(), 1)).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(boundCompanyEntityOld)) {
            // 不设置当前企业为默认企业
            defaultCompanyFlag = 0;
        }


        // 2、更新邀请记录
        String action = command.getAction();
        if (StrUtil.equals(action, "10") && StrUtil.isNotEmpty(employeeId)) {
            // 同意
            umEmployeeBindingRecord.setBindingStatus(20);

            // 1、更新ctp员工信息表
            UmCompanyMemberEntity umCompanyMemberEntity = new UmCompanyMemberEntity();
            umCompanyMemberEntity.setLevelType("2");
            umCompanyMemberEntity.setOperatorId(operatorId);
            umCompanyMemberEntity.setUserId(employeeId);
            umCompanyMemberEntity.setUserName(tokenInfo.getUsername());
            umCompanyMemberEntity.setLoginName(tokenInfo.getLoginName());
            umCompanyMemberEntity.setUpdaterId(userId);
            umCompanyMemberEntity.setDefaultCompanyFlag(defaultCompanyFlag);
            umCompanyMemberEntity.setUserMobile(userMobile);
            umCompanyMemberEntity.setUpdaterId(userId);
            umCompanyMemberRepository.updateByOperatorId(umCompanyMemberEntity);
            log.info("更新ctp员工信息表成功");

            // 2、更新cmd操作员表
            MdmOperatorCommand mdmOperatorCommand = new MdmOperatorCommand();
            mdmOperatorCommand.setId(operatorId);
            mdmOperatorCommand.setOperatorType("20");
            mdmOperatorCommand.setOperatorAccount(tokenInfo.getLoginName());
            mdmOperatorCommand.setOperatorName(tokenInfo.getUsername());
            mdmOperatorCommand.setUserId(employeeId);
            mdmOperatorCommand.setOperatorPhone(userMobile);
            mdmOperatorCommand.setUpdaterId(userId);
            mdmOperatorCommand.setUpdaterName(tokenInfo.getUsername());
            mdmOperatorCommand.setInviteStatus("30");
            mdmExchangeService.operatorUpdate(mdmOperatorCommand);
            log.info("更新cmd操作员表成功");

            // 刷新redis
            refreshRedis(userId, defaultCompanyFlag, tokenInfo, operatorId, companyId);


        } else if (StrUtil.equals(action, "20")) {
            // 拒绝
            umEmployeeBindingRecord.setBindingStatus(90);
            MdmOperatorCommand mdmOperatorCommand = new MdmOperatorCommand();
            mdmOperatorCommand.setId(operatorId);
            mdmOperatorCommand.setInviteStatus("10");
            mdmOperatorCommand.setUpdaterId(userId);
            mdmExchangeService.operatorUpdate(mdmOperatorCommand);

        } else {
            throw new CtpCorePartnerException("操作类型只能是同意或拒绝");
        }
        umEmployeeBindingRecord.setUpdaterId(tokenInfo.getUserId());
        // 4、更新邀请记录
        umEmployeeBindingRecordRepository.updateById(umEmployeeBindingRecord);
    }


    public void setDefaultCompany(SetDefaultCompanyCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }

        String userId = getUserIdUtil.getUserId();

        // 1、查询当前员工已经绑定的公司信息
        UmCompanyMemberCondition umCompanyMemberCondition = new UmCompanyMemberCondition();
        umCompanyMemberCondition.setUserId(userId);
        List<UmCompanyMemberEntity> umCompanyMemberEntities = umCompanyMemberRepository.queryList(umCompanyMemberCondition);
        if (CollUtil.isNotEmpty(umCompanyMemberEntities)) {
            // 获取已设为默认的公司
            UmCompanyMemberEntity umCompanyMemberEntity = umCompanyMemberEntities.stream()
                    .filter(item -> ObjectUtil.equal(item.getDefaultCompanyFlag(), DefaultCompanyFlagTypeEnum.YES.getCode()))
                    .findFirst()
                    .orElse(null);

            if (ObjectUtil.isNotEmpty(umCompanyMemberEntity)) {
                // 更新为非默认
                umCompanyMemberEntity.setDefaultCompanyFlag(DefaultCompanyFlagTypeEnum.NO.getCode());
                umCompanyMemberRepository.updateById(umCompanyMemberEntity);
            }
            // 获取入参企业
            String companyId = command.getCompanyId();
            UmCompanyMemberEntity umCompanyMemberEntityNow = umCompanyMemberEntities.stream()
                    .filter(item -> item.getCompanyId().equals(companyId))
                    .findFirst()
                    .orElse(null);

            if (ObjectUtil.isNotEmpty(umCompanyMemberEntityNow)) {
                // 更新为默认企业
                umCompanyMemberEntityNow.setDefaultCompanyFlag(DefaultCompanyFlagTypeEnum.YES.getCode());
                umCompanyMemberRepository.updateById(umCompanyMemberEntityNow);
            }
        }
    }

    public PageInfo<BoundCompanyEntity> queryBoundCompanyInfo(PagingInfo<QueryBoundInfoCondition> conditionPage) {
        QueryBoundInfoCondition condition = conditionPage.getFilterModel();
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }
        // 用户id
        String userId = getUserIdUtil.getUserId();

        condition.setUserId(userId);
        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        List<BoundCompanyEntity> list = umCompanyMemberRepository.queryBoundCompanyInfo(condition);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        Map<String, UmAccreditAuditEntity> auditMap = new HashMap<>();
        Map<Long, MdmUserInfoDTO> mdmUserInfoDTOMap = new HashMap<>();
        Map<String, MdmOperatorDTO> operatorDTOMap = new HashMap<>();

        // 获取所有的公司id
        List<String> companyIdList = list.stream().map(BoundCompanyEntity::getCompanyId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(companyIdList)) {
            // 查询公司是否存在管理员过户
            UmAccreditAuditCondition auditCondition = new UmAccreditAuditCondition();
            auditCondition.setCompanyIdList(companyIdList);
            auditCondition.setAuditStatus(1);
            List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(auditCondition);
            if (CollUtil.isNotEmpty(umAccreditAuditEntities)) {
                // 根据企业id转map
                auditMap = umAccreditAuditEntities.stream()
                        .collect(Collectors.toMap(UmAccreditAuditEntity::getCompanyId,
                                item -> item, (existing, replacement) -> existing));
            }
        }

        if (StrUtil.isNotEmpty(userId)) {
            MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
            mdmUserInfoQuery.setId(Long.valueOf(userId));
            ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.userInfoQueryList(mdmUserInfoQuery);
            if (!resultMode.isSucceed()) {
                log.error("调用主数据查询查询用户信息失败：" + resultMode.getMessage());
            }
            List<MdmUserInfoDTO> mdmUserInfoDTOList = resultMode.getModel();
            if (CollUtil.isNotEmpty(mdmUserInfoDTOList)) {
                // 根据用户id转map
                mdmUserInfoDTOMap = mdmUserInfoDTOList.stream().collect(Collectors.toMap(MdmUserInfoDTO::getId, item -> item));
            }
        }

        // 查询操作员列表
        MdmOperatorQuery mdmOperatorQuery = new MdmOperatorQuery();
        mdmOperatorQuery.setUserId(userId);
        ResultMode<List<MdmOperatorDTO>> operatorModelResult = mdmExchangeService.queryOperatorByCondition(mdmOperatorQuery);
        if (!operatorModelResult.isSucceed()) {
            log.error("调用主数据查询批量查询操作员信息失败：" + operatorModelResult.getMessage());
        }
        List<MdmOperatorDTO> mdmOperatorDTOList = operatorModelResult.getModel();
        if (CollUtil.isNotEmpty(mdmOperatorDTOList)) {
            // 根据企业id转map
            operatorDTOMap = mdmOperatorDTOList.stream()
                    .collect(Collectors.toMap(MdmOperatorDTO::getCompanyId,
                            item -> item, (existing, replacement) -> existing));
        }

        for (BoundCompanyEntity boundCompanyEntity : list) {
            MdmUserInfoDTO mdmUserInfoDTO = mdmUserInfoDTOMap.get(Long.valueOf(userId));
            if (ObjectUtil.isNotEmpty(mdmUserInfoDTO)) {
                if (ObjectUtil.isNotEmpty(mdmUserInfoDTO.getMobile())) {
                    // 手机号赋值
                    boundCompanyEntity.setPhone((String) PhoneUtil.hideBetween(mdmUserInfoDTO.getMobile()));
                }
            }
            String companyId = boundCompanyEntity.getCompanyId();
            MdmOperatorDTO mdmOperatorDTO = operatorDTOMap.get(companyId);
            if (ObjectUtil.isNotEmpty(mdmOperatorDTO)) {
                // 操作员id
                boundCompanyEntity.setOperatorId(mdmOperatorDTO.getId());
            }
            UmAccreditAuditEntity umAccreditAuditEntity = auditMap.get(companyId);
            if (ObjectUtil.isNotEmpty(umAccreditAuditEntity)) {
                boundCompanyEntity.setCompanyStatus("20");
            }
        }

        // 处理默认企业  小程序不处理
        if (condition.getPlatformChannel() == null || condition.getPlatformChannel().equals(PlatformChannelEnum.PC.getCode().toString()))
            processDefaultCompany(list);

        PageInfo<BoundCompanyEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    private static void processDefaultCompany(List<BoundCompanyEntity> list) {
        // 设置默认企业排在首位
        BoundCompanyEntity defaultCompany = null;
        for (BoundCompanyEntity boundCompanyEntity : list) {
            if (boundCompanyEntity.getDefaultCompanyFlag() == 1) {
                defaultCompany = boundCompanyEntity;
                break;
            }
        }
        if (ObjectUtil.isNotEmpty(defaultCompany)) {
            list.remove(defaultCompany);
            list.add(0, defaultCompany);
        }
    }

    public SwitchCompanyDTO switchCompany(SwitchCompanyCondition condition) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }
        SwitchCompanyDTO dto = new SwitchCompanyDTO();

        // 获取当前用户的userId
        String userId = getUserIdUtil.getUserId();
        dto.setUserId(userId);

        // redisKey
        String redisKey = CommonRedisConstants.REDIS_TOKENINFO_KEY + userId;

        dto.setResult("10");
        TokenInfo redisTokenInfo = new TokenInfo();
        redisTokenInfo.setUserBaseId(tokenInfo.getUserBaseId());
        // 业务场景 10-登录 20-手动切换
        String scene = condition.getScene();

        // 查询用户信息
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.queryUserInfo(Long.valueOf(userId));
        if (ObjectUtil.isEmpty(mdmUserInfoDTO)) {
            return dto;
        }
        dto.setUserName(mdmUserInfoDTO.getUserName());

        redisTokenInfo.setUsername(mdmUserInfoDTO.getUserName());
        redisTokenInfo.setLoginName(mdmUserInfoDTO.getLoginName());
        redisTokenInfo.setUserId(userId);

        // 查询员工信息
        List<UmCompanyMemberEntity> memberList = getMemberList(userId);

        // 当前用户未绑定任何企业
        if (CollUtil.isEmpty(memberList)) {
            // 刷新redis
            redisService.set(redisKey, JSONUtil.toJsonStr(redisTokenInfo));
            return dto;
        }

        // 登录场景
        if (StrUtil.equals(scene, SwitchCompanyTypeEnum.LOGIN.getCode())) {
            // 获取默认企业
            UmCompanyMemberEntity defaultCompanyMemberEntity = memberList.stream().filter(item ->
                            ObjectUtil.equal(item.getDefaultCompanyFlag(), DefaultCompanyFlagTypeEnum.YES.getCode()))
                    .findFirst().orElse(null);

            // 未获取到默认企业
            if (ObjectUtil.isEmpty(defaultCompanyMemberEntity)) {
                // 刷新redis
                redisService.set(redisKey, JSONUtil.toJsonStr(redisTokenInfo));
                return dto;
            }

            // 获取操作员id
            String operatorId = defaultCompanyMemberEntity.getOperatorId();
            // 查询操作员id
            MdmOperatorDTO mdmOperatorDTO = queryOperatorDetail(operatorId);
            String redisKeyBase = null;
            if (StrUtil.isNotEmpty(mdmOperatorDTO.getOperatorCode())) {
                redisKeyBase = CommonRedisConstants.REDIS_TOKENINFO_KEY_USER_BASE + mdmOperatorDTO.getOperatorCode();
            }
            dto.setCompanyId(defaultCompanyMemberEntity.getCompanyId());
            dto.setCompanyName(defaultCompanyMemberEntity.getCompanyName());
            dto.setUserName(defaultCompanyMemberEntity.getUserName());
            dto.setMemberId(String.valueOf(defaultCompanyMemberEntity.getId()));
            dto.setResult("20");
            // 刷新redis
            updateRedisUserInfo(redisKey, redisKeyBase, defaultCompanyMemberEntity, mdmOperatorDTO, redisTokenInfo);
            CompletableFuture.runAsync(() -> {
                logExchangeService.addLogCompanyLogin(LogCompanyLoginAssembler.buildLogLogOnAssembler(defaultCompanyMemberEntity, mdmOperatorDTO, tokenInfo, userId));
                MdmOperatorDTO mdmOperatorLogOut = queryOperatorDetail(tokenInfo.getOperatorId());
                logExchangeService.addLogCompanyLogin(LogCompanyLoginAssembler.buildLogLogOutAssembler(tokenInfo, mdmOperatorLogOut.getOperatorCode()));

            });
            return dto;

        } else {
            // 手动切换
            String id = condition.getId();
            if (StrUtil.isEmpty(id)) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ID_NOT_EMPTY);
            }
            // 获取要切换企业的员工信息
            UmCompanyMemberEntity umCompanyMemberEntity = memberList.stream().filter(item ->
                    ObjectUtil.equal(item.getId(), Long.valueOf(id))).findFirst().orElse(null);

            if (ObjectUtil.isEmpty(umCompanyMemberEntity)) {
                throw new CtpCorePartnerException("未查询到要切换企业绑定的员工信息");
            }

            // 获取操作员id
            String operatorId = umCompanyMemberEntity.getOperatorId();

            // 查询操作员信息
            MdmOperatorDTO mdmOperatorDTO = queryOperatorDetail(operatorId);
            String redisKeyBase = CommonRedisConstants.REDIS_TOKENINFO_KEY_USER_BASE + mdmOperatorDTO.getOperatorCode();
            dto.setCompanyId(umCompanyMemberEntity.getCompanyId());
            dto.setCompanyName(umCompanyMemberEntity.getCompanyName());
            dto.setUserName(umCompanyMemberEntity.getUserName());
            dto.setMemberId(String.valueOf(umCompanyMemberEntity.getId()));
            dto.setResult("20");

            // 刷新redis
            updateRedisUserInfo(redisKey, redisKeyBase, umCompanyMemberEntity, mdmOperatorDTO, redisTokenInfo);

            CompletableFuture.runAsync(() -> {
                logExchangeService.addLogCompanyLogin(LogCompanyLoginAssembler.buildLogLogOnAssembler(umCompanyMemberEntity, mdmOperatorDTO, tokenInfo, userId));
                MdmOperatorDTO mdmOperatorLogOut = queryOperatorDetail(tokenInfo.getOperatorId());
                logExchangeService.addLogCompanyLogin(LogCompanyLoginAssembler.buildLogLogOutAssembler(tokenInfo, mdmOperatorLogOut.getOperatorCode()));

            });

            return dto;
        }
    }

    /**
     * 根据用户id查询员工列表
     *
     * @param userId
     * @return
     */
    public List<UmCompanyMemberEntity> getMemberList(String userId) {
        Map<Long, UmCompanyEntity> companyMap = new HashMap<>();
        // 查询ctp 员工信息表
        UmCompanyMemberCondition umCompanyMemberCondition = new UmCompanyMemberCondition();
        umCompanyMemberCondition.setUserId(userId);
        List<UmCompanyMemberEntity> memberEntities = umCompanyMemberRepository.queryList(umCompanyMemberCondition);
        if (CollUtil.isNotEmpty(memberEntities)) {
            // 获取所有的企业id
            List<String> companyIds = memberEntities.stream().map(item -> item.getCompanyId()).collect(Collectors.toList());
            // 批量查询企业信息
            List<UmCompanyEntity> umCompanyEntities = umOrgRepository.queryCompanyList(companyIds);
            if (CollUtil.isNotEmpty(umCompanyEntities)) {
                // 根据企业id转map
                companyMap = umCompanyEntities.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            }
        }

        // 组装数据
        for (UmCompanyMemberEntity memberEntity : memberEntities) {
            String companyId = memberEntity.getCompanyId();
            UmCompanyEntity umCompanyEntity = companyMap.get(Long.valueOf(companyId));
            if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
                // 企业名称
                memberEntity.setLicenseNo(umCompanyEntity.getSocialCreditCode());
            }
        }
        return memberEntities;
    }

    /**
     * 查询操作员信息
     *
     * @param operatorId
     * @return <p>
     */
    public MdmOperatorDTO queryOperatorDetail(String operatorId) {
        // 查询操作员信息
        MdmOperatorQuery mdmOperatorQuery = new MdmOperatorQuery();
        mdmOperatorQuery.setId(operatorId);
        ResultMode<MdmOperatorDTO> mdmOperatorDTOResultMode = mdmExchangeService.queryOperatorDetail(mdmOperatorQuery);
        if (!mdmOperatorDTOResultMode.isSucceed()) {
            throw new CtpCorePartnerException("未查询到操作员信息");
        }
        MdmOperatorDTO mdmOperatorDTO = mdmOperatorDTOResultMode.getModel();
        if (ObjectUtil.isEmpty(mdmOperatorDTO)) {
            throw new CtpCorePartnerException("未查询到操作员信息");
        }

        return mdmOperatorDTO;
    }

    /**
     * 更新redis用户信息
     *
     * @param redisKey
     * @param umCompanyMemberEntity
     * @param mdmOperatorDTO
     * @param redisTokenInfo
     */
    public void updateRedisUserInfo(String redisKey, String redisKeyBase, UmCompanyMemberEntity umCompanyMemberEntity, MdmOperatorDTO mdmOperatorDTO, TokenInfo redisTokenInfo) {
        try {
            log.info("redisTokenInfo:{}", redisTokenInfo);
            redisTokenInfo.setUserBaseId(mdmOperatorDTO.getOperatorCode());
            redisTokenInfo.setUserId(mdmOperatorDTO.getUserId());
            redisTokenInfo.setCompanyIdForCmd(mdmOperatorDTO.getCompanyId());
            redisTokenInfo.setCompanyName(mdmOperatorDTO.getCompanyName());
            redisTokenInfo.setCompanyId(umCompanyMemberEntity.getCompanyId());
            redisTokenInfo.setLicenseNo(umCompanyMemberEntity.getLicenseNo());
            redisTokenInfo.setMemberId(String.valueOf(umCompanyMemberEntity.getId()));
            redisTokenInfo.setOperatorId(mdmOperatorDTO.getId());
            redisTokenInfo.setOperatorType(mdmOperatorDTO.getOperatorType());
            redisService.set(redisKey, JSONUtil.toJsonStr(redisTokenInfo));
            if (StrUtil.isNotEmpty(redisKeyBase)) {
                redisService.set(redisKeyBase, JSONUtil.toJsonStr(redisTokenInfo));
            }
            log.info("redisTokenInfo:{}", redisTokenInfo);
        } catch (Exception e) {
            log.error("更新redis用户信息失败:" + e.getMessage());
        }
    }

    public void queryUserRealNameStatus(String phone) {
        // 根据手机号查询用户信息
        MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
        mdmUserInfoQuery.setMobile(phone);
        ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.userInfoQueryList(mdmUserInfoQuery);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(resultMode.getCode(), resultMode.getMessage());
        }
        List<MdmUserInfoDTO> resultModeModel = resultMode.getModel();
        if (CollUtil.isEmpty(resultModeModel)) {
            throw new CtpCorePartnerException("指定用户未在商贸注册");
        }

        MdmUserInfoDTO userInfoDTO = IterUtil.getFirst(resultModeModel);
        if (ObjectUtil.isNotEmpty(userInfoDTO.getId())) {
            // 查询用户实名认证状态
            UmUserAuthAuditEntity auditEntity = umUserAuthAuditRepository.queryByUserId(String.valueOf(userInfoDTO.getId()), CommonAuditStatusEnum.PASS.getCode());
            if (ObjectUtil.isEmpty(auditEntity)) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_AUTH_AUDIT_NOT_PASS, "指定用户未在平台实名认证，您可提醒ta进行实名认证后，方可进行管理员过户");
            }
        }
    }

    /**
     * 创建实名认证日志
     *
     * @param bizId
     * @param content
     * @return
     */
    public static LogOperationRecordCommand createUserAuthAuditLog(String bizId, String userName, String operateType, String content) {
        LogOperationRecordCommand result = new LogOperationRecordCommand();
        result.setBizId(bizId);
        result.setBizType(LogOperationRecordEnum.CTP_PARTNER_USER_AUTH_AUDIT.getBizType());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        result.setUserBaseId(tokenInfo.getUserId());
        if (StringUtils.isNotBlank(tokenInfo.getUsername())) {
            result.setUserName(tokenInfo.getUsername());
        } else {
            result.setUserName(userName);
        }
        result.setUserAccount(tokenInfo.getLoginName());
        result.setOperateTime(DateUtil.date());
        result.setOperateType(operateType);
        result.setOperateContent(content);
        return result;
    }

    /**
     * 创建实名认证日志
     *
     * @param bizId
     * @param content
     * @return
     */
    public LogOperationRecordCommand createCompanyManagerLog(String bizId, Integer sourceType, String content) {
        String operateType = "";
        if (sourceType == 20) {
            operateType = "申请成为管理员";
        } else if (sourceType == 30) {
            operateType = "管理员过户";
        }
        // 查询管理员有没有被审核拒绝的记录
        List<UmAccreditAuditEntity> auditEntities = checkIfCompanyHasRejectAudit(bizId, sourceType);
        if (CollUtil.isNotEmpty(auditEntities)) {
            operateType = "重新认证";
        }
        LogOperationRecordCommand result = new LogOperationRecordCommand();
        result.setBizId(bizId);
        result.setBizType(LogOperationRecordEnum.CTP_PARTNER_MANAGER_AUDIT.getBizType());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        result.setUserBaseId(tokenInfo.getUserId());
        result.setUserName(tokenInfo.getUsername());
        result.setUserAccount(tokenInfo.getLoginName());
        result.setOperateTime(DateUtil.date());
        result.setOperateType(operateType);
        result.setOperateContent(content);
        return result;
    }

    /**
     * 证件日期转换为是否长期有效
     */
    public Integer transferIsValidLongTime(Date time) {
        if (ObjectUtil.isEmpty(time)) {
            return 0;
        }
        DateTime limit = DateUtil.parse("2099-12-31 00:00:00");
        return DateUtil.compare(time, limit) < 0 ? 0 : 1;
    }

    /**
     * 刷新redis
     *
     * @param userId
     * @param defaultCompanyFlag
     * @param tokenInfo
     * @param operatorId
     * @param companyId
     */
    private void refreshRedis(String userId, Integer defaultCompanyFlag, TokenInfo tokenInfo, String operatorId, String companyId) {
        String redisKey = CommonRedisConstants.REDIS_TOKENINFO_KEY + userId;
        if (ObjectUtil.equal(defaultCompanyFlag, 1)) {
            TokenInfo redisTokenInfo = BeanUtil.copyProperties(tokenInfo, TokenInfo.class);
            // 根据operatorId查询member信息
            UmCompanyMemberCondition memberCondition = new UmCompanyMemberCondition();
            memberCondition.setOperatorId(operatorId);
            List<UmCompanyMemberEntity> memberEntities = umCompanyMemberRepository.queryList(memberCondition);
            if (CollUtil.isNotEmpty(memberEntities)) {
                UmCompanyMemberEntity first = IterUtil.getFirst(memberEntities);
                if (ObjectUtil.isNotEmpty(first)) {
                    redisTokenInfo.setMemberId(String.valueOf(first.getId()));
                }
            }

            // 根据企业id查询企业信息
            UmCompanyEntity umCompanyEntity = umCompanyRepository.queryById(companyId);
            if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
                redisTokenInfo.setCompanyId(String.valueOf(umCompanyEntity.getId()));
                redisTokenInfo.setCompanyName(umCompanyEntity.getCompanyName());
                redisTokenInfo.setLicenseNo(umCompanyEntity.getSocialCreditCode());
            }
            // 根据operatorId查询
            MdmOperatorDTO mdmOperatorDTO = mdmExchangeService.getCmdOperator(operatorId);
            if (ObjectUtil.isNotEmpty(mdmOperatorDTO)) {
                redisTokenInfo.setOperatorId(mdmOperatorDTO.getId());
                redisTokenInfo.setOperatorType(mdmOperatorDTO.getOperatorType());
                redisTokenInfo.setCompanyIdForCmd(mdmOperatorDTO.getCompanyId());
                redisTokenInfo.setUserBaseId(mdmOperatorDTO.getOperatorCode());
            }
            redisService.set(redisKey, JSONUtil.toJsonStr(redisTokenInfo));

        }
    }
}

