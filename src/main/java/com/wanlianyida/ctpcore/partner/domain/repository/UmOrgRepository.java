package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyMemberEntity;

import java.util.List;
import java.util.Set;

public interface UmOrgRepository {

    /**
     * 查询组织管理员信息
     *
     * @param companyId    公司 ID
     * @param platformType 平台类型
     * @return {@link UmCompanyMemberEntity }
     */
    UmCompanyMemberEntity queryCompanyAdmin(String companyId, String platformType);


    /**
     * 查询公司管理员列表
     *
     * @param platformType 平台类型
     * @return {@link List }<{@link UmCompanyMemberEntity }>
     */
    List<UmCompanyMemberEntity> queryCompanyAdminList(Set<String> licenseNo, String platformType);

    /**
     * 查询企业详情
     *
     * @param id 身份证
     * @return {@link UmCompanyEntity }
     */
    UmCompanyEntity queryDetail(Long id);

    /**
     * 查询企业列表
     * @param ids
     * @return {@link List }<{@link UmCompanyEntity }>
     */
    List<UmCompanyEntity> queryCompanyList(List<String> ids);
}
