package com.wanlianyida.ctpcore.partner.domain.model.condition;

import lombok.Data;

@Data
public class UmAddressManageCondition {

    /**
     * 公司 ID
     */
    private String companyId;
    /**
     * 类型 10买家收货地址 20卖家发货地址 30买家仓库地址 40卖家仓库地址
     */
    private String type;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 中文（省/直辖市）
     */
    private String province;

    /**
     * 中文（市）
     */
    private String city;

    /**
     * 中文（区）
     */
    private String area;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 仓库名称
     */
    private String shortName;
}
