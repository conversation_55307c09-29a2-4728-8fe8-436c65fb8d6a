package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.BdSensitiveWordsCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.BdSensitiveWordsEntity;

import java.util.List;

public interface BdSensitiveWordsRepository {

    /**
     * 敏感词更新
     *
     * @param entity 实体
     * @return {@link Boolean }
     */
    Boolean updateInfo(BdSensitiveWordsEntity entity);

    /**
     * 批量删除
     *
     * @param entity 实体
     * @return {@link Boolean }
     */
    Boolean batchDelete(BdSensitiveWordsEntity entity);

    /**
     * 分页查询
     *
     * @param condition 条件
     * @return {@link List }<{@link BdSensitiveWordsEntity }>
     */
    List<BdSensitiveWordsEntity> queryPage(BdSensitiveWordsCondition condition);

    /**
     * 查询已启用的敏感词
     *
     * @return {@link List }<{@link BdSensitiveWordsEntity }>
     */
    List<BdSensitiveWordsEntity> selectSensitiveWords();

    /**
     * 批量插入
     *
     * @param entities 实体
     */
    void batchInsert(List<BdSensitiveWordsEntity> entities);

    /**
     * 查询敏感词
     *
     * @return {@link List }<{@link BdSensitiveWordsEntity }>
     */
    List<BdSensitiveWordsEntity> selectAll();

    /**
     * 根据id查询
     *
     * @param id 身份证
     * @return {@link BdSensitiveWordsEntity }
     */
    BdSensitiveWordsEntity selectById(Long id);
}
