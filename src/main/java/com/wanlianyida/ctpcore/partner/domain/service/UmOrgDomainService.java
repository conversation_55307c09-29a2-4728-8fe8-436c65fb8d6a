package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.basemdm.api.model.command.UmOrgCommand;
import com.wanlianyida.basemdm.api.model.dto.UmOrgDTO;
import com.wanlianyida.basemdm.api.model.query.UmOrgConditionQuery;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmOrgCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyMemberEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmOrgEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.UmOrgRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.OrgOperationTypeEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.SetBaseInfo;
import com.wanlianyida.framework.ctpcommon.entity.BaseException;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
@Slf4j
public class UmOrgDomainService {

    @Resource
    private UmOrgRepository repository;

    @Resource
    private MdmExchangeService mdmExchangeService;

    public Boolean add(UmOrgEntity umOrgEntity) {
        SetBaseInfo.setCreatorInfo(umOrgEntity, JwtUtil.getTokenInfo());

        return executeCommand(umOrgEntity, mdmExchangeService::add, "调用主数据新增组织异常");
    }

    public Boolean update(UmOrgEntity umOrgEntity) {
        // 单查
        ResultMode<UmOrgDTO> queryModel = queryDetail(umOrgEntity.getId(), umOrgEntity.getPlatformType(), null, null, null);
        UmOrgDTO model = queryModel.getModel();
        if (ObjectUtil.isEmpty(model)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UPDATE_ORG_NOT_EXISTS);
        }
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        // 更新登录人所在的组织无需校验
        if (ObjectUtil.equal(String.valueOf(umOrgEntity.getId()), tokenInfo.getCompanyId())) {
            SetBaseInfo.setUpdaterInfo(umOrgEntity, tokenInfo);
            return executeCommand(umOrgEntity, mdmExchangeService::update, "调用主数据更新组织异常");
        }

        // 获取上级信用代码
        String parentSocialCreditCode = umOrgEntity.getParentSocialCreditCode();
        if (ObjectUtil.isNotNull(parentSocialCreditCode)) {
            // 获取已绑定的上级信用代码
            String nowParentSocialCreditCode = model.getParentLicenseNo();
            if (ObjectUtil.isNotEmpty(parentSocialCreditCode) && ObjectUtil.notEqual(parentSocialCreditCode, nowParentSocialCreditCode)) {
                // 校验同一平台同一个上级组织下组织名称是否重复
                validateUniqueOrgName(umOrgEntity);
            }
        }

        log.info("更新组织关系参数：{}", umOrgEntity);
        SetBaseInfo.setUpdaterInfo(umOrgEntity, tokenInfo);

        return executeCommand(umOrgEntity, mdmExchangeService::update, "调用主数据更新组织异常");
    }

    public UmOrgEntity delete(UmOrgEntity umOrgEntity) {
        Long id = umOrgEntity.getId();

        // 单查
        ResultMode<UmOrgDTO> queryModel = queryDetail(umOrgEntity.getId(), null, null, null, null);
        UmOrgDTO model = queryModel.getModel();
        if (ObjectUtil.isEmpty(model)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.DELETE_ORG_NOT_EXISTS);
        }

        SetBaseInfo.setUpdaterInfo(umOrgEntity, JwtUtil.getTokenInfo());
        umOrgEntity.setParentSocialCreditCode("");
        umOrgEntity.setRemark(model.getRemark());
        executeCommand(umOrgEntity, mdmExchangeService::delete, "调用主数据删除组织异常");

        UmOrgEntity umOrg = new UmOrgEntity();
        umOrg.setId(id);
        umOrg.setParentOrgName(model.getParentLicenseName());
        if (StrUtil.isNotEmpty(model.getLicenseNo())){
            umOrg.setSocialCreditCode(model.getLicenseNo());
        }
        // 查询要删除的组织的管理员信息(组装站内信所需参数)
        Set<String> licenseNoList = new HashSet<>();
        licenseNoList.add(model.getLicenseNo());
        UmCompanyMemberEntity umCompanyMemberEntity = null;
        List<UmCompanyMemberEntity> umCompanyMemberEntities = repository.queryCompanyAdminList(licenseNoList, umOrgEntity.getPlatformType());
        if (ObjectUtil.isNotNull(umCompanyMemberEntities)){
            umCompanyMemberEntity = IterUtil.getFirst(umCompanyMemberEntities);
        }

        if (ObjectUtil.isNotEmpty(umCompanyMemberEntity)) {
            umOrg.setUserBaseId(umCompanyMemberEntity.getUserBaseId());
            umOrg.setAdminAccount(umCompanyMemberEntity.getLoginName());
            umOrg.setPhone(umCompanyMemberEntity.getUserMobile());
        }
        return umOrg;
    }


    public UmOrgEntity buildOrganizationTree(UmOrgCondition condition) {
        // 参数校验
        validatedParams(condition);

        // 获取公司数据
        List<UmOrgEntity> companyData = getCompanyData(condition);

        // 查询根节点
        Optional<UmOrgEntity> rootOptional = companyData.stream()
                .filter(UmOrgEntity -> StrUtil.equals(UmOrgEntity.getSocialCreditCode(), condition.getSocialCreditCode()))
                .findFirst();

        // 获取根节点，如果不存在则直接返回null
        UmOrgEntity root = rootOptional.orElse(null);
        log.info("获取根节点，返回结果：{}", root);
        if (ObjectUtil.isEmpty(root)) {
            return null;
        }
        // 递归查询子级
        Set<Long> visitedNodes = new HashSet<>();
        buildChildTree(root, companyData, visitedNodes);
        // 清空集合 释放内存
        companyData.clear();
        log.info("递归查询子级，返回结果：{}", root);

        return root;
    }

    /**
     * 参数校验
     *
     * @param condition 条件
     */
    private void validatedParams(UmOrgCondition condition) {
        String socialCreditCode = condition.getSocialCreditCode();
        if (StrUtil.isEmpty(socialCreditCode)){
            condition.setSocialCreditCode(JwtUtil.getTokenInfo().getLicenseNo());
        }
        String platformType = condition.getPlatformType();
        if (ObjectUtil.isEmpty(platformType)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.PLATFORM_TYPE_NOT_EMPTY);
        }
    }

    /**
     * 递归子级
     *
     * @param parent       起源
     * @param companyList  公司名单
     * @param visitedNodes 访问节点
     */
    private void buildChildTree(UmOrgEntity parent, List<UmOrgEntity> companyList, Set<Long> visitedNodes) {
        // 检查是否已经访问过该节点
        Long orgId = parent.getId();
        if (visitedNodes.contains(orgId)) {
            return;
        }

        // 标记该节点为已访问
        visitedNodes.add(orgId);
        // 查询子节点
        List<UmOrgEntity> children = companyList.stream()
                .filter(item -> ObjectUtil.equal(item.getParentOrgId(), String.valueOf(orgId)))
                .collect(Collectors.toList());

        // 判断子节点与当前父节点存在循环依赖
        children.removeIf(item -> ObjectUtil.equal(String.valueOf(item.getId()), parent.getParentOrgId()));


        if (children != null && !children.isEmpty()) {
            for (UmOrgEntity child : children) {
                // 检查是否已经访问过子节点
                if (visitedNodes.contains(child.getId())) {
                    return;
                }
                parent.addChild(child);
                buildChildTree(child, companyList, visitedNodes);
            }
        }
    }

    /**
     * 获取公司数据
     *
     * @param condition 条件
     * @return {@code List<UmOrgEntity> }
     */
    private List<UmOrgEntity> getCompanyData(UmOrgCondition condition) {
        String platformType = condition.getPlatformType();
        ResultMode<List<UmOrgDTO>> resultMode = mdmExchangeService.queryAll(platformType);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.QUERY_ORG_INFO_FAILED);
        }

        List<UmOrgDTO> model = resultMode.getModel();
        if (CollUtil.isEmpty(model)) {
            return CollUtil.newArrayList();
        }

        // 获取所有信用代码
        Set<String> licenseNoList = model.stream().map(UmOrgDTO::getLicenseNo).collect(Collectors.toSet());

        // 查询公司管理员列表并转换为Map
        List<UmCompanyMemberEntity> umCompanyMemberEntities = repository.queryCompanyAdminList(licenseNoList, platformType);
        Map<String, UmCompanyMemberEntity> umCompanyAdminMap = CollUtil.isNotEmpty(umCompanyMemberEntities)
                ? umCompanyMemberEntities.stream()
                .collect(Collectors.toMap(UmCompanyMemberEntity::getLicenseNo, item -> item, (existing, replacement) -> existing))
                : Collections.emptyMap();


        // 构建UmOrgEntity列表
        return model.stream().map(dto -> {
            UmOrgEntity umOrg = new UmOrgEntity();
            String id = dto.getId();
            String licenseNo = dto.getLicenseNo();
            umOrg.setId(Long.valueOf(id));
            umOrg.setOrgName(dto.getLicenseName());
            umOrg.setSocialCreditCode(licenseNo);
            umOrg.setParentOrgId(dto.getParentId());
            umOrg.setParentOrgName(dto.getParentLicenseName());
            umOrg.setParentSocialCreditCode(dto.getParentLicenseNo());
            umOrg.setCreatedDate(dto.getCreatedDate());
            umOrg.setPlatformType(dto.getPlatformType());
            umOrg.setRemark(dto.getRemark());
            umOrg.setPhone(dto.getPhone());

            // 设置管理员信息
            UmCompanyMemberEntity admin = umCompanyAdminMap.get(licenseNo);
            if (admin != null) {
                umOrg.setAdminAccount(admin.getLoginName());
                umOrg.setUserBaseId(admin.getUserBaseId());
            }

            return umOrg;
        }).collect(Collectors.toList());
    }


    /**
     * 校验上级组织是否存在
     *
     * @param umOrgEntity 组织实体
     * @return {@link UmOrgEntity }
     */
    private UmOrgEntity validateParentOrg(UmOrgEntity umOrgEntity) {

        String parentOrgId = umOrgEntity.getParentOrgId();
        ResultMode<UmOrgDTO> resultMode = queryDetail(Long.valueOf(parentOrgId), umOrgEntity.getPlatformType(), null, null, null);
        UmOrgDTO model = resultMode.getModel();

        if (ObjectUtil.isEmpty(model)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.PARENT_ORG_NOT_EXISTS);
        }

        String licenseNo = model.getLicenseNo();
        if (ObjectUtil.isEmpty(licenseNo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.PARENT_ORG_DATA_EXCEPTION);
        }

        UmOrgEntity umOrg = UmOrgEntity.builder()
                .id(Long.valueOf(model.getId()))
                .orgName(model.getLicenseName())
                .socialCreditCode(licenseNo)
                .parentSocialCreditCode(model.getParentLicenseNo())
                .phone(model.getPhone())
                .remark(model.getRemark()).build();

        return umOrg;
    }

    /**
     * 查询详情
     *
     * @param id              id
     * @param platformType    平台类型
     * @param licenseNo       信用代码
     * @param licenseName     公司名称
     * @param parentLicenseNo 父级信用代码
     * @return {@link ResultMode }<{@link UmOrgDTO }>
     */
    private ResultMode<UmOrgDTO> queryDetail(Long id, String platformType, String licenseNo,
                                             String licenseName, String parentLicenseNo) {
        UmOrgConditionQuery query = new UmOrgConditionQuery();

        setIfPresent(query::setId, id);
        // Opt.ofNullable(id).ifPresent(query::setId);
        setIfPresent(query::setPlatformType, platformType);
        setIfPresent(query::setLicenseNo, licenseNo);
        setIfPresent(query::setLicenseName, licenseName);
        setIfPresent(query::setParentLicenseNo, parentLicenseNo);
        ResultMode<UmOrgDTO> resultMode = mdmExchangeService.queryDetail(query);

        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.QUERY_ORG_INFO_FAILED.getMsg() + resultMode.getMessage()
                    ,CtpCorePartnerExceptionEnum.PLATFORM_TYPE_NOT_EMPTY.getCode());
        }
        return resultMode;
    }

    private <T> void setIfPresent(Consumer<T> setter, T value) {
        if (value != null) {
            setter.accept(value);
        }
    }

    /**
     * 校验同一个上级组织下组织名称是否重复
     *
     * @param umOrgEntity 实体
     */
    private void validateUniqueOrgName(UmOrgEntity umOrgEntity) {
        ResultMode<UmOrgDTO> resultMode = queryDetail(null, umOrgEntity.getPlatformType(),
                null, umOrgEntity.getOrgName(), umOrgEntity.getParentSocialCreditCode());

        UmOrgDTO model = resultMode.getModel();
        if (ObjectUtil.isNotEmpty(model)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ORG_ALREADY_EXISTS);
        }
    }

    /**
     * 校验组织名称、社会信用代码及管理员账号是否合法
     *
     * @param umOrgEntity 组织实体
     * @return {@link UmOrgEntity }
     */
    private UmOrgEntity validateOrgInfo(UmOrgEntity umOrgEntity, UmOrgEntity umParentOrg) {
        String operationType = umOrgEntity.getOperationType();

        // 查询要更新的组织信息
        ResultMode<UmOrgDTO> resultMode = queryDetail(null, umOrgEntity.getPlatformType(),
                umOrgEntity.getSocialCreditCode(),null, null);

        UmOrgDTO model = resultMode.getModel();
        if (ObjectUtil.isEmpty(model)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_FULL_NAME_REQUIRED);
        }

        String licenseNo = model.getLicenseNo();
        if (ObjectUtil.isEmpty(licenseNo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_DATA_EXCEPTION);
        }

        if (ObjectUtil.equal(operationType, OrgOperationTypeEnum.ORG_ADD.getCode())) {
            String parentLicenseNo = model.getParentLicenseNo();
            if (ObjectUtil.isNotEmpty(parentLicenseNo)) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_ALREADY_BOUND);
            }
        }

        String socialCreditCode = umOrgEntity.getSocialCreditCode();
        if (ObjectUtil.notEqual(socialCreditCode, licenseNo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_SOCIAL_CREDIT_CODE_REQUIRED);
        }

        // 获取父级的父级信用代码
        String parentSocialCreditCode = umParentOrg.getParentSocialCreditCode();

        if (ObjectUtil.isNotEmpty(parentSocialCreditCode) && ObjectUtil.equal(parentSocialCreditCode, socialCreditCode)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ORG_DATA_EXCEPTION);
        }
        model.setParentLicenseNo(umParentOrg.getSocialCreditCode());
        model.setParentId(String.valueOf(umParentOrg.getId()));

        String adminAccount = umOrgEntity.getAdminAccount();
        // 校验管理员信息
        UmCompanyMemberEntity adminInfo = validateAdminInfo(model.getLicenseNo(), umOrgEntity.getPlatformType(), adminAccount);

        return buildUmOrgEntity(model, adminInfo);
    }

    /**
     * 构建 组织实体
     *
     * @param model     型
     * @param adminInfo 管理员信息
     * @return {@link UmOrgEntity }
     */
    private UmOrgEntity buildUmOrgEntity(UmOrgDTO model, UmCompanyMemberEntity adminInfo) {
        return UmOrgEntity.builder()
                .id(Long.valueOf(model.getId()))
                .orgName(model.getLicenseName())
                .socialCreditCode(model.getLicenseNo())
                .parentOrgId(model.getParentId())
                .parentOrgName(model.getParentLicenseName())
                .parentSocialCreditCode(model.getParentLicenseNo())
                .phone(adminInfo.getUserMobile())
                .remark(model.getRemark())
                .userBaseId(adminInfo.getUserBaseId())
                .adminAccount(adminInfo.getLoginName())
                .build();
    }

    /**
     * 验证公司 ID
     *
     * @param companyId 公司 ID
     */
    private void validateCompanyId(String companyId) {
        if (ObjectUtil.isEmpty(companyId)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_ID_NOT_FOUND_ADMIN);
        }
    }

    /**
     * 验证参数
     *
     * @param umOrgEntity UM 组织实体
     * @return {@link Boolean }
     */
    public UmOrgEntity validatedParams(UmOrgEntity umOrgEntity) {
        // 基本信息校验
        validatedBaseInfo(umOrgEntity);

        // 操作类型
        String operationType = umOrgEntity.getOperationType();

        UmOrgEntity umParentOrg = null;
        if (ObjectUtil.equal(OrgOperationTypeEnum.ORG_ADD.getCode(), operationType)
                || ObjectUtil.equal(OrgOperationTypeEnum.ORG_UPDATE.getCode(), operationType)) {
            // 校验上级组织是否存在
            umParentOrg = validateParentOrg(umOrgEntity);
            umOrgEntity.setParentSocialCreditCode(umParentOrg.getSocialCreditCode());
            if (ObjectUtil.equal(OrgOperationTypeEnum.ORG_ADD.getCode(), operationType)) {
                // 校验同一个上级组织下组织名称是否重复
                validateUniqueOrgName(umOrgEntity);
            }
        }
        // 校验组织名称、社会信用代码及管理员账号是否合法 & 校验是否已关联上级组织
        return validateOrgInfo(umOrgEntity, umParentOrg);
    }

    /**
     * 验证管理员信息
     *
     * @param licenseNo    公司 ID
     * @param platformType 平台类型
     * @return {@link UmCompanyMemberEntity }
     */
    private UmCompanyMemberEntity validateAdminInfo(String licenseNo, String platformType, String adminAccount) {
        Set<String> licenseNoList = new HashSet<>();
        licenseNoList.add(licenseNo);
        // 查询管理员信息
        List<UmCompanyMemberEntity> umCompanyMemberEntities = repository.queryCompanyAdminList(licenseNoList, platformType);
        UmCompanyMemberEntity umCompanyMemberEntity = null;
        if (ObjectUtil.isEmpty(umCompanyMemberEntities)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ADMIN_ACCOUNT_REQUIRED);
        }
        umCompanyMemberEntity = umCompanyMemberEntities.get(0);
        if (ObjectUtil.isEmpty(umCompanyMemberEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ADMIN_ACCOUNT_REQUIRED);
        }
        String loginName = umCompanyMemberEntity.getLoginName();
        if (ObjectUtil.isEmpty(loginName)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ADMIN_INFO_NOT_FOUND);
        }
        if (ObjectUtil.notEqual(adminAccount, loginName)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ADMIN_ACCOUNT_REQUIRED);
        }
        return umCompanyMemberEntity;
    }

    /**
     * 基础信息校验
     *
     * @param umOrgEntity 组织实体
     */
    private void validatedBaseInfo(UmOrgEntity umOrgEntity) {
        // 校验操作类型
        String operationType = umOrgEntity.getOperationType();
        Boolean checkEnum = OrgOperationTypeEnum.checkEnum(operationType);
        if (!checkEnum || ObjectUtil.isEmpty(operationType)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.OPERATION_TYPE_INVALID);
        }
        if (StrUtil.equals(OrgOperationTypeEnum.ORG_UPDATE.getCode(), operationType)) {
            if (ObjectUtil.isEmpty(umOrgEntity.getId())) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ORG_ID_NOT_EMPTY);
            }
        }
        String orgName = umOrgEntity.getOrgName();
        String parentOrgName = umOrgEntity.getParentOrgName();
        if (ObjectUtil.isNotEmpty(orgName) && ObjectUtil.isNotEmpty(parentOrgName)) {
            if (ObjectUtil.equal(orgName, parentOrgName)) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ORG_NAME_SAME_AS_PARENT);
            }
        }
    }

    /**
     * 此方法用于封装命令的执行逻辑，包括命令的组装、执行以及结果的处理
     *
     * @param umOrgEntity     UmOrgEntity实例
     * @param commandExecutor 命令执行器，一个函数式接口实例，用于执行具体的命令
     * @param errorMessage    错误消息，当命令执行失败时抛出的异常中会使用该消息
     * @return 命令执行结果的模型部分
     * @throws BaseException 当命令执行失败时抛出的异常
     */
    private Boolean executeCommand(UmOrgEntity umOrgEntity, Function<UmOrgCommand, ResultMode<Boolean>> commandExecutor, String errorMessage) {
        UmOrgCommand command = assembleCommand(umOrgEntity);
        // 执行命令并获取结果
        ResultMode<Boolean> resultMode = commandExecutor.apply(command);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(errorMessage + ":" + resultMode.getMessage());
        }
        return resultMode.getModel();
    }

    /**
     * 根据UmOrgEntity实例中的信息，创建并返回一个UmOrgCommand实例
     *
     * @param umOrgEntity UmOrgEntity实例
     * @return UmOrgCommand实例
     */
    private UmOrgCommand assembleCommand(UmOrgEntity umOrgEntity) {
        UmOrgCommand command = new UmOrgCommand();
        command.setId(umOrgEntity.getId());
        command.setPlatformType(umOrgEntity.getPlatformType());
        command.setRemark(umOrgEntity.getRemark());
        command.setParentLicenseNo(umOrgEntity.getParentSocialCreditCode());
        return command;
    }
}
