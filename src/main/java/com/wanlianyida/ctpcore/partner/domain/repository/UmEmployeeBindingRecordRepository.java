package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmEmployeeBindingRecordCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmEmployeeBindingRecordEntity;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmEmployeeBindingRecordQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 17:04
 */
public interface UmEmployeeBindingRecordRepository {

    boolean insert(UmEmployeeBindingRecordEntity entity);

    boolean updateById(UmEmployeeBindingRecordEntity entity);

    List<UmEmployeeBindingRecordEntity> queryCondition(UmEmployeeBindingRecordCondition condition);

    UmEmployeeBindingRecordEntity queryById(Long id);

    int countCondition(UmEmployeeBindingRecordCondition condition);

    Integer countPendingInvitedEmployees(UmEmployeeBindingRecordQuery query);
}
