package com.wanlianyida.ctpcore.partner.domain.model.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 消息模板分类表 Condition
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
@Data
public class MsgTemplateCategoryCondition {

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 消息模板信息ID
	 */
	private Long templateId;

	/**
	 * 模板类型:10站内信,20短信,30微信,40邮件
	 */
	private String templateType;

	/**
	 * 模板标题
	 */
	private String templateTitle;

	/**
	 * 模板内容
	 */
	private String templateContent;

	/**
	 * 三方id
	 */
	private String bizId;

	/**
	 * 模板开启:1是,0否
	 */
	private String enable;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

	/**
	 * 消息模板信息IDs
	 */
	private List<Long> templateIdList;
}
