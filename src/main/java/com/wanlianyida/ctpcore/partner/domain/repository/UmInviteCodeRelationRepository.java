package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmInviteCodeRelationCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmInviteCodeRelationEntity;

/**
 * <p>
 * 分享码关系记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface UmInviteCodeRelationRepository {

    void saveInviteCodeRelation(UmInviteCodeRelationEntity entity);
    void updateInviteCodeRelationById(UmInviteCodeRelationEntity entity);
    void updateInviteeName(UmInviteCodeRelationEntity entity);
    UmInviteCodeRelationEntity queryInviteCodeRelationByCondition(UmInviteCodeRelationCondition condition);
    UmInviteCodeRelationEntity queryInviteCodeRelationByConditionPriority(UmInviteCodeRelationCondition condition);
}
