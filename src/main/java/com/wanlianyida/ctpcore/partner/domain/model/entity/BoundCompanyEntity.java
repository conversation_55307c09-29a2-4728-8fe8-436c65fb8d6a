package com.wanlianyida.ctpcore.partner.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BoundCompanyEntity {
    /**
     * id
     */
    private String id;
    /**
     * 企业id
     */
    private String companyId;


    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 用户id
     */
    private String userBaseId;

    private String userId;

    /**
     * 默认企业标志[0-否,1-是]
     */
    private Integer defaultCompanyFlag;

    /**
     * 操作员类型[1-管理员,2-员工]
     */
    private String levelType;

    /**
     *绑定时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date bindingTime;

    /**
     * 管理员手机号，脱敏
     */
    private String phone;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 10-正常 20-存在管理员过户
     */
    private String companyStatus = "10";
}
