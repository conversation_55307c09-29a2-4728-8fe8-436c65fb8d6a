package com.wanlianyida.ctpcore.partner.domain.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 10:13
 */
@Data
@ApiModel("公司法定代表人审核表")
public class UmCompanyLegalPersonAuditEntity {

    private Long id;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty("用户邮箱")
    private String userEmail;

    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    private Date birthday;

    @ApiModelProperty("性别[1-男,2-女]")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countyCode;

    @ApiModelProperty("县名称")
    private String countyName;

    @ApiModelProperty("详细地址")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issueAgency;

    @ApiModelProperty("开始时间")
    private Date startDate;

    @ApiModelProperty("截止日期")
    private Date untilDate;

    @ApiModelProperty("证件长期有效标志[11-是,21-否]")
    private Integer licenseLongValidFlag;

    @ApiModelProperty("审核状态[1-待审核,2-不通过,3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    @ApiModelProperty("申请来源[1-大宗平台,2-物流平台']")
    private String applySource;

    @ApiModelProperty("删除标志[0-未删除,1-已删除]")
    private Integer delFlag;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;

    private Integer versionCode;
}
