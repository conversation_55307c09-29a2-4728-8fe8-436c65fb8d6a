package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.condition.BdExpressCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.BdExpressEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.BdExpressRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class BdExpressDomainService {
    @Resource
    private BdExpressRepository repository;

    public Boolean add(BdExpressEntity entity) {
        // 名称去重校验
        Long count = repository.selectCountByCompanyName(entity.getCompanyName());
        if (count > 0) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.LOGISTICS_COMPANY_NAME_EXISTS);
        }
        return repository.add(entity);
    }

    public Boolean update(BdExpressEntity entity) {
        // 单查
        BdExpressEntity expressEntity = repository.selectById(entity.getId());

        if (ObjectUtil.isEmpty(expressEntity)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UPDATE_DATA_NOT_EXISTS);
        }
        if (ObjectUtil.notEqual(expressEntity.getCompanyName(), entity.getCompanyName())){
            // 名称去重校验
            Long count = repository.selectCountByCompanyName(entity.getCompanyName());
            if (count > 0) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.LOGISTICS_COMPANY_NAME_EXISTS);
            }
        }

        return repository.update(entity);
    }

    public Boolean batchDelete(List<Long> ids) {
        return repository.batchDelete(ids);
    }

    public PageInfo<BdExpressEntity> queryPage(PagingInfo<BdExpressCondition> conditionPage) {
        BdExpressCondition condition = conditionPage.getFilterModel();
        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        List<BdExpressEntity> list = repository.queryPage(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        PageInfo<BdExpressEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }
}
