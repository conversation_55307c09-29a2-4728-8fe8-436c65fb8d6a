package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.condition.UmInviteCodeCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmInviteCodeEntity;

/**
 * <p>
 * 分享码表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface UmInviteCodeRepository {

    UmInviteCodeEntity queryInviteCodeByCondition(UmInviteCodeCondition condition);

    Boolean saveInviteCode(UmInviteCodeEntity entity);

    Long countByInviteCode(String inviteCode);
    void updateUsedCount(UmInviteCodeEntity entity);
}
