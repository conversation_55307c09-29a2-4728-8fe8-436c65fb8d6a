package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.basemdm.api.inter.MdmOrgInter;
import com.wanlianyida.basemdm.api.model.query.UmOrgConditionQuery;
import com.wanlianyida.ctpcore.partner.domain.model.bo.OperatorBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmAccreditAuditCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmMemberCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.*;
import com.wanlianyida.ctpcore.partner.domain.repository.UmAccreditAuditRepository;
import com.wanlianyida.ctpcore.partner.domain.repository.UmEmployeeBindingRecordRepository;
import com.wanlianyida.ctpcore.partner.domain.repository.UmMemberRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.KafkaConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.CommonAuditStatusEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.PlatformEnums;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.po.UmDeptBizPO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.MemberConditionDTO;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.constant.CommonRedisConstants;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssuserauth.api.enums.UserAuthEnums;
import com.wanlianyida.partner.api.inter.UmCompanyInter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
@Slf4j
public class UmMemberDomainService {
    @Resource
    private UmMemberRepository repository;

    @Resource
    private MdmOrgInter mdmOrgInter;
    @Resource
    private RedisService redisService;
    @Resource
    private UmCompanyInter umCompanyInter;
    @Resource
    private UmEmployeeBindingRecordRepository employeeBindingHistoryRepository;
    @Resource
    private MqEventPublisher mqEventPublisher;
    @Resource
    private UmAccreditAuditRepository umAccreditAuditRepository;

    public Boolean add(UmMemberEntity umMemberEntity) {
        log.info("<<<<<<<新增用户，请求参数：{}<<<<<<<<<<<<<<", umMemberEntity);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        if (ObjectUtil.isEmpty(umMemberEntity.getCompanyId())){
            // 校验企业id
            validateCompanyId(tokenInfo.getCompanyId());
            umMemberEntity.setCompanyId(tokenInfo.getCompanyId());
        }

        // 处理部门id
        List<String> deptIds = umMemberEntity.getDeptIds();
        if (CollUtil.isNotEmpty(deptIds)) {
            // 验证部门和岗位是否存在
            validateDeptAndPost(deptIds, umMemberEntity.getPostId(), umMemberEntity.getCompanyId());
            umMemberEntity.setDepartment(String.join(",", deptIds));
        }

        // 发送mq
        sendNotificationIfNecessary(umMemberEntity, tokenInfo, UserAuthEnums.PermissionChangeSceneEnum.CREATE_COMPANY_OPERATOR.getCode());

        return repository.add(umMemberEntity);
    }

    public Boolean update(UmMemberEntity umMemberEntity) {
        if (ObjectUtil.isEmpty(umMemberEntity.getId())) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_ID_IS_EMPTY);
        }
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        // 校验企业id
        String companyId = tokenInfo.getCompanyId();
        validateCompanyId(companyId);
        umMemberEntity.setCompanyId(companyId);
        // 单查
        UmCompanyMemberEntity umCompanyMemberEntity = repository.queryById(Long.valueOf(umMemberEntity.getId()));
        if (ObjectUtil.isEmpty(umCompanyMemberEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.UPDATE_DATA_NOT_FOUND);
        }

        // 获取用户状态
        String userStatus = umMemberEntity.getUserStatus();
        if (ObjectUtil.equal(userStatus, PlatformEnums.ValidStatusEnum.NO.getCode())) {
            String levelType = umCompanyMemberEntity.getLevelType();
            // 校验是否为管理员
            if (ObjectUtil.equal(levelType, PlatformEnums.AccountLevelTypeEnum.ADMIN.getCode())) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ADMIN_ACCOUNT_CANNOT_BE_DISABLED);
            }
        }

        String bizScene = umMemberEntity.getBizScene();
        // 更新员工，则校验部门和岗位是否存在。发送mq,更新租户权限
        if (StrUtil.isNotEmpty(bizScene) && StrUtil.equals(bizScene,"20")){
            // 处理部门id
            List<String> deptIds = umMemberEntity.getDeptIds();

            if (CollUtil.isNotEmpty(deptIds)) {
                // 验证部门和岗位是否存在
                validateDeptAndPost(deptIds, umMemberEntity.getPostId(), umMemberEntity.getCompanyId());
                umMemberEntity.setDepartment(String.join(",", deptIds));
            }

            // 发送mq
            sendNotificationIfNecessary(umMemberEntity, tokenInfo, UserAuthEnums.PermissionChangeSceneEnum.UPDATE_COMPANY_OPERATOR.getCode());
        } else if (StrUtil.equals(bizScene,"50")) { // 管理员解绑员工
            // 校验被解绑用户当前的登录企业
            String userId = umCompanyMemberEntity.getUserId();
            // redisKey
            String redisKey = CommonRedisConstants.REDIS_TOKENINFO_KEY + userId;
            TokenInfo redisTokenInfoNow = JSONUtil.toBean(redisService.get(redisKey), TokenInfo.class);
            String companyIdNow = redisTokenInfoNow.getCompanyId();
            // 被解绑用户的当前正在登录的企业是被解绑的企业 则刷新redis
            if (StrUtil.equals(companyIdNow, companyId)) {
                TokenInfo redisTokenInfo = new TokenInfo();
                redisTokenInfo.setUsername(redisTokenInfoNow.getUsername());
                redisTokenInfo.setLoginName(redisTokenInfoNow.getLoginName());
                redisTokenInfo.setLoginId(redisTokenInfoNow.getLoginId());
                redisTokenInfo.setUserId(redisTokenInfoNow.getUserId());
                redisTokenInfo.setUserBaseId(redisTokenInfoNow.getUserId());

                // 刷新redis
                redisService.set(redisKey, JSONUtil.toJsonStr(redisTokenInfo));
            }


        }else if (StrUtil.equals(bizScene,"60")){ // 员工主动与公司解绑
            // 如果当前解绑的公司是当前登录公司，则刷新redis
            if (StrUtil.equals(umMemberEntity.getCompanyId(), tokenInfo.getCompanyId())) {
                String userId = umCompanyMemberEntity.getUserId();
                // redisKey
                String redisKey = CommonRedisConstants.REDIS_TOKENINFO_KEY + userId;
                TokenInfo redisTokenInfo = new TokenInfo();
                redisTokenInfo.setUserId(tokenInfo.getUserId());
                redisTokenInfo.setLoginId(tokenInfo.getLoginId());
                redisTokenInfo.setLoginName(tokenInfo.getLoginName());
                redisTokenInfo.setUsername(tokenInfo.getUsername());
                redisTokenInfo.setUserBaseId(tokenInfo.getUserId());
                // 刷新redis
                redisService.set(redisKey, JSONUtil.toJsonStr(redisTokenInfo));
            }
        }

        return repository.update(umMemberEntity);
    }

    public Boolean delete(UmMemberEntity umMemberEntity) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }
        // 校验企业id
        validateCompanyId(tokenInfo.getCompanyId());

        // 单查
        UmCompanyMemberEntity umCompanyMemberEntity = repository.queryById(Long.valueOf(umMemberEntity.getId()));
        String levelType = umCompanyMemberEntity.getLevelType();
        if (ObjectUtil.isNotEmpty(umCompanyMemberEntity)) {
            if (ObjectUtil.isNotEmpty(levelType) && ObjectUtil.equal(levelType, PlatformEnums.AccountLevelTypeEnum.ADMIN.getCode())) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ADMIN_ACCOUNT_CANNOT_BE_DELETED);
            }
        }
        // 发送mq
        sendNotificationIfNecessary(umMemberEntity, tokenInfo, UserAuthEnums.PermissionChangeSceneEnum.DELETE_COMPANY_OPERATOR.getCode());
        return repository.delete(umMemberEntity);
    }

    /**
     * 验证公司 ID
     *
     * @param companyId 公司 ID
     */
    private void validateCompanyId(String companyId) {
        if (ObjectUtil.isEmpty(companyId)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_ID_NOT_FOUND_ADMIN);
        }
    }

    public UmMemberEntity queryDetail(UmMemberCondition condition) {
        // 校验企业id
        validateCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        UmMemberEntity umMemberEntity = repository.queryDetail(condition);

        UmOrgConditionQuery query = new UmOrgConditionQuery();
        // 查询企业信息
        UmCompanyEntity umCompanyEntity = repository.selectById(Long.valueOf(umMemberEntity.getCompanyId()));
        if (ObjectUtil.isEmpty(umCompanyEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_INFO_NOT_FOUND);
        }
        String companyName = umCompanyEntity.getCompanyName();
        if (ObjectUtil.isNotEmpty(companyName)) {
            umMemberEntity.setCompanyName(companyName);
        }
        // 获取部门ID
        String department = umMemberEntity.getDepartment();
        if (ObjectUtil.isNotEmpty(department)) {
            List<String> departmentList = StrUtil.split(department, ",");
            List<UmDeptBizPO> umDeptPos = repository.selectDeptList(new HashSet<>(departmentList));
            if (CollUtil.isNotEmpty(umDeptPos)) {
                List<UmDeptEntity> umDeptEntities = BeanUtil.copyToList(umDeptPos, UmDeptEntity.class);
                umMemberEntity.setDeptList(umDeptEntities);
            }

        }
        return umMemberEntity;
    }

    public List<UmMemberEntity> queryByCondition(UmMemberCondition condition) {
        // 查询条件为空，则查询当前用户所属企业下的所有用户
        if (ObjectUtil.isEmpty(condition.getCompanyId())) {
            // 校验企业id
            validateCompanyId(JwtUtil.getTokenInfo().getCompanyId());
            condition.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        }

        List<UmMemberEntity> list = repository.queryByCondition(condition);
        return list;
    }

    /**
     * 验证部门和岗位是否存在
     *
     * @param deptIds 深度ID
     * @param postId  帖子id
     */
    private void validateDeptAndPost(List<String> deptIds, String postId, String companyId) {
        repository.validateDeptAndPost(deptIds, postId, companyId);
    }

    public MemberConditionDTO queryCompanyInfoByUserId(String userBaseId) {
        // 查询员工信息
        MemberConditionDTO memberConditionDTO = repository.queryByUserId(userBaseId);
        if (ObjectUtil.isEmpty(memberConditionDTO)) {
            return null;
        }

        // 查询企业信息
        UmCompanyEntity umCompanyEntity = repository.selectById(Long.valueOf(memberConditionDTO.getCompanyId()));
        if (ObjectUtil.isEmpty(umCompanyEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_INFO_NOT_FOUND);
        }
        String companyName = umCompanyEntity.getCompanyName();
        if (ObjectUtil.isEmpty(companyName)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_NAME_NOT_FOUND);
        }
        memberConditionDTO.setCompanyName(companyName);
        String socialCreditCode = umCompanyEntity.getSocialCreditCode();
        if (ObjectUtil.isEmpty(socialCreditCode)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_SOCIAL_CREDIT_CODE_NOT_FOUND);
        }
        memberConditionDTO.setLicenseNo(socialCreditCode);
        return memberConditionDTO;
    }

    public PageInfo<UmMemberEntity> queryPage(PagingInfo<UmMemberCondition> conditionPage) {
        UmMemberCondition condition = conditionPage.getFilterModel();
        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        List<UmMemberEntity> list = repository.queryPage(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        // 处理公司信息
        processCompanyInfo(list);
        // 处理部门信息
        processDeptInfo(list);

        PageInfo<UmMemberEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    /**
     * 处理公司信息
     *
     * @param list 列表
     */
    public void processCompanyInfo(List<UmMemberEntity> list) {
        // 提取公司ID并转换为Long类型
        List<Long> companyIdList = list.stream()
                .map(UmMemberEntity::getCompanyId)
                .map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(companyIdList)) {
            // 批量查询公司信息
            List<UmCompanyEntity> model = repository.batchQueryCompany(companyIdList);
            if (CollUtil.isEmpty(model)) {
                return;
            }
            Map<String, UmCompanyEntity> companyMap = model.stream()
                    .collect(Collectors.toMap(item -> String.valueOf(item.getId()), item -> item));
            if (CollUtil.isNotEmpty(companyMap)) {
                // 处理员工公司信息
                list.forEach(memberEntity -> {
                    String companyId = memberEntity.getCompanyId();
                    UmCompanyEntity umCompanyEntity = companyMap.get(companyId);
                    if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
                        memberEntity.setCompanyName(umCompanyEntity.getCompanyName());
                    }
                });
            }
        }
    }

    /**
     * 处理部门信息
     *
     * @param list 列表
     */
    public void processDeptInfo(List<UmMemberEntity> list) {
        // 提取部门ID
        Set<String> deptIdList = list.stream()
                .filter(entity -> ObjectUtil.isNotEmpty(entity.getDepartment()))
                .flatMap(entity -> Arrays.stream(entity.getDepartment().split(",")))
                .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(deptIdList)) {
            // 批量查询部门信息
            List<UmDeptBizPO> deptBizPOS = repository.selectDeptList(deptIdList);

            if (CollUtil.isNotEmpty(deptBizPOS)) {
                Map<Long, UmDeptBizPO> deptBizPOMap = CollUtil.isEmpty(deptBizPOS) ? Collections.emptyMap() :
                        deptBizPOS.stream().collect(Collectors.toMap(UmDeptBizPO::getId, Function.identity()));
                // 设置员工的部门信息
                setDeptListToMemberEntities(list, deptBizPOMap);
            }
        }
    }

    /**
     * 将部门信息设置到用户实体
     *
     * @param memberEntities 成员实体
     * @param deptBizPOMap   Dept biz Pomap
     */
    private void setDeptListToMemberEntities(List<UmMemberEntity> memberEntities, Map<Long, UmDeptBizPO> deptBizPOMap) {
        for (UmMemberEntity umMemberEntity : memberEntities) {
            String department = umMemberEntity.getDepartment();
            if (ObjectUtil.isNotEmpty(department)) {
                List<UmDeptEntity> deptList = new ArrayList<>();
                // 部门ID集合
                List<String> ids = StrUtil.split(department, ",");
                for (String id : ids) {
                    UmDeptBizPO umDeptBizPO = deptBizPOMap.get(Long.valueOf(id));
                    if (ObjectUtil.isNotEmpty(umDeptBizPO)) {
                        deptList.add(BeanUtil.copyProperties(umDeptBizPO, UmDeptEntity.class));
                    }
                }
                umMemberEntity.setDeptList(deptList);
            }
        }
    }

    public List<UmUserCacheEntity> queryAllUserInfo() {
        List<UmUserCacheEntity> umUserCacheEntities = repository.queryAllUserInfo();
        List<UmUserCacheEntity> userCacheEntityList = new ArrayList<>();
        // 更新缓存
        for (UmUserCacheEntity entity : umUserCacheEntities) {
            boolean hasKey = redisService.hasKey(CommonRedisConstants.REDIS_TOKENINFO_KEY + entity.getUserBaseId());
            if (hasKey) {
                umCompanyInter.redisUserCompany(entity.getUserBaseId());
                userCacheEntityList.add(entity);
            }
        }
        return userCacheEntityList;
    }

    public List<UmMemberEntity> queryListByCondition(UmMemberCondition condition) {
        List<UmMemberEntity> list = repository.queryPage(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        // 处理公司信息
        processCompanyInfo(list);
        // 处理部门信息
        processDeptInfo(list);

        // 查询管理员信息
        UmMemberEntity adminUmMemberEntity = list.stream().filter(item -> StrUtil.equals(item.getLevelType(), "1")).findFirst().orElse(null);
        if (ObjectUtil.isNotEmpty(adminUmMemberEntity)){
            String companyId = adminUmMemberEntity.getCompanyId();
            if (StrUtil.isNotEmpty(companyId)){
                // 查询授权委托书
                UmAccreditAuditCondition umAccreditAuditCondition = new UmAccreditAuditCondition();
                umAccreditAuditCondition.setCompanyId(companyId);
                umAccreditAuditCondition.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
                List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(umAccreditAuditCondition);
                if (CollUtil.isNotEmpty(umAccreditAuditEntities)){
                    adminUmMemberEntity.setCompanyStatus("20");
                }
            }
        }
        return list;
    }


    public void batchDeleteByOperatorIds(List<String> operatorIds) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.USER_NOT_LOGIN);
        }

        for (String operatorId : operatorIds) {
            UmMemberEntity umMemberEntity = new UmMemberEntity();
            umMemberEntity.setOperatorId(operatorId);
            // 发送mq
            sendNotificationIfNecessary(umMemberEntity, tokenInfo, UserAuthEnums.PermissionChangeSceneEnum.DELETE_COMPANY_OPERATOR.getCode());
        }

        repository.batchDeleteByOperatorIds(operatorIds);
    }

    public UmMemberEntity queryByOperatorId(UmMemberCondition condition) {
        return repository.queryByOperatorId(condition.getOperatorId());
    }

    /**
     * 发送mq消息
     * @param umMemberEntity
     * @param tokenInfo
     * @param scene
     */
    private void sendNotificationIfNecessary(UmMemberEntity umMemberEntity, TokenInfo tokenInfo,String scene) {
        try {
            // 发送mq 消息
            OperatorBO event = new OperatorBO();
            event.setId(Long.valueOf(umMemberEntity.getOperatorId()));
            event.setCompanyId(tokenInfo.getCompanyId());
            event.setCompanyName(tokenInfo.getCompanyName());
            event.setSelPermissionIdList(umMemberEntity.getSelPermissionIdList());
            event.setScene(scene);
            MqEventMessage.EventMessage<OperatorBO> operatorBOEventMessage =
                    MqEventMessage.buildEventMessage(KafkaConstants.CTP_OPERATOR_STATUS_CHANGE_NOTICE, event);
            mqEventPublisher.publishNormalMessage(KafkaConstants.CTP_OPERATOR_STATUS_CHANGE_NOTICE, operatorBOEventMessage);
            log.info("发送mq消息成功:{},消息场景：{}，", operatorBOEventMessage, scene);
        } catch (Exception e) {
            log.error("发送mq消息失败", e);
        }
    }

    public List<UmMemberEntity> queryByCompanyIds(UmMemberCondition condition) {
        // 查询条件为空，则查询当前用户所属企业下的所有用户
        if (CollUtil.isEmpty(condition.getCompanyIds())) {
            // 校验企业id
            validateCompanyId(JwtUtil.getTokenInfo().getCompanyId());
            condition.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        }

        List<UmMemberEntity> list = repository.queryByCondition(condition);
        return list;
    }
}
