package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.ctpcore.partner.application.assembler.UmAcctCertApplyAssembler;
import com.wanlianyida.ctpcore.partner.domain.model.bo.StatusStatisticsBO;
import com.wanlianyida.ctpcore.partner.domain.model.bo.UmAcctCertApplyBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.AcctCertApplyCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmAcctCertApplyUpdateCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmAcctCertApplyEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.UmAcctCertApplyRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.CommonAuditStatusEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.po.UmAcctCertApplyPO;
import com.wanlianyida.ctpcore.partner.infrastructure.util.BizLogUtilService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.PartnerException;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmAcctCertAuditCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.AcctCertStatusStatisticsDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmAcctCertApplyDetailDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UpdateAcctCertDTO;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcommon.enums.CommonCodeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 账户证书审核
 * <AUTHOR>
 */
@Slf4j
@Service
public class UmAcctCertApplyDomainService {

    @Resource
    private UmAcctCertApplyRepository umAcctCertApplyRepository;

    @Resource
    private UploadExchangeService uploadExchangeService;

    public List<UmAcctCertApplyEntity> pageList(AcctCertApplyCondition condition) {
        return umAcctCertApplyRepository.pageList(condition);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> createAcctCertApply(UmAcctCertApplyBO crtApply) {
        // 根据企业信用代码查询证书审核记录
        UmAcctCertApplyBO auditInfo = umAcctCertApplyRepository.queryBySocialCreditCode(crtApply.getSocialCreditCode());
        // 获取登录信息
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String userBaseId = tokenInfo.getUserBaseId();
        String username = tokenInfo.getUsername();
        crtApply.setCreatorId(userBaseId);
        crtApply.setCreatorName(username);
        UmAcctCertApplyEntity applyEntity = UmAcctCertApplyAssembler.buildUmAcctCertApplyEntity(crtApply);
        // 保存用户证书审核记录
        umAcctCertApplyRepository.createAcctCertApply(BeanUtil.toBean(applyEntity, UmAcctCertApplyPO.class));
        // 逻辑删除原有的证书审核记录
        if (ObjectUtils.isNotEmpty(auditInfo)) {
            UmAcctCertApplyUpdateCondition condition = new UmAcctCertApplyUpdateCondition();
            condition.setId(auditInfo.getId());
            condition.setDelFlag(1);
            condition.setUpdaterId(userBaseId);
            condition.setUpdaterName(username);
            umAcctCertApplyRepository.updateByPrimaryKey(condition);
        }
        return ResultMode.success(crtApply.getSocialCreditCode());
    }

    public ResultMode<UmAcctCertApplyDetailDTO> queryAcctCertApplyDetail(AcctCertApplyCondition condition) {
        UmAcctCertApplyBO applyBO = umAcctCertApplyRepository.queryByPrimaryKey(condition.getId());
        // 转换文件url
        dealUrl(applyBO);
        return ResultMode.success(BeanUtil.toBean(applyBO, UmAcctCertApplyDetailDTO.class));
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultMode<UpdateAcctCertDTO> auditAcctCert(UmAcctCertAuditCommand command) {
        // 查询审核记录
        UmAcctCertApplyBO apply = umAcctCertApplyRepository.queryByPrimaryKey(command.getId());
        if (ObjectUtils.isEmpty(apply)) {
            throw new PartnerException(CtpCorePartnerExceptionEnum.AUDIT_RECORD_NOT_EXISTS);
        }
        if (!CommonAuditStatusEnum.WAITING.getCode().equals(apply.getAuditStatus())) {
            throw new PartnerException(CtpCorePartnerExceptionEnum.AUDIT_RECORD_ALREADY_AUDIT);
        }
        if (CommonAuditStatusEnum.REJECT.getCode().equals(command.getAuditStatus()) && StringUtils.isBlank(command.getRejectReason())) {
            throw new PartnerException(CommonCodeEnum.USER_ERROR_PARAM_NOTNULL, "驳回原因不能为空");
        }
        // 获取登录信息
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        UmAcctCertApplyUpdateCondition condition = BeanUtil.toBean(command, UmAcctCertApplyUpdateCondition.class);
        condition.setAuditorId(tokenInfo.getUserBaseId());
        condition.setAuditorName(tokenInfo.getUsername());
        condition.setAuditorAccount(tokenInfo.getLoginName());
        condition.setAuditTime(new Date());
        condition.setUpdaterId(tokenInfo.getUserBaseId());
        condition.setUpdaterName(tokenInfo.getUsername());
        if (CommonAuditStatusEnum.PASS.getCode().equals(condition.getAuditStatus())) {
            condition.setRejectReason("");
        }
        umAcctCertApplyRepository.updateByPrimaryKey(condition);
        // 记录操作日志
        condition.setSocialCreditCode(apply.getSocialCreditCode());
        BizLogUtilService.sendLogBizMsg(condition.getSocialCreditCode(), condition.getAuditTime(),condition.getAuditStatus(),
                condition.getRejectReason(), LogOperationRecordEnum.CTP_PARTNER_AUDIT_ACCT_CERT_AUDIT);
        // 响应信息用于同步用户证书上传状态
        UpdateAcctCertDTO resultDto = UmAcctCertApplyAssembler.buildUpdateAcctCertDTO(apply, condition);
        return ResultMode.success(resultDto);
    }

    public ResultMode<AcctCertStatusStatisticsDTO> queryStatusStatistics(AcctCertApplyCondition condition) {
        // 查询账户证书各审核状态数量
        Map<Integer, StatusStatisticsBO> countMap = umAcctCertApplyRepository.queryStatusStatistics(condition);
        AcctCertStatusStatisticsDTO dto = UmAcctCertApplyAssembler.buildAcctCertStatusStatisticsDTO(countMap);
        return ResultMode.success(dto);
    }

    /**
     * 转换文件url
     * @param applyBO
     */
    private void dealUrl(UmAcctCertApplyBO applyBO){
        // 附件url转换
        if(ObjectUtils.isEmpty(applyBO)){
            return;
        }
        if(StringUtils.isBlank(applyBO.getPoaFileUrl())){
            return;
        }
        Map<String, String> urlMap = uploadExchangeService.convertUrlList(Arrays.asList(applyBO.getPoaFileUrl()));
        if(ObjectUtils.isEmpty(urlMap)){
            return;
        }
        if (StringUtils.isNotBlank(urlMap.get(applyBO.getPoaFileUrl()))) {
            applyBO.setPoaFileUrl(urlMap.get(applyBO.getPoaFileUrl()));
        }
    }
}
