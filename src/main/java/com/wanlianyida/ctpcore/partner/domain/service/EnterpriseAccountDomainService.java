package com.wanlianyida.ctpcore.partner.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.wanlianyida.basemdm.api.model.command.MdmCompanyApplyCommand;
import com.wanlianyida.basemdm.api.model.command.MdmOperatorCommand;
import com.wanlianyida.basemdm.api.model.dto.*;
import com.wanlianyida.basemdm.api.model.query.*;
import com.wanlianyida.baseots.api.model.dto.OtsAddressDetailDTO;
import com.wanlianyida.baseots.api.model.dto.OtsCompanyCheckDataDTO;
import com.wanlianyida.baseots.api.model.dto.OtsIdentityCardOcrDTO;
import com.wanlianyida.baseots.api.model.dto.TwoFactorsDTO;
import com.wanlianyida.baseots.api.model.query.Certification2FactorsQuery;
import com.wanlianyida.baseots.api.model.query.CompanyDetailQuery;
import com.wanlianyida.baseots.api.model.query.DirectionOcrQuery;
import com.wanlianyida.baseots.api.model.query.OtsEnums;
import com.wanlianyida.ctpcore.partner.domain.model.bo.CreateCompanyBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.*;
import com.wanlianyida.ctpcore.partner.domain.model.entity.*;
import com.wanlianyida.ctpcore.partner.domain.repository.*;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.CommonConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.KafkaConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.constant.ServiceNameConstants;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.*;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerException;
import com.wanlianyida.ctpcore.partner.infrastructure.exception.CtpCorePartnerExceptionEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.OtsExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.BizLogUtilService;
import com.wanlianyida.ctpcore.partner.infrastructure.util.CommonUtil;
import com.wanlianyida.ctpcore.partner.infrastructure.util.GetUserIdUtil;
import com.wanlianyida.ctpcore.partner.infrastructure.util.OtsUtil;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.BusinessLicenseInfoCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.IdCardOcrCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.command.UmCompanyLegalPersonAuditAddCommand;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.*;
import com.wanlianyida.ctpcore.partner.interfaces.model.query.UmEmployeeBindingRecordQuery;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.IdUtil;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnterpriseAccountDomainService {

    @Resource
    private UmEmployeeBindingRecordRepository employeeBindingHistoryRepository;

    @Resource
    private UmCompanyRepository umCompanyRepository;

    @Resource
    private UmRoleRepository umRoleRepository;

    @Resource
    private UmAccreditAuditRepository umAccreditAuditRepository;

    @Resource
    private MdmExchangeService mdmExchangeService;

    @Resource
    private OtsExchangeService otsExchangeService;

    @Resource
    private UmCompanyMemberRepository umCompanyMemberRepository;

    @Resource
    private IdUtil idUtil;

    @Resource
    private UmUserAuthAuditRepository umUserAuthAuditRepository;

    @Resource
    private IDictValueRepository dictValueRepository;

    @Resource
    private UmCompanyBusinessLicenseRepository umCompanyBusinessLicenseRepository;

    @Resource
    MqEventPublisher mqEventPublisher;

    @Resource
    private UploadExchangeService uploadExchangeService;

    @Resource
    private UmCompanyLegalPersonAuditRepository umCompanyLegalPersonAuditRepository;

    @Resource
    private GetUserIdUtil getUserIdUtil;

    @Resource
    private OtsUtil otsUtil;

    @Resource
    private UmCompanyConfigRepository umCompanyConfigRepository;

    public List<UmEmployeeBindingRecordEntity> queryBindingHistoryCondition(UmEmployeeBindingRecordCondition condition) {
        return employeeBindingHistoryRepository.queryCondition(condition);
    }

    public Integer countPendingInvitedEmployees(UmEmployeeBindingRecordQuery query) {
        return employeeBindingHistoryRepository.countPendingInvitedEmployees(query);
    }

    public int countBindingHistoryCondition(UmEmployeeBindingRecordCondition condition) {
        return employeeBindingHistoryRepository.countCondition(condition);
    }

    public String createCompany(CreateCompanyBO bo) {
        // 1. 校验用户实名认证状态
        checkUserRealNameAuthentication(bo);

        // 2. 校验企业是否存在待审核的授权委托书
        checkPendingAccreditAudit(bo.getSocialCreditCode());

        // 3. 校验企业是否存在待审核的工商信息
        checkPendingBusinessRegistration(bo.getSocialCreditCode());

        // 4. 校验企业是否已在商贸注册
        checkCompanyInCommerceTrade(bo.getSocialCreditCode());

        // 5. 校验企业是否在主数据中注册
        MdmCompanyInfoDTO mdmCompanyInfo = handleMainDataRegistration(bo);

        // 6、调用ots检验公司名称、信用代码、法人名称是否一致
        checkCompanyInfoConsistency(bo);

        // 7、 企业法人二要素校验
        checkLegalPersonTwoElements(bo);

        // 8. 组装数据
        dataAssemble(bo, mdmCompanyInfo);

        // 9. 执行企业创建流程
        String companyId = SpringUtil.getBean(EnterpriseAccountDomainService.class).executeCompanyCreationProcess(bo);

        // 10、保存操作日志
        saveLog(bo, companyId);

        return companyId;
    }

    /**
     * 调用ots检验公司名称、信用代码、法人名称是否一致
     *
     * @param bo
     */
    private void checkCompanyInfoConsistency(CreateCompanyBO bo) {
        String configValue = null;
        UmCompanyConfigCondition condition = new UmCompanyConfigCondition();
        condition.setCompanyId(CommonConstants.SYSTEM_NAME);
        List<UmCompanyConfigEntity> umCompanyConfigEntities = umCompanyConfigRepository.queryList(condition);
        if (CollUtil.isNotEmpty(umCompanyConfigEntities)) {
            UmCompanyConfigEntity first = IterUtil.getFirst(umCompanyConfigEntities);
            configValue = first.getConfigValue();
        }

        // 未查询到配置或20(关闭状态) 不进行数据校验
        if (StrUtil.isEmpty(configValue) || CompanyAudtiConfigEnum.CLOSE.getCode().equals(configValue)) {
            return;
        }

        Boolean isRegisteredInMainData = bo.getIsRegisteredInMainData();
        CompanyDetailQuery companyDetailQuery = new CompanyDetailQuery();
        String companyNameForAudit = bo.getCompanyName();
        String socialCreditCodeForAudit = bo.getSocialCreditCode();
        if (isRegisteredInMainData) {

            companyDetailQuery.setEntName(companyNameForAudit);
        } else {
            companyDetailQuery.setCreditCode(socialCreditCodeForAudit);
        }

        ResultMode<OtsCompanyCheckDataDTO> dtoResultMode = otsExchangeService.companyDetailCheck(companyDetailQuery);
        if (dtoResultMode.isSucceed() && dtoResultMode.getModel() != null) {
            OtsCompanyCheckDataDTO otsCompanyCheckDataDTO = dtoResultMode.getModel();
            if (ObjectUtil.isEmpty(otsCompanyCheckDataDTO)) {
                throw new CtpCorePartnerException("查询到您的营业执照信息填写可能有误，无法提交");
            }
            compareCompanyInfo(bo, otsCompanyCheckDataDTO);
        }
    }

    /**
     * 比对企业本地信息与OTS返回的信息是否一致
     *
     * @param bo      创建企业的业务对象
     * @param otsData OTS返回的数据
     */
    private void compareCompanyInfo(CreateCompanyBO bo, OtsCompanyCheckDataDTO otsData) {
        String localName = bo.getCompanyName();
        String localCreditCode = bo.getSocialCreditCode();
        String localLegalPerson = bo.getMdmCompanyApplyCommand().getLegalPerson();

        String remoteName = otsData.getCompanyName();
        String remoteCreditCode = otsData.getCreditNo();
        String remoteLegalPerson = otsData.getLegalPerson();

        if (!localName.equals(remoteName)
                || !localCreditCode.equals(remoteCreditCode)
                || !localLegalPerson.equals(remoteLegalPerson)) {
            log.warn("企业信息不一致：本地数据[名称={}, 信用代码={}, 法人={}]，OTS数据[名称={}, 信用代码={}, 法人={}]",
                    localName, localCreditCode, localLegalPerson, remoteName, remoteCreditCode, remoteLegalPerson);
            throw new CtpCorePartnerException("查询到您的营业执照信息填写可能有误，无法提交");
        }

        log.info("企业信息一致性验证通过，名称：{}, 信用代码：{}, 法人：{}", localName, localCreditCode, localLegalPerson);
    }

    /**
     * 企业法人二要素校验
     *
     * @param bo
     */
    private void checkLegalPersonTwoElements(CreateCompanyBO bo) {
        UmCompanyLegalPersonAuditEntity umCompanyLegalPersonAuditEntity = bo.getUmCompanyLegalPersonAuditEntity();
        if (ObjectUtil.isNotEmpty(umCompanyLegalPersonAuditEntity)) {
            String userName = umCompanyLegalPersonAuditEntity.getUserName();
            String idCardNo = umCompanyLegalPersonAuditEntity.getIdCardNo();
            if (ObjectUtil.isNotEmpty(userName) && ObjectUtil.isNotEmpty(idCardNo)) {
                Certification2FactorsQuery certification2FactorsQuery = new Certification2FactorsQuery();
                certification2FactorsQuery.setName(userName);
                certification2FactorsQuery.setIdcard(idCardNo);
                // 2、二要素识别
                ResultMode<TwoFactorsDTO> twoFactorsDTOResultMode = otsExchangeService.twoFactorsCheck(certification2FactorsQuery);
                if (!twoFactorsDTOResultMode.isSucceed()) {
                    throw new RuntimeException("企业法人信息二要素识别失败");
                }
                TwoFactorsDTO twoFactorsDTO = twoFactorsDTOResultMode.getModel();
                if (ObjectUtil.isEmpty(twoFactorsDTO)) {
                    throw new RuntimeException("企业法人信息二要素识别结果为空，请稍后重试");
                }
                if (!twoFactorsDTO.getResult().equals("1")) {
                    // TODO-zz 测试放开
                    throw new RuntimeException("法定代表人姓名与证件号码不匹配");
                }
            }
        }
    }

    /**
     * 保存操作日志
     *
     * @param bo
     * @param companyId
     */
    private void saveLog(CreateCompanyBO bo, String companyId) {
        // 根据企业id查询企业信息
        String socialCreditCode = null;
        UmCompanyEntity umCompanyEntity = umCompanyRepository.queryById(companyId);
        if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
            socialCreditCode = umCompanyEntity.getSocialCreditCode();
        }
        // 记录授权委托书日志 CTP10
        if (ObjectUtil.isNotEmpty(socialCreditCode)) {
            BizLogUtilService.sendLogBizMsg(socialCreditCode, LogOperationRecordEnum.CTP_PARTNER_ACCREDIT_AUDIT,
                    LogOperationRecordEnum.OperateEnum.CREATE_AN_ENTERPRISE, "用户创建企业，新增授权委托书审核");
        }
        // 记录管理员变更日志 CTP50
        if (StrUtil.isNotEmpty(socialCreditCode)) {
            BizLogUtilService.sendLogBizMsg(socialCreditCode, LogOperationRecordEnum.CTP_PARTNER_MANAGER_AUDIT,
                    LogOperationRecordEnum.OperateEnum.CREATE_AN_ENTERPRISE, "创建企业");
        }
        Boolean isRegisteredInMainData = bo.getIsRegisteredInMainData();

        // 如果已在主数据注册过，则不记录操作日志
        if (!isRegisteredInMainData) {
            // 记录营业执照日志 CTP30
            BizLogUtilService.sendLogBizMsg(socialCreditCode, LogOperationRecordEnum.CTP_PARTNER_LICENSE_AUDIT,
                    LogOperationRecordEnum.OperateEnum.CREATE_AN_ENTERPRISE, "用户创建企业，新增工商信息审核");
        }
    }

    /**
     * 校验用户实名认证状态
     */
    private void checkUserRealNameAuthentication(CreateCompanyBO bo) {
        String userId = getUserIdUtil.getUserId();

        UmUserAuthAuditEntity auditEntity = umUserAuthAuditRepository.queryByUserId(userId, CommonAuditStatusEnum.PASS.getCode());
        if (ObjectUtil.isEmpty(auditEntity)) {
            throw new CtpCorePartnerException("用户未在平台实名认证");
        }

        // 校验被授权人的姓名与身份证号与当前操作人的姓名与身份证号是否一致
        UmAccreditAuditEntity accreditAuditEntity = bo.getAccreditAuditEntity();
        String transferUserName = accreditAuditEntity.getTransferUserName();
        String transferIdCardNo = accreditAuditEntity.getTransferIdCardNo();

        if (!StrUtil.equals(transferUserName, auditEntity.getUserName()) || !StrUtil.equals(transferIdCardNo, auditEntity.getIdCardNo())) {
            throw new CtpCorePartnerException("被授权人姓名或身份证号与操作人姓名与身份证号不一致");
        }
    }

    /**
     * 校验企业是否存在待审核的授权委托书
     *
     * @param socialCreditCode
     */
    private void checkPendingAccreditAudit(String socialCreditCode) {
        if (StrUtil.isEmpty(socialCreditCode)) {
            throw new CtpCorePartnerException("socialCreditCode不能为空");
        }
        UmAccreditAuditCondition condition = new UmAccreditAuditCondition();
        condition.setSocialCreditCode(socialCreditCode);
        condition.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        List<UmAccreditAuditEntity> audits = umAccreditAuditRepository.queryCondition(condition);
        if (CollUtil.isNotEmpty(audits)) {
            throw new CtpCorePartnerException("当前企业已存在待审核的授权委托书");
        }
    }

    /**
     * 校验企业是否存在待审核的工商信息
     *
     * @param socialCreditCode
     */
    private void checkPendingBusinessRegistration(String socialCreditCode) {
        MdmCompanyApplyQuery query = new MdmCompanyApplyQuery();
        query.setLicenseNo(socialCreditCode);
        query.setApplySource("1");
        query.setAptitudeStatus("1");
        ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(query);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(resultMode.getCode(), resultMode.getMessage());
        }
        List<MdmCompanyApplyDTO> model = resultMode.getModel();
        if (CollUtil.isNotEmpty(model)) {
            throw new CtpCorePartnerException("当前企业已存在待审核的工商信息");
        }
    }

    /**
     * 校验企业是否已在商贸注册
     *
     * @param socialCreditCode
     */
    private void checkCompanyInCommerceTrade(String socialCreditCode) {
        UmCompanyCondition condition = new UmCompanyCondition();
        condition.setSocialCreditCode(socialCreditCode);
        List<UmCompanyEntity> companyList = umCompanyRepository.queryCompanyStatus(condition);
        if (CollUtil.isEmpty(companyList)) {
            return;
        }
        // 校验状态是否在审核中 或审核通过
        for (UmCompanyEntity entity : companyList) {
            String aptitudeStatus = entity.getAptitudeStatus();
            if (StrUtil.equals(aptitudeStatus, "1") || StrUtil.equals(aptitudeStatus, "3")) {
                throw new CtpCorePartnerException("当前企业已存在商贸注册");
            }
        }
    }

    /**
     * 处理主数据注册
     *
     * @param bo
     */
    private MdmCompanyInfoDTO handleMainDataRegistration(CreateCompanyBO bo) {
        String socialCreditCode = bo.getSocialCreditCode();
        MdmCompanyInfoDTO mainDataCompany = mdmExchangeService.getMdmCmdCompanyByLicenseNo(socialCreditCode);
        if (ObjectUtil.isNotEmpty(mainDataCompany)) {
            MdmCompanyApplyCommand command = new MdmCompanyApplyCommand();
            BeanUtil.copyProperties(mainDataCompany, command, "creatorId", "updateId", "remark", "reviewReason");
            command.setForntFileUrl(bo.getMdmCompanyApplyCommand().getForntFileUrl());
            command.setLicenseName(bo.getMdmCompanyApplyCommand().getLicenseName());
            command.setLegalPerson(bo.getMdmCompanyApplyCommand().getLegalPerson());
            bo.setMdmCompanyApplyCommand(command);
            bo.setIsRegisteredInMainData(true);
        }
        return mainDataCompany;
    }

    /**
     * 执行企业创建流程
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String executeCompanyCreationProcess(CreateCompanyBO bo) {
        // 1. 新增企业信息表记录
        String companyId = addCompany(bo);
        bo.setCompanyId(companyId);

        // 2. 新增企业角色表数据
        addRole(bo);

        // 3. 新增授权委托书审核记录
        addAccreditAudit(bo);

        // 4. 新增主数据企业资质表审核记录
        addCompanyAptitudeAudit(bo);

        // 5. 新增企业营业执照表数据
        addCompanyBusinessLicense(bo);

        // 6. 新增企业法人审核记录
        addCompanyLegalPersonAudit(bo);

        return companyId;
    }

    private void addCompanyLegalPersonAudit(CreateCompanyBO bo) {
        String socialCreditCode = bo.getSocialCreditCode();
        if (StrUtil.isNotEmpty(socialCreditCode)) {
            // 更新企业历史法人审核记录为已删除
            umCompanyLegalPersonAuditRepository.deleteBySocialCreditCode(socialCreditCode);
            updateHistoryAccreditAudit(socialCreditCode);
        }

        UmCompanyLegalPersonAuditEntity entity = bo.getUmCompanyLegalPersonAuditEntity();

        if (ObjectUtil.isNotEmpty(entity)) {
            umCompanyLegalPersonAuditRepository.insert(entity);
        }
    }

    /**
     * 新增企业角色
     *
     * @param bo
     */
    public void addRole(CreateCompanyBO bo) {
        String companyId = bo.getCompanyId();
        if (StrUtil.isNotEmpty(companyId)) {
            // 删除企业角色信息
            umRoleRepository.deleteByCompanyId(companyId);
        }

        List<UmRoleEntity> umRoleEntityList = bo.getUmRoleEntityList();
        if (CollUtil.isNotEmpty(umRoleEntityList)) {
            umRoleEntityList.forEach(umRoleEntity -> {
                umRoleEntity.setCompanyId(companyId);
                umRoleEntity.setCompanyName(bo.getCompanyName());
                umRoleRepository.add(umRoleEntity);
            });
        }
        log.info("新增企业角色成功");
    }

    /**
     * 新增授权委托书审核记录
     *
     * @param bo
     */
    public void addAccreditAudit(CreateCompanyBO bo) {
        String companyId = bo.getCompanyId();

        // 更新公司历史审核数据为已删除
        updateHistoryAccreditAudit(companyId);

        UmAccreditAuditEntity accreditAuditEntity = bo.getAccreditAuditEntity();
        accreditAuditEntity.setUserName(bo.getUserName());
        accreditAuditEntity.setCreatorId(bo.getUserId());
        accreditAuditEntity.setUpdaterId(bo.getUserId());
        if (ObjectUtil.isNotEmpty(accreditAuditEntity)) {
            accreditAuditEntity.setCompanyId(companyId);
            umAccreditAuditRepository.insert(accreditAuditEntity);
        }
        log.info("新增授权委托书审核记录成功");

    }

    /**
     * 新增主数据企业资质审核
     *
     * @param bo
     */
    public void addCompanyAptitudeAudit(CreateCompanyBO bo) {
        // 更新历史审核记录为已删除
        String socialCreditCode = bo.getSocialCreditCode();
        updateHistoryCompanyAptitudeAudit(socialCreditCode);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        MdmCompanyApplyCommand companyApplyCommand = bo.getMdmCompanyApplyCommand();
        String manageScope = companyApplyCommand.getManageScope();
        companyApplyCommand.setManageScope(StrUtil.isNotEmpty(manageScope) ? manageScope : "");
        companyApplyCommand.setApplyUsername(bo.getUserName());
        companyApplyCommand.setApplySource(CmdEnums.ApplySourcesEnum.BULK_TRADE_PLATFORM.getCode());
        companyApplyCommand.setApplyLoginName(tokenInfo.getLoginName());
        companyApplyCommand.setApplyUsername(tokenInfo.getUsername());
        Long model = null;
        if (ObjectUtil.isNotEmpty(companyApplyCommand)) {
            ResultMode<Long> longResultMode = mdmExchangeService.insertCompanyApply(companyApplyCommand);
            if (!longResultMode.isSucceed()) {
                throw new CtpCorePartnerException(longResultMode.getCode(), longResultMode.getMessage());
            }
            model = longResultMode.getModel();
            bo.getMdmCompanyApplyCommand().setId(model);
            log.info("主数据企业资质审核记录新增成功,主数据企业资质审核记录ID:{}", model);
        }
//        Boolean isRegisteredInMainData = bo.getIsRegisteredInMainData();

//        // 如果在主数据中已经注册过。则走自动审核，不需要人工审核
//        if (isRegisteredInMainData) {
//            // 发送mq消息
//            sendMessage(String.valueOf(model), bo);
//        }
        log.info("新增主数据企业资质审核成功");
    }

    /**
     * 新增企业信息
     *
     * @param bo
     */
    public String addCompany(CreateCompanyBO bo) {
        UmCompanyEntity umCompanyEntity = bo.getUmCompanyEntity();

        // 删除审核驳回的历史数据
        String socialCreditCode = umCompanyEntity.getSocialCreditCode();
        umCompanyRepository.deleteBySocialCreditCode(socialCreditCode);

        if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
            umCompanyRepository.createCompany(umCompanyEntity);
        }
        log.info("新增企业信息成功");
        return String.valueOf(umCompanyEntity.getId());
    }


    /**
     * 新增营业执照
     *
     * @param bo
     */
    public void addCompanyBusinessLicense(CreateCompanyBO bo) {
        UmCompanyBusinessLicenseEntity umCompanyBusinessLicenseEntity = bo.getUmCompanyBusinessLicenseEntity();
        umCompanyBusinessLicenseEntity.setCompanyId(bo.getCompanyId());
        if (ObjectUtil.isNotEmpty(umCompanyBusinessLicenseEntity)) {
            int count = umCompanyBusinessLicenseRepository.insertSelective(umCompanyBusinessLicenseEntity);
            if (count > 0) {
                log.info("新增企业营业执照成功");
            } else {
                log.info("新增企业营业执照失败");
            }
        }
    }

    /**
     * 数据组装
     *
     * @param bo
     * @param mdmCompanyInfo
     */
    public void dataAssemble(CreateCompanyBO bo, MdmCompanyInfoDTO mdmCompanyInfo) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        // 用户Id
        String userId = getUserIdUtil.getUserId();

        bo.setUserId(userId);
        // 企业授权委托书
        UmAccreditAuditEntity accreditAuditEntity = bo.getAccreditAuditEntity();
        // 文件路径处理
        accreditAuditEntity.setFileUrl(StrUtil.subBefore(accreditAuditEntity.getFileUrl(), "?", true));

        // 企业工商信息
        MdmCompanyApplyCommand companyApplyCommand = bo.getMdmCompanyApplyCommand();
        companyApplyCommand.setForntFileUrl(StrUtil.subBefore(companyApplyCommand.getForntFileUrl(), "?", true));
        if (ObjectUtil.isNotEmpty(mdmCompanyInfo)) {
            companyApplyCommand.setManageScope(mdmCompanyInfo.getManageScope());
            companyApplyCommand.setCompanyType(mdmCompanyInfo.getCompanyType());
            companyApplyCommand.setRegisteredCapital(mdmCompanyInfo.getRegisteredCapital());
            companyApplyCommand.setProvince(mdmCompanyInfo.getProvince());
            companyApplyCommand.setProvinceName(mdmCompanyInfo.getProvinceName());
            companyApplyCommand.setCity(mdmCompanyInfo.getCity());
            companyApplyCommand.setCityName(mdmCompanyInfo.getCityName());
            companyApplyCommand.setArea(mdmCompanyInfo.getArea());
            companyApplyCommand.setAreaName(mdmCompanyInfo.getAreaName());
            companyApplyCommand.setStreet(mdmCompanyInfo.getStreet());
            companyApplyCommand.setStreetName(mdmCompanyInfo.getStreetName());
            companyApplyCommand.setAddressDetail(mdmCompanyInfo.getAddressDetail());
//            companyApplyCommand.setForntFileUrl(mdmCompanyInfo.getFrontFileUrl());
            companyApplyCommand.setCreatorId(userId);
            companyApplyCommand.setUpdaterId(userId);
            companyApplyCommand.setFoundDate(mdmCompanyInfo.getFoundDate());
        }
        // 企业角色
        List<UmRoleEntity> umRoleEntityList = bo.getUmRoleEntityList();

        // 1、获取当前登录人联系方式
        String contactPhone = getContactPhone(userId);
        bo.setPhone(contactPhone);
        bo.setSocialCreditCode(companyApplyCommand.getLicenseNo());

        // 2、填充企业信息
        UmCompanyEntity umCompanyEntity = assembleCompanyInfo(bo, mdmCompanyInfo,userId);
        bo.setUmCompanyEntity(umCompanyEntity);

        // 3、填充授权委托书信息
        accreditAuditEntity.setSourceType(10);
        accreditAuditEntity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        accreditAuditEntity.setCompanyName(bo.getCompanyName());
        accreditAuditEntity.setUserId(bo.getUserId());
        accreditAuditEntity.setSocialCreditCode(bo.getSocialCreditCode());
        accreditAuditEntity.setTransferUserId(bo.getUserId());
        accreditAuditEntity.setTransferMobile(bo.getPhone());
        accreditAuditEntity.setTransferAccount(tokenInfo.getLoginName());
        accreditAuditEntity.setTransferIdCardNo(bo.getIdCard());
        accreditAuditEntity.setBizType(AcreditBizTypeEnum.ADD.getCode());
        accreditAuditEntity.setLatestDataFlag(1);
        // 4、填充企业角色信息
        if (CollUtil.isNotEmpty(umRoleEntityList)) {
            umRoleEntityList.forEach(umRoleEntity -> {
                umRoleEntity.setCompanyName(bo.getCompanyName());
            });
        }

        if (!bo.getIsRegisteredInMainData()) {
            // 5、组装主数据企业资质审核信息
            syncMsgToCmdApply(bo, tokenInfo);
        }

        // 6、 组装ctp营业执照信息表
        UmCompanyBusinessLicenseEntity umCompanyBusinessLicenseEntity = new UmCompanyBusinessLicenseEntity();
        umCompanyBusinessLicenseEntity.setId(idUtil.generateId(ServiceNameConstants.CORE_PARTNER));
        umCompanyBusinessLicenseEntity.setCompanyId(bo.getCompanyId());
        umCompanyBusinessLicenseEntity.setLicenseNo(bo.getSocialCreditCode());
        umCompanyBusinessLicenseEntity.setLegalPerson(bo.getMdmCompanyApplyCommand().getLegalPerson());
        umCompanyBusinessLicenseEntity.setContacts(bo.getUserName());
        umCompanyBusinessLicenseEntity.setPhone(bo.getPhone());
        bo.setUmCompanyBusinessLicenseEntity(umCompanyBusinessLicenseEntity);

        // 处理企业法人审核信息
        UmCompanyLegalPersonAuditEntity umCompanyLegalPersonAuditEntity = bo.getUmCompanyLegalPersonAuditEntity();
        umCompanyLegalPersonAuditEntity.setSocialCreditCode(bo.getSocialCreditCode());
        // 处理文件地址
        umCompanyLegalPersonAuditEntity.setFrontUrl(StrUtil.subBefore(umCompanyLegalPersonAuditEntity.getFrontUrl(), "?", true));
        umCompanyLegalPersonAuditEntity.setBehindUrl(StrUtil.subBefore(umCompanyLegalPersonAuditEntity.getBehindUrl(), "?", true));
        umCompanyLegalPersonAuditEntity.setCreatorId(bo.getUserId());
        umCompanyLegalPersonAuditEntity.setUpdaterId(bo.getUserId());
        umCompanyLegalPersonAuditEntity.setApplySource(CmdEnums.ApplySourcesEnum.BULK_TRADE_PLATFORM.getCode());
        umCompanyLegalPersonAuditEntity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());

    }


    /**
     * 组装企业信息
     *
     * @param bo
     * @param mdmCompanyInfo
     * @return 企业信息表Entity
     */
    public UmCompanyEntity assembleCompanyInfo(CreateCompanyBO bo, MdmCompanyInfoDTO mdmCompanyInfo,String userId) {
        UmCompanyEntity companyEntity = new UmCompanyEntity();
        companyEntity.setCompanyName(bo.getCompanyName());
        companyEntity.setCreatorId(userId);
        companyEntity.setUpdaterId(userId);
        companyEntity.setPlatformType("10");
        companyEntity.setContacts(bo.getUserName());
        companyEntity.setPhone(bo.getPhone());
        companyEntity.setSocialCreditCode(bo.getSocialCreditCode());
        if (mdmCompanyInfo != null) {
            companyEntity.setApplySource(mdmCompanyInfo.getApplySource());
        } else {
            companyEntity.setApplySource(CmdEnums.ApplySourcesEnum.BULK_TRADE_PLATFORM.getCode());
        }
        companyEntity.setSignStatus("1");
        return companyEntity;
    }

    /**
     * 获取当前登录人的联系方式
     *
     * @param userId
     * @return
     */
    private String getContactPhone(String userId) {
        // 获取当前登录人的联系方式
        MdmUserContactQuery query = new MdmUserContactQuery();
        query.setUserId(Long.valueOf(userId));
        query.setCategory("10");
        query.setEnableFlag("1");
        List<MdmUserContactDTO> mdmCmdUserContactDTOS = mdmExchangeService.queryUserContactList(query);
        if (CollUtil.isNotEmpty(mdmCmdUserContactDTOS)) {
            return mdmCmdUserContactDTOS.get(0).getContactInfo();
        }
        return null;
    }

    /**
     * 组装企业审核过程
     *
     * @param bo
     */
    private void syncMsgToCmdApply(CreateCompanyBO bo, TokenInfo tokenInfo) {
        Boolean isRegisteredInMainData = bo.getIsRegisteredInMainData();
        String companyName = bo.getCompanyName();
        Long id = idUtil.generateId(ServiceNameConstants.MDM_SERVICE);
        MdmCompanyApplyCommand mdmCmdCompanyApplyCommand = new MdmCompanyApplyCommand();
        mdmCmdCompanyApplyCommand.setApplyLoginName(tokenInfo.getLoginName());
        mdmCmdCompanyApplyCommand.setForntFileUrl(bo.getMdmCompanyApplyCommand().getForntFileUrl());
        if (!isRegisteredInMainData) {
            // 查询ots获取企业资质信息
            CompanyDetailQuery companyDetailQuery = new CompanyDetailQuery();
            companyDetailQuery.setEntName(companyName);
            ResultMode<OtsCompanyCheckDataDTO> dtoResultMode = otsExchangeService.companyDetailCheck(companyDetailQuery);
            if (!dtoResultMode.isSucceed()) {
                log.error("调用ots查询企业信息失败:" + dtoResultMode.getMessage());
                throw new CtpCorePartnerException("调用ots查询企业信息失败");
            }
            OtsCompanyCheckDataDTO otsCompanyCheckDataDTO = dtoResultMode.getModel();
            if (ObjectUtil.isNotEmpty(otsCompanyCheckDataDTO)) {
                mdmCmdCompanyApplyCommand.setId(id);
                // 来源
                mdmCmdCompanyApplyCommand.setApplySource("1");
                // 审核状态
                mdmCmdCompanyApplyCommand.setAptitudeStatus("1");
                // 法人身份证号
                mdmCmdCompanyApplyCommand.setLegalPersonIdCard(bo.getIdCard());
                // 联系人电话
                mdmCmdCompanyApplyCommand.setPhone(bo.getPhone());
                // 联系人
                mdmCmdCompanyApplyCommand.setContacts(bo.getUserName());
                // 企业名称
                mdmCmdCompanyApplyCommand.setLicenseName(companyName);
                // 企业法人
                mdmCmdCompanyApplyCommand.setLegalPerson(bo.getMdmCompanyApplyCommand().getLegalPerson());
                // 企业统一社会信用代码
                mdmCmdCompanyApplyCommand.setLicenseNo(bo.getSocialCreditCode());
                // 注册资金
                if (StrUtil.isNotEmpty(otsCompanyCheckDataDTO.getCapital())) {
                    BigDecimal registeredCapital = new BigDecimal(otsCompanyCheckDataDTO.getCapital());
                    mdmCmdCompanyApplyCommand.setRegisteredCapital(registeredCapital);
                }
                // 经营范围
                mdmCmdCompanyApplyCommand.setManageScope(otsCompanyCheckDataDTO.getBusinessScope());
                // 企业类型
                mdmCmdCompanyApplyCommand.setCompanyType(otsCompanyCheckDataDTO.getCompanyType());
                // 成立日期
                mdmCmdCompanyApplyCommand.setFoundDate(otsCompanyCheckDataDTO.getEstablishDate());
                // 发证机关
                mdmCmdCompanyApplyCommand.setLicenseDepartmentGov(otsCompanyCheckDataDTO.getAuthority());
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String operationStartdate = otsCompanyCheckDataDTO.getOperationStartdate();
                String operationEnddate = otsCompanyCheckDataDTO.getOperationEnddate();
                // 解析并设置证件有效期-起始
                mdmCmdCompanyApplyCommand.setLicenseStartDate(parseDate(operationStartdate, dateFormat));
                // 解析并设置证件有效期-结束
                mdmCmdCompanyApplyCommand.setLicenseEndDate(parseDate(operationEnddate, dateFormat));

                // 详细地址
                String companyAddress = otsCompanyCheckDataDTO.getCompanyAddress();
                if (StrUtil.isNotEmpty(companyAddress)) {
                    mdmCmdCompanyApplyCommand.setAddressDetail(companyAddress);
                    // 处理三级地址
                    handleThreeLevelAddress(companyAddress, mdmCmdCompanyApplyCommand);
                }

                bo.setMdmCompanyApplyCommand(mdmCmdCompanyApplyCommand);
            } else {
                throw new CtpCorePartnerException("ots查询企业信息为空，请核对企业名称后重试");
            }
        }
    }

    /**
     * 处理三级地址
     *
     * @param address
     */
    private void handleThreeLevelAddress(String address, MdmCompanyApplyCommand mdmCmdCompanyApplyCommand) {
        if (StrUtil.isEmpty(address)) {
            return;
        }
        OtsAddressDetailDTO otsAddressDetailDTO = otsUtil.addressParse(address);
        if (ObjectUtil.isEmpty(otsAddressDetailDTO)) {
            return;
        }
        mdmCmdCompanyApplyCommand.setProvince(otsAddressDetailDTO.getProvinceCode());
        mdmCmdCompanyApplyCommand.setProvinceName(otsAddressDetailDTO.getProvince());
        mdmCmdCompanyApplyCommand.setCity(otsAddressDetailDTO.getCityCode());
        mdmCmdCompanyApplyCommand.setCityName(otsAddressDetailDTO.getCity());
        mdmCmdCompanyApplyCommand.setArea(otsAddressDetailDTO.getCountyCode());
        mdmCmdCompanyApplyCommand.setAreaName(otsAddressDetailDTO.getCounty());
    }

    private Date parseDate(String dateStr, SimpleDateFormat dateFormat) {
        if (ObjectUtil.isNotEmpty(dateStr)) {
            try {
                return dateFormat.parse(dateStr);
            } catch (ParseException e) {
                // 处理解析异常，例如记录日志或设置默认值
                log.error("日期解析异常: " + dateStr, e);
            }
        }
        return null; // 或者设置默认日期
    }

    public void openRole(UmRoleEntity umRoleEntity) {
        // 查询当前企业是否已开通该角色
        List<UmRoleEntity> umRoleEntities = umRoleRepository.queryByCompanyId(umRoleEntity.getCompanyId());
        if (CollUtil.isNotEmpty(umRoleEntities)) {
            umRoleEntities.forEach(umRoleEntity1 -> {
                if (umRoleEntity1.getRoleCode().equals(umRoleEntity.getRoleCode())) {
                    throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.COMPANY_HAS_OPEN_ROLE);
                }
            });
        }
        // 入库
        umRoleRepository.add(umRoleEntity);
        log.info("新增角色成功");
    }

    public EnterpriseInfoDTO getEntProfileDetailPage(EnterpriseCondition condition) {
        // 初始化返回对象
        EnterpriseInfoDTO enterpriseInfoDTO = new EnterpriseInfoDTO();

        // 处理企业角色信息
        processCompanyRole(condition, enterpriseInfoDTO);

        // 获取并设置企业基本信息和工商信息
        BusinessLicenseInfoDTO businessLicenseInfo = fetchBusinessLicenseInfo(condition);
        enterpriseInfoDTO.setBusinessLicenseInfo(businessLicenseInfo);
        enterpriseInfoDTO.setCompanyId(condition.getCompanyId());
        enterpriseInfoDTO.setCompanyName(businessLicenseInfo.getCompanyName());

        // 获取并设置授权委托书信息
        EnterpriseAuthorizationInfoDTO authorizationInfo = fetchAuthorizationInfo(condition, businessLicenseInfo.getSocialCreditCode());
        enterpriseInfoDTO.setPowerOfAttorneyInfo(authorizationInfo);


        // 获取并设置法定代表人信息
        UmCompanyLegalPersonAuditDTO legalPersonInfo = fetchLegalPersonInfo(businessLicenseInfo.getSocialCreditCode());
        enterpriseInfoDTO.setLegalPersonInfo(legalPersonInfo);

        enterpriseInfoDTO.setHasQualification(businessLicenseInfo.isHasQualification());
        enterpriseInfoDTO.setHasPoA(authorizationInfo.isHasPoA());
        return enterpriseInfoDTO;
    }

    /**
     * 获取并设置法定代表人信息
     *
     * @param socialCreditCode
     * @return {@link UmCompanyLegalPersonAuditDTO }
     */
    private UmCompanyLegalPersonAuditDTO fetchLegalPersonInfo(String socialCreditCode) {
        if (StrUtil.isEmpty(socialCreditCode)) return null;

        // 查询企业基础信息
        UmCompanyEntity umCompanyEntity = queryCompanyStatus(null,socialCreditCode);

        if (StrUtil.isBlank(socialCreditCode)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_SOCIAL_CREDIT_CODE_NOT_FOUND);
        }
        // 商贸营业执照审核状态
        String aptitudeStatus = umCompanyEntity.getAptitudeStatus();
        UmCompanyLegalPersonAuditDTO legalPersonInfo = new UmCompanyLegalPersonAuditDTO();

        // 查询主数据企业法人信息
        MdmCompanyLegalPersonQuery query = new MdmCompanyLegalPersonQuery();
        query.setSocialCreditCode(socialCreditCode);
        ResultMode<List<MdmCompanyLegalPersonInfoDTO>> resultMode = mdmExchangeService.queryConditionCompanyLegalPerson(query);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(resultMode.getCode(), resultMode.getMessage());
        }

        List<MdmCompanyLegalPersonInfoDTO> mdmCompanyLegalPersonInfoDTOS = resultMode.getModel();

        // 查询企业法人审核记录
        UmCompanyLegalPersonAuditCondition condition = new UmCompanyLegalPersonAuditCondition();
        condition.setApplySource("1");
        condition.setSocialCreditCode(socialCreditCode);
        List<UmCompanyLegalPersonAuditEntity> umCompanyLegalPersonAuditEntities = umCompanyLegalPersonAuditRepository.queryByCondition(condition);

        // 主数据和审核记录都为空，返回空对象
        if (CollUtil.isEmpty(umCompanyLegalPersonAuditEntities) && CollUtil.isEmpty(mdmCompanyLegalPersonInfoDTOS)) {
            return null;
        }
        MdmCompanyLegalPersonInfoDTO mdmCompanyLegalPersonInfoDTO = IterUtil.getFirst(mdmCompanyLegalPersonInfoDTOS);
        UmCompanyLegalPersonAuditEntity personAuditEntity = null;
        if (CollUtil.isNotEmpty(umCompanyLegalPersonAuditEntities)) {
            personAuditEntity = IterUtil.getFirst(umCompanyLegalPersonAuditEntities);
            legalPersonInfo.setAuditStatus(personAuditEntity.getAuditStatus());
        }

        List<String> aptitudeStatusList = Arrays.asList(CompanyAuditStatusEnum.WAITING.getCode(), CompanyAuditStatusEnum.REJECT.getCode());

        // 待审核、审核不通过,取审核数据
        if (StrUtil.isNotEmpty(aptitudeStatus) && aptitudeStatusList.contains(aptitudeStatus)){
            setLegalPersonInfoFromAudit(legalPersonInfo, personAuditEntity);
        }
        // 审核通过，取主数据
        if (StrUtil.isNotEmpty(aptitudeStatus) && CompanyAuditStatusEnum.PASS.getCode().equals(aptitudeStatus)){
            setLegalPersonInfoFromMasterData(legalPersonInfo, mdmCompanyLegalPersonInfoDTO);
        }

        legalPersonInfo.setAuditStatus(personAuditEntity.getAuditStatus());
        Date untilDate = legalPersonInfo.getUntilDate();
        // 法人证件是否过期
        handleIdCardExpiration(legalPersonInfo, untilDate);
        return legalPersonInfo;
    }

    /**
     * 从主数据设置法人信息
     *
     * @param legalPersonInfo
     * @param mdmCompanyLegalPersonInfoDTO
     */
    private void setLegalPersonInfoFromMasterData(UmCompanyLegalPersonAuditDTO legalPersonInfo, MdmCompanyLegalPersonInfoDTO mdmCompanyLegalPersonInfoDTO) {
        BeanUtil.copyProperties(mdmCompanyLegalPersonInfoDTO, legalPersonInfo);
        // 处理身份证url
        String frontUrl = mdmCompanyLegalPersonInfoDTO.getFrontUrl();
        if (StrUtil.isNotEmpty(frontUrl)) {
            legalPersonInfo.setFrontUrl(uploadExchangeService.getUrl(frontUrl));
        }
        String behindUrl = mdmCompanyLegalPersonInfoDTO.getBehindUrl();
        if (StrUtil.isNotEmpty(behindUrl)) {
            legalPersonInfo.setBehindUrl(uploadExchangeService.getUrl(behindUrl));
        }
        legalPersonInfo.setProvinceCode(mdmCompanyLegalPersonInfoDTO.getProvinceCode());
        legalPersonInfo.setProvinceName(mdmCompanyLegalPersonInfoDTO.getProvinceName());
        legalPersonInfo.setCityCode(mdmCompanyLegalPersonInfoDTO.getCityCode());
        legalPersonInfo.setCityName(mdmCompanyLegalPersonInfoDTO.getCityName());
        legalPersonInfo.setCountyCode(mdmCompanyLegalPersonInfoDTO.getCountyCode());
        legalPersonInfo.setCountyName(mdmCompanyLegalPersonInfoDTO.getCountyName());
        legalPersonInfo.setAddrDetail(mdmCompanyLegalPersonInfoDTO.getAddrDetail());
    }

    /**
     * 从审核记录设置法人信息
     *
     * @param legalPersonInfo
     * @param personAuditEntity
     */
    private void setLegalPersonInfoFromAudit(UmCompanyLegalPersonAuditDTO legalPersonInfo, UmCompanyLegalPersonAuditEntity personAuditEntity) {
        BeanUtil.copyProperties(personAuditEntity, legalPersonInfo);
        if (ObjectUtil.isNotEmpty(personAuditEntity.getFrontUrl())) {
            legalPersonInfo.setFrontUrl(uploadExchangeService.getUrl(personAuditEntity.getFrontUrl()));
        }
        if (ObjectUtil.isNotEmpty(personAuditEntity.getBehindUrl())) {
            legalPersonInfo.setBehindUrl(uploadExchangeService.getUrl(personAuditEntity.getBehindUrl()));
        }
        legalPersonInfo.setProvinceCode(personAuditEntity.getProvinceCode());
        legalPersonInfo.setProvinceName(personAuditEntity.getProvinceName());
        legalPersonInfo.setCityCode(personAuditEntity.getCityCode());
        legalPersonInfo.setCityName(personAuditEntity.getCityName());
        legalPersonInfo.setCountyCode(personAuditEntity.getCountyCode());
        legalPersonInfo.setCountyName(personAuditEntity.getCountyName());
        legalPersonInfo.setAddrDetail(personAuditEntity.getAddrDetail());
        legalPersonInfo.setAddrDetail(personAuditEntity.getAddrDetail());
    }

    private void handleIdCardExpiration(UmCompanyLegalPersonAuditDTO dto, Date untilDate) {
        int expiredStatus;

        if (untilDate == null) {
            // 身份证有效期为空
            expiredStatus = 30;
        } else if (DateUtil.compare(untilDate, DateUtil.date()) < 0) {
            // 身份证已过期
            expiredStatus = 20;
        } else {
            // 身份证未过期
            expiredStatus = 10;
        }

        dto.setIsExpired(expiredStatus);
    }

    /**
     * 获取并设置企业工商信息
     *
     * @param condition
     * @return {@link BusinessLicenseInfoDTO }
     */
    private BusinessLicenseInfoDTO fetchBusinessLicenseInfo(EnterpriseCondition condition) {
        if (StrUtil.isEmpty(condition.getCompanyId())) return null;

        // 查询企业基础信息
        UmCompanyEntity umCompanyEntity = queryCompanyStatus(condition.getCompanyId(), null);

        String socialCreditCode = umCompanyEntity.getSocialCreditCode();
        if (StrUtil.isBlank(socialCreditCode)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_SOCIAL_CREDIT_CODE_NOT_FOUND);
        }
        // 商贸营业执照审核状态
        String aptitudeStatus = umCompanyEntity.getAptitudeStatus();

        // 查询工商审核记录
        MdmCompanyApplyQuery query = new MdmCompanyApplyQuery();
        query.setLicenseNo(socialCreditCode);
        query.setApplySource("1");
        ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(query);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException("调用主数据查询工商审核记录失败：" + resultMode.getMessage());
        }

        List<MdmCompanyApplyDTO> applyDTOS = resultMode.getModel();
        if (CollUtil.isEmpty(applyDTOS)) {
            throw new CtpCorePartnerException("未查询到该企业的工商审核记录");
        }

        // 获取最新一条审核记录
        MdmCompanyApplyDTO latestApply = applyDTOS.stream()
                .max(Comparator.comparing(MdmCompanyApplyDTO::getUpdatedDate))
                .orElseThrow(() -> new CtpCorePartnerException("无法获取最新的工商审核记录"));

        // 查询主数据中的企业信息
        MdmCompanyInfoDTO cmdCompanyInfo = mdmExchangeService.getMdmCmdCompanyByLicenseNo(socialCreditCode);
        BusinessLicenseInfoDTO dto = new BusinessLicenseInfoDTO();

        List<String> aptitudeStatusList = Arrays.asList(CompanyAuditStatusEnum.WAITING.getCode(), CompanyAuditStatusEnum.REJECT.getCode());

        // 待审核、审核不通过,取审核数据
        if (StrUtil.isNotEmpty(aptitudeStatus) && aptitudeStatusList.contains(aptitudeStatus)){
            setQualificationFromApplyRecord(dto, latestApply);
        }
        // 审核通过，取主数据
        if (StrUtil.isNotEmpty(aptitudeStatus) && CompanyAuditStatusEnum.PASS.getCode().equals(aptitudeStatus)){
            setQualificationFromMasterData(dto, cmdCompanyInfo);
        }

        // 设置审核状态和拒绝原因
        dto.setAptitudeStatus(latestApply.getAptitudeStatus());
        dto.setRejectReason(latestApply.getReviewReason());
        dto.setLicenseStartDate(latestApply.getLicenseStartDate());
        dto.setLicenseEndDate(latestApply.getLicenseEndDate());

        return dto;
    }

    /**
     * 查询商贸营业执照状态
     *
     * @param companyId
     * @param socialCreditCode
     * @return 企业信息表Entity
     */
    private UmCompanyEntity queryCompanyStatus(String companyId,String socialCreditCode){
        // 查询企业基础信息
        UmCompanyCondition companyCondition = new UmCompanyCondition();
        if (StrUtil.isNotEmpty(companyId)){
            companyCondition.setId(Long.valueOf(companyId));
        }
        if (StrUtil.isNotEmpty(socialCreditCode)){
            companyCondition.setSocialCreditCode(socialCreditCode);
        }
        List<UmCompanyEntity> umCompanyEntities = umCompanyRepository.queryCompanyStatus(companyCondition);
        if (CollUtil.isEmpty(umCompanyEntities)){
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_INFO_NOT_FOUND);
        }
        return IterUtil.getFirst(umCompanyEntities);
    }

    /**
     * 从主数据中获取资质信息
     *
     * @param dto
     * @param cmdCompanyInfo
     */
    private void setQualificationFromMasterData(BusinessLicenseInfoDTO dto, MdmCompanyInfoDTO cmdCompanyInfo) {
        queryBusinessLicenseAuditInfo(dto, cmdCompanyInfo.getLicenseNo());
        dto.setCompanyName(cmdCompanyInfo.getLicenseName());
        dto.setSocialCreditCode(cmdCompanyInfo.getLicenseNo());
        dto.setLegalPerson(cmdCompanyInfo.getLegalPerson());
        dto.setAddressDetail(cmdCompanyInfo.getAddressDetail());
        dto.setLicenseValidIsLong(cmdCompanyInfo.getLicenseValidIsLong());
        dto.setOrgLongTermFlag(cmdCompanyInfo.getOrgLongTermFlag());
        dto.setProvince(cmdCompanyInfo.getProvince());
        dto.setProvinceName(cmdCompanyInfo.getProvinceName());
        dto.setCity(cmdCompanyInfo.getCity());
        dto.setCityName(cmdCompanyInfo.getCityName());
        dto.setArea(cmdCompanyInfo.getArea());
        dto.setAreaName(cmdCompanyInfo.getAreaName());
        dto.setStreet(cmdCompanyInfo.getStreet());
        dto.setStreetName(cmdCompanyInfo.getStreetName());
        if (StrUtil.isNotEmpty(cmdCompanyInfo.getFrontFileUrl())) {
            dto.setFileUrl(uploadExchangeService.getUrl(cmdCompanyInfo.getFrontFileUrl()));
        }
    }

    /**
     * 查询商贸营业执照信息
     *
     * @param dto
     * @param licenseNo
     */
    private void queryBusinessLicenseAuditInfo(BusinessLicenseInfoDTO dto, String licenseNo) {
        if (StrUtil.isEmpty(licenseNo)) {
            dto.setHasQualification(false);
            return;
        }
        UmCompanyBusinessLicenseEntity umCompanyBusinessLicenseEntity = umCompanyBusinessLicenseRepository.queryByLicenseNo(licenseNo);
        if (umCompanyBusinessLicenseEntity == null) {
            dto.setHasQualification(false);
        }
        String aptitudeStatus = umCompanyBusinessLicenseEntity.getAptitudeStatus();
        if (StrUtil.isNotEmpty(aptitudeStatus) && CompanyAuditStatusEnum.PASS.getCode().equals(aptitudeStatus)) {
            dto.setHasQualification(true);
        } else {
            dto.setHasQualification(false);
        }

    }

    /**
     * 从审核记录设置企业资质信息
     *
     * @param dto
     * @param latestApply
     */
    private void setQualificationFromApplyRecord(BusinessLicenseInfoDTO dto, MdmCompanyApplyDTO latestApply) {
        dto.setHasQualification(false);
        dto.setCompanyName(latestApply.getLicenseName());
        dto.setSocialCreditCode(latestApply.getLicenseNo());
        dto.setLegalPerson(latestApply.getLegalPerson());
        if (StrUtil.isNotEmpty(latestApply.getForntFileUrl())) {
            dto.setFileUrl(uploadExchangeService.getUrl(latestApply.getForntFileUrl()));
        }
        dto.setProvince(latestApply.getProvince());
        dto.setProvinceName(latestApply.getProvinceName());
        dto.setCity(latestApply.getCity());
        dto.setCityName(latestApply.getCityName());
        dto.setArea(latestApply.getArea());
        dto.setAreaName(latestApply.getAreaName());
        dto.setStreet(latestApply.getStreet());
        dto.setStreetName(latestApply.getStreetName());
        dto.setAddressDetail(latestApply.getAddressDetail());
    }


    /**
     * 获取并设置企业授权委托书信息
     *
     * @param condition
     * @param socialCreditCode
     * @return {@link EnterpriseAuthorizationInfoDTO }
     */
    private EnterpriseAuthorizationInfoDTO fetchAuthorizationInfo(EnterpriseCondition condition, String socialCreditCode) {
        EnterpriseAuthorizationInfoDTO authInfoDTO = new EnterpriseAuthorizationInfoDTO();

        // 查询ctp 授权委托书记录
        UmAccreditAuditCondition auditCondition = new UmAccreditAuditCondition();
        auditCondition.setCompanyId(condition.getCompanyId());
        List<UmAccreditAuditEntity> auditEntities = umAccreditAuditRepository.queryCondition(auditCondition);
        if (CollUtil.isEmpty(auditEntities)) {
            throw new CtpCorePartnerException("当前企业未找到授权委托书审核记录");
        }

        UmAccreditAuditEntity auditEntity = IterUtil.getFirst(auditEntities);
        authInfoDTO.setMallPoAId(String.valueOf(auditEntity.getId()));
        // 查询主数据中的授权委托书信息
        MdmCompanyInfoDTO cmdCompanyInfo = mdmExchangeService.getMdmCmdCompanyByLicenseNo(socialCreditCode);
        authInfoDTO.setAuditStatus(auditEntity.getAuditStatus());
        authInfoDTO.setHasPoA(false);
        if (ObjectUtil.isNotEmpty(cmdCompanyInfo) && ObjectUtil.isNotEmpty(cmdCompanyInfo.getId())) {
            authInfoDTO.setMdmPoAId(String.valueOf(cmdCompanyInfo.getId()));
            // 如果主数据存在，则查主数据的授权委托书
            MdmAccreditOperatorQuery operatorQuery = new MdmAccreditOperatorQuery();
            operatorQuery.setCompanyId(String.valueOf(cmdCompanyInfo.getId()));
            operatorQuery.setPlatformCode("22");
            ResultMode<List<MdmAccreditOperatorDTO>> operatorResult = mdmExchangeService.getAccreditOperatorList(operatorQuery);
            if (!operatorResult.isSucceed()) {
                throw new CtpCorePartnerException(operatorResult.getCode(), operatorResult.getMessage());
            }

            List<MdmAccreditOperatorDTO> operatorDTOS = operatorResult.getModel();
            if (CollUtil.isNotEmpty(operatorDTOS)) {
                MdmAccreditOperatorDTO mdmAccreditOperatorDTO = operatorDTOS.stream()
                        .max(Comparator.comparing(MdmAccreditOperatorDTO::getCreatedDate))
                        .orElse(null);

                if (mdmAccreditOperatorDTO != null) {
                    authInfoDTO.setHasPoA(true);
                    // 根据操作员ID查询用户信息
                    MdmOperatorQuery mdmOperatorQuery = new MdmOperatorQuery();
                    mdmOperatorQuery.setId(mdmAccreditOperatorDTO.getOperatorId());
                    ResultMode<MdmOperatorDTO> operatorDTOResultMode = mdmExchangeService.queryOperatorDetail(mdmOperatorQuery);
                    if (!operatorDTOResultMode.isSucceed()) {
                        throw new CtpCorePartnerException(operatorDTOResultMode.getCode(), operatorDTOResultMode.getMessage());
                    }
                    MdmOperatorDTO mdmOperatorDTO = operatorDTOResultMode.getModel();
                    if (mdmOperatorDTO == null) {
                        throw new CtpCorePartnerException("操作员不存在");
                    }

                    // 查询用户信息
                    MdmUserInfoDTO userInfo = mdmExchangeService.queryUserInfo(Long.valueOf(mdmOperatorDTO.getUserId()));
                    if (userInfo == null) {
                        throw new CtpCorePartnerException("未查询到企业授权人信息");
                    }

                    authInfoDTO.setIdCard(userInfo.getIdCardNo());
                    authInfoDTO.setUserName(userInfo.getUserName());
                    if (StrUtil.isNotEmpty(mdmAccreditOperatorDTO.getFileUrl())) {
                        authInfoDTO.setFileUrl(uploadExchangeService.getUrl(mdmAccreditOperatorDTO.getFileUrl()));
                    }
                    return authInfoDTO;
                }
            }
        }
        // 主数据中无企业授权信息，从ctp 授权审核记录中取
        authInfoDTO.setIdCard(auditEntity.getTransferIdCardNo());
        authInfoDTO.setUserName(auditEntity.getTransferUserName());
        if (StrUtil.isNotEmpty(auditEntity.getFileUrl())) {
            authInfoDTO.setFileUrl(uploadExchangeService.getUrl(auditEntity.getFileUrl()));
        }
        return authInfoDTO;

    }

    /**
     * 查询企业状态
     *
     * @param condition
     * @return
     */
    public CompanyStatusDTO checkCompanyStatusByName(CheckCompanyStatusCondition condition) {
        CompanyStatusDTO dto = new CompanyStatusDTO();
        dto.setCompanyName(condition.getCompanyName());

        // 1、查询公司是否在商贸注册
        // 1.1查询授权委托书
//        UmAccreditAuditCondition umAccreditAuditCondition = new UmAccreditAuditCondition();
//        umAccreditAuditCondition.setCompanyName(condition.getCompanyName());
//        umAccreditAuditCondition.setAuditStatus(3);
//        List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(umAccreditAuditCondition);

//        // 1.2查询工商信息
//        MdmCompanyApplyQuery mdmCompanyApplyQuery = new MdmCompanyApplyQuery();
//        mdmCompanyApplyQuery.setLicenseName(condition.getCompanyName());
//        mdmCompanyApplyQuery.setAptitudeStatus("3");
//        mdmCompanyApplyQuery.setApplySource("1");
//        ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(mdmCompanyApplyQuery);
//        if (!resultMode.isSucceed()) {
//            throw new CtpCorePartnerException("查询主数据企业资质失败，请联系管理员");
//        }

        // 1.3查询公司信息
        UmCompanyCondition umCompanyCondition = new UmCompanyCondition();
        umCompanyCondition.setCompanyName(condition.getCompanyName());
        umCompanyCondition.setAptitudeStatus("3");
        List<UmCompanyEntity> umCompanyEntityList = umCompanyRepository.queryCompanyStatus(umCompanyCondition);
        UmCompanyEntity umCompanyEntity = null;
        if (CollUtil.isNotEmpty(umCompanyEntityList)) {
            umCompanyEntity = IterUtil.getFirst(umCompanyEntityList);
        }
        // 企业数据都存在，则已在商贸已注册
        if (ObjectUtil.isNotEmpty(umCompanyEntity)) {
            dto.setRegisteredStatus("20");
            return dto;
        }
        // 以下情况皆视为未在商贸注册
        dto.setRegisteredStatus("10");

        // 2、查询公司是否在主数据注册
        MdmCompanyInfoQuery query = new MdmCompanyInfoQuery();
        query.setLicenseName(condition.getCompanyName());
        ResultMode<MdmCompanyInfoDTO> companyInfoDTOResultMode = mdmExchangeService.getMdmCmdCompanyByShortName(query);
        if (!companyInfoDTOResultMode.isSucceed()) {
            throw new CtpCorePartnerException("查询主数据企业资质失败，请联系管理员");
        }
        MdmCompanyInfoDTO companyInfoDto = companyInfoDTOResultMode.getModel();
        if (ObjectUtil.isNotEmpty(companyInfoDto)) {
            dto.setLegalPerson(companyInfoDto.getLegalPerson());
            dto.setLicenseNo(companyInfoDto.getLicenseNo());
            dto.setCompanyName(companyInfoDto.getLicenseName());
            String frontFileUrl = companyInfoDto.getFrontFileUrl();
            // 若公司已在主数据中注册，则返回工商照片地址
            if (StrUtil.isNotEmpty(frontFileUrl)) {
                dto.setFileUrl(uploadExchangeService.getUrl(frontFileUrl));
            }
            dto.setRegisteredStatus("30");
            return dto;
        } else {
            // 3、查询公司是否在审核中
            MdmCompanyApplyQuery applyQuery = new MdmCompanyApplyQuery();
            applyQuery.setLicenseName(condition.getCompanyName());
            applyQuery.setAptitudeStatus("1");
            ResultMode<List<MdmCompanyApplyDTO>> applyResultModel = mdmExchangeService.queryCompanyApplyList(applyQuery);
            if (!applyResultModel.isSucceed()) {
                throw new CtpCorePartnerException("查询主数据企业审核记录失败，请联系管理员");
            } else if (CollUtil.isNotEmpty(applyResultModel.getModel())) {
                throw new CtpCorePartnerException("该企业工商信息已存在并审核中，请您稍后再试");
            } else {
                // 4、未在主数据注册过，则查询工商信息
                CompanyDetailQuery companyDetailQuery = new CompanyDetailQuery();
                companyDetailQuery.setEntName(condition.getCompanyName());
                ResultMode<OtsCompanyCheckDataDTO> dtoResultMode = otsExchangeService.companyDetailCheck(companyDetailQuery);
                if (!dtoResultMode.isSucceed()) {
                    log.error("查询ost工商信息失败:", dtoResultMode.getMessage());
                    throw new CtpCorePartnerException("查询ost工商信息失败，请联系管理员");
                }
                OtsCompanyCheckDataDTO model = dtoResultMode.getModel();
                dto.setCompanyName(model.getCompanyName());
                if (ObjectUtil.isEmpty(model)) {
                    dto.setLegalPerson(null);
                    dto.setLicenseNo(null);
                    return dto;
                }

                dto.setLegalPerson(model.getLegalPerson());
                dto.setLicenseNo(model.getCreditNo());

            }
        }
        return dto;
    }

    /**
     * 处理企业角色信息
     *
     * @param condition
     * @param enterpriseInfoDTO
     */
    public void processCompanyRole(EnterpriseCondition condition, EnterpriseInfoDTO enterpriseInfoDTO) {
        // 1、查询企业已开通角色
        List<UmRoleEntity> umRoleEntities = umRoleRepository.queryByCompanyId(condition.getCompanyId());

        // 使用 Map 存储已开通的角色代码
        Set<String> activeRoleCodes = umRoleEntities.stream()
                .map(UmRoleEntity::getRoleCode)
                .collect(Collectors.toSet());

        DictValueCondition dictValueCondition = new DictValueCondition();
        dictValueCondition.setDictCode("role_code");
        List<DictValue> dictValues = dictValueRepository.queryCondition(dictValueCondition);

        if (CollUtil.isEmpty(dictValues)) {
            throw new CtpCorePartnerException("未查询到角色字典值");
        }

        List<PlatformRoleDTO> dtoList = dictValues.stream()
                .map(dictValue -> {
                    PlatformRoleDTO dto = new PlatformRoleDTO();
                    dto.setStatus("20");
                    dto.setRoleCode(dictValue.getDictValue());
                    dto.setRoleName(dictValue.getValueName());
                    // 如果角色已开通，设置状态为“已开通”
                    if (activeRoleCodes.contains(dictValue.getDictValue())) {
                        dto.setStatus("10");
                    }
                    return dto;
                })
                .collect(Collectors.toList());

        enterpriseInfoDTO.setPlatformRoleList(dtoList);
    }

    /**
     * 查询公司状态
     * 是否在商贸注册
     * 已注册-》是否存在管理员
     * 存在管理员-》公司已注册并当前有管理员，您可联系管理员进行过户
     * 不存在管理员-》是否在审核中
     * 存在管理员过户或申请成为管理员的待审核数据-》公司已注册并当前有管理员，您可联系管理员进行过户
     * *        不存在审核数据-》可以申请成为管理员
     * 未注册-》是否在审核中
     *
     * @param condition
     * @return
     */
    public CompanyInfoDTO checkCompanyStatusByLicenseNo(CheckCompanyStatusCondition condition) {
        CompanyInfoDTO resultDto = new CompanyInfoDTO();
        resultDto.setLicenseNo(condition.getLicenseNo());
        String licenseNo = condition.getLicenseNo();
        String companyId = null;

        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException("当前用户未登录");
        }
        String userId = tokenInfo.getUserId();

        // 查询当前用户的手机号
        String phone = queryUserPhone(userId);
        // 用户手机号脱敏处理
        resultDto.setPhone((String) PhoneUtil.hideBetween(phone));

        // 1、查询公司是否在商贸注册
        UmCompanyCondition umCompanyCondition = new UmCompanyCondition();
        umCompanyCondition.setSocialCreditCode(licenseNo);
        umCompanyCondition.setAptitudeStatus("3");
        List<UmCompanyEntity> umCompanyEntityList = umCompanyRepository.queryCompanyStatus(umCompanyCondition);
        UmCompanyEntity companyEntity = null;
        if (CollUtil.isNotEmpty(umCompanyEntityList)) {
            companyEntity = IterUtil.getFirst(umCompanyEntityList);
        }
        if (ObjectUtil.isNotEmpty(companyEntity)) {
            companyId = String.valueOf(companyEntity.getId());
            resultDto.setCompanyId(String.valueOf(companyEntity.getId()));
            resultDto.setCompanyName(companyEntity.getCompanyName());
            // 查询该公司是否存在管理员
            UmCompanyMemberCondition memberCondition = new UmCompanyMemberCondition();
            memberCondition.setCompanyId(companyId);
            memberCondition.setLevelType("1");
            List<UmCompanyMemberEntity> umCompanyMemberEntities = umCompanyMemberRepository.queryList(memberCondition);
            if (CollUtil.isNotEmpty(umCompanyMemberEntities)) {
                resultDto.setCompanyStatus("20");
            } else {
                // 查询该公司是否有审核中的管理员
                UmEmployeeBindingRecordCondition bindingCondition = new UmEmployeeBindingRecordCondition();
                bindingCondition.setCompanyId(companyId);
                List<UmEmployeeBindingRecordEntity> umEmployeeBindingRecordEntities = employeeBindingHistoryRepository.queryCondition(bindingCondition);
                if (CollUtil.isEmpty(umEmployeeBindingRecordEntities)) {
                    resultDto.setCompanyStatus("40");
                    return resultDto;
                }
                for (UmEmployeeBindingRecordEntity umEmployeeBindingRecordEntity : umEmployeeBindingRecordEntities) {
                    if (umEmployeeBindingRecordEntity.getBindingStatus().equals("21")
                            && umEmployeeBindingRecordEntity.getBindingSource().equals("22")
                            && umEmployeeBindingRecordEntity.getBindingStatus().equals("10")) {
                        resultDto.setCompanyStatus("30");
                    }
                    return resultDto;
                }

            }
        } else {
            resultDto.setCompanyStatus("10");
        }
        return resultDto;
    }

    private String queryUserPhone(String userId) {
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.queryUserInfo(Long.valueOf(userId));
        if (ObjectUtil.isEmpty(mdmUserInfoDTO)) {
            throw new CtpCorePartnerException("未查询到用户信息");
        }
        String mobile = mdmUserInfoDTO.getMobile();
        if (StrUtil.isBlank(mobile)) {
            throw new CtpCorePartnerException("当前用户未绑定手机号");
        }

        return mobile;

    }

    public void updatePowerOfAttorney(UmAccreditAuditEntity umAccreditAuditEntity) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }

        Integer bizType = umAccreditAuditEntity.getBizType();
        String companyId = umAccreditAuditEntity.getCompanyId();
        // 根据企业id查询公司信息
        String licenseNo = getCompanyInfo(companyId);

        // 校验当前企业的营业执照和法人信息是否审核通过
        validateCompanyAptitudeAndLegalPerson(licenseNo);


        String transferIdCardNo = umAccreditAuditEntity.getTransferIdCardNo();
        // 根据身份证号查询用户信息
        queryUserInfoByIdCardNo(umAccreditAuditEntity, transferIdCardNo);

        String userId = getUserIdUtil.getUserId();

        // 2、查询当前企业是否存在审核中或审核通过的记录
        UmAccreditAuditCondition umAccreditAuditCondition = new UmAccreditAuditCondition();
        umAccreditAuditCondition.setCompanyId(companyId);
        List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(umAccreditAuditCondition);
        if (CollUtil.isNotEmpty(umAccreditAuditEntities)) {
            // 是否存在审核中或审核通过的记录
            boolean hasPendingOrPassedAudit = umAccreditAuditEntities.stream()
                    .anyMatch(entity -> entity.getAuditStatus().equals(CommonAuditStatusEnum.WAITING.getCode()));
            if (hasPendingOrPassedAudit) {
                throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.AUDIT_ACCREDIT_EXISTS1);
            }
        }

        // 处理授权委托书变更的场景
        if (bizType.equals(AcreditBizTypeEnum.UPDATE.getCode())) {
            String mallPoAId = umAccreditAuditEntity.getMallPoAId();
            if (StrUtil.isBlank(mallPoAId)){
                throw new CtpCorePartnerException("变更授权委托书，id不能为空");
            }

            // 查询审核记录
            UmAccreditAuditEntity auditEntity = umAccreditAuditRepository.queryById(Long.valueOf(mallPoAId));
            if (ObjectUtil.isEmpty(auditEntity)) {
                throw new CtpCorePartnerException("未查询到授权委托书审核信息");
            }

            // 更新企业历史审核记录为已删除
            updateHistoryAccreditAudit(companyId);
            // 截取文件路径
            auditEntity.setFileUrl(StrUtil.subBefore(umAccreditAuditEntity.getFileUrl(), "?", true));
            auditEntity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
            auditEntity.setUpdaterId(userId);
            auditEntity.setBizType(AcreditBizTypeEnum.UPDATE.getCode());
            auditEntity.setLatestDataFlag(1);
            auditEntity.setUpdatedDate(DateUtil.date());
            umAccreditAuditRepository.updateById(auditEntity);
        }

        // 处理创建企业授权委托书驳回-再次提交的场景
        if (bizType.equals(AcreditBizTypeEnum.ADD.getCode())) {
            // 更新企业历史审核记录为已删除
            updateHistoryAccreditAudit(companyId);
            // 新增授权书审核记录
            umAccreditAuditEntity.setUserId(userId);
            umAccreditAuditEntity.setUserName(tokenInfo.getUsername());
            umAccreditAuditEntity.setSourceType(AcreditTypeEnum.CREATE_COMPANY.getCode());
            umAccreditAuditEntity.setBizType(AcreditBizTypeEnum.ADD.getCode());
            umAccreditAuditEntity.setLatestDataFlag(1);
            // 截取文件路径
            umAccreditAuditEntity.setFileUrl(StrUtil.subBefore(umAccreditAuditEntity.getFileUrl(), "?", true));
            umAccreditAuditEntity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
            umAccreditAuditEntity.setSocialCreditCode(licenseNo);
            umAccreditAuditEntity.setCreatorId(userId);
            umAccreditAuditEntity.setUpdaterId(userId);
            umAccreditAuditRepository.insert(umAccreditAuditEntity);
        }


        // 记录日志
        String code = umAccreditAuditEntity.getSocialCreditCode();
        if (ObjectUtil.isNotEmpty(code)) {
            BizLogUtilService.sendLogBizMsg(code, LogOperationRecordEnum.CTP_PARTNER_ACCREDIT_AUDIT,
                    LogOperationRecordEnum.OperateEnum.UPDATE_POWER_OF_ATTORNEY, "变更授权委托书");
        }

    }


    /**
     * 校验企业资质审核状态和法人信息审核状态是否通过
     *
     * @param licenseNo 营业执照编号
     */
    private void validateCompanyAptitudeAndLegalPerson(String licenseNo) {
        // 查询企业资质审核状态
        MdmCompanyApplyQuery mdmCompanyApplyQuery = new MdmCompanyApplyQuery();
        mdmCompanyApplyQuery.setLicenseNo(licenseNo);
        ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(mdmCompanyApplyQuery);

        if (!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            throw new CtpCorePartnerException("未查询到营业执照审核信息");
        }

        MdmCompanyApplyDTO mdmCompanyApply = IterUtil.getFirst(resultMode.getModel());
        String aptitudeStatus = ObjectUtil.isNotEmpty(mdmCompanyApply) ?
                mdmCompanyApply.getAptitudeStatus() : null;

        // 查询法人信息审核状态
        UmCompanyLegalPersonAuditEntity personAuditEntity =
                umCompanyLegalPersonAuditRepository.queryBySocialCreditCode(licenseNo);

        if (personAuditEntity == null) {
            throw new CtpCorePartnerException(
                    CtpCorePartnerExceptionEnum.COMPANY_LEGAL_PERSON_NOT_EXIT);
        }

        Integer auditStatus = personAuditEntity.getAuditStatus();

        // 校验审核状态是否通过
        if (auditStatus.equals(CommonAuditStatusEnum.WAITING.getCode()) ||
                StrUtil.equals(aptitudeStatus, CompanyAuditStatusEnum.WAITING.getCode())) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.COMPANY_NOT_PASS);
        }
    }

    /**
     * 根据身份证号查询用户信息
     *
     * @param umAccreditAuditEntity
     * @param transferIdCardNo
     */
    private void queryUserInfoByIdCardNo(UmAccreditAuditEntity umAccreditAuditEntity, String transferIdCardNo) {
        if (StrUtil.isEmpty(transferIdCardNo)) {
            return;
        }

        MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
        mdmUserInfoQuery.setIdCardNo(transferIdCardNo);
        ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.userInfoQueryList(mdmUserInfoQuery);
        if (!resultMode.isSucceed()) {
            throw new CtpCorePartnerException(resultMode.getCode(), resultMode.getMessage());
        }
        List<MdmUserInfoDTO> resultModeModel = resultMode.getModel();
        if (CollUtil.isEmpty(resultModeModel)) {
            throw new CtpCorePartnerException("未查询到用户信息");
        }
        MdmUserInfoDTO userInfoDTO = IterUtil.getFirst(resultModeModel);
        if (ObjectUtil.isNotEmpty(userInfoDTO)) {
            umAccreditAuditEntity.setTransferUserId(String.valueOf(userInfoDTO.getId()));
            umAccreditAuditEntity.setTransferAccount(userInfoDTO.getLoginName());
            umAccreditAuditEntity.setTransferMobile(userInfoDTO.getMobile());
        }
    }

    /**
     * 获取公司信息
     *
     * @param companyId
     * @return
     */
    private String getCompanyInfo(String companyId) {
        if (StrUtil.isEmpty(companyId)) {
            return null;
        }
        // 根据企业id查询公司信息
        UmCompanyEntity umCompanyEntity = umCompanyRepository.queryById(companyId);
        if (ObjectUtil.isEmpty(umCompanyEntity)) {
            return null;
        }
        return umCompanyEntity.getSocialCreditCode();

    }

    public void updateBusinessLicenseInfo(BusinessLicenseInfoCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }

        String userId = getUserIdUtil.getUserId();

        // 1、查询是否存在审核中的数据
        MdmCompanyApplyQuery query = new MdmCompanyApplyQuery();
        query.setLicenseNo(command.getLicenseNo());
        ResultMode<List<MdmCompanyApplyDTO>> listResultMode = mdmExchangeService.queryCompanyApplyList(query);
        if (!listResultMode.isSucceed()) {
            throw new CtpCorePartnerException("查询工商信息审核记录失败");
        }
        MdmCompanyApplyDTO mdmCompanyApplyDTO = new MdmCompanyApplyDTO();
        if (CollUtil.isNotEmpty(listResultMode.getModel())) {
            List<MdmCompanyApplyDTO> mdmCompanyApplyDTOS = listResultMode.getModel();
            mdmCompanyApplyDTO = IterUtil.getFirst(mdmCompanyApplyDTOS);
            boolean hasResult = mdmCompanyApplyDTOS.stream().anyMatch(item -> ObjectUtil.equal(item.getAptitudeStatus(), "1"));
            if (hasResult) {
                throw new CtpCorePartnerException("当前工商信息审核中，请勿重复提交");
            }
        }

        // 2、更新企业历史记审核记录为已删除
        updateHistoryCompanyAptitudeAudit(command.getLicenseNo());

        // 3、新增工商信息审核记录
        MdmCompanyApplyCommand mdmCompanyApplyCommand = BeanUtil.copyProperties(mdmCompanyApplyDTO, MdmCompanyApplyCommand.class
                , "id", "creatorId", "updaterId", "remark", "reviewReason");
        // 处理文件路径
        mdmCompanyApplyCommand.setForntFileUrl(StrUtil.subBefore(command.getFileUrl(), "?", true));
        mdmCompanyApplyCommand.setId(idUtil.generateId(ServiceNameConstants.MDM_SERVICE));
        mdmCompanyApplyCommand.setLicenseNo(command.getLicenseNo());
        mdmCompanyApplyCommand.setApplySource("1");
        mdmCompanyApplyCommand.setAptitudeStatus("1");
        String username = tokenInfo.getUsername();
        mdmCompanyApplyCommand.setApplyUsername(StrUtil.isNotEmpty(username) ? username : mdmCompanyApplyDTO.getApplyUsername());
        mdmCompanyApplyCommand.setLicenseName(command.getCompanyName());
        mdmCompanyApplyCommand.setLegalPerson(command.getLegalPerson());
        mdmCompanyApplyCommand.setCreatorId(userId);
        mdmCompanyApplyCommand.setUpdaterId(userId);
        ResultMode<Long> longResultMode = mdmExchangeService.insertCompanyApply(mdmCompanyApplyCommand);
        if (!longResultMode.isSucceed()) {
            throw new CtpCorePartnerException("新增工商信息审核记录失败");
        }
        // 记录日志
        String code = command.getLicenseNo();
        if (ObjectUtil.isNotEmpty(code)) {
            // 记录营业执照日志 CTP30
            BizLogUtilService.sendLogBizMsg(code, LogOperationRecordEnum.CTP_PARTNER_LICENSE_AUDIT,
                    LogOperationRecordEnum.OperateEnum.UPDATE_BUSINESS_LICENSE_INFO, "变更工商信息");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void invite(UmEmployeeBindingRecordEntity umEmployeeEntity) {
        String id = getUserIdUtil.getUserId();
        // 1、查询当前“坑位”是否存在未处理的邀请记录
        UmEmployeeBindingRecordCondition umEmployeeBindingRecordCondition = new UmEmployeeBindingRecordCondition();
        umEmployeeBindingRecordCondition.setOperatorId(umEmployeeEntity.getOperatorId());
        umEmployeeBindingRecordCondition.setBindingSource(10);
        umEmployeeBindingRecordCondition.setBindingStatus(10);
        umEmployeeBindingRecordCondition.setEmployeeType(20);
        List<UmEmployeeBindingRecordEntity> umEmployeeBindingRecordEntityList = employeeBindingHistoryRepository.queryCondition(umEmployeeBindingRecordCondition);
        if (CollUtil.isNotEmpty(umEmployeeBindingRecordEntityList)) {
            UmEmployeeBindingRecordEntity umEmployeeBindingRecord = IterUtil.getFirst(umEmployeeBindingRecordEntityList);
            Integer bindingStatus = umEmployeeBindingRecord.getBindingStatus();

            if (ObjectUtil.equal(bindingStatus, 10)) {
                throw new CtpCorePartnerException("已存在未处理的邀请记录，请勿重复提交");
            }
        }

        // 2、查询当前员工是否注册
        MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
        mdmUserInfoQuery.setMobile(umEmployeeEntity.getUserMobile());
        ResultMode<List<MdmUserInfoDTO>> resultMode = mdmExchangeService.userInfoQueryList(mdmUserInfoQuery);
        List<MdmUserInfoDTO> userInfoDTOList = resultMode.getModel();
        if (CollUtil.isEmpty(userInfoDTOList)) {
            throw new CtpCorePartnerException("您邀请的用户未注册，无法发起邀请");
        }
        MdmUserInfoDTO userInfoDTO = IterUtil.getFirst(userInfoDTOList);

        Long employeeId = userInfoDTO.getId();
        // 被邀请员工 用户id
        umEmployeeEntity.setEmployeeId(String.valueOf(employeeId));

        // 不能邀请自己成为员工
        if (StrUtil.equals(id, umEmployeeEntity.getEmployeeId())) {
            throw new CtpCorePartnerException("不能邀请自己成为员工");
        }

        // 3、查询被邀请邀功是否进行实名认证
        UmUserAuthAuditEntity auditEntity = umUserAuthAuditRepository.queryByUserId(String.valueOf(employeeId), CommonAuditStatusEnum.PASS.getCode());
        if (ObjectUtil.isEmpty(auditEntity)) {
            throw new CtpCorePartnerException("您邀请的用户未实名认证，无法发起邀请");
        }

        // 4、查询是否存在重复邀请
        UmEmployeeBindingRecordCondition condition = new UmEmployeeBindingRecordCondition();
        condition.setEmployeeType(20);
        condition.setEmployeeId(String.valueOf(employeeId));
        condition.setCompanyId(umEmployeeEntity.getCompanyId());
        condition.setBindingStatus(10);
        List<UmEmployeeBindingRecordEntity> employeeBindingRecordEntities = employeeBindingHistoryRepository.queryCondition(condition);
        if (CollUtil.isNotEmpty(employeeBindingRecordEntities)) {
            throw new CtpCorePartnerException("您邀请的用户已存在待处理的记录，不可重复邀请");
        }

        // 5、查询当前员工是否已绑定该公司
        QueryBoundInfoCondition queryBoundInfoCondition = new QueryBoundInfoCondition();
        queryBoundInfoCondition.setUserId(String.valueOf(employeeId));
        List<BoundCompanyEntity> boundCompanyEntities = umCompanyMemberRepository.queryBoundCompanyInfo(queryBoundInfoCondition);
        if (CollUtil.isNotEmpty(boundCompanyEntities)) {
            // 是否已绑定
            boolean hasBound = boundCompanyEntities.stream()
                    .anyMatch(entity -> entity.getCompanyId().equals(umEmployeeEntity.getCompanyId()));
            if (hasBound) {
                throw new CtpCorePartnerException("您邀请的用户已绑定该企业，无法发起邀请");
            }
        }

        // 6、新增邀请记录
        employeeBindingHistoryRepository.insert(umEmployeeEntity);

        // 7、更新operator表状态为邀请中
        MdmOperatorCommand mdmOperatorCommand = new MdmOperatorCommand();
        mdmOperatorCommand.setId(String.valueOf(umEmployeeEntity.getOperatorId()));
        mdmOperatorCommand.setInviteStatus("20");
        ResultMode<Void> updateOperator = mdmExchangeService.updateOperator(mdmOperatorCommand);
        if (!updateOperator.isSucceed()) {
            throw new CtpCorePartnerException("更新operator表状态为邀请中失败");
        }
        log.info("新增邀请记录成功:{}", umEmployeeEntity);

    }

    /**
     * 发送mq 消息
     *
     * @param id
     */
    private void sendMessage(String id, CreateCompanyBO bo) {
        try {
            MdmCompanyApplyCommand mdmCompanyApplyCommand = bo.getMdmCompanyApplyCommand();
            mdmCompanyApplyCommand.setId(Long.valueOf(id));
            mdmCompanyApplyCommand.setAptitudeStatus("3");
            MqEventMessage.EventMessage<MdmCompanyApplyCommand> operatorBOEventMessage =
                    MqEventMessage.buildEventMessage(KafkaConstants.CTP_BUSINESS_LICENSE_AUTO_AUDIT_TOPIC, mdmCompanyApplyCommand);
            mqEventPublisher.publishNormalMessage(KafkaConstants.CTP_BUSINESS_LICENSE_AUTO_AUDIT_TOPIC + ":" + KafkaConstants.COMPANY_AUTO_AUDIT_TAG, operatorBOEventMessage);
        } catch (Exception e) {
            log.error("企业自动审核发送mq消息失败", e);
        }
    }

    /**
     * 更新企业授权委托书历史审核记录为已删除
     *
     * @param companyId
     */
    private void updateHistoryAccreditAudit(String companyId) {
        if (StrUtil.isEmpty(companyId)) {
            return;
        }
        UmAccreditAuditEntity entity = new UmAccreditAuditEntity();
        entity.setDelFlag(DeletedFlagEnum.DELETED.getCode());
        entity.setLatestDataFlag(0);
        entity.setCompanyId(companyId);
        Integer count = umAccreditAuditRepository.update(entity);
        log.info("更新历史授权审核记录成功，更新条数：{}", count);
    }

    /**
     * 更新该企业历史工商信息审核记录为已删除
     *
     * @param licenseNo
     */
    private void updateHistoryCompanyAptitudeAudit(String licenseNo) {
        if (StrUtil.isEmpty(licenseNo)) {
            return;
        }
        MdmCompanyApplyCommand updateCompanyApplyCommand = new MdmCompanyApplyCommand();
        updateCompanyApplyCommand.setLicenseNo(licenseNo);
        updateCompanyApplyCommand.setApplySource("1");
        updateCompanyApplyCommand.setDelFlag(String.valueOf(DeletedFlagEnum.DELETED.getCode()));
        ResultMode<Integer> updateResultMode = mdmExchangeService.updateCompanyApplyStatus(updateCompanyApplyCommand);
        if (!updateResultMode.isSucceed()) {
            throw new CtpCorePartnerException(updateResultMode.getCode(), updateResultMode.getMessage());
        }
    }


    public EnterpriseInfoDTO getPlatformCompanyDetail(EnterpriseCondition condition) {
        EnterpriseInfoDTO enterpriseInfoDTO = new EnterpriseInfoDTO();

        // 1、处理企业角色信息
        processCompanyRole(condition, enterpriseInfoDTO);


        // 2、查询公司基本信息
        UmCompanyEntity umCompanyEntity = umCompanyRepository.selectByPrimaryKey(Long.valueOf(condition.getCompanyId()));
        if (ObjectUtil.isEmpty(umCompanyEntity)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.ENTERPRISE_INFO_NOT_FOUND);
        }
        enterpriseInfoDTO.setCompanyId(umCompanyEntity.getId().toString());
        enterpriseInfoDTO.setCompanyName(umCompanyEntity.getCompanyName());

        // 2、查询主数据授权委托书信息
        MdmCompanyInfoDTO mdmCmdCompany = mdmExchangeService.getMdmCmdCompanyByLicenseNo(umCompanyEntity.getSocialCreditCode());
        EnterpriseAuthorizationInfoDTO enterpriseAuthorizationInfo = new EnterpriseAuthorizationInfoDTO();
        UmAccreditAuditCondition umAccreditAuditCondition = new UmAccreditAuditCondition();
        umAccreditAuditCondition.setCompanyId(condition.getCompanyId());

        // 查询授权委托书审核记录
        List<UmAccreditAuditEntity> umAccreditAuditEntities = umAccreditAuditRepository.queryCondition(umAccreditAuditCondition);
        UmAccreditAuditEntity umAccreditAuditEntity = umAccreditAuditEntities.get(0);
        if (mdmCmdCompany != null) {
            MdmAccreditOperatorQuery mdmAccreditOperatorQuery = new MdmAccreditOperatorQuery();
            mdmAccreditOperatorQuery.setCompanyId(mdmCmdCompany.getId().toString());
            mdmAccreditOperatorQuery.setPlatformCode("22");
            ResultMode<List<MdmAccreditOperatorDTO>> accreditOperatorList = mdmExchangeService.getAccreditOperatorList(mdmAccreditOperatorQuery);
            if (!accreditOperatorList.isSucceed()) {
                throw new CtpCorePartnerException(accreditOperatorList.getCode(), accreditOperatorList.getMessage());
            }
            List<MdmAccreditOperatorDTO> accreditOperatorDTOS = accreditOperatorList.getModel();
            if (CollUtil.isNotEmpty(accreditOperatorDTOS)) {
                MdmAccreditOperatorDTO accreditOperator = accreditOperatorDTOS.get(0);
                enterpriseAuthorizationInfo.setId(accreditOperator.getId().toString());
                enterpriseAuthorizationInfo.setCompanyId(umCompanyEntity.getId().toString());
                enterpriseAuthorizationInfo.setCompanyName(umCompanyEntity.getCompanyName());
                enterpriseAuthorizationInfo.setUserName(accreditOperator.getOperatorName());
                enterpriseAuthorizationInfo.setAuditStatus(CommonAuditStatusEnum.PASS.getCode());
                enterpriseAuthorizationInfo.setFileUrl(accreditOperator.getFileUrl());
                MdmOperatorDTO cmdOperator = mdmExchangeService.getCmdOperator(accreditOperator.getOperatorId().toString());
                if (cmdOperator != null) {
                    MdmUserInfoDTO userInfo = mdmExchangeService.queryUserInfo(Long.parseLong(cmdOperator.getUserId()));
                    enterpriseAuthorizationInfo.setIdCard(userInfo.getIdCardNo());
                    enterpriseAuthorizationInfo.setTransferMobile(userInfo.getMobile());
                }
            }
        } else {
            BeanUtil.copyProperties(umAccreditAuditEntity, enterpriseAuthorizationInfo);
            enterpriseAuthorizationInfo.setIdCard(umAccreditAuditEntity.getTransferIdCardNo());
            enterpriseAuthorizationInfo.setUserName(umAccreditAuditEntity.getTransferUserName());
            enterpriseAuthorizationInfo.setTransferMobile(umAccreditAuditEntity.getTransferMobile());
        }
        // 3、查询审核记录表，取最新的审核数据
        if (ObjectUtil.isNotEmpty(umAccreditAuditEntity) && !CommonAuditStatusEnum.PASS.getCode().equals(umAccreditAuditEntity.getAuditStatus())) {
            enterpriseAuthorizationInfo.setAuditStatus(umAccreditAuditEntity.getAuditStatus());
            enterpriseAuthorizationInfo.setRejectReason(umAccreditAuditEntity.getRejectReason());
        }
        String fileUrl = enterpriseAuthorizationInfo.getFileUrl();
        if (StrUtil.isNotEmpty(fileUrl)) {
            enterpriseAuthorizationInfo.setFileUrl(uploadExchangeService.getUrl(fileUrl));

        }
        enterpriseInfoDTO.setPowerOfAttorneyInfo(enterpriseAuthorizationInfo);

        // 信用编码
        String socialCreditCode = umCompanyEntity.getSocialCreditCode();
        if (StrUtil.isNotEmpty(socialCreditCode)) {
            // 4、查询企业工商审核信息
            MdmCompanyApplyQuery query = new MdmCompanyApplyQuery();
            query.setLicenseNo(socialCreditCode);
            ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(query);
            if (!resultMode.isSucceed()) {
                throw new CtpCorePartnerException("调用主数据查询工商审核记录失败：" + resultMode.getMessage());
            }

            List<MdmCompanyApplyDTO> mdmCompanyApplyDTOS = resultMode.getModel();
            if (CollUtil.isEmpty(mdmCompanyApplyDTOS)) {
                throw new CtpCorePartnerException("未查询到该企业的工商审核记录");
            }

            // 按更新时间获取最新的一条审核数据
            MdmCompanyApplyDTO mdmCmdCompanyByLicenseNo = mdmCompanyApplyDTOS.stream().
                    max(Comparator.comparing(MdmCompanyApplyDTO::getUpdatedDate)).orElse(null);

            if (ObjectUtil.isNotEmpty(mdmCmdCompanyByLicenseNo)) {
                BusinessLicenseInfoDTO businessLicenseInfoDTO = BeanUtil.copyProperties(mdmCmdCompanyByLicenseNo, BusinessLicenseInfoDTO.class);
                businessLicenseInfoDTO.setRejectReason(mdmCmdCompanyByLicenseNo.getReviewReason());
                businessLicenseInfoDTO.setAptitudeStatus(mdmCmdCompanyByLicenseNo.getAptitudeStatus());
                businessLicenseInfoDTO.setCompanyName(mdmCmdCompanyByLicenseNo.getLicenseName());
                businessLicenseInfoDTO.setSocialCreditCode(mdmCmdCompanyByLicenseNo.getLicenseNo());
                businessLicenseInfoDTO.setLegalPerson(mdmCmdCompanyByLicenseNo.getLegalPerson());

                // 处理图片url
                if (StrUtil.isNotEmpty(mdmCmdCompanyByLicenseNo.getForntFileUrl())) {
                    businessLicenseInfoDTO.setFileUrl(uploadExchangeService.getUrl(mdmCmdCompanyByLicenseNo.getForntFileUrl()));
                }

                enterpriseInfoDTO.setBusinessLicenseInfo(businessLicenseInfoDTO);

                // 查询法人审核信息
                enterpriseInfoDTO.setLegalPersonInfo(fetchLegalPersonInfo(socialCreditCode));
            }
        }
        return enterpriseInfoDTO;
    }

    public IdCardOcrDTO identityCardOcr(IdCardOcrCommand command) {
        IdCardOcrDTO dto = new IdCardOcrDTO();
        DirectionOcrQuery directionOcrQuery = new DirectionOcrQuery();
        directionOcrQuery.setImageUrl(command.getFrontUrl());
        directionOcrQuery.setDirection(OtsEnums.OcrDirection.FRONT);
        // 1、ocr 识别
        // 1.1 身份证正面(国徽页)
        log.info("身份证国徽页ocr调用参数：{}", directionOcrQuery);
        ResultMode<OtsIdentityCardOcrDTO> frontResultModel = otsExchangeService.identityCardOcr(directionOcrQuery);
        log.info("身份证国徽页ocr调用结果：{}，识别结果：{}", frontResultModel, frontResultModel.getModel());
        if (!frontResultModel.isSucceed()) {
            return dto;
        }
        OtsIdentityCardOcrDTO frontModel = frontResultModel.getModel();
        if (ObjectUtil.isNotEmpty(frontModel)) {
            dto.setFrontUrl(StrUtil.subBefore(command.getFrontUrl(), "?", true));
            // 发证机关
            dto.setIssuingAgency(frontModel.getAuthority());
            // 身份证有效期-开始日期 & 截止日期
            dto.setStartDate(CommonUtil.safeParseForLocalDate(frontModel.getIssueDate()));
            dto.setUntilDate(CommonUtil.safeParseForLocalDate(frontModel.getTimeLimit()));

        } else {
            return dto;
        }

        // 1.2 身份证反面（人像页）
        directionOcrQuery.setImageUrl(command.getBehindUrl());
        directionOcrQuery.setDirection(OtsEnums.OcrDirection.BACK);
        log.info("身份证人像页ocr调用参数：{}", directionOcrQuery);
        ResultMode<OtsIdentityCardOcrDTO> backResultModel = otsExchangeService.identityCardOcr(directionOcrQuery);
        log.info("身份证人像页ocr调用结果：{}，识别结果：{}", backResultModel, backResultModel.getModel());
        if (!backResultModel.isSucceed()) {
            return dto;
        }
        OtsIdentityCardOcrDTO backModel = backResultModel.getModel();
        if (ObjectUtil.isNotEmpty(backModel)) {
            // 截取地址，第一个问号后边全部截掉
            dto.setBehindUrl(StrUtil.subBefore(command.getBehindUrl(), "?", false));
            dto.setUserName(backModel.getName());
            dto.setIdCardNo(backModel.getIdCard());
            dto.setAddrDetail(backModel.getAddress());
            String sex = backModel.getSex();
            if (StrUtil.isNotEmpty(sex)) {
                if (StrUtil.equals(sex, "男")) {
                    dto.setGender(1);
                } else {
                    dto.setGender(2);
                }
            }

            // 民族
            dto.setNation(backModel.getNation());
            // 出生日期
            dto.setBirthday(CommonUtil.safeParseForLocalDate(backModel.getBirthday()));

        } else {
            return dto;
        }
        return dto;
    }

    public void updateLegalInfo(UmCompanyLegalPersonAuditAddCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        if (ObjectUtil.isEmpty(tokenInfo)) {
            throw new CtpCorePartnerException(CtpCorePartnerExceptionEnum.CTP_USER_NOT_LOGIN);
        }
        // 获取用户id
        String userId = getUserIdUtil.getUserId();
        // 获取信用代码
        String licenseNo = command.getLicenseNo();
        // 查询是否存在待审核的法人审核记录
        UmCompanyLegalPersonAuditCondition condition = new UmCompanyLegalPersonAuditCondition();
        condition.setSocialCreditCode(licenseNo);
        condition.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        List<UmCompanyLegalPersonAuditEntity> personAuditEntityList = umCompanyLegalPersonAuditRepository.queryByCondition(condition);
        if (CollUtil.isNotEmpty(personAuditEntityList)) {
            throw new CtpCorePartnerException("当前企业存在待审核的法人审核记录，请勿重复提交");
        }


        UmCompanyLegalPersonAuditEntity entity = BeanUtil.copyProperties(command, UmCompanyLegalPersonAuditEntity.class);
        entity.setSocialCreditCode(licenseNo);
        entity.setCreatorId(userId);
        entity.setUpdaterId(userId);
        entity.setAuditStatus(CommonAuditStatusEnum.WAITING.getCode());
        entity.setApplySource(CmdEnums.ApplySourcesEnum.BULK_TRADE_PLATFORM.getCode());

        CreateCompanyBO bo = new CreateCompanyBO();
        bo.setUmCompanyLegalPersonAuditEntity(entity);
        bo.setSocialCreditCode(licenseNo);
        // 法人信息二要素认证
        checkLegalPersonTwoElements(bo);

        // 处理文件地址
        entity.setFrontUrl(StrUtil.subBefore(entity.getFrontUrl(), "?", true));
        entity.setBehindUrl(StrUtil.subBefore(entity.getBehindUrl(), "?", true));
        bo.setUmCompanyLegalPersonAuditEntity(entity);

        // 新增法人审核记录
        addCompanyLegalPersonAudit(bo);
    }

    public CompanyAuditInfoDTO queryCompanyAuditInfo(CompanyAuditInfoCondition condition) {
        if (condition == null || CollUtil.isEmpty(condition.getSceneTypeList())) {
            throw new CtpCorePartnerException("查询条件或场景类型不能为空");
        }

        CompanyAuditInfoDTO dto = new CompanyAuditInfoDTO();

        for (String sceneType : condition.getSceneTypeList()) {
            try {
                if (StrUtil.equals(sceneType, CompanyAuditSceneTypeEnum.COMPANY_QUALIFICATION.getCode())) {
                    handleBusinessLicenseInfo(condition, dto);
                } else if (StrUtil.equals(sceneType, CompanyAuditSceneTypeEnum.LEGAL_PERSON_INFO.getCode())) {
                    handleLegalPersonInfo(condition, dto);
                } else if (StrUtil.equals(sceneType, CompanyAuditSceneTypeEnum.POWER_OF_ATTORNEY.getCode())) {
                    handlePowerOfAttorneyInfo(condition, dto);
                } else {
                    log.warn("未知的企业审核场景类型：{}", sceneType);
                }
            } catch (Exception e) {
                log.error("处理企业审核场景 {} 时发生异常", sceneType, e);
                throw new CtpCorePartnerException("查询企业审核信息失败，请联系管理员");
            }
        }

        return dto;
    }

    /**
     * 处理企业资质审核信息（营业执照）
     */
    private void handleBusinessLicenseInfo(CompanyAuditInfoCondition condition, CompanyAuditInfoDTO dto) {
        MdmCompanyApplyQuery query = new MdmCompanyApplyQuery();
        query.setLicenseNo(condition.getLicenseNo());
        query.setApplySource(ApplySourceEnum.MALL.getCode());

        ResultMode<List<MdmCompanyApplyDTO>> resultMode = mdmExchangeService.queryCompanyApplyList(query);

        if (resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            MdmCompanyApplyDTO firstRecord = IterUtil.getFirst(resultMode.getModel());
            BusinessLicenseInfoDTO licenseInfo = new BusinessLicenseInfoDTO();
            licenseInfo.setId(firstRecord.getId());
            licenseInfo.setCompanyName(firstRecord.getLicenseName());
            licenseInfo.setSocialCreditCode(firstRecord.getLicenseNo());
            licenseInfo.setLegalPerson(firstRecord.getLegalPerson());
            licenseInfo.setAptitudeStatus(firstRecord.getAptitudeStatus());
            licenseInfo.setRejectReason(firstRecord.getReviewReason());
            licenseInfo.setProvince(firstRecord.getProvince());
            licenseInfo.setProvinceName(firstRecord.getProvinceName());
            licenseInfo.setCity(firstRecord.getCity());
            licenseInfo.setCityName(firstRecord.getCityName());
            licenseInfo.setArea(firstRecord.getArea());
            licenseInfo.setAreaName(firstRecord.getAreaName());
            licenseInfo.setStreet(firstRecord.getStreet());
            licenseInfo.setStreetName(firstRecord.getStreetName());
            licenseInfo.setAddressDetail(firstRecord.getAddressDetail());
            String forntFileUrl = firstRecord.getForntFileUrl();

            if (forntFileUrl != null) {
                licenseInfo.setFileUrl(uploadExchangeService.getUrl(forntFileUrl));
            }

            dto.setBusinessLicenseInfo(licenseInfo);
            dto.setCompanyName(firstRecord.getLicenseName());
            dto.setLicenseNo(firstRecord.getLicenseNo());
        } else {
            log.warn("未查询到营业执照审核信息，统一信用代码：{}", condition.getLicenseNo());
        }
    }

    /**
     * 处理法人信息审核
     */
    private void handleLegalPersonInfo(CompanyAuditInfoCondition condition, CompanyAuditInfoDTO dto) {
        UmCompanyLegalPersonAuditEntity entity = umCompanyLegalPersonAuditRepository.queryBySocialCreditCode(condition.getLicenseNo());
        if (entity != null) {
            UmCompanyLegalPersonAuditDTO auditDTO = new UmCompanyLegalPersonAuditDTO();
            auditDTO.setId(entity.getId());
            auditDTO.setUserName(entity.getUserName());
            auditDTO.setIdCardNo(entity.getIdCardNo());
            auditDTO.setGender(entity.getGender());
            auditDTO.setBirthday(entity.getBirthday());
            auditDTO.setAddrDetail(entity.getAddrDetail());
            auditDTO.setUserMobile(entity.getUserMobile());
            auditDTO.setUserEmail(entity.getUserEmail());
            auditDTO.setStartDate(entity.getStartDate());
            auditDTO.setUntilDate(entity.getUntilDate());
            auditDTO.setLicenseLongValidFlag(entity.getLicenseLongValidFlag());
            auditDTO.setProvinceCode(entity.getProvinceCode());
            auditDTO.setProvinceName(entity.getProvinceName());
            auditDTO.setCityCode(entity.getCityCode());
            auditDTO.setCityName(entity.getCityName());
            auditDTO.setCountyCode(entity.getCountyCode());
            auditDTO.setCountyName(entity.getCountyName());
            auditDTO.setAddrDetail(entity.getAddrDetail());
            auditDTO.setAuditStatus(entity.getAuditStatus());
            String frontUrl = entity.getFrontUrl();
            if (frontUrl != null) {
                auditDTO.setFrontUrl(uploadExchangeService.getUrl(frontUrl));
            }
            String behindUrl = entity.getBehindUrl();
            if (behindUrl != null) {
                auditDTO.setBehindUrl(uploadExchangeService.getUrl(behindUrl));
            }
            dto.setLegalPersonInfo(auditDTO);
        } else {
            log.warn("未查询到法人审核信息，统一信用代码：{}", condition.getLicenseNo());
        }
    }

    /**
     * 处理授权委托书审核信息
     */
    private void handlePowerOfAttorneyInfo(CompanyAuditInfoCondition condition, CompanyAuditInfoDTO dto) {
        UmAccreditAuditCondition auditCondition = new UmAccreditAuditCondition();
        auditCondition.setSocialCreditCode(condition.getLicenseNo());

        List<UmAccreditAuditEntity> entities = umAccreditAuditRepository.queryCondition(auditCondition);
        if (CollUtil.isNotEmpty(entities)) {
            // 根据创建时间取最新一条数据
            UmAccreditAuditEntity first = entities.stream()
                    .max(Comparator.comparing(UmAccreditAuditEntity::getCreatedDate))
                    .orElse(IterUtil.getFirst(entities));
            EnterpriseAuthorizationInfoDTO infoDTO = new EnterpriseAuthorizationInfoDTO();
            infoDTO.setAuditStatus(first.getAuditStatus());
            infoDTO.setRejectReason(first.getRejectReason());
            infoDTO.setCompanyName(first.getCompanyName());
            infoDTO.setCompanyId(first.getCompanyId());
            infoDTO.setUserName(first.getUserName());
            infoDTO.setIdCard(first.getTransferIdCardNo());
            infoDTO.setTransferMobile(first.getTransferMobile());
            String fileUrl = first.getFileUrl();
            if (fileUrl != null) {
                infoDTO.setFileUrl(uploadExchangeService.getUrl(fileUrl));
            }
            dto.setPowerOfAttorneyInfo(infoDTO);
        } else {
            log.warn("未查询到授权委托书审核信息，统一信用代码：{}", condition.getLicenseNo());
        }
    }

}
