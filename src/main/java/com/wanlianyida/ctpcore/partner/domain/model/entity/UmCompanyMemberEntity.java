package com.wanlianyida.ctpcore.partner.domain.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业员工信息表 Entity
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("um_company_member")
public class UmCompanyMemberEntity implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 平台类型 10用户端 20平台端
	 */
	public String platformType;

	/**
	 * 企业信息ID
	 */
	@TableField("company_id")
	private String companyId;

	@TableField(exist = false)
	private String companyName;

	/**
	 * 信用编码
	 */
	@TableField(exist = false)
	private String licenseNo;

	/**
	 * 用户等级:1管理员,2普通员工
	 */
	@TableField("level_type")
	private String levelType;

	/**
	 * 用户id
	 */
	private String userBaseId;

	/**
	 * 用户名
	 */
	private String userName;

	/**
	 * 用户账号
	 */
	private String loginName;

	/**
	 * 部门id
	 */
	private String department;

	/**
	 * 岗位ID
	 */
	private String post;

	/**
	 * 状态:11有效,21无效
	 */
	private String status;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	@TableField("del_flag")
	private String delFlag;

	private String remark;

	/**
	 * 操作员id
	 */
	private String operatorId;

	/**
	 * 默认企业标志 0-否 1-是
	 */
	private Integer defaultCompanyFlag;

	/**
	 * 用户手机号
	 */
	private String userMobile;

	@TableField("user_id")
	private String userId;

}
