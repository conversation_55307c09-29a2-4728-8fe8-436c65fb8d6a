package com.wanlianyida.ctpcore.partner.domain.model.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:18
 */
@Data
@ApiModel("用户实名认证审核")
public class UmUserAuthAuditCondition {

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("审核状态[10-待审核，20-通过，90-不通过]")
    private Integer auditStatus;
}
