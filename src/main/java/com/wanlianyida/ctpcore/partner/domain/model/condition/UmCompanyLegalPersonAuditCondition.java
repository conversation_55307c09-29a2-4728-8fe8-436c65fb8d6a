package com.wanlianyida.ctpcore.partner.domain.model.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 10:13
 */
@Data
@ApiModel("公司法定代表人审核表")
public class UmCompanyLegalPersonAuditCondition {

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("企业社会信用代码")
    private List<String> socialCreditCodeList;

    @ApiModelProperty("审核状态[1-待审核,2-不通过,3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("申请来源[1-大宗平台,2-物流平台']")
    private String applySource;
}
