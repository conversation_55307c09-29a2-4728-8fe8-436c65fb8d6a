package com.wanlianyida.ctpcore.partner.domain.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业营业执照信息表 Entity
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
@TableName("um_company_business_license")
public class UmCompanyBusinessLicenseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 企业信息ID
	 */
	private String companyId;

	/**
	 * 证照类型:1000企营,1100个体,2100道运资质,1900其他
	 */
	private String certificateType;

	/**
	 * 证照企业企业名称
	 */
	private String licenseName;

	/**
	 * 统一社会信用代码 
	 */
	private String licenseNo;

	/**
	 * 企业法人
	 */
	private String legalPerson;

	/**
	 * 企业法人身份证号
	 */
	private String legalPersonIdCard = "";

	/**
	 * 发证机关单位
	 */
	private String licenseDepartmentGov;

	/**
	 * 证照有效期起日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date licenseStartDate;

	/**
	 * 证照有效期止日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date licenseEndDate = null;

	/**
	 * 有效期总数
	 */
	private Integer licenseValidDate;

	/**
	 * 是否为长期有效:11是,21否
	 */
	private String licenseValidIsLong;

	/**
	 * 证照正面照片地址
	 */
	private String forntFileUrl;

	/**
	 * 经营范围
	 */
	private String manageScope;

	/**
	 * 企业类型(证照里面的文本)
	 */
	private String companyType;

	/**
	 * 状态:1待审核,2审核不通过,3审核通过
	 */
	private String aptitudeStatus;

	/**
	 * 审核不通过原因
	 */
	private String reviewReason = "";

	/**
	 * 企业地址(省Code)
	 */
	private String province;

	/**
	 * 企业地址(省)
	 */
	private String provinceName;

	/**
	 * 企业地址(市Code)
	 */
	private String city;

	/**
	 * 企业地址(市)
	 */
	private String cityName;

	/**
	 * 企业地址(区/县Code)
	 */
	private String area;

	/**
	 * 企业地址(区/县)
	 */
	private String areaName;

	/**
	 * 企业地址(镇/街道Code)
	 */
	private String street;

	/**
	 * 企业地址(镇/街道)
	 */
	private String streetName;

	/**
	 * 企业详细地址
	 */
	private String addressDetail;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 联系人
	 */
	private String contacts;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 主管税务机关
	 */
	private String taxAuthority = "";

	/**
	 * 注册资本
	 */
	private BigDecimal registeredCapital;

	/**
	 * 成立日期
	 */
	private String foundDate;

	/**
	 * 法人代表证明及授权书url
	 */
	private String legalPersonAuthUrl;

	/**
	 * 法人授权状态:1已授权,2未授权
	 */
	private String legalPersonAuthStatus;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;


}
