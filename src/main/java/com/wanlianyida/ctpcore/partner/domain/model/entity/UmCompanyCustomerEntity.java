package com.wanlianyida.ctpcore.partner.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业客户/供应商信息表 entity
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@Data
public class UmCompanyCustomerEntity implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 企业信息ID
	 */
	private String companyId;

	/**
	 * 平台已注册:1是,0否
	 */
	private String isRegister;

	/**
	 * 类型:10客户,20供应商
	 */
	private String customerType;

	/**
	 * 客户/供应商名称
	 */
	private String customerName;

	/**
	 * 统一社会信用代码
	 */
	private String socialCreditCode;

	/**
	 * 联系人
	 */
	private String contacts;

	/**
	 * 联系电话
	 */
	private String phone;

	/**
	 * 备注
	 */
	private String remark = "";

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

	/**
	 * 企业来源[10-订单转化 20-手动录入]
	 */
	private Integer companySource;

	/**
	 * 成交情况[10-暂无成交 20-有过成交]
	 */
	private Integer dealStatus;

}
