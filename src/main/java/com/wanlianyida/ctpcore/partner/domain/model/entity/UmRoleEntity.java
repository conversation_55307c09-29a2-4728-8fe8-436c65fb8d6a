package com.wanlianyida.ctpcore.partner.domain.model.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Data
public class UmRoleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 角色编码[10-采购,20-销售]
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 开通状态[10-开通,20-未开通]
     */
    private String status;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 创建用户名称
     */
    private String creatorName;


    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 更新用户名称
     */
    private String updateName;

}
