package com.wanlianyida.ctpcore.partner.domain.repository;

import com.wanlianyida.ctpcore.partner.domain.model.bo.UmShopAddAndUpdateBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.ShopHallByPageCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmShopCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyBusinessLicenseEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopAreaEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopCategoryEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmShopEntity;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmShopCompanyDTO;

import java.util.List;

public interface UmShopRepository {
    /**
     * 添加店铺
     *
     * @param umShopAddAndUpdateBO um商店添加和更新bo
     * @return {@code Boolean }
     */
    Boolean addShop(UmShopAddAndUpdateBO umShopAddAndUpdateBO);

    /**
     * 更新店铺
     *
     * @param umShopAddAndUpdateBO um商店添加和更新bo
     * @return {@code Boolean }
     */
    Boolean updateShop(UmShopAddAndUpdateBO umShopAddAndUpdateBO);

    /**
     * 更新店铺导航类别
     *
     * @param id      身份证件
     * @param navType 没有类型
     * @return {@code Boolean }
     */
    Boolean updateCategoriesForShopNav(Long id, String navType);

    /**
     * 按店铺id查询店铺主营地区信息
     *
     * @param shopIds 店铺ID
     * @return {@code List<UmShopAreaEntity> }
     */
    List<UmShopAreaEntity> selectShopAreaByShopIds(List<Long>shopIds);

    /**
     * 按店铺id查询店铺品类信息
     *
     * @param shopIds 店铺ID
     * @return {@code List<UmShopCategoryEntity> }
     */
    List<UmShopCategoryEntity> selectShopCategoryByShopIds(List<Long>shopIds);

    /**
     * 按店铺id/公司ID 查询店铺详细信息
     *
     * @param shopId 店铺id
     * @param companyId 公司ID
     * @return {@code UmShopEntity }
     */
    UmShopEntity shopDetailByShopIdOrCompanyId(Long shopId,String companyId);

    /**
     * 按店铺ID批量查询列表
     *
     * @param shopIds 店铺ID
     * @return {@code List<UmShopEntity> }
     */
    List<UmShopEntity> batchQueryListByShopIds(List<Long> shopIds);

    /**
     * 批量获取店铺联系人列表
     *
     * @param shopIds 店铺ID
     * @return {@code List<UmShopEntity> }
     */
    List<UmShopEntity> getShopContactsList(List<Long> shopIds);

    /**
     * 根据公司ID获取店铺id集合
     *
     * @param companyId 公司id
     * @return {@code List<Long> }
     */
    List<Long> getShopIdsForCompanyId(String companyId);

    /**
     * 按id查询
     *
     * @param id 身份证件
     * @return {@code UmShopEntity }
     */
    UmShopEntity queryById(Long id);

    /**
     * 分页查询
     *
     * @param condition 条件
     * @return {@code List<UmShopEntity> }
     */
    List<UmShopEntity> queryPage(UmShopCondition condition);

    void addShop(UmCompanyBusinessLicenseEntity entity);

    /**
     * 查询指定范围的数据
     * @param i
     * @param timeUnit
     * @return
     */
    List<UmShopEntity> queryDataByDateRange(String i, String timeUnit);

    /**
     * 店铺大厅
     * @param condition
     * @return
     */
    List<UmShopEntity> queryShopHallByPage(ShopHallByPageCondition condition);

    UmShopCompanyDTO queryShopCompanyIdList(String str);
}
