package com.wanlianyida.ctpcore.partner.domain.repository;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.ctpcore.partner.domain.model.bo.UmCompanyCustomerBO;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmCompanyCustomerCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmSupplierCustomerCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyCustomerEntity;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmCompanyCustomerDTO;
import com.wanlianyida.ctpcore.partner.interfaces.model.dto.UmSupplierCustomerDTO;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;

import java.util.List;

/**
 * 企业客户/供应商信息表 Service
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
public interface UmCompanyCustomerRepository {

	/**
	 * 分页查询
	 * @param pagingInfo 分页参数
	 * @return {@link PageInfo}<{@link UmCompanyCustomerDTO}>
	 */
	PageInfo<UmCompanyCustomerDTO> queryPage(PagingInfo<UmCompanyCustomerCondition> pagingInfo);

	/**
	 * 列表查询
	 *
	 * @param condition
	 * @return {@link List}<{@link UmCompanyCustomerEntity}>
	 */
	List<UmCompanyCustomerEntity> queryList(UmCompanyCustomerCondition condition);

	/**
     * 新增
     *
     * @param bo
	 * @return Long
     */
	Long insert(UmCompanyCustomerBO bo);

	/**
	 * 修改
	 *
	 * @param bo
	 * @return Long
	 */
	Long update(UmCompanyCustomerBO bo);

	/**
	 * 逻辑删除
	 *
	 * @param id
	 * @return Long
	 */
	Long delete(Long id);

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link UmCompanyCustomerEntity}
	 */
	UmCompanyCustomerEntity selectByPrimaryKey(Long id);

	List<UmSupplierCustomerDTO> querySupplierList(UmSupplierCustomerCondition toBean);

	void updateUmCompanyCustomerByCompanyId(UmCompanyCustomerCondition condition);

	Long insertCust(UmCompanyCustomerBO bo) ;
}
