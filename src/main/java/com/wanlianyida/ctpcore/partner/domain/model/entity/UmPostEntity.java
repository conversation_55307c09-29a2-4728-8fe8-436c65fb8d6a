package com.wanlianyida.ctpcore.partner.domain.model.entity;

import lombok.Data;

import java.util.Date;

/**
 * 岗位管理entity
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@Data
public class UmPostEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;


    /**
     * 企业ID
     */
    private String relCompanyId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位级别
     */
    private String postGrade;

    /**
     * 状态(0停用 1启用)
     */
    private Short enableStatus;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序序号
     */
    private Integer sortNumber;


    /**
     * 创建时间
     */
    private Date createdDate;


    /**
     * 平台类型 10-用户端 20-平台端
     */
    private String platformType;

}
