package com.wanlianyida.ctpcore.partner.infrastructure.exchange;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.ShortUrlGeneratorInter;
import com.wanlianyida.support.api.model.dto.ShortUrlResponse;
import com.wanlianyida.support.api.model.query.ShirtUrlRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

import javax.annotation.Resource;

/**
 * @ClassName: ShortUrlExchangeService
 * @description:
 * @author: zhang<PERSON>hen
 * @date: 2025年07月28日
 * @version: 1.0
 */
@Slf4j
@Component
public class ShortUrlExchangeService {

    @Resource
    private ShortUrlGeneratorInter shortUrlGeneratorInter;

    /**
     * @description: 生成短链
     * @param request
     * @return: String
     */
    public String generateShortUrl(ShirtUrlRequest request){
        log.info("ShortUrlExchangeService.generateShortUrl request:{}",request);
        try {
            ResultMode<ShortUrlResponse> resultMode = shortUrlGeneratorInter.generateShortUrl(request);
            if (resultMode.isSucceed() && Objects.nonNull(resultMode.getModel())){
                return resultMode.getModel().getShort_url();
            }
        } catch (Exception e) {
            log.error("生成短链失败", e);
        }
        return null ;
    }
}
