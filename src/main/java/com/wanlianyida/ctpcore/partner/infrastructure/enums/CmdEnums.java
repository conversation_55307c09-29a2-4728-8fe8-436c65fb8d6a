package com.wanlianyida.ctpcore.partner.infrastructure.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/9/24 10:58
 */
public class CmdEnums {

    /**
     * 资质审核过程状态:1待审核,2审核不通过,3审核通过
     */
    @Getter
    public enum AuditStatusEnum {
        AUDIT_STATUS_WAIT("1", "待审核"),
        AUDIT_STATUS_NO("2", "审核不通过"),
        AUDIT_STATUS_YES("3", "审核通过");

        private final String desc;
        private final String code;

        AuditStatusEnum(String code, String desc) {
            this.desc = desc;
            this.code = code;
        }
    }


    /**
     * 注册来源:1大宗平台,2物流平台
     */
    @Getter
    public enum ApplySourcesEnum {
        BULK_TRADE_PLATFORM("1", "大宗平台"),
        LOGISTICS_PLATFORM("2", "物流平台");

        private final String desc;
        private final String code;

        ApplySourcesEnum(String code, String desc) {
            this.desc = desc;
            this.code = code;
        }
    }

    /**
     * 1是0否
     */
    @Getter
    public enum YesNoEnum {
        YES("1", "是"),
        NO("0", "否");

        private final String desc;
        private final String code;

        YesNoEnum(String code, String desc) {
            this.desc = desc;
            this.code = code;
        }
    }

}
