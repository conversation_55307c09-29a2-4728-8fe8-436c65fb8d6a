package com.wanlianyida.ctpcore.partner.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmCompanyMemberCondition;
import com.wanlianyida.ctpcore.partner.domain.model.condition.UmPostCondition;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyMemberEntity;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmPostEntity;
import com.wanlianyida.ctpcore.partner.domain.repository.UmPostRepository;
import com.wanlianyida.ctpcore.partner.infrastructure.enums.DeletedFlagEnum;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.mapper.UmCompanyMemberMapper;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.mapper.UmPostBizMapper;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.po.UmPostBizPO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

@Service
public class UmPostBizRepositoryImpl implements UmPostRepository {
    @Resource
    private UmPostBizMapper umPostBizMapper;

    @Resource
    private UmCompanyMemberMapper umCompanyMemberMapper;

    @Override
    public UmPostEntity selectInfo(UmPostCondition condition) {
        LambdaQueryWrapper<UmPostBizPO> queryWrapper = new LambdaQueryWrapper<UmPostBizPO>()
                .eq(ObjectUtil.isNotEmpty(condition.getId()), UmPostBizPO::getId, condition.getId())
                .eq(ObjectUtil.isNotEmpty(condition.getPostName()), UmPostBizPO::getPostName, condition.getPostName())
                .eq(ObjectUtil.isNotEmpty(condition.getPostGrade()), UmPostBizPO::getPostGrade, condition.getPostGrade())
                .eq(ObjectUtil.isNotEmpty(condition.getRelCompanyId()), UmPostBizPO::getRelCompanyId, condition.getRelCompanyId())
                .eq(UmPostBizPO::getDeleted, DeletedFlagEnum.UN_DELETED.getCode());

        UmPostBizPO umPostBizPO = umPostBizMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(umPostBizPO)) {
            return BeanUtil.copyProperties(umPostBizPO, UmPostEntity.class);
        }
        return null;
    }

    @Override
    public Boolean add(UmPostEntity umPostEntity) {
        // entity 转po
        UmPostBizPO umPostBizPO = BeanUtil.copyProperties(umPostEntity, UmPostBizPO.class);
        int count = umPostBizMapper.insert(umPostBizPO);
        return count > 0;
    }

    @Override
    public Boolean update(UmPostEntity umPostEntity) {
        // entity 转po
        UmPostBizPO umPostBizPO = BeanUtil.copyProperties(umPostEntity, UmPostBizPO.class);
        int count = umPostBizMapper.update(umPostBizPO, new LambdaQueryWrapper<UmPostBizPO>()
                .eq(UmPostBizPO::getId, umPostEntity.getId())
                .eq(UmPostBizPO::getDeleted, DeletedFlagEnum.UN_DELETED.getCode()));
        return count > 0;
    }

    @Override
    public Boolean delete(UmPostEntity umPostEntity) {
        // entity 转po
        UmPostBizPO umPostBizPO = BeanUtil.copyProperties(umPostEntity, UmPostBizPO.class);
        int count = umPostBizMapper.update(umPostBizPO, new LambdaUpdateWrapper<UmPostBizPO>()
                .set(UmPostBizPO::getDeleted, DeletedFlagEnum.DELETED.getCode())
                .eq(UmPostBizPO::getRelCompanyId, umPostEntity.getRelCompanyId())
                .eq(UmPostBizPO::getPlatformType, umPostEntity.getPlatformType())
                .eq(UmPostBizPO::getId, umPostEntity.getId()));

        return count > 0;
    }

    @Override
    public List<UmPostEntity> queryPage(UmPostCondition condition) {
        List<UmPostBizPO> list = umPostBizMapper.queryPage(condition);
        if (IterUtil.isNotEmpty(list)) {
            List<UmPostEntity> biSensitiveWordsEntities = BeanUtil.copyToList(list, UmPostEntity.class);
            return biSensitiveWordsEntities;
        }
        return Collections.emptyList();
    }

    @Override
    public Integer validatePostIsUsed(UmPostCondition condition) {
        UmCompanyMemberCondition memberCondition = new UmCompanyMemberCondition();
        memberCondition.setPost(String.valueOf(condition.getId()));
        memberCondition.setCompanyId(condition.getRelCompanyId());
        memberCondition.setPlatformType(condition.getPlatformType());
        List<UmCompanyMemberEntity> memberEntities = umCompanyMemberMapper.queryByCondition(memberCondition);
        return memberEntities.size();
    }
}
