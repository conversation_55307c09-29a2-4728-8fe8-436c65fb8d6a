package com.wanlianyida.ctpcore.partner.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月28日 15:08
 */
@Data
@TableName("um_business_license_audit")
@ApiModel("营业执照审核")
public class UmBusinessLicenseAuditPO extends Model<UmBusinessLicenseAuditPO> {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("企业类型")
    private String companyType;

    @ApiModelProperty("法定代表人")
    private String legalPerson;

    @ApiModelProperty("注册资本（万元）")
    private String registeredCapital;

    @ApiModelProperty("成立日期")
    private Date foundDate;

    @ApiModelProperty("营业期限截止日期")
    private Date expireDate;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countryCode;

    @ApiModelProperty("县")
    private String countryName;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("照片url")
    private String fileUrl;

    @ApiModelProperty("主管税务机关")
    private String taxAuthority;

    @ApiModelProperty("经营范围")
    private String manageScope;

    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;

    private Integer versionCode;
}
