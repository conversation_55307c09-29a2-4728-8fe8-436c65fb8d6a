package com.wanlianyida.ctpcore.partner.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 字典值(DictValue)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:17
 */
@Data
@ApiModel("字典值")
@TableName("bd_dict_value")
public class DictValuePO extends Model<DictValuePO> {
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("字典编码")
    @TableField("dict_code")
    private String dictCode;

    @ApiModelProperty("字典值")
    @TableField("dict_value")
    private String dictValue;

    @ApiModelProperty("名称")
    @TableField("value_name")
    private String valueName;

    @ApiModelProperty("排序")
    @TableField("sort_num")
    private Integer sortNum;

    @ApiModelProperty("字典描述")
    @TableField("dict_desc")
    private String dictDesc;

    @ApiModelProperty("是否启用，1启用，0禁用")
    @TableField("dict_status")
    private Integer dictStatus;

    @ApiModelProperty("创建人")
    @TableField("creator_id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    @TableField("created_date")
    private Date createdDate;

    @ApiModelProperty("最后更新人")
    @TableField("updater_id")
    private String updaterId;

    @ApiModelProperty("最后更新时间")
    @TableField("updated_date")
    private Date updatedDate;

    @ApiModelProperty("版本号")
    @TableField("version_code")
    private Integer versionCode;

}

