package com.wanlianyida.ctpcore.partner.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 字典表(DictInfo)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:17
 */
@Data
@ApiModel("字典表")
@TableName("bd_dict_info")
public class DictInfoPO extends Model<DictInfoPO> {
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("平台编号")
    @TableField("platform_code")
    private String platformCode;

    @ApiModelProperty("字典编码")
    @TableField("dict_code")
    private String dictCode;

    @ApiModelProperty("字典名")
    @TableField("dict_name")
    private String dictName;

    @ApiModelProperty("描述")
    @TableField("dict_desc")
    private String dictDesc;

    @ApiModelProperty("是否删除，1是0否")
    @TableField("deleted")
    private Integer deleted;

    @ApiModelProperty("创建人")
    @TableField("creator_id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    @TableField("created_date")
    private Date createdDate;

    @ApiModelProperty("最后更新人")
    @TableField("updater_id")
    private String updaterId;

    @ApiModelProperty("最后更新时间")
    @TableField("updated_date")
    private Date updatedDate;

    @ApiModelProperty("版本号")
    @TableField("version_code")
    private Integer versionCode;

}

