package com.wanlianyida.ctpcore.partner.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:05
 */
@Data
@TableName("um_employee_binding_record")
@ApiModel("企业员工绑定记录")
public class UmEmployeeBindingRecordPO extends Model<UmEmployeeBindingRecordPO> {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("员工id")
    private String employeeId;

    @ApiModelProperty("员工编码")
    private String employeeCode;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业编码")
    private String companyCode;

    @ApiModelProperty("员工类型[10-:管理员,20-普通员工]")
    private Integer employeeType;

    @ApiModelProperty("绑定来源[10-邀请,11-注册,20-创建企业,21-过户,22-申请]")
    private Integer bindingSource;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("绑定状态[10-待审核(待确认),20-已绑定,90-已拒绝]")
    private Integer bindingStatus;

    @ApiModelProperty("删除标志[0-未删除,1-已删除]")
    private Integer delFlag;

    /**
     * 操作员id
     */
    private String operatorId;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;

    private Integer versionCode;
}
