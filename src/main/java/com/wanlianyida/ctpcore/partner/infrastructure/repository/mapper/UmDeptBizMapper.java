package com.wanlianyida.ctpcore.partner.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.ctpcore.partner.domain.model.entity.UmDeptEntity;
import com.wanlianyida.ctpcore.partner.infrastructure.repository.po.UmDeptBizPO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户端部门信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
public interface UmDeptBizMapper extends BaseMapper<UmDeptBizPO> {
    /**
     * 查询所有部门列表
     *
     * @param licenseNoList 许可证无列表
     * @param platformType  平台类型
     * @return {@link List }<{@link UmDeptEntity }>
     */
    List<UmDeptEntity> queryAllDeptList(Set<String>licenseNoList,String platformType);
}
