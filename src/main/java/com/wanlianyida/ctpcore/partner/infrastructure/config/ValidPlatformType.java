package com.wanlianyida.ctpcore.partner.infrastructure.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PlatformTypeValidator.class)
public @interface ValidPlatformType {
    String message() default "平台类型不存在或不合法！";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
