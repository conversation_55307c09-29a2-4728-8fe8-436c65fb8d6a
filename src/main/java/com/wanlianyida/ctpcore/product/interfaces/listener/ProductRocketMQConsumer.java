package com.wanlianyida.ctpcore.product.interfaces.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.ctpcore.product.application.command.BatchChangeSaleStatusCommand;
import com.wanlianyida.ctpcore.product.application.service.ProductImportAppService;
import com.wanlianyida.ctpcore.product.application.service.ProductInventoryAppService;
import com.wanlianyida.ctpcore.product.application.service.ProductOperateAppService;
import com.wanlianyida.ctpcore.product.domain.model.bo.SyncMapTableBO;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditLogSpuCondition;
import com.wanlianyida.ctpcore.product.domain.model.condition.ProductSkuCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.AuditLogEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.SkuEntity;
import com.wanlianyida.ctpcore.product.domain.service.AuditDomainService;
import com.wanlianyida.ctpcore.product.domain.service.ProductDomainService;
import com.wanlianyida.ctpcore.product.domain.service.ProductMapDomainService;
import com.wanlianyida.ctpcore.product.infrastructure.constant.RedisConstant;
import com.wanlianyida.ctpcore.product.infrastructure.constant.RocketMqConstant;
import com.wanlianyida.ctpcore.product.infrastructure.enums.InventoryHandlerTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.OnSaleStatusEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.OutSaleTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.event.product.ProductImportEvent;
import com.wanlianyida.ctpcore.product.infrastructure.event.product.ProductSkuSyncMapEvent;
import com.wanlianyida.ctpcore.product.infrastructure.mq.message.OrderStatusUpdMessage;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcore.event.KafkaEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Component
public class ProductRocketMQConsumer {
    @Resource
    private ProductInventoryAppService productInventoryAppService;

    @Resource
    private RedisService redisService;

    @Resource
    private ProductMapDomainService productMapDomainService;

    @Resource
    private ProductImportAppService productImportAppService;

    @Resource
    private ProductDomainService productDomainService;

    @Resource
    private AuditDomainService auditDomainService;

    @Resource
    private ProductOperateAppService productOperateAppService;

    @Service
    @RocketMQMessageListener(
            namespace = "${rocketmq.push-consumer.name-space:}",
            topic= RocketMqConstant.CTP_ORDER_STATUS_UPDATED_TO_INVENTORY_TOPIC,
            consumerGroup=RocketMqConstant.CTP_ORDER_STATUS_UPDATED_TO_INVENTORY_GROUP,
            tag =RocketMqConstant.COMMON_TAG)
    public class handleOrderStatusUpd implements RocketMQListener {
        @Override
        public ConsumeResult consume(MessageView messageView) {
            log.info("rocketMq消费#handleOrderStatusUpd消费开始：{}",messageView);
            ByteBuffer body = messageView.getBody();
            if(messageView.getBody() == null){
                log.info("rocketMq消费#handleOrderStatusUpd消费没有获取到消息体");
                return ConsumeResult.SUCCESS;
            }
            String messageBody = StandardCharsets.UTF_8.decode(body).toString();
            log.info("rocketMq消费#handleOrderStatusUpd,messageBody={}",messageBody);

            KafkaEventMessage.EventMessage<JSONObject> kafkaEventMessage = JSONUtil.toBean(messageBody, KafkaEventMessage.EventMessage.class);
            OrderStatusUpdMessage bean = JSONUtil.toBean(messageBody, OrderStatusUpdMessage.class);
            kafkaEventMessage.setData(JSONUtil.parseObj(bean));
            kafkaEventMessage.setMessageId(String.valueOf(messageView.getMessageId()));
            String key = StrUtil.format(RedisConstant.CTP_KAFKA_CONSUMER_MSGID_PREFIX, kafkaEventMessage.getMessageId());
            String value = redisService.get(key);
            if (StrUtil.isNotBlank(value)) {
                log.info("handleOrderStatusUpd#消息重复消费:{}", kafkaEventMessage.getMessageId());
                return ConsumeResult.SUCCESS;
            }

             productInventoryAppService.handleOrderStatusUpd(kafkaEventMessage);

            List<OrderStatusUpdMessage.OrderProduct> orderProductList = bean.getOrderProductList();
            List<String> skuCodeList = orderProductList.stream().map(OrderStatusUpdMessage.OrderProduct::getSkuCode).collect(Collectors.toList());
            if(bean.getOrderOpt().equals(InventoryHandlerTypeEnum.ORDER_CANCEL.getCode())){
                log.info("订单取消更新商品状态{}",skuCodeList);
                updateStateOrNot(skuCodeList);
            }



            return ConsumeResult.SUCCESS;
        }
    }


    @Service
    @RocketMQMessageListener(
            namespace = "${rocketmq.push-consumer.name-space:}",
            topic= RocketMqConstant.CTP_PRODUCT_SKU_MAP_SYNC_TOPIC,
            consumerGroup=RocketMqConstant.CTP_PRODUCT_SKU_MAP_SYNC_GROUP,
            tag =RocketMqConstant.COMMON_TAG)
    public class handleProductSkuMapSync implements RocketMQListener {
        @Override
        public ConsumeResult consume(MessageView messageView) {
            log.info("rocketMq消费#handleProductSkuMapSync消费开始,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
            ByteBuffer body = messageView.getBody();
            if(messageView.getBody() == null){
                log.info("rocketMq消费#handleProductSkuMapSync消费没有获取到消息体,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
                return ConsumeResult.SUCCESS;
            }
            String messageBody = StandardCharsets.UTF_8.decode(body).toString();
            ProductSkuSyncMapEvent mapEvent = JSONUtil.toBean(messageBody, ProductSkuSyncMapEvent.class);
            if (ObjectUtil.isNull(mapEvent)) {
                log.info("rocketMq消费#handleProductSkuMapSync消费体转换失败,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
                return ConsumeResult.SUCCESS;
            } else {
                log.info("rocketMq消费#handleProductSkuMapSync消息体，skuIdList={}",mapEvent.getSkuIdList());
            }
            try {
                productMapDomainService.syncDataToSkuSpuMapTable(SyncMapTableBO.builder().skuIdList(mapEvent.getSkuIdList()).syncType(1).build());
            } catch (Exception e) {
                log.error(String.format("rocketMq消费#handleProductSkuMapSync#同步失败#%s", JSONUtil.toJsonStr(mapEvent.getSkuIdList())), e);
                return ConsumeResult.SUCCESS;
            }
            log.info("rocketMq消费#handleProductSkuMapSync消费成功,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
            return ConsumeResult.SUCCESS;
        }
    }



    @Service
    @RocketMQMessageListener(
            namespace = "${rocketmq.push-consumer.name-space:}",
            topic= RocketMqConstant.CTP_PRODUCT_IMPORT_TASK_TOPIC,
            consumerGroup=RocketMqConstant.CTP_PRODUCT_IMPORT_TASK_GROUP,
            tag =RocketMqConstant.COMMON_TAG)
    public class handleProductImportTaskMapSync implements RocketMQListener {
        @Override
        public ConsumeResult consume(MessageView messageView) {
            log.info("rocketMq消费#handleProductSkuMapSync消费开始,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
            ByteBuffer body = messageView.getBody();
            if(messageView.getBody() == null){
                log.info("rocketMq消费#handleProductImportTaskMapSync消费没有获取到消息体");
                return ConsumeResult.SUCCESS;
            }
            String messageBody = StandardCharsets.UTF_8.decode(body).toString();
            ProductImportEvent mapEvent = JSONUtil.toBean(messageBody, ProductImportEvent.class);
            if (ObjectUtil.isNull(mapEvent)) {
                log.info("rocketMq消费#handleProductImportTaskMapSync消费体转换失败,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
                return ConsumeResult.SUCCESS;
            } else {
                log.info("rocketMq消费#handleProductImportTaskMapSync消息体，任务taskId={}",mapEvent.getTaskId());
            }

            try {
                //调用生产导入任务
                log.info("rocketMq消费#handleProductImportTaskMapSync，消息参数{}",JSONUtil.toJsonStr(mapEvent));
                productImportAppService.processImportDataSync(mapEvent);
            } catch (Exception e) {
                log.error(String.format("rocketMq消费#handleProductImportTaskMapSync#导入任务生成失败#%s", JSONUtil.toJsonStr(mapEvent)), e);
                return ConsumeResult.SUCCESS;
            }
            log.info("rocketMq消费#handleProductImportTaskMapSync消费成功,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
            return ConsumeResult.SUCCESS;
        }
    }

    private void updateStateOrNot(List<String> skuCodeList) {
        try {
            String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            log.info("{}=updateStateOrNot#skuCodeList={}", dateStr, skuCodeList);
            ProductSkuCondition condition = new ProductSkuCondition();
            condition.setSkuCodeList(skuCodeList);
            condition.setOnSaleStatus(OnSaleStatusEnum.REJECT.getType());
            condition.setOutSaleType(OutSaleTypeEnum.SELL_OUT.getType());
            condition.setDeleted(0);
            List<SkuEntity> skuEntities = productDomainService.skuQueryCondition(condition);
            if (CollUtil.isEmpty(skuEntities)) {
                log.info("{}=updateStateOrNot#没有需要处理的数据", dateStr);
                return;
            }
            List<Long> skuIdList = skuEntities.stream().map(SkuEntity::getId).collect(Collectors.toList());
            log.info("{}=updateStateOrNot#skuIdList={}", dateStr, skuIdList);
            List<String> spuCodeList = skuEntities.stream().map(SkuEntity::getSpuCode).collect(Collectors.toList());
            log.info("{}=updateStateOrNot#spuCodeList={}", dateStr, spuCodeList);
            AuditLogSpuCondition auditLogCondition = new AuditLogSpuCondition();
            auditLogCondition.setSpuCodeList(spuCodeList);
            auditLogCondition.setAuditStatus(10);
            auditLogCondition.setDeleted(0);
            List<AuditLogEntity> list = auditDomainService.queryAuditLogListByCondition(auditLogCondition);
            if (CollUtil.isNotEmpty(list)) {
                log.info("{}=updateStateOrNot#有审核数据，需要处理", dateStr);
                Set<String> auditSpuCodeSet = list.stream().map(AuditLogEntity::getSpuCode).collect(Collectors.toSet());
                skuIdList = skuEntities
                        .stream()
                        .filter(skuEntity -> !auditSpuCodeSet.contains(skuEntity.getSpuCode()))
                        .map(SkuEntity::getId).collect(Collectors.toList());
                log.info("{}=updateStateOrNot#skuIdList#AuditLog={}", dateStr, skuIdList);
            }
            if (CollUtil.isEmpty(skuIdList)) {
                log.info("{}=updateStateOrNot#没有需要处理的数据", dateStr);
                return;
            }
            BatchChangeSaleStatusCommand command = new BatchChangeSaleStatusCommand();
            command.setSkuIdList(skuIdList);
            command.setOnSaleStatus(OnSaleStatusEnum.OK.getType());
            log.info("{}=updateStateOrNot#command={}", dateStr, JSONUtil.toJsonStr(command));
            Integer i = productOperateAppService.batchChangeSaleStatus(command);
            log.info("{}=updateStateOrNot#上架处理结果={}", dateStr, i);
        } catch (Exception e) {
            log.error("updateStateOrNot#处理失败#{}", e.getMessage());
        }
    }
}
