package com.wanlianyida.ctpcore.product.interfaces.facade;

import com.wanlianyida.ctpcore.product.application.command.AuditLogAuditCommand;
import com.wanlianyida.ctpcore.product.application.command.AuditRejectReasonAddCommand;
import com.wanlianyida.ctpcore.product.application.command.AuditRejectReasonSortCommand;
import com.wanlianyida.ctpcore.product.application.command.BatchAuditCommand;
import com.wanlianyida.ctpcore.product.application.dto.*;
import com.wanlianyida.ctpcore.product.application.query.AuditLogQuery;
import com.wanlianyida.ctpcore.product.application.query.AuditRejectReasonQuery;
import com.wanlianyida.ctpcore.product.application.query.AuditSkuListQuery;
import com.wanlianyida.ctpcore.product.application.service.AuditAppService;
import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月23日 17:39
 */
@Api("商品审核")
@RestController
@RequestMapping("/audit")
public class AuditController {

    @Resource
    private AuditAppService auditAppService;

    @ApiOperation("查询驳回原因列表")
    @PostMapping("/queryRejectReason")
    public ResultMode<List<AuditRejectReasonDTO>> queryRejectReason(@RequestBody @Validated AuditRejectReasonQuery query) {
        List<AuditRejectReasonDTO> result = auditAppService.queryRejectReason(query);
        return ResultMode.success(result);
    }

    @ApiOperation("添加驳回原因")
    @PostMapping("/addRejectReason")
    public ResultMode addRejectReason(@RequestBody @Validated AuditRejectReasonAddCommand command) {
        auditAppService.addRejectReason(command);
        return ResultMode.success();
    }

    @ApiOperation("驳回原因重排序")
    @PostMapping("/reasonResort")
    public ResultMode reasonResort(@RequestBody @Validated AuditRejectReasonSortCommand command) {
        auditAppService.reasonResort(command);
        return ResultMode.success();
    }

    @ApiOperation("删除驳回原因")
    @PostMapping("/reasonDelete")
    public ResultMode reasonDelete(@RequestBody @Validated IdCommand command) {
        auditAppService.reasonDelete(command);
        return ResultMode.success();
    }

    @ApiOperation("商品审核")
    @PostMapping("/audit")
    public ResultMode audit(@RequestBody @Validated AuditLogAuditCommand command) {
        auditAppService.audit(command);
        return ResultMode.success();
    }

    @ApiOperation("/审核列表(以spu纬度)")
    @PostMapping("/pageSpuCondition")
    public ResultMode<List<AuditLogSpuListDTO>> pageCondition(@RequestBody PagingInfo<AuditLogQuery> pageQuery) {
        return auditAppService.pageSpuCondition(pageQuery);
    }

    @ApiOperation("/审核列表(以sku纬度)")
    @PostMapping("/pageSkuCondition")
    public ResultMode<List<AuditLogSkuListDTO>> pageSkuCondition(@RequestBody PagingInfo<AuditSkuListQuery> pageQuery) {
        return auditAppService.pageSkuCondition(pageQuery);
    }

    @ApiOperation("/根据状态统计数据（spu）")
    @PostMapping("/statisticsAuditSpuList")
    public ResultMode<AuditStatisticsDTO> statisticsAuditSpuList(@RequestBody AuditLogQuery pageQuery) {
        AuditStatisticsDTO result = auditAppService.statisticsAuditSpuList(pageQuery);
        return ResultMode.success(result);
    }

    @ApiOperation("/根据状态统计数据（sku）")
    @PostMapping("/statisticsAuditSkuList")
    public ResultMode<AuditStatisticsDTO> statisticsAuditSkuList(@RequestBody AuditSkuListQuery pageQuery) {
        AuditStatisticsDTO result = auditAppService.statisticsAuditSkuList(pageQuery);
        return ResultMode.success(result);
    }

    @ApiOperation("批量审核")
    @PostMapping("/batchAudit")
    public ResultMode batchAudit(@RequestBody @Validated BatchAuditCommand command) {
        auditAppService.batchAudit(command);
        return ResultMode.success();
    }

    @ApiOperation("审核详情")
    @PostMapping("/queryDetail")
    public ResultMode<AuditLogDetailDTO> queryDetail(@RequestBody @Validated IdCommand command) {
        AuditLogDetailDTO result = auditAppService.queryDetail(command);
        return ResultMode.success(result);
    }

    @ApiOperation("查询spu审核列表")
    @PostMapping("/querySpuList")
    public ResultMode<List<AuditLogSpuListDTO>> querySpuList(@RequestBody PagingInfo<AuditLogQuery> pageQuery) {
        return auditAppService.querySpuList(pageQuery);
    }
}
