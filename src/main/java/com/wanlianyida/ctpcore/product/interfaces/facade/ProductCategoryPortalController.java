package com.wanlianyida.ctpcore.product.interfaces.facade;

import com.wanlianyida.ctpcore.product.application.command.ProductCategoryUpdateNavConfigCommand;
import com.wanlianyida.ctpcore.product.application.command.SetCategoryIconCommand;
import com.wanlianyida.ctpcore.product.application.command.SetRecommendBrandCommand;
import com.wanlianyida.ctpcore.product.application.dto.ProductCategoryDTO;
import com.wanlianyida.ctpcore.product.application.dto.ProductCategoryPortalPageDTO;
import com.wanlianyida.ctpcore.product.application.dto.ShopCategoryTreeDTO;
import com.wanlianyida.ctpcore.product.application.query.ProductCategoryPortalDirectSubQuery;
import com.wanlianyida.ctpcore.product.application.query.ProductCategoryPortalPageQuery;
import com.wanlianyida.ctpcore.product.application.query.ShopCategoryTreeQuery;
import com.wanlianyida.ctpcore.product.application.service.ProductCategoryAppService;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商品品类门户配置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/category/portal")
@Validated
public class ProductCategoryPortalController {

    @Resource
    private ProductCategoryAppService productCategoryAppService;

    /**
     * 更新品类导航配置
     * @param command 参数
     */
    @PostMapping(value = {"/updateNavConfig"})
    public ResultMode<?> updateNavConfig(@RequestBody @Valid ProductCategoryUpdateNavConfigCommand command) {
        return productCategoryAppService.updateNavConfig(command);
    }

    /**
     * 品类导航商品品类分页查询
     * @param query 分页参数
     */
    @PostMapping(value = {"/pageQuery"})
    public ResultMode<List<ProductCategoryPortalPageDTO>> pageQuery(@RequestBody PagingInfo<ProductCategoryPortalPageQuery> query) {
        return productCategoryAppService.pageQueryProductCategoryPortal(query);
    }

    /**
     * 品类导航获取分类下所有未删除的直接子级
     * @param query 分页参数
     */
    @PostMapping(value = {"/queryDirectSubLevel"})
    public ResultMode<List<ProductCategoryPortalPageDTO>> queryDirectSubLevelPortal(@RequestBody @Valid ProductCategoryPortalDirectSubQuery query) {
        return ResultMode.success(productCategoryAppService.queryDirectSubLevelPortal(query));
    }

    /**
     * 门户店铺商品分类树结构（平台品类）
     * @param query 参数
     */
    @PostMapping(value = {"/queryShopCategoryTree"})
    public ResultMode<List<ShopCategoryTreeDTO>> queryShopCategoryTree(@RequestBody ShopCategoryTreeQuery query) {
        return ResultMode.success(productCategoryAppService.queryShopCategoryTree(query));
    }

    /**
     * 设置品类图标
     */
    @PostMapping(value = {"/setCategoryIcon"})
    public ResultMode<?> setCategoryIcon(@RequestBody @Valid SetCategoryIconCommand command) {
        return productCategoryAppService.setCategoryIcon(command);
    }

    /**
     * 品类详情
     * @param idQuery 参数
     */
    @PostMapping(value = {"/categoryDetail"})
    public ResultMode<ProductCategoryDTO> categoryDetail(@RequestBody @Valid IdQuery idQuery) {
        return productCategoryAppService.categoryDetail(idQuery);
    }

    /**
     * 设置推荐品牌
     */
    @PostMapping(value = {"/setRecommendBrand"})
    public ResultMode<?> setRecommendBrand(@RequestBody @Valid SetRecommendBrandCommand command) {
        return productCategoryAppService.setRecommendBrand(command);
    }

}
