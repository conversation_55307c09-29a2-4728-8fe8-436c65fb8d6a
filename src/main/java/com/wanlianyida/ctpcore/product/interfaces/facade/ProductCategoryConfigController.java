package com.wanlianyida.ctpcore.product.interfaces.facade;

import com.wanlianyida.ctpcore.product.application.command.RelateAttributesCommand;
import com.wanlianyida.ctpcore.product.application.command.RelateBrandCommand;
import com.wanlianyida.ctpcore.product.application.dto.*;
import com.wanlianyida.ctpcore.product.application.query.ProductCategoryConfigPageQuery;
import com.wanlianyida.ctpcore.product.application.query.ProductCategoryQuery;
import com.wanlianyida.ctpcore.product.application.query.RelatedAttributeQuery;
import com.wanlianyida.ctpcore.product.application.query.RelatedBrandQuery;
import com.wanlianyida.ctpcore.product.application.service.ProductCategoryAppService;
import com.wanlianyida.ctpcore.product.infrastructure.validation.CollectionNotEmpty;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商品品类配置（关联）
 * <AUTHOR>
 */
@RestController
@RequestMapping("/category/relation")
@Validated
@Slf4j
public class ProductCategoryConfigController {

    @Resource
    private ProductCategoryAppService productCategoryAppService;

    /**
     * 获取已经关联的规格列表信息
     */
    @PostMapping(value = {"/queryRelatedAttributes"})
    public ResultMode<List<CategoryRelatedAttributeDTO>> queryRelatedAttributes(@RequestBody @Valid RelatedAttributeQuery query) {
        List<CategoryRelatedAttributeDTO> resultDtoList = productCategoryAppService.queryRelatedAttributes(query);
        return ResultMode.success(resultDtoList);
    }

    /**
     * 获取已经关联的品牌列表信息
     */
    @PostMapping(value = {"/queryRelatedBrands"})
    public ResultMode<List<CategoryRelatedBrandDTO>> queryRelatedBrands(@RequestBody @Valid RelatedBrandQuery query) {
        List<CategoryRelatedBrandDTO> resultDtoList = productCategoryAppService.queryRelatedBrands(query);
        return ResultMode.success(resultDtoList);
    }

    /**
     * 关联规格
     * @param commandList 参数
     */
    @PostMapping(value = {"/relateAttributes"})
    public ResultMode<?> relateAttributes(@RequestBody @Valid @CollectionNotEmpty List<RelateAttributesCommand> commandList) {
        return productCategoryAppService.relateAttributes(commandList);
    }

    /**
     * 关联品牌
     */
    @PostMapping(value = {"/relateBrands"})
    public ResultMode<?> relateBrands(@RequestBody @Valid RelateBrandCommand command) {
        return productCategoryAppService.relateBrands(command);
    }

    /**
     * 分页查询商品品类配置
     * @param query 分页参数
     */
    @PostMapping(value = {"/pageQuery"})
    public ResultMode<List<ProductCategoryConfigPageDTO>> pageQuery(@RequestBody PagingInfo<ProductCategoryConfigPageQuery> query) {
        return productCategoryAppService.pageQuery(query);
    }


    /**
     * 根据三级品类查询祖级名称以及祖级id
     * @param query
     * @return
     */
    @PostMapping(value = {"/queryByAncestorNames"})
    public ResultMode<List<ProductCategoryDTO>> queryCategoryIdByAncestorNames(@RequestBody ProductCategoryQuery query) {
        return productCategoryAppService.queryCategoryIdByAncestorNames(query);

    }


}
