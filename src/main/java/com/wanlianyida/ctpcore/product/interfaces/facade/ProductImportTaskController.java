package com.wanlianyida.ctpcore.product.interfaces.facade;

import com.wanlianyida.ctpcore.product.application.dto.ProductImportTaskDTO;
import com.wanlianyida.ctpcore.product.application.service.ProductImportTaskAppService;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * @ClassName ProductImportTaskController
 * @Description
 * <AUTHOR>
 * @Veriosn 1.0
 **/

@RequestMapping("/product/task")
@RestController
public class ProductImportTaskController {

    @Resource
    private ProductImportTaskAppService productImportTaskAppService;



    @ApiOperation("查询导入任务列表")
    @PostMapping("queryPageList")
    public ResultMode<List<ProductImportTaskDTO>> queryPageList(@RequestBody PagingInfo info){
        return productImportTaskAppService.queryPageList(info);
    }

}
