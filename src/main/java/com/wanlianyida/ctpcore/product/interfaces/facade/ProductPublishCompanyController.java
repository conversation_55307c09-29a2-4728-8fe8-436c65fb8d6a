package com.wanlianyida.ctpcore.product.interfaces.facade;

import com.wanlianyida.ctpcore.product.application.dto.PcProductPublishCompanyDTO;
import com.wanlianyida.ctpcore.product.application.query.PublishCompanyQuery;
import com.wanlianyida.ctpcore.product.application.service.ProductPublishCompanyAppService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/03/15/13:21
 */
@Api("商品发布客户表")
@RestController
@RequestMapping("/publishCompany")
public class ProductPublishCompanyController {

    @Resource
    private ProductPublishCompanyAppService productPublishCompanyAppService;

    @ApiOperation(value = "商品发布客户列表")
    @PostMapping("/publishCompanyList")
    public ResultMode<List<PcProductPublishCompanyDTO>> publishCompanyList(@RequestBody PublishCompanyQuery query) {
        return productPublishCompanyAppService.getPublishCompany(query);
    }

}
