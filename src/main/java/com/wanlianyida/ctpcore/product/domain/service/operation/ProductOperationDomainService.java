package com.wanlianyida.ctpcore.product.domain.service.operation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wanlianyida.ctpcore.product.application.assembler.AuditAssembler;
import com.wanlianyida.ctpcore.product.application.assembler.ProductAssembler;
import com.wanlianyida.ctpcore.product.application.assembler.ProductBrandAssembler;
import com.wanlianyida.ctpcore.product.application.dto.BatchChangeInventoryDTO;
import com.wanlianyida.ctpcore.product.domain.model.bo.*;
import com.wanlianyida.ctpcore.product.domain.model.bo.operation.AuditContextBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.operation.SnapshotRestoreBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.operation.SnapshotSaveBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.publish.*;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditLogCondition;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditLogSpuCondition;
import com.wanlianyida.ctpcore.product.domain.model.condition.QualificationRelCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.*;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductSkuAttrEntity;
import com.wanlianyida.ctpcore.product.domain.repository.*;
import com.wanlianyida.ctpcore.product.domain.service.AuditDomainService;
import com.wanlianyida.ctpcore.product.domain.service.factory.ProductHandlerFactory;
import com.wanlianyida.ctpcore.product.infrastructure.constant.BrandConstant;
import com.wanlianyida.ctpcore.product.infrastructure.enums.*;
import com.wanlianyida.ctpcore.product.infrastructure.event.EventPublisher;
import com.wanlianyida.ctpcore.product.infrastructure.event.product.ProductOperateEvent;
import com.wanlianyida.ctpcore.product.infrastructure.event.product.ProductSkuSyncMapEvent;
import com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductException;
import com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductExceptionEnum;
import com.wanlianyida.ctpcore.product.infrastructure.exechange.SensitiveExchangeService;
import com.wanlianyida.ctpcore.product.infrastructure.repository.converter.ProductSnapshotConverter;
import com.wanlianyida.ctpcore.product.infrastructure.util.ProductUtil;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.constant.CommonTopicConstants;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.support.api.model.command.MsgCommand;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import static com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductExceptionEnum.*;

@Service
@Slf4j
public class ProductOperationDomainService {
    @Resource
    IProductSpuRepository iProductSpuRepository;

    @Resource
    IProductBrandRepository iProductBrandRepository;

    @Resource
    IProductCategoryRepository iProductCategoryRepository;

    @Resource
    IProductSnapshotRepository iProductSnapshotRepository;

    @Resource
    IProductSkuRepository iProductSkuRepository;

    @Resource
    EventPublisher eventPublisher;

    @Resource
    IAuditLogRepository iAuditLogRepository;

    @Resource
    SensitiveExchangeService sensitiveExchangeService;

    @Resource
    IProductRepository productRepository;

    @Resource
    private IProductDraftRepository draftRepository;

    @Resource
    private MqEventPublisher mqEventPublisher;

    @Resource
    private AuditDomainService auditDomainService;

    public ResultMode<ProductPublishOrUpdateResBO> publishProduct(ProductPublishOrUpdateBO productBo) {
        ProductSnapshotCreateContext context = ProductSnapshotCreateContext.builder().data(productBo).build();
        // 数据校验
        ResultMode<?> checkRes = commonValidation(productBo, context);
        if (!checkRes.isSucceed()) {
            return ResultMode.fail(checkRes.getMessage());
        }
        // 数据封装
        ResultMode<SnapshotSaveBO> resultMode = handleSnapshot(AuditApplyTypeEnum.PUBLISH.getType(), context);
        if (!resultMode.isSucceed() || resultMode.getModel() == null) {
            return ResultMode.fail(resultMode.getMessage());
        }
        SnapshotSaveBO snapshotBO = resultMode.getModel();
        // skuCode判断重复
        if (checkSkuCodeRepeat(snapshotBO)) {
            return ResultMode.fail(PRODUCT_CODE_REPEAT.getMsg());
        }
        // 数据保存
        ProductPublishOrUpdateResBO ansBO = ProductSnapshotConverter.toResAns(snapshotBO);
        iProductSnapshotRepository.saveSnapshot(snapshotBO);
        // 事件
        ProductOperateEvent operateEvent = ProductOperateEvent.builder()
                .eventType(ProductOperateEventTypeEnum.UPDATE).skuCodeList(ansBO.getSkuCodeList())
                .detailId(StrUtil.isNotEmpty(productBo.getExtraBO().getDetailId())?productBo.getExtraBO().getDetailId():null).build();
        ProductAssembler.fillBaseInfo(operateEvent, JwtUtil.getTokenInfo());
        eventPublisher.publishOperateEvent(operateEvent);
        return ResultMode.success(ansBO);
    }

    /**
     * 检测skuCode是否重复
     */
    private boolean checkSkuCodeRepeat(SnapshotSaveBO snapshotSaveBO) {
        List<String> skuCodeList = snapshotSaveBO.getSkuSnapshotEntityList().stream().map(SkuSnapshotEntity::getSkuCode).collect(Collectors.toList());
        List<SkuEntity> skuList = iProductSkuRepository.getByCodeList(skuCodeList);
        return CollUtil.isNotEmpty(skuList);
    }

    public ResultMode<ProductBatchPublishAnsBO> batchPublishProductValidation(ProductBatchPublishBO batchPublishBO) {
        // 返回结果集合定义
        ProductBatchPublishAnsBO ansBO = new ProductBatchPublishAnsBO();
        // 数据封装集合定义
        SnapshotSaveBO totalSnapshots = new SnapshotSaveBO();
        // 发布的商品数组
        List<ProductPublishOrUpdateBO> productList = batchPublishBO.getProductList();
        int index = 0;
        for (ProductPublishOrUpdateBO productBo : productList) {
            index++;
            ProductSnapshotCreateContext context = ProductSnapshotCreateContext.builder().data(productBo).build();
            // 单个数据校验
            ResultMode<?> checkRes = commonValidation(productBo, context);
            if (!checkRes.isSucceed()) {
                ansBO.getResultList().add(ProductBatchPublishAnsBO.createFailItem(index, checkRes.getMessage()));
            }
        }
        return ResultMode.success(ansBO);
    }

    /**
     * 批量发布
     */
    public ResultMode<ProductBatchPublishAnsBO> batchPublishProduct(ProductBatchPublishBO batchPublishBO) {
        // 返回结果集合定义
        ProductBatchPublishAnsBO ansBO = new ProductBatchPublishAnsBO();
        // 数据封装集合定义
        SnapshotSaveBO totalSnapshots = new SnapshotSaveBO();
        // 发布的商品数组
        List<ProductPublishOrUpdateBO> productList = batchPublishBO.getProductList();
        int index = 0;
        for (ProductPublishOrUpdateBO productBo : productList) {
            index++;
            ProductSnapshotCreateContext context = ProductSnapshotCreateContext.builder().data(productBo).build();
            // 单个数据校验
            ResultMode<?> checkRes = commonValidation(productBo, context);
            if (!checkRes.isSucceed()) {
                ansBO.getResultList().add(ProductBatchPublishAnsBO.createFailItem(index, checkRes.getMessage()));
                continue;
            }
            // 单个数据封装
            ResultMode<SnapshotSaveBO> resultMode = handleSnapshot(AuditApplyTypeEnum.SELL.getType(), context);
            if (!resultMode.isSucceed() || resultMode.getModel() == null) {
                ansBO.getResultList().add(ProductBatchPublishAnsBO.createFailItem(index, checkRes.getMessage()));
                continue;
            }
            ansBO.getResultList().add(ProductBatchPublishAnsBO.createSuccessItem(index, resultMode.getModel()));
            // 数据封装组合
            totalSnapshots.merge(resultMode.getModel());
        }
        // 全部数据校验失败直接返回
        if (CollUtil.isEmpty(totalSnapshots.getAuditLogEntityList())) {
            return ResultMode.success(ansBO);
        }
        // 数据保存
        iProductSnapshotRepository.saveSnapshot(totalSnapshots);
        // 商品发布事件
        ProductOperateEvent operateEvent = ProductOperateEvent.builder()
                .eventType(ProductOperateEventTypeEnum.PUBLISH)
                .skuCodeList(totalSnapshots.getSkuSnapshotEntityList().stream().map(SkuSnapshotEntity::getSkuCode).collect(Collectors.toList()))
                .build();
        ProductAssembler.fillBaseInfo2(operateEvent, JwtUtil.getTokenInfo());
        eventPublisher.publishOperateEvent(operateEvent);
        if (batchPublishBO.getAutoApproved() == 1) {
            List<Long> auditIdList = totalSnapshots.getAuditLogEntityList().stream().map(AuditLogEntity::getId).collect(Collectors.toList());
            List<AuditLogEntity> auditLogEntitityList = AuditAssembler.createAutoBatchAudit(auditIdList);
            restoreDataAfterAuditApprove2(auditLogEntitityList, true);
            log.info("审核通过");
        }
        return ResultMode.success(ansBO);
    }

    public ResultMode<ProductPublishOrUpdateResBO> updateProduct(ProductPublishOrUpdateBO productBo) {
        ProductSnapshotCreateContext context = ProductSnapshotCreateContext.builder().data(productBo).build();
        // 通用数据校验
        ResultMode<?> checkRes = commonValidation(productBo, context);
        if (!checkRes.isSucceed()) {
            return ResultMode.fail(checkRes.getMessage());
        }
        // 更新数据校验
        ResultMode<?> checkAns = validationOnUpdate(productBo, context);
        if (!checkAns.isSucceed()) {
            return ResultMode.fail(checkAns.getMessage());
        }
        // 数据封装
        ResultMode<SnapshotSaveBO> resultMode = handleSnapshot(AuditApplyTypeEnum.UPDATE.getType(), context);
        if (!resultMode.isSucceed() || resultMode.getModel() == null) {
            return ResultMode.fail(resultMode.getMessage());
        }
        SnapshotSaveBO snapshotBO = resultMode.getModel();
        ProductPublishOrUpdateResBO ansBO = ProductSnapshotConverter.toResAns(snapshotBO);
        // 数据入库
        iProductSnapshotRepository.saveSnapshot(snapshotBO);

        // 事件
        ProductOperateEvent operateEvent = ProductOperateEvent.builder()
                .eventType(ProductOperateEventTypeEnum.UPDATE).skuCodeList(ansBO.getSkuCodeList()).build();
        ProductAssembler.fillBaseInfo(operateEvent, JwtUtil.getTokenInfo());
        eventPublisher.publishOperateEvent(operateEvent);
        return ResultMode.success(ansBO);
    }

    /**
     * 批量改价
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<?> batchChangePrice(BatchChangePriceBO bo) {
        List<BatchChangePriceBO.PriceBO> changeList = bo.getChangeList();
        // 数据校验
        ResultMode<?> checkRes = validationOnChangePrice(bo);
        if (!checkRes.isSucceed()) {
            if(checkRes.getModel() == null){
                return ResultMode.fail(CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getCode(), CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getMsg());
            }else{
                return ResultMode.fail(CtpCoreProductExceptionEnum.USER_ERROR_PARAM_CHECK.getCode(), checkRes.getMessage(),checkRes.getModel());
            }
        }
        // 审核数据封装
        ProductSnapshotCreateContext context = ProductSnapshotCreateContext.builder().changePriceBO(bo).build();
        ResultMode<SnapshotSaveBO> resultMode = handleSnapshot(AuditApplyTypeEnum.PRICE_CHANGE.getType(), context);
        if (!resultMode.isSucceed() || resultMode.getModel() == null) {
            return ResultMode.fail(resultMode.getMessage());
        }
        // 审核数据入库
        SnapshotSaveBO snapshotBO = resultMode.getModel();
        iProductSnapshotRepository.saveSnapshot(snapshotBO);

        // 自动执行：审核通过后数据入库
        AuditContextBO context2 = AuditContextBO.builder().queryList(snapshotBO.getAuditLogEntityList()).build();
        ResultMode<SnapshotRestoreBO> res = handleRestoreData(AuditApplyTypeEnum.PRICE_CHANGE.getType(), context2);
        iProductSnapshotRepository.saveRestoreData(res.getModel());
        return ResultMode.success();
    }

    public void changePriceEvent(List<Long> skuIdList) {
        // 商品改价事件
        ProductOperateEvent operateEvent = ProductOperateEvent.builder()
                .eventType(ProductOperateEventTypeEnum.CHANGE_PRICE)
                .skuIdList(skuIdList)
                .build();
        ProductAssembler.fillBaseInfo(operateEvent, JwtUtil.getTokenInfo());
        eventPublisher.publishOperateEvent(operateEvent);
        eventPublisher.publishSyncMapTableEvent(ProductSkuSyncMapEvent.builder().skuIdList(skuIdList).build());
    }

    public ResultMode<SnapshotSaveBO> handleSnapshot(String type, Object optParam) {
        DataProcessor dataProcessor = ProductHandlerFactory.getHandler(DataProcessor.class, type, false);
        if (ObjUtil.isNull(dataProcessor)) {
            return ResultMode.success();
        }
        ResultMode result = dataProcessor.handleSnapshotData(optParam);
        return result;
    }

    public ResultMode<SnapshotRestoreBO> handleRestoreData(String type, Object optParam) {
        DataProcessor dataProcessor = ProductHandlerFactory.getHandler(DataProcessor.class, type, false);
        if (ObjUtil.isNull(dataProcessor)) {
            return ResultMode.success();
        }
        ResultMode result = dataProcessor.handleRestoreData(optParam);
        return result;
    }

    /**
     * 改价参数校验
     */
    private ResultMode<?> validationOnChangePrice(BatchChangePriceBO changePriceBO) {
        if (CollUtil.isEmpty(changePriceBO.getChangeList())) {
            return ResultMode.fail(CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getCode(), CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getMsg());
        }
        List<Long> skuIdList = changePriceBO.getChangeList().stream().map(t -> t.getSkuId()).collect(Collectors.toList());
        List<SkuEntity> skuEntityList = iProductSkuRepository.getSkusByIds(skuIdList);
        if(CollUtil.isEmpty(skuEntityList)){
            return ResultMode.fail(CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getCode(), CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getMsg());
        }
        //校验违规下架
        List<String> violationSkuList = validateViolationList(skuEntityList);
        if(CollUtil.isNotEmpty(violationSkuList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_10,violationSkuList);
        }
        //查询待审核的记录
        List<String> auditSkuList = validateAuditList(skuEntityList);
        if(CollUtil.isNotEmpty(auditSkuList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_20,auditSkuList);
        }
        //校验价格
        List<String> priceSkuList = validatePriceList(skuEntityList,changePriceBO.getChangeList());
        if(CollUtil.isNotEmpty(priceSkuList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_50,priceSkuList);
        }
        return ResultMode.success();
    }

    private List<String> getQualificationIdList(List<String> spuCodeList) {
        List<PcProductQualificationRelEntity> list = productRepository.queryProductQualificationEntityList(QualificationRelCondition.builder().spuCodeList(spuCodeList).build());
        return list.stream().map(e->e.getQualificationId() + "").collect(Collectors.toList());
    }

    /**
     * 修改商品操作数据校验
     */
    private ResultMode<?> validationOnUpdate(ProductPublishOrUpdateBO bo, ProductSnapshotCreateContext context) {
        ProductPublishOrUpdateBO productBo = context.getData();
        if (Strings.isNullOrEmpty(bo.getSpu().getSpuCode())) {
            return ResultMode.fail(SPU_CODE_NOT_EMPTY.getMsg());
        }
        SpuEntity currentSpuEntity = iProductSpuRepository.getBySpuCode(productBo.getSpu().getSpuCode());
        if (currentSpuEntity == null) {
            // 审核驳回的场景
            AuditLogEntity auditLogEntity = iAuditLogRepository.queryLastBySpuCode(productBo.getSpu().getSpuCode());
            if (auditLogEntity == null) {
                return ResultMode.fail(SPU_NOT_FOUND.getMsg());
            }
        }
        if (CollUtil.isEmpty(productBo.getSkuList())) {
            return ResultMode.fail(SKU_NOT_EMPTY.getMsg());
        }
        if (currentSpuEntity != null) {
            // 校验前端传的code和库里的code是否匹配
            List<String> skuCodeList = productBo.getSkuList().stream().map(e -> e.getSku().getSkuCode())
                    .filter(skuCode -> !Strings.isNullOrEmpty(skuCode)).collect(Collectors.toList());
            List<String> nowSkuCodeList = iProductSkuRepository.querySkuCodeListBySpuCode(currentSpuEntity.getSpuCode());
            skuCodeList = skuCodeList.stream().sorted().collect(Collectors.toList());
            nowSkuCodeList = nowSkuCodeList.stream().sorted().collect(Collectors.toList());
            if (!CollUtil.isEqualList(skuCodeList, nowSkuCodeList)) {
                // 审核被修改的场景校验
                List<String> nowSnapshotCodeList = iProductSkuRepository.querySkuCodeListInRecentAudit(currentSpuEntity.getSpuCode());
                nowSnapshotCodeList = nowSnapshotCodeList.stream().sorted().collect(Collectors.toList());
                if (!CollUtil.isEqualList(skuCodeList, nowSnapshotCodeList)) {
                    return ResultMode.fail(SKU_CODE_PARAM_ERROR.getMsg());
                }
            }
        }
        AuditLogEntity auditLogEntity = iAuditLogRepository.queryWaitStatusAudit(productBo.getSpu().getSpuCode());
        if (ObjectUtil.isNotNull(auditLogEntity)) {
            return ResultMode.fail(MODIFY_STOCK_FAILED_PENDING_REVIEW.getMsg());
        }
        return ResultMode.success();
    }

    private ProductBrandEntity getProductBrandEntity(Long relBrandId){
        if(BrandConstant.BRAND_NAME_NO_ID.equals(relBrandId)){
            // 返回“无品牌”数据：id=1，名称=“无品牌”
            return ProductBrandAssembler.buildNoBrandEntity();
        } else {
            return iProductBrandRepository.queryBuId(relBrandId);
        }
    }

    /**
     * 修改和更新通用数据校验
     */
    private ResultMode<?> commonValidation(ProductPublishOrUpdateBO bo, ProductSnapshotCreateContext context) {
        SpuEntity spu = bo.getSpu();
        // 图片库<=5
        if (CollUtil.isNotEmpty(bo.getExtraBO().getPicList()) && bo.getExtraBO().getPicList().size() > 5) {
            return ResultMode.fail(SKU_PIC_TOO_MANY.getMsg());
        }
        ProductBrandEntity brandEntity = getProductBrandEntity(spu.getRelBrandId());
        if (brandEntity == null || Objects.equals(EnableStatusEnum.DISABLE.getStatusCode(), brandEntity.getEnableStatus())) {
            return ResultMode.fail(BRAND_NOT_EXIST_OR_DISABLE.getMsg());
        }
        context.setBrand(brandEntity);
        ProductCategoryEntity category1 = iProductCategoryRepository.selectById(spu.getCategoryId1());
        if (category1 == null || Objects.equals(category1.getEnableStatus(), EnableStatusEnum.DISABLE.getStatusCode())) {
            return ResultMode.fail(CATEGORY1_NOT_EXIST_OR_DISABLE.getMsg());
        }
        context.setCategory1(category1);
        ProductCategoryEntity category2 = iProductCategoryRepository.selectById(spu.getCategoryId2());
        if (category2 == null || Objects.equals(category2.getEnableStatus(), EnableStatusEnum.DISABLE.getStatusCode())) {
            return ResultMode.fail(CATEGORY2_NOT_EXIST_OR_DISABLE.getMsg());
        }
        context.setCategory2(category2);
        ProductCategoryEntity category3 = iProductCategoryRepository.selectById(spu.getCategoryId3());
        if (category3 == null || Objects.equals(category3.getEnableStatus(), EnableStatusEnum.DISABLE.getStatusCode())) {
            return ResultMode.fail(CATEGORY3_NOT_EXIST_OR_DISABLE.getMsg());
        }
        context.setCategory3(category3);
        // 查询品类关联的品牌
        List<ProductBrandSimpleBO> relatedBrandList = iProductCategoryRepository.queryProductBrandByCategoryId(spu.getCategoryId3());
        List<Long> relatedBrandIdList = relatedBrandList.stream().map(ProductBrandSimpleBO::getId).collect(Collectors.toList());
        // 关联列表不存在 && 品牌不是“无品牌”
        if (!relatedBrandIdList.contains(spu.getRelBrandId()) && !BrandConstant.BRAND_NAME_NO_ID.equals(spu.getRelBrandId())) {
            return ResultMode.fail(BRAND_ID_ERROR.getMsg());
        }
        // sku规格判断重复
        if (CollUtil.isNotEmpty(bo.getSkuList())) {
            List<String> specList = new ArrayList<>();
            int index = 1;
            for (ProductSkuDetailBO detail : bo.getSkuList()) {
                String spec = detail.getAttrList().stream().map(ProductSkuAttrEntity::getValueText).collect(Collectors.joining());
                int position = specList.indexOf(spec);
                if (position >= 0) {
                    return ResultMode.fail(String.format("第%s行和第%s行的商品规格重复", index + "", position + 1 + ""));
                }
                index++;
                specList.add(spec);
            }
        }
        // 查询品类关联的规格
        List<CategoryRelatedAttributeBO> relatedSpecList = iProductCategoryRepository.queryRelatedAttributes(spu.getCategoryId3());
        List<Long> relatedSpecIdList = relatedSpecList.stream()
                .map(CategoryRelatedAttributeBO::getRelAttributeId).collect(Collectors.toList());
        List<String> allSpecAttr = Lists.newArrayList();
        List<String> allDesc = Lists.newArrayList();
        if (CollUtil.isNotEmpty(bo.getAttrList())) {
            for (ProductSkuAttrEntity spuAttrEntity : bo.getAttrList()) {
                if (!relatedSpecIdList.contains(spuAttrEntity.getRelAttributeId())) {
                    log.error("spu规格ID参数错误：{}", spuAttrEntity.getRelAttributeId());
                    return ResultMode.fail(SPEC_ID_INVALID.getMsg());
                }
                if (!Strings.isNullOrEmpty(spuAttrEntity.getValueText())) {
                    allSpecAttr.add(spuAttrEntity.getValueText());
                }
            }
        }
        if (CollUtil.isNotEmpty(bo.getSkuList())) {
            for (ProductSkuDetailBO productSkuDetailBO : bo.getSkuList()) {
                if (CollUtil.isNotEmpty(productSkuDetailBO.getAttrList())) {
                    for (ProductSkuAttrEntity skuAttrEntity : productSkuDetailBO.getAttrList()) {
                        if (!relatedSpecIdList.contains(skuAttrEntity.getRelAttributeId())) {
                            log.error("sku规格ID参数错误：{}", skuAttrEntity.getRelAttributeId());
                            return ResultMode.fail(SKU_SPEC_ID_INVALID.getMsg());
                        }
                        if (!Strings.isNullOrEmpty(skuAttrEntity.getValueText())) {
                            allSpecAttr.add(skuAttrEntity.getValueText());
                        }
                    }
                    SkuEntity sku = productSkuDetailBO.getSku();
                    if (!Strings.isNullOrEmpty(sku.getSkuDesc())) {
                        allDesc.add(sku.getSkuDesc());
                    }
                }
            }
        }
        context.setRelatedSpecList(relatedSpecList);
        // 敏感词检测
        String ans = sensitiveExchangeService.checkWords(Joiner.on(",").join(allSpecAttr));
        if (!Strings.isNullOrEmpty(ans)) {
            return ResultMode.fail(SPEC_HAS_SENSITIVE_WORDS.getMsg() + ans);
        }
        ans = sensitiveExchangeService.checkWords(Joiner.on(",").join(allDesc));
        if (!Strings.isNullOrEmpty(ans)) {
            return ResultMode.fail(PRODUCT_DESC_HAS_SENSITIVE_WORDS.getMsg() + ans);
        }
        //富文本敏感词检测
        if (bo.getExtraBO() != null && StrUtil.isNotEmpty(bo.getExtraBO().getRichText())) {
            String checkRes = sensitiveExchangeService.checkWords(bo.getExtraBO().getRichText());
            if (!Strings.isNullOrEmpty(checkRes)) {
                return ResultMode.fail(PRODUCT_RICH_TEXT_HAS_SENSITIVE_WORDS.getMsg() + checkRes);
            }
        }
        // 预处理图片库url
        if (bo.getExtraBO() != null && CollUtil.isNotEmpty(bo.getExtraBO().getPicList())) {
            for (ProductPicBO productPicBO : bo.getExtraBO().getPicList()) {
                if (StrUtil.isNotEmpty(productPicBO.getPicUrl())) {
                    productPicBO.setPicUrl(ProductUtil.spitUrl(productPicBO.getPicUrl()));
                }
            }
        }
        return ResultMode.success();
    }

    /**
     * 恢复数据
     */
    public void restoreDataAfterAuditApprove2(List<AuditLogEntity> entityList, boolean isAutoApproved) {
        Map<Long, AuditLogEntity> commandMapping = entityList.stream().collect(Collectors.toMap(AuditLogEntity::getId, e -> e));
        AuditLogCondition condition = new AuditLogCondition();
        condition.setIdList(new ArrayList<>(commandMapping.keySet()));
        List<AuditLogEntity> auditLogEntities = iAuditLogRepository.queryCondition(condition);
        Map<String, List<AuditLogEntity>> applyTypeMapping = auditLogEntities.stream().collect(Collectors.groupingBy(AuditLogEntity::getApplyType));

        // 之前的资质数据
        List<String> spuCodeList = auditLogEntities.stream().map(AuditLogEntity::getSpuCode).collect(Collectors.toList());
        List<String> oldQualificationIdList = getQualificationIdList(spuCodeList);

        SnapshotRestoreBO restoreBO = new SnapshotRestoreBO();
        applyTypeMapping.forEach((k, v) -> {
            AuditContextBO context = new AuditContextBO();
            context.setQueryList(v);
            context.setCommandMapping(commandMapping);
            ResultMode<SnapshotRestoreBO> resultMode = handleRestoreData(k, context);
            restoreBO.merge(resultMode.getModel());
        });
        // 数据保存
        iProductSnapshotRepository.saveRestoreData(restoreBO);
        CompletableFuture.runAsync(() -> {
            Set<Long> auditIdSet = auditLogEntities.stream().map(AuditLogEntity::getId).collect(Collectors.toSet());
            List<Long> auditIdList = new ArrayList<>(auditIdSet);
            Map<Long, List<SkuSnapshotEntity>> skuSnapshotEntityList = getSkuSnapshotEntityList(auditIdList);
            List<MsgCommand> msgCommandList = ProductAssembler.buildAuditMsgCommand(skuSnapshotEntityList, entityList);
            log.info("发送商品审核消息mq:{}", JSONUtil.toJsonStr(msgCommandList));
            for (MsgCommand msgCommand : msgCommandList) {
                mqEventPublisher.syncSendNormalMessage(MqEventMessage.buildEventMessage(CommonTopicConstants.BIZ_MSG_TOPIC, msgCommand));
            }
        });

        List<SkuEntity> skuList = Lists.newArrayList();
        skuList.addAll(restoreBO.getSkuListForInsert());
        skuList.addAll(restoreBO.getSkuListForUpdate());
        if (CollUtil.isEmpty(skuList)) {
            return; // 没有审核通过的商品
        }

        // 审核通过事件组装
        List<String> skuCodeList = skuList.stream().map(SkuEntity::getSkuCode).collect(Collectors.toList());
        List<SkuEntity> currentEntityList = iProductSkuRepository.getByCodeList(skuCodeList);
        List<Long> skuIdList = currentEntityList.stream().map(SkuEntity::getId).collect(Collectors.toList());
        // 资质
        List<String> newQualificationIdList = getQualificationIdList(spuCodeList);
        Set<String> qualificationIds = Sets.newHashSet(oldQualificationIdList);
        qualificationIds.addAll(newQualificationIdList);
        log.info("资质id数据组装参数：{}, old={}, new={}", spuCodeList, oldQualificationIdList, newQualificationIdList);
        // 审核事件发送
        ProductOperateEvent productOperateEvent = ProductOperateEvent.builder()
                .eventType(ProductOperateEventTypeEnum.AUDIT_SUCCESS)
                .skuIdList(skuIdList)
                .updateOrInsertSkuEntityListWhenAuditSuccess(currentEntityList)
                .changeQualificationIdList(Lists.newArrayList(qualificationIds))
                .build();
        if (!isAutoApproved) {
            ProductAssembler.fillBaseInfo(productOperateEvent, JwtUtil.getTokenInfo());
        } else {
            ProductAssembler.fillBaseInfo2(productOperateEvent, JwtUtil.getTokenInfo());
        }
        eventPublisher.publishOperateEvent(productOperateEvent);

        // 同步聚合表
        eventPublisher.publishSyncMapTableEvent(ProductSkuSyncMapEvent.builder().skuIdList(skuIdList).build());
    }

    /**
     * 审核校验
     */
    private void auditValidation(List<AuditLogEntity> entityList, List<AuditLogEntity> queryList) {
        Map<Long, AuditLogEntity> mapping = queryList.stream().collect(Collectors.toMap(AuditLogEntity::getId, e -> e));
        for (AuditLogEntity entity : entityList) {
            AuditLogEntity query = mapping.get(entity.getId());
            if (query == null) {
                throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_LOG_NOT_EXISTS);
            }
            if (AuditStatusEnum.CANCELED.getCode().equals(query.getAuditStatus())) {
                throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_LOG_CANCELED);
            }
            if (AuditStatusEnum.CANCELED.getCode().equals(entity.getAuditStatus()) && !AuditStatusEnum.WAIT.getCode().equals(query.getAuditStatus())) {
                throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_NOT_ALLOWED_CANCEL);
            }
        }
    }

    /**
     * 查询草稿详情
     * @param detailId
     * @return <p>
     */
    public ProductDraftEntity queryProductDraft(String detailId){
        return draftRepository.draftDetail(detailId);
    }

    public Map<Long, List<SkuSnapshotEntity>> getSkuSnapshotEntityList(List<Long> auditIdList){
        Map<Long, List<SkuSnapshotEntity>> skuSnapshotEntityList = iProductSnapshotRepository.getSkuSnapshotEntityList(auditIdList);
        return skuSnapshotEntityList;
    }
    public ResultMode<BatchChangeInventoryDTO> batchChangeInventory(BatchChangeInventoryBO changeInventoryBO) {
        // 数据校验
        ResultMode<BatchChangeInventoryDTO> checkRes = validationOnBatchChangeInventory(changeInventoryBO);
        if (!checkRes.isSucceed()) {
            if(checkRes.getModel() == null){
                return ResultMode.fail(CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getCode(), CtpCoreProductExceptionEnum.SKU_NOT_FOUND.getMsg());
            }else{
                return ResultMode.fail(CtpCoreProductExceptionEnum.USER_ERROR_PARAM_CHECK.getCode(), checkRes.getMessage(),checkRes.getModel());
            }
        }
        // 审核数据封装
        ProductSnapshotCreateContext context = ProductSnapshotCreateContext.builder().batchChangeInventoryBO(changeInventoryBO).build();
        ResultMode<SnapshotSaveBO> resultMode = handleSnapshot(AuditApplyTypeEnum.INVENTORY_CHANGE.getType(), context);
        if (!resultMode.isSucceed() || resultMode.getModel() == null) {
            return ResultMode.fail(resultMode.getMessage());
        }
        // 审核数据入库
        SnapshotSaveBO snapshotBO = resultMode.getModel();
        iProductSnapshotRepository.saveSnapshot(snapshotBO);

        // 自动执行：审核通过后数据入库
        AuditContextBO context2 = AuditContextBO.builder().queryList(snapshotBO.getAuditLogEntityList()).build();
        ResultMode<SnapshotRestoreBO> res = handleRestoreData(AuditApplyTypeEnum.INVENTORY_CHANGE.getType(), context2);
        iProductSnapshotRepository.saveRestoreData(res.getModel());
        return ResultMode.success();
    }

    private ResultMode<BatchChangeInventoryDTO> validationOnBatchChangeInventory(BatchChangeInventoryBO changeInventoryBO) {
        List<BatchChangeInventoryBO.ChangeInventoryBO> changeSkuList = changeInventoryBO.getChangeSkuList();
        List<Long> skuIdList = changeSkuList.stream().map(t -> t.getSkuId()).collect(Collectors.toList());
        List<SkuEntity> skuEntityList = iProductSkuRepository.getSkusByIds(skuIdList);
        if(CollUtil.isEmpty(skuEntityList)){
            return ResultMode.fail("", "请至少修改一个商品的库存数量");
        }
        //校验违规下架
        List<String> violationSkuList = validateViolationList(skuEntityList);
        if(CollUtil.isNotEmpty(violationSkuList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_10,violationSkuList);
        }
        //查询待审核的记录
        List<String> auditSkuList = validateAuditList(skuEntityList);
        if(CollUtil.isNotEmpty(auditSkuList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_20,auditSkuList);
        }

        //校验最少可用库存
        List<String> minQuantityList = validateMinQuantityList(skuEntityList, changeSkuList);
        if(CollUtil.isNotEmpty(minQuantityList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_30,minQuantityList);
        }
        //校验修改库存不能和数据库中的库存值相同
        List<String> quantityList =  validateQuantityList(skuEntityList, changeSkuList);
        if(CollUtil.isNotEmpty(quantityList)){
            return ProductAssembler.buildValidateMessage(ValidateMessageEnum.TYPE_40,quantityList);
        }
        return ResultMode.success();
    }

    public void batchChangeInventoryEvent(List<Long> skuIdList) {
        // 库存事件
        List<SkuEntity> currentEntityList = iProductSkuRepository.getSkusByIds(skuIdList);
        ProductOperateEvent operateEvent = ProductOperateEvent.builder()
                .eventType(ProductOperateEventTypeEnum.CHANGE_INVENTORY).skuIdList(skuIdList)
                .updateOrInsertSkuEntityListWhenAuditSuccess(currentEntityList)
        .build();
        ProductAssembler.fillBaseInfo(operateEvent, JwtUtil.getTokenInfo());
        eventPublisher.publishOperateEvent(operateEvent);
        eventPublisher.publishSyncMapTableEvent(ProductSkuSyncMapEvent.builder().skuIdList(skuIdList).build());
    }

    /**
     * 验证库存
     */
    private List<String> validateQuantityList(List<SkuEntity> skuEntityList, List<BatchChangeInventoryBO.ChangeInventoryBO> changeSkuList) {
        if(CollUtil.isEmpty(skuEntityList)){
            return null;
        }
        Map<Long, BigDecimal> skuIdQuantity = skuEntityList.stream().collect(Collectors.toMap(SkuEntity::getId, SkuEntity::getQuantity, (old, newVal) -> old));
        List<String> skuIdList = new ArrayList<>();
        changeSkuList.stream().filter(t -> t.getChangeValue().compareTo(skuIdQuantity.get(t.getSkuId()))==0).forEach(t -> {
            skuIdList.add(StrUtil.toString(t.getSkuId()));
        });
        return skuIdList;
    }

    /**
     * 验证最小可用库存
     * @return
     */
    private List<String> validateMinQuantityList(List<SkuEntity> skuEntityList, List<BatchChangeInventoryBO.ChangeInventoryBO> changeSkuList) {
        if(CollUtil.isEmpty(skuEntityList)){
            return null;
        }
        List<String> skuIdList = new ArrayList<>();
        Map<Long, BigDecimal> skuIdMinQuantity = skuEntityList.stream().collect(Collectors.toMap(SkuEntity::getId, SkuEntity::getMinQuantity, (old, newVal) -> old));
        for (BatchChangeInventoryBO.ChangeInventoryBO inventoryBO : changeSkuList) {
            BigDecimal changeValue = inventoryBO.getChangeValue();
            if (changeValue.compareTo(skuIdMinQuantity.get(inventoryBO.getSkuId())) < 0) {
                skuIdList.add(StrUtil.toString(inventoryBO.getSkuId()));
            }
        }
        return skuIdList;
    }
    /**
     * 验证违规下架
     */
    private List<String> validateViolationList(List<SkuEntity> skuEntityList) {
        if (CollUtil.isEmpty(skuEntityList)) {
            return null;
        }
        List<String> skuIdList = new ArrayList<>();
        for (SkuEntity skuEntity : skuEntityList) {
            if (ObjectUtil.equals(skuEntity.getOnSaleStatus(), OnSaleStatusEnum.REJECT.getType())
                    && ObjectUtil.equals(skuEntity.getOutSaleType(), OutSaleTypeEnum.BREAK_RULE.getType())) {
                skuIdList.add(StrUtil.toString(skuEntity.getId()));
            }
        }
       return skuIdList;
    }
    /**
     * 验证审核记录
     */
    private List<String> validateAuditList(List<SkuEntity> skuEntityList) {
        if (CollUtil.isEmpty(skuEntityList)) {
            return null;
        }
        List<String> spuCodelist = skuEntityList.stream().map(SkuEntity::getSpuCode).distinct().collect(Collectors.toList());
        Map<String, List<SkuEntity>> spuSkuListEntity = skuEntityList.stream().collect(Collectors.groupingBy(SkuEntity::getSpuCode));
        AuditLogSpuCondition auditLogCondition = new AuditLogSpuCondition();
        auditLogCondition.setSpuCodeList(spuCodelist);
        auditLogCondition.setAuditStatus(AuditStatusEnum.WAIT.getCode());
        auditLogCondition.setDeleted(DeletedStatusEnum.NOT_DELETED.getDeletedStatusCode());
        List<AuditLogEntity> auditLogEntities = auditDomainService.queryAuditLogListByCondition(auditLogCondition);
        List<String> auditSpuCodeList = auditLogEntities.stream().map(AuditLogEntity::getSpuCode).collect(Collectors.toList());
        List<String> skuIdList = new ArrayList<>();
        for (String spuCode : auditSpuCodeList) {
            List<SkuEntity> skuEntitysAudit = spuSkuListEntity.get(spuCode);
            for (SkuEntity skuEntity : skuEntitysAudit) {
                skuIdList.add(StrUtil.toString(skuEntity.getId()));
            }
        }
        return skuIdList;
    }

    /**
     * 验证价格
     */
    private List<String> validatePriceList(List<SkuEntity> skuEntityList, List<BatchChangePriceBO.PriceBO> priceBOList) {
        if (CollUtil.isEmpty(skuEntityList)) {
            return null;
        }
        Map<Long, BigDecimal> skuIdPrice = skuEntityList.stream().collect(Collectors.toMap(SkuEntity::getId, SkuEntity::getPriceFee, (old, newVal) -> old));
        List<String> skuIdList = new ArrayList<>();
        for (BatchChangePriceBO.PriceBO priceBO : priceBOList) {
            if (priceBO.getPriceFee().compareTo(skuIdPrice.get(priceBO.getSkuId())) == 0) {
                skuIdList.add(StrUtil.toString(priceBO.getSkuId()));
            }
            if (Objects.isNull(priceBO.getPriceFee()) || Objects.isNull(priceBO.getSkuId())) {
                skuIdList.add(StrUtil.toString(priceBO.getSkuId()));
            }
            if (priceBO.getPriceFee().compareTo(BigDecimal.ZERO) < 0) {
                skuIdList.add(StrUtil.toString(priceBO.getSkuId()));
            }
        }
        return skuIdList;
    }
}
