package com.wanlianyida.ctpcore.product.domain.model.bo;

import com.wanlianyida.ctpcore.product.domain.model.entity.ProductBrandEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductCategoryBrandEntity;
import lombok.Data;

import java.util.List;

/**
 * 卖家新增品牌BO
 * <AUTHOR>
 */
@Data
public class UserBrandAddBO {

    /**
     * 新增的品牌（如果品牌库已经存在，该实体为空）
     */
    private ProductBrandEntity brandEntityAdd;

    /**
     * 品类品牌配置
     */
    private List<ProductCategoryBrandEntity> categoryBrandEntityList;

    /**
     * 关联的品类id集合
     */
    private List<Long> categoryIdList;

    /**
     * 如果当前品牌在品牌库中存在，储存一下品牌id
     */
    private Long brandId;
}
