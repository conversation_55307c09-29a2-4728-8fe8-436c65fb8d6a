package com.wanlianyida.ctpcore.product.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.product.domain.model.bo.*;
import com.wanlianyida.ctpcore.product.domain.model.condition.*;
import com.wanlianyida.ctpcore.product.domain.model.entity.*;
import com.wanlianyida.ctpcore.product.domain.repository.*;
import com.wanlianyida.ctpcore.product.infrastructure.enums.PublishTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductException;
import com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductExceptionEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月25日 10:27
 */
@Service
public class ProductDomainService {

    @Resource
    private IProductSkuSnapshotRepository productSkuSnapshotRepository;
    @Resource
    private IProductRepository productRepository;
    @Resource
    private IProductSpuRepository productSpuRepository;
    @Resource
    private IProductSkuRepository productSkuRepository;
    @Resource
    private IProductSkuAttrRepository productSkuAttrRepository;
    @Resource
    private ISkuPicRepository skuPicRepository;
    @Resource
    private IPcProductQualificationRelRepository productQualificationRelRepository;
    @Resource
    private IPcProductAuditRelRepository productAuditRelRepository;
    @Resource
    IPcProductPublishCompanyRepository pcProductPublishCompanyRepository;

    @Resource
    private IPcProductQualificationRelRepository pcProductQualificationRelRepository;

    @Resource
    private ProductPublishCompanyDomainService productPublishCompanyDomainService;
    /**
     * 查询审核快照
     */
    public ProductDetailBO queryAuditSnapshot(Long auditId) {
        return productRepository.getProductSnapshotByAuditId(auditId);
    }

    /**
     * 查询商品详情(spu纬度)
     */
    public List<ProductDetailBO> queryProductSpuDetail(ProductSpuCondition condition) {
        if (CollectionUtil.isNotEmpty(condition.getSpuIdList())) {
            return productSpuRepository.getSpuDetailEntityList(condition.getSpuIdList());
        } else if (CollectionUtil.isNotEmpty(condition.getSpuCodeList())) {
            return productSpuRepository.getSpuDetailEntityListByCode(condition.getSpuCodeList());
        }
        return Collections.EMPTY_LIST;
    }

    public List<ProductSkuDetailBO> queryProductSkuDetailBySpuCode(List<String> spuCodeList) {
        return productSkuRepository.getSkuDetailEntityListBySpuCode(spuCodeList);
    }

    /**
     * 查询商品详情(sku纬度)
     */
    public List<ProductSkuDetailBO> queryProductSkuDetail(ProductSkuCondition condition) {
        if (CollectionUtil.isNotEmpty(condition.getSkuIdList())) {
            return productSkuRepository.getSkuDetailEntityList(condition.getSkuIdList());
        } else if (CollectionUtil.isNotEmpty(condition.getSkuCodeList())) {
            return productSkuRepository.getSkuDetailEntityListByCode(condition.getSkuCodeList());
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 查询商品spu信息
     */
    public ProductSpuInfoBO queryProductSpuInfo(Long spuId) {
        SpuEntity spu = productSpuRepository.getById(spuId);
        if (spu == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SPU_NOT_FOUND);
        }
        ProductSpuInfoBO bo = new ProductSpuInfoBO();
        bo.setSpuId(spuId);
        bo.setSpuCode(spu.getSpuCode());
        bo.setProductSpu(spu);
        bo.setProductSpuAttrList(productSkuAttrRepository.querySpuBySpuCode(spu.getSpuCode()));
        return bo;
    }

    /**
     * 查询商品spu信息
     */
    public ProductSpuInfoBO queryProductSpuInfoAll(Long spuId) {
        SpuEntity spu = productSpuRepository.getByIdAll(spuId);
        if (spu == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SPU_NOT_FOUND);
        }
        ProductSpuInfoBO bo = new ProductSpuInfoBO();
        bo.setSpuId(spuId);
        bo.setSpuCode(spu.getSpuCode());
        bo.setProductSpu(spu);
        bo.setProductSpuAttrList(productSkuAttrRepository.querySpuBySpuCode(spu.getSpuCode()));
        return bo;
    }

    public SpuEntity queryProductSpu(Long spuId) {
        SpuEntity spu = productSpuRepository.getById(spuId);
        if (spu == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SPU_NOT_FOUND);
        }
        return spu;
    }

    /**
     * 查询商品sku信息
     */
    public ProductSkuInfoBO queryProductSkuInfo(Long skuId) {
        ProductSkuDetailBO skuDetail = productSkuRepository.getSkuDetailEntity(skuId);
        if (skuDetail == null || skuDetail.getSku() == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SKU_NOT_FOUND);
        }
        SpuEntity spu = productSpuRepository.getBySpuCode(skuDetail.getSku().getSpuCode());
        if (spu == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SPU_NOT_FOUND);
        }
        ProductSkuInfoBO bo = new ProductSkuInfoBO();
        bo.setSkuId(skuDetail.getSkuId());
        bo.setSkuCode(skuDetail.getSkuCode());
        bo.setSpuId(spu.getId());
        bo.setSpuCode(spu.getSpuCode());
        bo.setProductSku(skuDetail.getSku());
        bo.setProductSkuAttrList(skuDetail.getAttrList());
        return bo;
    }

    public ProductSkuInfoBO queryProductSkuInfo(String skuCode) {
        SkuEntity sku = productSkuRepository.getSkuBySkuCodeAll(skuCode);
        if (sku == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SKU_NOT_FOUND);
        }
        ProductSkuDetailBO skuDetail = new ProductSkuDetailBO();
        skuDetail.setSku(sku);
        skuDetail.setAttrList(productSkuAttrRepository.queryBySkuCode(skuCode));
        SpuEntity spu = productSpuRepository.getBySpuCodeAll(skuDetail.getSku().getSpuCode());
        if (spu == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.SPU_NOT_FOUND);
        }
        ProductSkuInfoBO bo = new ProductSkuInfoBO();
        bo.setSkuId(skuDetail.getSkuId());
        bo.setSkuCode(skuDetail.getSkuCode());
        bo.setSpuId(spu.getId());
        bo.setSpuCode(spu.getSpuCode());
        bo.setProductSku(skuDetail.getSku());
        bo.setProductSkuAttrList(skuDetail.getAttrList());
        String salesPublishType = this.getSalesPublishType(sku);
        bo.setSalesPublishType(salesPublishType);
        return bo;
    }

    /**
     * sku条件查询
     */
    public List<SkuEntity> skuQueryCondition(ProductSkuCondition condition) {
        return productSkuRepository.queryCondition(condition);
    }

    /**
     * sku条件查询
     */
    public List<SkuEntity> skuQueryConditionWithCustomCategory(ProductSkuCondition condition) {
        if (ObjUtil.isNotEmpty(condition.getQualificationId())) {
            ProductQualificationRelCondition relCondition = new ProductQualificationRelCondition(Collections.singletonList(Long.valueOf(condition.getQualificationId())));
            List<PcProductQualificationRelEntity> list = pcProductQualificationRelRepository.queryCondition(relCondition);
            if (ObjUtil.isEmpty(list)) {
                return Collections.emptyList();
            }
            condition.setSpuCodeList(list.stream().map(PcProductQualificationRelEntity::getSpuCode).collect(Collectors.toList()));
        }
       return productSkuRepository.queryConditionWithCustomCategory(condition);
    }

    public List<SpuEntity> querySpuListById(List<Long> spuIdList) {
        return productSpuRepository.getSpuList(spuIdList);
    }

    public List<SpuEntity> querySpuListByCode(List<String> spuCodeList) {
        return productSpuRepository.getSpuListBySpuCode(spuCodeList);
    }

    /**
     * 查询商品基础信息
     */
    public ProductRelInfoBO queryProductRelInfoBO(List<String> spuCodeList) {
        List<SkuPicEntity> picEntityList = this.queryProductSkuPic(SkuPicCondition.builder().spuCodeList(spuCodeList).build());
        List<ProductQualificationBO> qualificationBOList = this.queryProductQualificationBO(QualificationRelCondition.builder().spuCodeList(spuCodeList).build());
        List<PcProductPublishCompanyEntity> entityList = this.getPublishCompany(PublishCompanyCondition.builder().spuCodeList(spuCodeList).build());
        return ProductRelInfoBO.builder()
                .picList(picEntityList)
                .publishCompanyList(entityList)
                .qualificationList(qualificationBOList)
                .build();
    }


    public List<PcProductPublishCompanyEntity> getPublishCompany(PublishCompanyCondition condition) {
        return pcProductPublishCompanyRepository.queryByCondition(condition);
    }
    /**
     * 查询商品图片库
     */
    public List<SkuPicEntity> queryProductSkuPic(SkuPicCondition picCondition) {
        return skuPicRepository.queryList(picCondition);
    }

    /**
     * 查询资质
     */
    public List<ProductQualificationBO> queryProductQualificationBO(QualificationRelCondition condition) {
        return productRepository.queryProductQualification(condition);
    }

    /**
     * 根据id查询资质信息
     */
    public List<ProductQualificationBO> queryQualificationByIdList(List<Long> qualificationIdList) {
        return productRepository.queryQualificationByIdList(qualificationIdList);
    }

    /**
     * 查询审核关联信息列表
     * @param auditId
     * @return
     */
    public List<PcProductAuditRelEntity> queryAuditRelEntityList(Long auditId) {
        return productAuditRelRepository.listAll(auditId);
    }

    /**
     * 当前商品当前用户是否是可售卖范围
     * salesScope = 0 可售卖
     * salesScope = 1 不可售卖
     */
    private String getSalesPublishType(SkuEntity sku) {
        String salesScope = "0";
        String publishType = sku.getPublishType();
        if (PublishTypeEnum.PUBLIC.getCode().equals(publishType)) {
            salesScope = "0";
        } else {
            String companyId = JwtUtil.getTokenInfo().getCompanyId();
            if (ObjUtil.isEmpty(companyId)) {
                salesScope = "0";
                return salesScope;
            }
            List<String> spuCodeList = Collections.singletonList(sku.getSpuCode());
            if (CollUtil.isNotEmpty(spuCodeList)) {
                List<PcProductPublishCompanyEntity> publishCompanyList = productPublishCompanyDomainService.getPublishCompany(PublishCompanyCondition.builder().spuCodeList(spuCodeList).build());
                Map<String, List<String>> spuCodePublishCompanyIdMap = publishCompanyList.stream()
                        .collect(Collectors.groupingBy(
                                PcProductPublishCompanyEntity::getSpuCode,
                                Collectors.mapping(PcProductPublishCompanyEntity::getPublishCompanyId, Collectors.toList())
                        ));
                List<String> publishCompanyIdList = spuCodePublishCompanyIdMap.get(sku.getSpuCode());
                if (publishCompanyIdList != null && publishCompanyIdList.contains(companyId)) {
                    salesScope = "0";
                }else{
                    salesScope = "1";
                }
            }
        }
        return salesScope;
    }

    public List<SkuEntity> getSkusByIds(List<Long> skuIdList) {
        return productSkuRepository.getSkusByIds(skuIdList);
    }
}

