package com.wanlianyida.ctpcore.product.domain.service.operation.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductExtraRelBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductSkuDetailBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.RichTextBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.operation.AuditContextBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.operation.SnapshotRestoreBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.operation.SnapshotSaveBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.publish.ProductSnapshotCreateContext;
import com.wanlianyida.ctpcore.product.domain.model.entity.*;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductSkuAttrEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductSkuAttrSnapshotEntity;
import com.wanlianyida.ctpcore.product.domain.repository.IAuditLogRepository;
import com.wanlianyida.ctpcore.product.domain.repository.IPcProductAuditRelRepository;
import com.wanlianyida.ctpcore.product.domain.repository.IProductSnapshotRepository;
import com.wanlianyida.ctpcore.product.domain.service.operation.DataProcessor;
import com.wanlianyida.ctpcore.product.domain.service.operation.commonoperation.ProductCommonOperationService;
import com.wanlianyida.ctpcore.product.infrastructure.enums.*;
import com.wanlianyida.ctpcore.product.infrastructure.repository.converter.AuditOperateLogConvert;
import com.wanlianyida.ctpcore.product.infrastructure.repository.converter.ProductSnapshotConverter;
import com.wanlianyida.ctpcore.product.infrastructure.util.ProductIdGenerator;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import static com.wanlianyida.ctpcore.product.infrastructure.repository.converter.ProductSnapshotConverter.fillBaseDataOnCreate;

@Service
@Slf4j
public class PublishDataProcessor implements DataProcessor<ProductSnapshotCreateContext, AuditContextBO> {

    @Resource
    ProductSnapshotConverter snapshotConverter;

    @Resource
    IAuditLogRepository iAuditLogRepository;

    @Resource
    IProductSnapshotRepository iProductSnapshotRepository;

    @Resource
    ProductIdGenerator productIdGenerator;

    @Resource
    IPcProductAuditRelRepository pcProductAuditRelRepository;

    @Resource
    ProductCommonOperationService productCommonOperationService;

    @Override
    public boolean handlerType(String type) {
        return SnapshotBizTypeEnum.PUBLISH.getType().equals(type);
    }

    @Override
    public ResultMode<SnapshotSaveBO> handleSnapshotData(ProductSnapshotCreateContext context) {
        ProductSnapshotConverter.fillCompanyInfo(context);
        // spu快照信息
        AuditLogEntity auditLog = snapshotConverter.generateAuditLog(context, AuditApplyTypeEnum.PUBLISH, null);
        List<ProductSkuAttrSnapshotEntity> allAttrsSnapshotList = Lists.newArrayList();
        List<ProductSkuAttrEntity> list = BeanUtil.copyToList(context.getData().getAttrList(), ProductSkuAttrEntity.class);
        // spu属性快照信息
        for (ProductSkuAttrEntity attr : list) {
            allAttrsSnapshotList.add(snapshotConverter.generateAttrSnapshot(context, attr, auditLog.getId(), auditLog.getSpuCode(), null));
        }
        // 生成sku快照信息
        List<SkuSnapshotEntity> skuSnapshotEntityList = Lists.newArrayList();
        for (ProductSkuDetailBO skuDetail : context.getData().getSkuList()) {
            SkuSnapshotEntity skuSnapshot = snapshotConverter.generateSkuSnapshot(context, skuDetail, auditLog, null);
            skuSnapshotEntityList.add(skuSnapshot);
            for (ProductSkuAttrEntity attr : skuDetail.getAttrList()) {
                allAttrsSnapshotList.add(snapshotConverter.generateAttrSnapshot(context, attr, auditLog.getId(), auditLog.getSpuCode(), skuSnapshot.getSkuCode()));
            }
        }
        // 生成富文本数据
        RichTextBO richTextBO = snapshotConverter.generateRichTextBO(context, auditLog);
        // spu关联数据快照信息
        List<PcProductAuditRelEntity> relEntityList = snapshotConverter.generateAuditRelEntityList(context, auditLog);

        // 组装数据
        SnapshotSaveBO snapshotBO = SnapshotSaveBO.builder()
                .auditLogEntityList(Lists.newArrayList(auditLog))
                .skuSnapshotEntityList(skuSnapshotEntityList)
                .skuAttrSnapshotEntityList(allAttrsSnapshotList)
                .relEntityList(relEntityList)
                .richTextList(Lists.newArrayList(richTextBO))
                .build();
        return ResultMode.success(snapshotBO);
    }

    @Override
    public ResultMode<SnapshotRestoreBO> handleRestoreData(AuditContextBO t) {
        SnapshotRestoreBO ansBO = new SnapshotRestoreBO();
        for (AuditLogEntity auditLogEntity : t.getQueryList()) {
            AuditLogEntity auditLogSave = t.getCommandMapping().get(auditLogEntity.getId());
            // 第一步，审核信息和日志
            ansBO.getAuditLogEntityForUpdate().add(auditLogSave);
            ansBO.getAuditOperateLogEntityForInsert().add(AuditOperateLogConvert.createOperateLogEntity(auditLogSave, AuditStatusEnum.getByCode(auditLogSave.getAuditStatus()).getOperateType(),false));
            if (!AuditStatusEnum.PASS.getCode().equals(auditLogSave.getAuditStatus())) {
                continue;
            }
            // 第二步. 处理spu
            // 审核单的创建人,作为下面的创建人或更新人
            String userId = auditLogEntity.getCreatorId();
            String userName = auditLogEntity.getCreatorName();
            SpuEntity spuEntity = new SpuEntity();

            BeanUtil.copyProperties(auditLogEntity, spuEntity);
            spuEntity.setId(productIdGenerator.generateSnowId()); // 发布时生成新的ID
            // 发布肯定为新增spu
            fillBaseDataOnCreate(spuEntity, userId, userName);
            ansBO.getSpuForInsert().add(spuEntity);

            // 第三步. 处理sku
            List<SkuSnapshotEntity> skuSnapshotEntityList = iProductSnapshotRepository.getSkuSnapshotEntityList(auditLogEntity.getId());
            List<SkuEntity> skuEntityList = ProductSnapshotConverter.toSkuEntityList(skuSnapshotEntityList);
            // 发布肯定为新增sku
            skuEntityList.forEach(e -> fillBaseDataOnCreate(e, userId, userName));
            for (SkuEntity skuEntity : skuEntityList) {
                skuEntity.setSpuCode(spuEntity.getSpuCode());
                if (Objects.equals(auditLogEntity.getOnSaleAfterAudit(), 1)) {
                    // 发布后立即上架
                    skuEntity.setOnSaleStatus(OnSaleStatusEnum.OK.getType());
                    skuEntity.setPublishStatus(PublishStatusEnum.ON_SALE.getType());
                    skuEntity.setOnSaleDate(new Date());
                }else{
                    //首次发布商品发布后暂不上架，下架时间为当前时间
                    skuEntity.setOutSaleDate(new Date());
                    if(StrUtil.isBlank(skuEntity.getOutSaleType())){
                        skuEntity.setOutSaleType(OutSaleTypeEnum.NORMAL.getType());
                    }

                }
            }
            // sku数据
            ansBO.getSkuListForInsert().addAll(skuEntityList);

            // 处理商品关联信息数据
            List<PcProductAuditRelEntity> relEntityList = pcProductAuditRelRepository.listAll(auditLogEntity.getId());
            ProductExtraRelBO relBO = ProductSnapshotConverter.toProductExtraRelBo(relEntityList);
            String spuCode = spuEntity.getSpuCode();
            ansBO.getSkuPicEntityListForReplace().put(spuCode, relBO.getSkuPicEntityList());
            ansBO.getPcProductQualificationRelEntityListForReplace().put(spuCode, relBO.getQualificationRelEntityList());
            ansBO.getPcProductPublishCompanyEntityListForReplace().put(spuCode, relBO.getPublishCompanyEntityList());

            // 属性处理，所有spu的属性先删除；再批量添加
            List<ProductSkuAttrSnapshotEntity> skuAttrSnapshotEntityList = iProductSnapshotRepository.getSkuAttrSnapshotEntityList(auditLogEntity.getId());
            List<ProductSkuAttrEntity> skuAttrEntityList = ProductSnapshotConverter.toSkuAttrEntityList(skuAttrSnapshotEntityList);
            skuAttrEntityList.forEach(e -> fillBaseDataOnCreate(e, userId, userName));
            ansBO.getSkuAttrListForInsert().addAll(skuAttrEntityList);

            // 富文本
            ansBO.getRichTextList().add(RichTextBO.builder().spuCode(auditLogEntity.getSpuCode()).auditId(auditLogEntity.getId()).build());
        }

        return ResultMode.success(ansBO);
    }
}
