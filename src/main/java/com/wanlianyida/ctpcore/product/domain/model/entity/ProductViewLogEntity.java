package com.wanlianyida.ctpcore.product.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户浏览商品记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Data
public class ProductViewLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 浏览时间到秒
     */
    private Date viewTime;

    /**
     * 浏览时间到天
     */
    private String viewTimeDay;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新人名字
     */
    private String updaterName;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;


}
