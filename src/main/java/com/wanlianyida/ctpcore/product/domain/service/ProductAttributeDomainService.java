package com.wanlianyida.ctpcore.product.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductAttributeBO;
import com.wanlianyida.ctpcore.product.domain.model.condition.ProductAttributeQueryCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductAttributeEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductAttributeValueEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductCategoryAttributeEntity;
import com.wanlianyida.ctpcore.product.domain.repository.IProductAttributeRepository;
import com.wanlianyida.ctpcore.product.infrastructure.enums.UpdateCategoryConfigEventEnum;
import com.wanlianyida.ctpcore.product.infrastructure.event.EventPublisher;
import com.wanlianyida.ctpcore.product.infrastructure.event.product.CategoryConfigUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 商品规格
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductAttributeDomainService {

    @Resource
    private IProductAttributeRepository productAttributeRepository;

    @Resource
    private EventPublisher eventPublisher;

    /**
     * 新增商品规格
     */
    @Transactional(rollbackFor = Exception.class)
    public void addAttribute(ProductAttributeBO productAttributeBO) {
        productAttributeRepository.insertAttribute(productAttributeBO);
    }

    /**
     * 编辑商品规格
     */
    public void editAttribute(ProductAttributeBO productAttributeBO) {
        // 先查询出当前库中的名称
        ProductAttributeEntity entity = productAttributeRepository.queryAttributeByAttributeId(productAttributeBO.getId());

        productAttributeRepository.updateAttribute(productAttributeBO);

        // 品类配置更新事件（这里针对规格名称更新）
        // 规格名称发生变化才会发布更新事件
        if(entity != null && !StringUtils.isEmpty(entity.getAttributeName()) && !entity.getAttributeName().equals(productAttributeBO.getAttributeName())){
            updateCategoryConfigEvent(productAttributeBO.getId());
        }
    }

    private void updateCategoryConfigEvent(Long attributeId){
        // 查询该规格被哪些品类关联
        List<ProductCategoryAttributeEntity> categoryAttributeList = productAttributeRepository.categoryAttributeByAttributeId(attributeId);
        if(CollectionUtil.isEmpty(categoryAttributeList)){
            return;
        }
        List<Long> relCategoryIdList = categoryAttributeList.stream().map(ProductCategoryAttributeEntity::getRelCategoryId).collect(Collectors.toList());
        // 发布更新品类配置信息
        eventPublisher.publishCategoryConfigUpdateEvent(
                CategoryConfigUpdateEvent.builder().categoryIdList(relCategoryIdList).eventEnum(UpdateCategoryConfigEventEnum.UPDATE_ATTRIBUTE_NAMES).build());
    }

    /**
     * 删除商品规格
     */
    public void deleteAttribute(ProductAttributeEntity productAttributeEntity) {
        productAttributeRepository.updateById(productAttributeEntity);
    }

    /**
     * 查询商品规格
     */
    public List<ProductAttributeEntity> queryCondition(ProductAttributeQueryCondition condition){
        return productAttributeRepository.queryCondition(condition);
    }

    public boolean isRelated(Long attributeId){
        List<ProductCategoryAttributeEntity> list = productAttributeRepository.categoryAttributeByAttributeId(attributeId);
        return !CollectionUtil.isEmpty(list);
    }

    public List<ProductAttributeValueEntity> queryAttributeValueByAttributeId(Long attributeId){
       return productAttributeRepository.queryAttributeValueByAttributeId(attributeId);
    }

    public ProductAttributeEntity queryAttributeByAttributeId(Long attributeId){
        return productAttributeRepository.queryAttributeByAttributeId(attributeId);
    }

    public List<ProductAttributeEntity> queryAttributeByAttributeIdList(List<Long> attributeIdList){
        return productAttributeRepository.queryAttributeByAttributeIdList(attributeIdList);
    }

    public List<ProductAttributeValueEntity> queryAttributeValueByAttributeIdList(List<Long> attributeIdList){
        return productAttributeRepository.queryAttributeValueByAttributeIdList(attributeIdList);
    }

    /**
     * 批量更新规格信息
     */
    public void batchUpdateAttributeById(List<ProductAttributeEntity> attributeEntityList) {
        productAttributeRepository.batchUpdateAttributeById(attributeEntityList);
    }

}
