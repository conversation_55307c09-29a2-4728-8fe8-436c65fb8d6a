package com.wanlianyida.ctpcore.product.domain.repository;

import com.wanlianyida.ctpcore.product.domain.model.condition.ProductQualificationRelCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.PcProductQualificationRelEntity;

import java.util.List;

/**
 * <p>
 * 商品资质关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface IPcProductQualificationRelRepository {
    /**
     * 查询列表
     * @param condition
     * @return
     */
    List<PcProductQualificationRelEntity> queryCondition(ProductQualificationRelCondition condition);

    /**
     * 删除spuCode的关联关系
     * @param spuCode spuCode
     */
    void removeRelation(String spuCode);
}
