package com.wanlianyida.ctpcore.product.domain.repository;

import com.wanlianyida.ctpcore.product.domain.model.entity.AuditLogEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.AuditOperateLogEntity;
import com.wanlianyida.ctpcore.product.infrastructure.enums.AuditOperateTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月23日 18:09
 */
public interface IAuditOperateLogRepository {

    /**
     * 保存操作日志
     */
    boolean createAuditOperateLog(AuditLogEntity auditLog, AuditOperateTypeEnum operateTypeEnum);

    /**
     * 查询操作日志
     */
    List<AuditOperateLogEntity> queryAuditOperateLog(Long auditId);

    void batchInsertLog(List<AuditLogEntity> list);
}
