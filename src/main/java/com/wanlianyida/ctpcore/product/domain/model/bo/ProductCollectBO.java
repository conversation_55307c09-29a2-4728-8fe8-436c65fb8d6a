package com.wanlianyida.ctpcore.product.domain.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户收藏店铺表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Data
public class ProductCollectBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 收藏时间
     */
    private Date collectDate;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 商品综合状态（10未上架 20已上架 30违规下架）
     */
    private Integer onSaleStatus;

    /**
     * 是否删除 1删除 0未删除
     */
    private Integer deleted;

    /**
     * 价格
     */
    private BigDecimal priceFee;

    /**
     * 分类图片
     */
    private String categoryPic;

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 关联的计费单位id
     */
    private Integer relPricingUnitId;

    private List<String> publishCompanyId;

    /**
     * 发布类型
     */
    private String publishType;

}
