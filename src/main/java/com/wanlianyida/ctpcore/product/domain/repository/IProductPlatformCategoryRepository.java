package com.wanlianyida.ctpcore.product.domain.repository;

import com.wanlianyida.ctpcore.product.domain.model.condition.ProductPlatformCategoryQueryCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductPlatformCategoryEntity;

import java.util.List;

/**
 * 商品平台品类关联仓储
 * <AUTHOR>
 */
public interface IProductPlatformCategoryRepository {
    /**
     * 根据条件查询列表数据
     * @param condition 条件
     * @return 结果集
     */
    List<ProductPlatformCategoryEntity> queryCondition(ProductPlatformCategoryQueryCondition condition);

    /**
     * 新增一条
     * @param entity 实体
     */
    void insert(ProductPlatformCategoryEntity entity);

    /**
     * 根据id更新
     * @param entity 实体
     */
    void updateById(ProductPlatformCategoryEntity entity);

    /**
     * 清空所有数据，慎用（当前应用场景是定时任务已经上架商品的平台品类关联表数据做补偿时需要使用）
     */
    void deleteAll();

    /**
     * 根据id增加 productCount
     * @param entity 实体
     */
    void increaseProductCountById(ProductPlatformCategoryEntity entity);

    /**
     * 根据id减少 productCount
     * @param entity 实体
     */
    void reduceProductCountById(ProductPlatformCategoryEntity entity);
}
