package com.wanlianyida.ctpcore.product.domain.model.entity.inventory;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商品库存流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
@Data
public class ProductInventoryFlowEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 商品sku_code
     */
    private String skuCode;

    /**
     * 商品sku_id
     */
    private Long skuId;

    /**
     * 企业id(店铺id)
     */
    private String companyId;

    /**
     * 业务编号(订单号)
     */
    private String busNum;

    /**
     * 库存变化数量
     */
    private BigDecimal changeQuantity;

    /**
     * 业务动作:10-商品上架,20-下单扣减,30-订单取消
     * InventoryBusActionEnum
     */
    private String busAction;

    /**
     * 操作动作:10-增加,20-减少
     */
    private String operationAction;

    /**
     * 操作状态:10-成功,20-失败,30-处理中
     */
    private Integer operationStatus;

    /**
     * 操作时间
     */
    private Date operationDate;

    /**
     * 操作人id
     */
    private String operationId;

    /**
     * 操作人姓名
     */
    private String operationName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除:0-否,1-是
     */
    private Integer deleted;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 商品sku_id
     */
    private List<Long> skuIdList;

    /**
     * 商品sku_code
     */
    private List<String> skuCodeList;

    /**
     * id
     */
    private List<Long> idList;

}