package com.wanlianyida.ctpcore.product.domain.model.bo;

import lombok.Data;

import java.util.Date;

/**
 * 品类规格-规格值BO
 */
@Data
public class CategoryAttributeValueBO {

    /**
     * id
     */
    private Long id;

    /**
     * 品类规格关联表id
     */
    private Long categoryAttributeId;

    /**
     * 规格值
     */
    private String attributeValue;

    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 创建来源，10平台 20卖家
     */
    private String createSource;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新人名字
     */
    private String updaterName;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 关联的品类id
     */
    private Long relCategoryId;

    /**
     * 关联的规格id
     */
    private Long relAttributeId;
}
