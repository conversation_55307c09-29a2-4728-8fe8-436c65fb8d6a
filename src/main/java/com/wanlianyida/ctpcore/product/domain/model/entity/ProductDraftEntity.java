package com.wanlianyida.ctpcore.product.domain.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商品草稿箱表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
public class ProductDraftEntity  {
    /**
     * 主键
     */
    private Long id;

    /**
     * 大文本详情id
     */
    private String detailId;

    /**
     * 商品名称(品牌+品类(3级)+sku规格)
     */
    private String productName;

    /**
     * 图片url
     */
    private String picUrl;

    /**
     * 商品单价
     */
    private BigDecimal priceFee;

    /**
     * 商品数量
     */
    private BigDecimal quantity;

    /**
     * 计价单位编号
     */
    private Integer relPricingUnitId;

    /**
     * 计量单位编号
     */
    private Integer relMeasurementUnitId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 更新人名字
     */
    private String updaterName;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer deleted;
}
