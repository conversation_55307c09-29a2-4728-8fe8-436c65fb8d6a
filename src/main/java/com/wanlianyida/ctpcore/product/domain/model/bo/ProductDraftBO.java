package com.wanlianyida.ctpcore.product.domain.model.bo;

import com.wanlianyida.ctpcore.product.application.command.ProductDraftPicCommand;
import com.wanlianyida.ctpcore.product.application.command.ProductDraftSkuDetailCommand;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.Valid;

@Data
public class ProductDraftBO {
    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("品类3级ID")
    private Long categoryId3;

    @ApiModelProperty("3级品类名称")
    private String categoryName3;

    @ApiModelProperty("店铺ID")
    private Long shopId;

    @ApiModelProperty("计价单位ID")
    private Integer relPricingUnitId;

    @ApiModelProperty("计量单位ID")
    private Integer relMeasurementUnitId;

    @ApiModelProperty("图片列表")
    private List<ProductDraftPicCommand> picList;

    @Valid
    @ApiModelProperty("sku信息")
    private List<ProductDraftSkuDetailCommand> skuList;

    @ApiModelProperty("草稿箱id")
    private Long draftId;

    @ApiModelProperty("创建人id")
    private String creatorId;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("企业id")
    private String companyId;
}
