package com.wanlianyida.ctpcore.product.domain.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 上下架参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangeSaleStatusBO {
    private Long skuId;

    private List<Long> skuIdList;

    private Integer onSaleStatus;

    // 下架原因
    private String reason;
}
