package com.wanlianyida.ctpcore.product.domain.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductCollectBO;
import com.wanlianyida.ctpcore.product.domain.model.entity.PcProductPublishCompanyEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.SkuEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.SpuEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/02/06/13:28
 */
public class ProductCollectAssembler {

    public static List<ProductCollectBO> buildProductInfoCondition(List<ProductCollectBO> productViewLogBOS,
                                                                   List<SkuEntity> skuEntityList,
                                                                   Map<String, SpuEntity> spuMap,
                                                                   List<PcProductPublishCompanyEntity> companyEntityList) {
        if (ObjUtil.isEmpty(productViewLogBOS) || ObjUtil.isEmpty(skuEntityList)) {
            return productViewLogBOS;
        }
        Map<Long, SkuEntity> collect = skuEntityList.stream().collect(Collectors.toMap(SkuEntity::getId, spuEntity -> spuEntity));

        Map<String, List<String>> resultMap = companyEntityList.stream()
                .collect(Collectors.groupingBy(
                        PcProductPublishCompanyEntity::getSpuCode,
                        Collectors.mapping(PcProductPublishCompanyEntity::getPublishCompanyId, Collectors.toList())
                ));
        productViewLogBOS.forEach(productViewLogBO -> {
            Long productId = productViewLogBO.getProductId();
            SkuEntity skuEntity = collect.get(productId);
            if (ObjUtil.isNotEmpty(skuEntity)) {
                BeanUtil.copyProperties(skuEntity, productViewLogBO);
                productViewLogBO.setCategoryPic(skuEntity.getPicUrl());
            }
            if (ObjUtil.isNotEmpty(spuMap) && ObjUtil.isNotEmpty(skuEntity)) {
                SpuEntity spuEntity = spuMap.get(skuEntity.getSpuCode());
                productViewLogBO.setRelPricingUnitId(ObjUtil.isNotEmpty(spuEntity) ? spuEntity.getRelPricingUnitId() : 0);
            }
            if (ObjUtil.isNotEmpty(resultMap) && ObjUtil.isNotEmpty(skuEntity)) {
                List<String> publishCompanyIdList = resultMap.get(skuEntity.getSpuCode());
                productViewLogBO.setPublishCompanyId(ObjUtil.isNotEmpty(publishCompanyIdList) ? publishCompanyIdList : new ArrayList<>());
            }
        });
        return productViewLogBOS;
    }
}
