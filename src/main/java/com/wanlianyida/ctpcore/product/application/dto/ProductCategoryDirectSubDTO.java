package com.wanlianyida.ctpcore.product.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 父类直接子级
 * <AUTHOR>
 */
@Data
public class ProductCategoryDirectSubDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 图片
     */
    private String categoryPic;

    /**
     * 启用状态(1启用 0停用)
     */
    private Integer enableStatus;

    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 品类描述
     */
    private String categoryDesc;

    /**
     * 品类级别：1->1级；2->2级；3->3级ji
     */
    private Integer level;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 关联的规格名称列表
     */
    private String relAttributeName;

    /**
     * 关联的品牌名称列表
     */
    private String relBrandName;

}
