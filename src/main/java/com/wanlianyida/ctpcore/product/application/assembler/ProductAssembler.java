package com.wanlianyida.ctpcore.product.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanlianyida.ctpcore.product.application.dto.*;
import com.wanlianyida.ctpcore.product.domain.model.bo.*;
import com.wanlianyida.ctpcore.product.domain.model.condition.ProductSkuCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.*;
import com.wanlianyida.ctpcore.product.infrastructure.constant.AutoAuditConstant;
import com.wanlianyida.ctpcore.product.infrastructure.enums.AuditStatusEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.DeletedStatusEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.OnSaleStatusEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.PublishStatusEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.PublishTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.OutSaleTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.*;
import com.wanlianyida.ctpcore.product.infrastructure.event.product.ProductOperateEvent;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.support.api.model.command.MsgCommand;
import com.wanlianyida.support.api.model.enums.MsgTemplateEnum;
import com.wanlianyida.support.api.model.command.BdDelayTaskCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月27日 09:53
 */
@Slf4j
public class ProductAssembler {

    public static List<ProductSpuDetailDTO> createProductSpuDetailDTO(List<ProductDetailBO> list, List<ProductSkuDetailBO> skuList, ProductRelInfoBO productRelInfoBO) {
        List<ProductSpuDetailDTO> result = new ArrayList<>();
        Map<String, List<ProductSkuDetailBO>> mapping = skuList.stream().collect(Collectors.groupingBy(e -> e.getSku().getSpuCode()));
        for (ProductDetailBO productDetailBO : list) {
            ProductSpuDetailDTO detail = BeanUtil.toBean(productDetailBO.getSpu(), ProductSpuDetailDTO.class);
            if (productRelInfoBO != null) {
                // 企业
                List<PcProductPublishCompanyEntity> companyEntityList = productRelInfoBO.getPublishCompanyList();
                List<PcProductPublishCompanyEntity> companyEntities = companyEntityList.stream().filter(e -> e.getSpuCode().equals(detail.getSpuCode())).collect(Collectors.toList());
                detail.setPublishCompanyList(BeanUtil.copyToList(companyEntities, ProductPublishCompanyDTO.class));
                // 图片
                List<SkuPicEntity> picList = productRelInfoBO.getPicList();
                detail.setPicList(BeanUtil.copyToList(picList, SkuPicDTO.class));
                // 资质
                List<ProductQualificationBO> qualificationList = productRelInfoBO.getQualificationList();
                detail.setQualificationList(toQualificationList(qualificationList));
            }
            // 属性
            detail.setProductSpuAttrList(BeanUtil.copyToList(productDetailBO.getAttrList(), ProductSkuAttrDTO.class));
            detail.setProductSkuDetailList(mapping.get(productDetailBO.getSpuCode()).stream().map(e -> createProductSkuInfoDTO(e, productDetailBO.getSpuCode())).collect(Collectors.toList()));
            result.add(detail);
        }
        return result;
    }

    public static List<ProductSkuDetailDTO> createProductSkuDetailDTO(List<ProductSkuDetailBO> list, List<ProductDetailBO> spuList, ProductRelInfoBO productRelInfoBO) {
        List<ProductSkuDetailDTO> result = new ArrayList<>();
        Map<String, ProductDetailBO> mapping = spuList.stream().collect(Collectors.toMap(ProductDetailBO::getSpuCode, e -> e));
        for (ProductSkuDetailBO productSkuDetailBO : list) {
            ProductSkuDetailDTO detail = BeanUtil.toBean(productSkuDetailBO.getSku(), ProductSkuDetailDTO.class);
            ProductDetailBO spu = mapping.get(productSkuDetailBO.getSku().getSpuCode());
            if (spu != null) {
                ProductSpuDTO spuDTO = BeanUtil.toBean(spu.getSpu(), ProductSpuDTO.class);
                if (productRelInfoBO != null) {
                    // 企业
                    List<PcProductPublishCompanyEntity> companyEntityList = productRelInfoBO.getPublishCompanyList();
                    List<PcProductPublishCompanyEntity> companyEntities = companyEntityList.stream().filter(e -> e.getSpuCode().equals(spuDTO.getSpuCode())).collect(Collectors.toList());
                    spuDTO.setPublishCompanyList(BeanUtil.copyToList(companyEntities, ProductPublishCompanyDTO.class));
                    // 图片
                    List<SkuPicEntity> picList = productRelInfoBO.getPicList();
                    spuDTO.setPicList(BeanUtil.copyToList(picList, SkuPicDTO.class));
                    // 资质
                    List<ProductQualificationBO> qualificationList = productRelInfoBO.getQualificationList();
                    spuDTO.setQualificationList(toQualificationList(qualificationList));
                }
                detail.setProductSpu(spuDTO);
                detail.setProductSpuAttrList(BeanUtil.copyToList(spu.getAttrList(), ProductSkuAttrDTO.class));
                detail.setSpuCode(spu.getSpuCode());
            }
            detail.setProductSkuAttrList(BeanUtil.copyToList(productSkuDetailBO.getAttrList(), ProductSkuAttrDTO.class));
            result.add(detail);
        }
        return result;
    }

    private static List<ProductQualificationListDTO> toQualificationList(List<ProductQualificationBO> boList) {
        List<ProductQualificationListDTO> dtoList = Lists.newArrayList();
        for (ProductQualificationBO bo : boList) {
            ProductQualificationListDTO dto = BeanUtil.toBean(bo, ProductQualificationListDTO.class);
            dto.setProductAttachmentListDTOList(BeanUtil.copyToList(bo.getAttachmentList(), PcProductAttachmentListDTO.class));
            dtoList.add(dto);
        }
        return dtoList;
    }

    public static ProductSpuInfoDTO createProductSpuInfoDTO(ProductDetailBO bo) {
        ProductSpuInfoDTO result = BeanUtil.copyProperties(bo.getSpu(), ProductSpuInfoDTO.class);
        result.setProductSpuAttrList(BeanUtil.copyToList(bo.getAttrList(), ProductSkuAttrDTO.class));
        return result;
    }

    public static ProductSkuInfoDTO createProductSkuInfoDTO(ProductSkuDetailBO bo, String spuCode) {
        ProductSkuInfoDTO result = BeanUtil.copyProperties(bo.getSku(), ProductSkuInfoDTO.class);
        result.setSpuCode(spuCode);
        result.setProductSkuAttrList(BeanUtil.copyToList(bo.getAttrList(), ProductSkuAttrDTO.class));
        return result;
    }

    public static ProductSkuPortalDetailDTO createSkuPortalDetail(ProductSkuInfoBO bo, ProductSpuInfoBO spuBO) {
        ProductSkuPortalDetailDTO result = new ProductSkuPortalDetailDTO();
        ProductSkuPortalSkuDTO sku = BeanUtil.toBean(bo.getProductSku(), ProductSkuPortalSkuDTO.class);
        ProductSkuPortalSpuDTO spu = BeanUtil.toBean(spuBO.getProductSpu(), ProductSkuPortalSpuDTO.class);
        List<ProductSkuAttrEntity> productSkuAttrList = bo.getProductSkuAttrList();
        List<ProductSkuAttrEntity> productSpuAttrList = spuBO.getProductSpuAttrList();
        JSONObject dynamicAttr = new JSONObject();
        JSONObject staticAttr = new JSONObject();
        for (ProductSkuAttrEntity productSkuAttrEntity : productSkuAttrList) {
            dynamicAttr.put(productSkuAttrEntity.getAttributeName(), productSkuAttrEntity.getValueText());
        }
        for (ProductSkuAttrEntity productSpuAttrEntity : productSpuAttrList) {
            staticAttr.put(productSpuAttrEntity.getAttributeName(), productSpuAttrEntity.getValueText());
        }
        boolean isUnitEqual = Objects.equals(spu.getRelPricingUnitId(), spu.getRelMeasurementUnitId());
        spu.setIsUnitEqual(isUnitEqual);
        spu.setAttrInfo(staticAttr);
        sku.setAttrInfo(dynamicAttr);
        sku.setPicUrl(bo.getProductSku().getPicUrl());
        sku.setSalesPublishType(bo.getSalesPublishType());
        result.setSkuInfo(sku);
        result.setSpuInfo(spu);
        result.setAttrInfo(dynamicAttr);
        return result;
    }

    public static void fillBaseInfo(ProductOperateEvent entity, TokenInfo tokenInfo) {
        try {
            entity.setOperatorId(tokenInfo.getUserBaseId());
            entity.setOperatorName(tokenInfo.getUsername());
            entity.setCompanyId(tokenInfo.getCompanyId());
            entity.setOperateAccount(tokenInfo.getLoginName());
            entity.setCompanyName(tokenInfo.getCompanyName());
        } catch (Exception e) {
            log.error("tokenInfo获取失败", e);
        }
    }

    public static void fillBaseInfo2(ProductOperateEvent entity, TokenInfo tokenInfo) {
        try {
            entity.setOperatorId(tokenInfo.getUserBaseId());
            entity.setOperatorName(AutoAuditConstant.CTP_PRODUCT_AUTO_AUDIT_OPERATOR_NAME);
            entity.setCompanyId(tokenInfo.getCompanyId());
            entity.setOperateAccount(AutoAuditConstant.CTP_PRODUCT_AUTO_AUDIT_OPERATOR_ACCOUNT);
            entity.setCompanyName(tokenInfo.getCompanyName());
        } catch (Exception e) {
            log.error("tokenInfo获取失败", e);
        }
    }

    public static ProductBatchPublishDTO toProductBatchPublishDTO(ProductBatchPublishAnsBO ansBO) {
        ProductBatchPublishDTO batchPublishDTO = new ProductBatchPublishDTO();
        List<ProductBatchPublishDTO.ItemDTO> itemDTOList = BeanUtil.copyToList(ansBO.getResultList(), ProductBatchPublishDTO.ItemDTO.class);
        batchPublishDTO.setResultList(itemDTOList);
        return batchPublishDTO;
    }



    public static ProductSkuCondition buildSyncCustomRelCondition() {
        ProductSkuCondition condition = new ProductSkuCondition();
        condition.setDeleted(DeletedStatusEnum.NOT_DELETED.getDeletedStatusCode());
        condition.setOnSaleStatus(OnSaleStatusEnum.OK.getType());
        return condition;
    }

    public static ProductSkuCondition buildSkuCodeListCondition(List<String> skuCodeList) {
        ProductSkuCondition condition = new ProductSkuCondition();
        condition.setSkuCodeList(skuCodeList);
        return condition;
    }

    public static Map<Long, Integer> buildCondition(List<PcProductQualificationRelEntity> list, List<Long> collect, List<SkuEntity> skuEntities) {
        Map<String, Integer> spuCodeMaps = skuEntities.stream()
                .collect(Collectors.groupingBy(SkuEntity::getSpuCode, Collectors.summingInt(e -> 1)));
        Map<Long, Integer> resultMap = new HashMap<>();
        list.forEach(e -> {
            Integer i = spuCodeMaps.getOrDefault(e.getSpuCode(), 1);
            if (!resultMap.containsKey(e.getQualificationId())) {
                resultMap.put(e.getQualificationId(), i);
            } else {
                resultMap.put(e.getQualificationId(), resultMap.get(e.getQualificationId()) + i);
            }
        });
        collect.forEach(e -> {
            if (!resultMap.containsKey(e)) {
                resultMap.put(e, 0);
            }
        });
        return resultMap;
    }
    public static List<MsgCommand> buildAuditMsgCommand(Map<Long, List<SkuSnapshotEntity>> skuSnapshotEntityList, List<AuditLogEntity> entityList) {
        List<MsgCommand> msgCommandListResult = new ArrayList<>();
        Iterator<Map.Entry<Long, List<SkuSnapshotEntity>>> iterator = skuSnapshotEntityList.entrySet().iterator();
        while (iterator.hasNext()){
            Map.Entry<Long, List<SkuSnapshotEntity>> next = iterator.next();
            List<SkuSnapshotEntity> skuSnapshotEntities = next.getValue();
            List<MsgCommand> msgCommandList = buildSkuSnapshotMsgCommand(skuSnapshotEntities, entityList);
            msgCommandListResult.addAll(msgCommandList);
        }
        return msgCommandListResult;
    }
    public static List<MsgCommand> buildSkuSnapshotMsgCommand(List<SkuSnapshotEntity> skuList, List<AuditLogEntity> entityList) {
        List<MsgCommand> msgCommandList = new ArrayList<>();
        AuditLogEntity auditLogEntity = IterUtil.getFirst(entityList);
        Integer auditStatus = auditLogEntity.getAuditStatus();
        String templateId = "";
        //20审核通过; 30审核驳回
        if (ObjectUtil.equals(auditStatus, AuditStatusEnum.PASS.getCode())) {
            templateId = MsgTemplateEnum.PRODUCT_APPROVED.getId();
        } else if (ObjectUtil.equals(auditStatus, AuditStatusEnum.REJECT.getCode())) {
            templateId = MsgTemplateEnum.PRODUCT_REJECTED.getId();
        }
        Map<String, List<SkuSnapshotEntity>> spuCodeSkuList = skuList.stream().collect(Collectors.groupingBy(SkuSnapshotEntity::getSpuCode));
        Iterator<Map.Entry<String, List<SkuSnapshotEntity>>> iterator = spuCodeSkuList.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<SkuSnapshotEntity>> next = iterator.next();
            List<SkuSnapshotEntity> value = next.getValue();
            SkuSnapshotEntity skuEntity = IterUtil.getFirst(value);
            String companyId = skuEntity.getCompanyId();
            String companyName = skuEntity.getCompanyName();
            List<String> targetUserList = new ArrayList<>();
            targetUserList.add(StrUtil.toString(skuEntity.getCreatorId()));
            String skuCode = skuEntity.getSkuCode();
            String level3categoryName = skuEntity.getCategoryName3();
            if (value.size() > 1) {
                level3categoryName = level3categoryName + "等";
            }
            MsgCommand msgCommand = sendProductMsg(templateId, companyId, targetUserList, skuCode, level3categoryName,companyName);
            msgCommandList.add(msgCommand);
        }
        return msgCommandList;
    }

    public static MsgCommand sendProductMsg(String templateId, String companyId, List<String> targetUserList, String skuCode, String level3categoryName,String companyName) {
        MsgCommand msgCommand = MsgCommand.builder()
                .templateId(templateId)
                .companyId(companyId)
                .targetUserBaseIdList(targetUserList)
                .addParam("companyName",companyName)
                .addParam("skuCode", skuCode)
                .addParam("level3categoryName", level3categoryName)
                .build();
        return msgCommand;
    }
    public static List<BdDelayTaskCommand> buildDelayTasks(List<ProductSkuSpuMapEntity> skuIdList) {
        List<BdDelayTaskCommand> result = new ArrayList<>();
        skuIdList.forEach(e -> {
            // 上架定向发布
            if (PublishStatusEnum.ON_SALE.getType().equals(e.getOnSaleStatus()) &&
                    PublishTypeEnum.COMPANY.getCode().equals(e.getPublishType())){
                BdDelayTaskCommand command = new BdDelayTaskCommand();
                // 任务类型
                command.setTaskType(AutoAuditConstant.PRODUCT_24_DELAY_NOTICE);
                // 回调类型:[10-mq,20-http]
                command.setCallType(10);
                // 业务id
                command.setBizId(e.getSkuCode());
                command.setExecTime(DateUtils.addHours(e.getOnSaleDate(),24));
                result.add(command);
            }
        });
        return result;
    }

    public static ProductModifyOrNotListDTO buildProductModifyOrNotListDTO(List<SkuEntity> skuEntityList, Set<String> auditSpuCodeSet
            , Map<String, String> mapPic, Map<Long, String> cateIdPicMap,Map<String, SpuEntity> spuCodeMap) {
        List<ProductModifyOrNotListDTO.ProductModifyOrNotDTO> result = Lists.newArrayList();
        for (SkuEntity skuEntity : skuEntityList) {
            ProductModifyOrNotListDTO.ProductModifyOrNotDTO productModifyOrNotDTO = BeanUtil.toBean(skuEntity, ProductModifyOrNotListDTO.ProductModifyOrNotDTO.class);
            productModifyOrNotDTO.setNotModifiableFlag(false);
            SpuEntity spuEntity = spuCodeMap.get(skuEntity.getSpuCode());
            productModifyOrNotDTO.setRelPricingUnitId(spuEntity.getRelPricingUnitId());
            productModifyOrNotDTO.setRelMeasurementUnitId(spuEntity.getRelMeasurementUnitId());
            productModifyOrNotDTO.setNotModifiableReason("");
            if (auditSpuCodeSet.contains(skuEntity.getSpuCode())) {
                productModifyOrNotDTO.setNotModifiableReason("存在待审核的修改记录");
                productModifyOrNotDTO.setNotModifiableFlag(true);
            }
            if (ObjectUtil.equals(skuEntity.getOnSaleStatus(), OnSaleStatusEnum.REJECT.getType())
                    && ObjectUtil.equals(skuEntity.getOutSaleType(), OutSaleTypeEnum.BREAK_RULE.getType())) {
                productModifyOrNotDTO.setNotModifiableReason("违规下架商品");
                productModifyOrNotDTO.setNotModifiableFlag(true);
            }
            String picUrl = productModifyOrNotDTO.getPicUrl();
            if (StringUtils.isNotBlank(picUrl)) {
                productModifyOrNotDTO.setPicUrl(mapPic.get(picUrl));
            } else {
                String catePic = cateIdPicMap.get(Convert.toLong(productModifyOrNotDTO.getCategoryId3()));
                productModifyOrNotDTO.setPicUrl(mapPic.get(catePic));
            }
            result.add(productModifyOrNotDTO);
        }
        Map<Boolean, List<ProductModifyOrNotListDTO.ProductModifyOrNotDTO>> collect = result.stream().collect(Collectors.partitioningBy(ProductModifyOrNotListDTO.ProductModifyOrNotDTO::getNotModifiableFlag));
        List<ProductModifyOrNotListDTO.ProductModifyOrNotDTO> notModifiableFlag = collect.get(true);
        List<ProductModifyOrNotListDTO.ProductModifyOrNotDTO> modifiableFlag = collect.get(false);
        ProductModifyOrNotListDTO build = ProductModifyOrNotListDTO.builder().modifiableList(modifiableFlag)
                .notModifiableList(notModifiableFlag)
                .build();
        return build;
    }

    public static ResultMode buildValidateMessage(ValidateMessageEnum validateMessageEnum, List<String> skuIdList) {
        BatchChangeInventoryDTO validateMessageDTO = new BatchChangeInventoryDTO();
        BatchChangeInventoryDTO.ValidateMessage validateMessage = new BatchChangeInventoryDTO.ValidateMessage();
        validateMessage.setValidateType(validateMessageEnum.getType());
        validateMessage.setMessage(validateMessageEnum.getMessage());
        validateMessage.setSkuIdList(skuIdList);
        validateMessageDTO.setValidateMessage(validateMessage);
        return ResultMode.fail(validateMessage.getMessage(), validateMessageDTO);
    }
}
