package com.wanlianyida.ctpcore.product.application.handler;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CommonImportVerifyHandler implements IExcelVerifyHandler<Map<String,Object>> {

    @Override
    public ExcelVerifyHandlerResult verifyHandler(Map<String,Object> map) {
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        //过滤空行
        if(isAllNull(map)){
            result.setSuccess(false);
        }
        return result;
    }

    private boolean isAllNull(Map<String,Object> map){
        boolean isAllNull = true;

        if(map == null){
            return true;
        }

        for(String key : map.keySet()){
            if(map.get(key) != null){
                isAllNull = false;
            }
        }

        return isAllNull;
    }

}
