package com.wanlianyida.ctpcore.product.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.ctpcore.product.application.assembler.AuditAssembler;
import com.wanlianyida.ctpcore.product.application.command.AuditLogAuditCommand;
import com.wanlianyida.ctpcore.product.application.command.AuditRejectReasonAddCommand;
import com.wanlianyida.ctpcore.product.application.command.AuditRejectReasonSortCommand;
import com.wanlianyida.ctpcore.product.application.command.BatchAuditCommand;
import com.wanlianyida.ctpcore.product.application.dto.*;
import com.wanlianyida.ctpcore.product.application.query.AuditLogQuery;
import com.wanlianyida.ctpcore.product.application.query.AuditRejectReasonQuery;
import com.wanlianyida.ctpcore.product.application.query.AuditSkuListQuery;
import com.wanlianyida.ctpcore.product.domain.model.bo.AuditLogDetailBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.AuditSkuListBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductDetailBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.StatusStatisticsBO;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditLogCondition;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditRejectReasonCondition;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditRelCondition;
import com.wanlianyida.ctpcore.product.domain.model.condition.AuditSkuListCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.AuditLogEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.AuditRejectReasonEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.PcProductAuditRelEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductCategoryEntity;
import com.wanlianyida.ctpcore.product.domain.service.AuditDomainService;
import com.wanlianyida.ctpcore.product.domain.service.ProductAuditRelDomainService;
import com.wanlianyida.ctpcore.product.domain.service.ProductCategoryDomainService;
import com.wanlianyida.ctpcore.product.domain.service.ProductDomainService;
import com.wanlianyida.ctpcore.product.domain.service.operation.ProductOperationDomainService;
import com.wanlianyida.ctpcore.product.infrastructure.enums.AuditStatusEnum;
import com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductException;
import com.wanlianyida.ctpcore.product.infrastructure.exception.CtpCoreProductExceptionEnum;
import com.wanlianyida.ctpcore.product.infrastructure.exechange.UploadExchangeService;
import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 商品审核管理
 * @Date 2024年11月22日 15:42
 */
@Slf4j
@Service
public class AuditAppService {

    @Resource
    private AuditDomainService auditDomainService;
    @Resource
    private ProductDomainService productDomainService;
    @Resource
    private UploadExchangeService uploadExchangeService;
    @Resource
    private ProductCategoryDomainService categoryDomainService;
    @Resource
    private ProductOperationDomainService productOperationDomainService;
    @Resource
    private ProductAuditRelDomainService productAuditRelDomainService;

    /**
     * 查询驳回原因列表
     */
    public List<AuditRejectReasonDTO> queryRejectReason(AuditRejectReasonQuery query) {
        AuditRejectReasonCondition condition = BeanUtil.toBean(query, AuditRejectReasonCondition.class);
        return BeanUtil.copyToList(auditDomainService.queryRejectReason(condition), AuditRejectReasonDTO.class);
    }

    /**
     * 添加驳回原因
     */
    public void addRejectReason(AuditRejectReasonAddCommand command) {
        auditDomainService.addRejectReason(AuditAssembler.addRejectReason(command));
    }

    /**
     * 商品审核
     */
    public void audit(AuditLogAuditCommand command) {
        if (AuditStatusEnum.REJECT.getCode().equals(command.getAuditStatus()) && StrUtil.isBlank(command.getRejectReason())) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_REASON_BLANK);
        }
        AuditLogEntity query = auditDomainService.queryById(command.getId());
        if (query == null) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_LOG_NOT_EXISTS);
        }
        if (AuditStatusEnum.PASS.getCode().equals(query.getAuditStatus()) || AuditStatusEnum.REJECT.getCode().equals(query.getAuditStatus())) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_ALREADY_AUDIT);
        }
        if (AuditStatusEnum.CANCELED.getCode().equals(query.getAuditStatus())) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_LOG_CANCELED);
        }
        if (AuditStatusEnum.CANCELED.getCode().equals(command.getAuditStatus()) && !AuditStatusEnum.WAIT.getCode().equals(query.getAuditStatus())) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_NOT_ALLOWED_CANCEL);
        }
        AuditLogEntity entity = AuditAssembler.createAuditUpdateEntity(command);
        productOperationDomainService.restoreDataAfterAuditApprove2(Collections.singletonList(entity),false);
    }

    /**
     * spu维度审核列表
     */
    public ResultMode<List<AuditLogSpuListDTO>> pageSpuCondition(PagingInfo<AuditLogQuery> pageQuery) {
        Page<AuditLogSpuListDTO> page = PageHelper.startPage(pageQuery.currentPage, pageQuery.pageLength, pageQuery.getCountTotal());
        AuditLogCondition condition = BeanUtil.copyProperties(pageQuery.getFilterModel(), AuditLogCondition.class);
        List<AuditLogEntity> list = auditDomainService.pageSpuCondition(condition);
        if (CollectionUtil.isEmpty(list)) {
            return ResultMode.successPageList(Collections.EMPTY_LIST, 0);
        }
        List<AuditLogSpuListDTO> result = BeanUtil.copyToList(list, AuditLogSpuListDTO.class);
        List<Long> categoryIdList = result.stream().map(AuditLogSpuListDTO::getCategoryId3).distinct().collect(Collectors.toList());
        List<ProductCategoryEntity> categoryList = categoryDomainService.queryByIds(categoryIdList);
        Map<Long, ProductCategoryEntity> picMapping = categoryList.stream().collect(Collectors.toMap(ProductCategoryEntity::getId, e -> e));
        // 图片链接转换
        List<String> ulrList = picMapping.values().stream().map(ProductCategoryEntity::getCategoryPic).collect(Collectors.toList());
        Map<String, String> urlMapping = uploadExchangeService.convertUrlList(ulrList);
        for (AuditLogSpuListDTO auditLogSpuListDTO : result) {
            if (urlMapping != null) {
                ProductCategoryEntity category = picMapping.get(auditLogSpuListDTO.getCategoryId3());
                if (category != null) {
                    auditLogSpuListDTO.setPicUrl(urlMapping.get(category.getCategoryPic()));
                }
            }
            auditLogSpuListDTO.setCategoryFullName(auditLogSpuListDTO.getCategoryFullName().replace("_", ">"));
        }
        return ResultMode.successPageList(result, (int) page.getTotal());
    }

    /**
     * sku维度审核列表
     */
    public ResultMode<List<AuditLogSkuListDTO>> pageSkuCondition(PagingInfo<AuditSkuListQuery> pageQuery) {
        Page<AuditLogSpuListDTO> page = PageHelper.startPage(pageQuery.currentPage, pageQuery.pageLength, pageQuery.getCountTotal());
        AuditSkuListCondition condition = BeanUtil.copyProperties(pageQuery.getFilterModel(), AuditSkuListCondition.class);
        List<AuditSkuListBO> list = auditDomainService.pageSkuCondition(condition);
        if (CollectionUtil.isEmpty(list)) {
            return ResultMode.successPageList(Collections.EMPTY_LIST, 0);
        }
        List<AuditLogSkuListDTO> result = BeanUtil.copyToList(list, AuditLogSkuListDTO.class);
        // 图片链接转换
        List<String> urlList = result.stream().map(AuditLogSkuListDTO::getPicUrl).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(urlList)) {
            Map<String, String> urlMapping = uploadExchangeService.convertUrlList(urlList);
            for (AuditLogSkuListDTO auditLogSkuListDTO : result) {
                auditLogSkuListDTO.setPicUrl(urlMapping.get(auditLogSkuListDTO.getPicUrl()));
            }
        }
        return ResultMode.successPageList(result, (int) page.getTotal());
    }

    /**
     * 根据状态统计数据（spu）
     */
    public AuditStatisticsDTO statisticsAuditSpuList(AuditLogQuery query) {
        Map<Integer, StatusStatisticsBO> mapping = auditDomainService.statisticsAuditSpuList(BeanUtil.copyProperties(query, AuditLogCondition.class));
        return AuditAssembler.createStatisticsDTO(mapping);
    }

    /**
     * 根据状态统计数据（sku）
     */
    public AuditStatisticsDTO statisticsAuditSkuList(AuditSkuListQuery query) {
        Map<Integer, StatusStatisticsBO> mapping = auditDomainService.statisticsAuditSkuList(BeanUtil.copyProperties(query, AuditSkuListCondition.class));
        return AuditAssembler.createStatisticsDTO(mapping);
    }

    /**
     * 审核详情
     */
    public AuditLogDetailDTO queryDetail(IdCommand command) {
        AuditLogDetailBO auditLogDetail = auditDomainService.queryDetail(command.getId());
        AuditLogDetailDTO result = BeanUtil.copyProperties(auditLogDetail, AuditLogDetailDTO.class);
        result.getAuditLog().setCategoryFullName(result.getAuditLog().getCategoryFullName().replace("_", ">"));
        // 封装商品信息
        ProductDetailBO productDetail = productDomainService.queryAuditSnapshot(command.getId());
        // 商品关联审核信息
        List<PcProductAuditRelEntity> relEntityList = productAuditRelDomainService.queryAuditRelData(AuditRelCondition.builder().auditId(auditLogDetail.getAuditLog().getId()).build());
        result.setAuditRelDataList(BeanUtil.copyToList(relEntityList, AuditRelDataDTO.class));
        // 商品图片转换
        String url = null;
        if (StrUtil.isNotBlank(productDetail.getSkuList().get(0).getSku().getPicUrl())) {
            Map<String, String> mapping = uploadExchangeService.convertUrlList(Collections.singletonList(productDetail.getSkuList().get(0).getSku().getPicUrl()));
            url = mapping.get(productDetail.getSkuList().get(0).getSku().getPicUrl());
        }
        AuditAssembler.packageProductInfo(result, productDetail, url);
        return result;
    }

    /**
     * 驳回原因重排序
     */
    public void reasonResort(AuditRejectReasonSortCommand command) {
        auditDomainService.reasonResort(BeanUtil.copyToList(command.getReasonList(), AuditRejectReasonEntity.class));
    }

    /**
     * 删除驳回原因
     */
    public void reasonDelete(IdCommand command) {
        auditDomainService.reasonDelete(command.getId());
    }

    /**
     * 批量审核
     */
    public void batchAudit(BatchAuditCommand command) {
        if (AuditStatusEnum.REJECT.getCode().equals(command.getAuditStatus()) && StrUtil.isBlank(command.getRejectReason())) {
            throw new CtpCoreProductException(CtpCoreProductExceptionEnum.AUDIT_REASON_BLANK);
        }
        List<AuditLogEntity> queryList = auditDomainService.queryByIdList(command.getIdList());
        Map<Long, AuditLogEntity> mapping = queryList.stream().collect(Collectors.toMap(AuditLogEntity::getId, e -> e));
        List<Long> idList = command.getIdList().stream().filter(e -> {
            AuditLogEntity query = mapping.get(e);
            if (query == null) {
                return false;
            }
            if (AuditStatusEnum.PASS.getCode().equals(query.getAuditStatus()) || AuditStatusEnum.REJECT.getCode().equals(query.getAuditStatus())) {
                return false;
            }
            if (AuditStatusEnum.CANCELED.getCode().equals(query.getAuditStatus())) {
                return false;
            }
            return !AuditStatusEnum.CANCELED.getCode().equals(command.getAuditStatus()) || AuditStatusEnum.WAIT.getCode().equals(query.getAuditStatus());
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        command.setIdList(idList);
        List<AuditLogEntity> list = AuditAssembler.createBatchAudit(command);
        productOperationDomainService.restoreDataAfterAuditApprove2(list,false);
    }

    /**
     * 查询spu审核列表
     */
    public ResultMode<List<AuditLogSpuListDTO>> querySpuList(PagingInfo<AuditLogQuery> pageQuery) {
        Page<AuditLogSpuListDTO> page = PageHelper.startPage(pageQuery.currentPage, pageQuery.pageLength, pageQuery.getCountTotal());
        AuditLogQuery query = pageQuery.getFilterModel();
        if (StrUtil.isBlank(query.getSpuCode())) {
            return ResultMode.fail(CtpCoreProductExceptionEnum.PRODUCT_NO_NOT_EMPTY.getMsg());
        }
        List<AuditLogEntity> list = auditDomainService.pageSpuCondition(BeanUtil.toBean(query, AuditLogCondition.class));
        return ResultMode.successPageList(BeanUtil.copyToList(list, AuditLogSpuListDTO.class), (int) page.getTotal());
    }
}
