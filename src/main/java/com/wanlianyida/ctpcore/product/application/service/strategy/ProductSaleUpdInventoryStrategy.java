package com.wanlianyida.ctpcore.product.application.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.product.application.service.ProductInventoryAppService;
import com.wanlianyida.ctpcore.product.domain.model.bo.inventory.ProductSaleUpdBO;
import com.wanlianyida.ctpcore.product.infrastructure.enums.InventoryHandlerTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.enums.MessageRetryTypeEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.event.LocalEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 商品上架本地事件
 */
@Slf4j
@Service
public class ProductSaleUpdInventoryStrategy implements MessageRetryStrategy<LocalEventMessage.EventMessage> {

    @Resource
    private ProductInventoryAppService productInventoryAppService;

    @Override
    public boolean handlerType(String type) {
        return StrUtil.equals(type, MessageRetryTypeEnum.PRODUCT_SALE.getTopic());
    }

    @Override
    public ResultMode messageRetry(LocalEventMessage.EventMessage eventMessage) {

        return productInventoryAppService.handleInventory(InventoryHandlerTypeEnum.PRODUCT_SALE.getCode(),
                BeanUtil.toBean(eventMessage.getData(), ProductSaleUpdBO.class));
    }

}
