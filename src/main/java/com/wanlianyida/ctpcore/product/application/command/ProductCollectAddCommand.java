package com.wanlianyida.ctpcore.product.application.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description 批量审核
 * @Date 2024年11月25日 17:01
 */
@Data
public class ProductCollectAddCommand extends BaseUserInfoCommand{

    /**
     *  用户id
     */
    @NotNull(message = "用户id不能为空")
    private String userBaseId;

    /**
     *  商品id
     */
    @NotNull(message = "商品id不能为空")
    private String productId;

    /**
     *  店铺id
     */
    @NotNull(message = "店铺id不能为空")
    private String shopId;

}
