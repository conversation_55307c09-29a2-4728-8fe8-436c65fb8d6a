package com.wanlianyida.ctpcore.product.application.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.ctpcore.product.application.service.ProductInventoryAppService;
import com.wanlianyida.ctpcore.product.infrastructure.enums.MessageRetryTypeEnum;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.event.KafkaEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ctp_order_status_upd_to_product_inventory
 */
@Slf4j
@Service
public class OrderStatusUpdInventoryStrategy implements MessageRetryStrategy<KafkaEventMessage.EventMessage> {

    @Resource
    private ProductInventoryAppService productInventoryAppService;

    @Override
    public boolean handlerType(String type) {
        return StrUtil.equals(type, MessageRetryTypeEnum.ORDER_STATUS_UPD_INVENTORY.getTopic());
    }

    @Override
    public ResultMode messageRetry(KafkaEventMessage.EventMessage kafkaEventMessage) {
        return productInventoryAppService.handleOrderStatusUpd(kafkaEventMessage);
    }

}
