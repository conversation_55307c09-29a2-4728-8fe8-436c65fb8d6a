package com.wanlianyida.ctpcore.product.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月26日 10:12
 */
@Data
public class ProductSkuListDTO {
    /**
     * sku id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 企业id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * SKU编号
     */
    private String skuCode;

    /**
     * spuCode
     */
    private String spuCode;

    /**
     * SKU规格名称(规格属性值value拼接)
     */
    private String skuSpecificationName;

    /**
     * SKU名称（品牌+三级品类+sku规格名称）
     */
    private String skuName;

    /**
     * 简单说明
     */
    private String skuDesc;

    /**
     * 上架状态（10未上架 20已上架 30违规下架）
     */
    private Integer onSaleStatus;

    /**
     * 上架时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onSaleDate;

    /**
     * 下架时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outSaleDate;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 单价
     */
    private BigDecimal priceFee;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 最小起订量
     */
    private BigDecimal minQuantity;

    /**
     * 单位转换 1吨=x吨度(计价单位转换为计量单位)
     */
    private BigDecimal unitTransfer;

    /**
     * 是否现货:1是 0否;
     */
    private Integer isSpotGoods;

    /**
     * 交货期限（天）
     */
    private String deliveryPeriod;

    /**
     * 计重方式（10理重 20过磅）
     */
    private String weightMeasurementType;

    /*
     * 品牌ID
     */
    private Long relBrandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 1级品类名称
     */
    private String categoryName1;

    /**
     * 2级品类名称
     */
    private String categoryName2;

    /**
     * 3级品类名称
     */
    private String categoryName3;

    /**
     * 品类名多级拼接
     */
    private String categoryFullName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 更新人名字
     */
    private String updaterName;

    /**
     * 计价单位ID
     */
    private Integer relPricingUnitId;

    /**
     * 计量单位ID
     */
    private Integer relMeasurementUnitId;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 下架原因
     */
    private String outSaleReason;

    /**
     * 分类列表
     */
    private List<String> customCategoryList;

    /**
     * 店铺分类数量
     */
    private Integer customCategoryQuantity;

    /**
     * 分类列表
     */
    private List<String> customCategories;

    /**
     * 店铺分类标签
     */
    private String customCategoryLabel;

    /**
     * 下架类型： 10正常下架 20违规下架 30 售罄下架
     */
    private String outSaleType;
}
