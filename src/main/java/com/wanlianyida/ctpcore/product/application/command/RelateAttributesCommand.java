package com.wanlianyida.ctpcore.product.application.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 关联规格参数
 * <AUTHOR>
 */
@Data
public class RelateAttributesCommand {
    /**
     * 关联的品类id
     */
    @NotNull(message = "关联的品类id不能为空")
    private Long relCategoryId;

    /**
     * 关联的规格id
     */
    @NotNull(message = "关联的规格id不能为空")
    private Long relAttributeId;

    /**
     * 关联的规格名称
     */
    @NotBlank(message = "关联的规格名称不能为空")
    private String attributeName;

    /**
     * 是否是销售规格(0不是 1是)
     */
    @NotNull(message = "是否是销售规格不能为空")
    private Integer typeFlag;

    /**
     * 是否必填(0非必填 1必填)
     */
    @NotNull(message = "是否必填不能为空")
    private Integer inputFlag;

    /**
     * 启用状态(1启用 0停用)
     */
    @NotNull(message = "启用状态不能为空")
    private Integer enableStatus;

    /**
     * 排序序号
     */
    @NotNull(message = "排序序号不能为空")
    private Integer sortNumber;

    /**
     * 输入方式(10选项 20文本)
     */
    @NotBlank(message = "输入方式不能为空")
    private String inputType;

    /**
     * 规格值列表
     */
    private List<CategoryAttributeValue> attributeValueList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CategoryAttributeValue {
        /**
         * 排序
         */
        Integer sortNumber;

        /**
         * 规格值
         */
        String attributeValue;
    }
}
