package com.wanlianyida.ctpcore.product.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商品审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
public class ProductCollectDTO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 商品Id
     */
    private String productId;

    /**
     * 收藏时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectDate;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 上架状态
     */
    private Integer onSaleStatus;

    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 价格
     */
    private BigDecimal priceFee;

    /**
     * 分类图片
     */
    private String categoryPic;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 关联的计费单位id
     */
    private Integer relPricingUnitId;

    /**
     * 定向发布公司id
     */
    private List<String> publishCompanyId;

    /**
     * 发布类型
     */
    private String publishType;
}
