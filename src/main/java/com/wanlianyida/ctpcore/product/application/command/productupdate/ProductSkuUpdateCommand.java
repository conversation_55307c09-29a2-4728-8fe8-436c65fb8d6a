package com.wanlianyida.ctpcore.product.application.command.productupdate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductSkuUpdateCommand {

    @ApiModelProperty("id（新增项为空；更新项必传）")
    private Long id;

    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("批号")
    private String batchNo;

    @ApiModelProperty("数量")
    @NotNull(message = "数量 不能为空")
    private BigDecimal quantity;

    @ApiModelProperty("最小起订量")
    @NotNull(message = "最小起订量 不能为空")
    private BigDecimal minQuantity;

    @ApiModelProperty("单价")
    @NotNull(message = "单价 不能为空")
    private BigDecimal priceFee;

    @ApiModelProperty("单位转换")
    @NotNull(message = "单位转换 不能为空")
    private BigDecimal unitTransfer;

    @ApiModelProperty("是否现货:1是 0否;")
    @NotNull(message = "是否现货 不能为空")
    private Integer isSpotGoods;

    @ApiModelProperty("交货期限")
    @Length(max = 50, min = 1, message = "交货期限 长度为1-50")
    private String deliveryPeriod;

    @ApiModelProperty("计重方式（'10'理重 '20'过磅）")
    private String weightMeasurementType;

    @ApiModelProperty("商品简要说明")
    @Length(max = 100, message = "商品简要说明 长度为0-100")
    private String skuDesc;
}
