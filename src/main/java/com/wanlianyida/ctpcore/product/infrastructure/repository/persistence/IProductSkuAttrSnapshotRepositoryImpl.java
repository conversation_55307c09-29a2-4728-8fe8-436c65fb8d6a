package com.wanlianyida.ctpcore.product.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductSkuAttrEntity;
import com.wanlianyida.ctpcore.product.domain.repository.IProductSkuAttrSnapshotRepository;
import com.wanlianyida.ctpcore.product.infrastructure.repository.mapper.ProductSkuAttrSnapshotMapper;
import com.wanlianyida.ctpcore.product.infrastructure.repository.po.ProductSkuAttrSnapshotPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class IProductSkuAttrSnapshotRepositoryImpl implements IProductSkuAttrSnapshotRepository {

    @Resource
    ProductSkuAttrSnapshotMapper skuAttrSnapshotMapper;

    @Override
    public List<ProductSkuAttrEntity> getSnapshotByCode(Long auditId, String skuCode) {
        LambdaQueryWrapper<ProductSkuAttrSnapshotPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductSkuAttrSnapshotPO::getRelAuditId, auditId)
                .eq(ProductSkuAttrSnapshotPO::getSkuCode, skuCode);
        List<ProductSkuAttrSnapshotPO> list1 = skuAttrSnapshotMapper.selectList(wrapper);
        return  BeanUtil.copyToList(list1, ProductSkuAttrEntity.class);
    }

}
