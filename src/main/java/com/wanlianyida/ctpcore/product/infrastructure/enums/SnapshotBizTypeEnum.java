package com.wanlianyida.ctpcore.product.infrastructure.enums;

import lombok.Getter;

@Getter
public enum SnapshotBizTypeEnum {
    PUBLISH("10", "发布商品"),
    UPDATE("20", "更新商品"),
    PRICE_CHANGE("30", "价格变更"),
    RESTORE("40", "审核通过"),
    SELL("50", "转卖商品"),
    INVENTORY_CHANGE("60","库存变更")
    ;

    private final String type;

    private final String desc;

    SnapshotBizTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
