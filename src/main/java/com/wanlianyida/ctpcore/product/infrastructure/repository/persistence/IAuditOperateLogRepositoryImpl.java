package com.wanlianyida.ctpcore.product.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wanlianyida.ctpcore.product.domain.model.entity.AuditLogEntity;
import com.wanlianyida.ctpcore.product.domain.model.entity.AuditOperateLogEntity;
import com.wanlianyida.ctpcore.product.domain.repository.IAuditOperateLogRepository;
import com.wanlianyida.ctpcore.product.infrastructure.enums.AuditOperateTypeEnum;
import com.wanlianyida.ctpcore.product.infrastructure.repository.converter.AuditOperateLogConvert;
import com.wanlianyida.ctpcore.product.infrastructure.repository.mapper.AuditOperateLogMapper;
import com.wanlianyida.ctpcore.product.infrastructure.repository.po.AuditOperateLogPO;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月23日 18:09
 */
@Component
public class IAuditOperateLogRepositoryImpl implements IAuditOperateLogRepository {

    @Resource
    private AuditOperateLogMapper auditOperateLogMapper;

    /**
     * 保存操作日志
     */
    @Override
    public boolean createAuditOperateLog(AuditLogEntity auditLog, AuditOperateTypeEnum operateTypeEnum) {
        return auditOperateLogMapper.insert(AuditOperateLogConvert.createOperateLog(auditLog, operateTypeEnum)) > 0;
    }

    /**
     * 查询操作日志
     */
    @Override
    public List<AuditOperateLogEntity> queryAuditOperateLog(Long auditId) {
        QueryWrapper<AuditOperateLogPO> wrapper = new QueryWrapper<>();
        wrapper.eq("rel_audit_id", auditId);
        return BeanUtil.copyToList(auditOperateLogMapper.selectList(wrapper), AuditOperateLogEntity.class);
    }

    @Override
    public void batchInsertLog(List<AuditLogEntity> list) {
        List<AuditOperateLogPO> save = AuditOperateLogConvert.batchInsert(list);
        auditOperateLogMapper.saveBatch(save);
    }
}
