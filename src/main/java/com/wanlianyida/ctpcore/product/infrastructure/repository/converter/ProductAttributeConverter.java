package com.wanlianyida.ctpcore.product.infrastructure.repository.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductAttributeBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductAttributeValueInfoBO;
import com.wanlianyida.ctpcore.product.infrastructure.repository.po.ProductAttributePO;
import com.wanlianyida.ctpcore.product.infrastructure.repository.po.ProductAttributeValuePO;

import java.util.LinkedList;
import java.util.List;

/**
 * 商品规格转换
 * <AUTHOR>
 */
public class ProductAttributeConverter {

    /**
     * 转换新增商品规格
     */
    public static ProductAttributePO productAttributeAddConverter(ProductAttributeBO productAttributeBO){
        return productAttributeConverter(productAttributeBO);
    }

    /**
     * 转换编辑商品规格
     */
    public static ProductAttributePO productAttributeUpdateConverter(ProductAttributeBO productAttributeBO){
        ProductAttributePO productAttributePo = productAttributeConverter(productAttributeBO);
        productAttributePo.setId(productAttributeBO.getId());
        return productAttributePo;
    }

    private static ProductAttributePO productAttributeConverter(ProductAttributeBO productAttributeBO){
        ProductAttributePO productAttributePo = new ProductAttributePO();
        // 规格名称
        productAttributePo.setAttributeName(productAttributeBO.getAttributeName());
        // 规格值，用逗号间隔
        productAttributePo.setAttributeValues(productAttributeBO.getAttributeValues());
        // 启用状态(1启用 0停用)
        productAttributePo.setEnableStatus(productAttributeBO.getEnableStatus());
        if(CollectionUtil.isEmpty(productAttributeBO.getValueInfoBOList())){
            // 规格值
            productAttributePo.setAttributeValues("");
        }

        productAttributePo.setCreatorId(productAttributeBO.getCreatorId());
        productAttributePo.setCreatorName(productAttributeBO.getCreatorName());
        productAttributePo.setUpdaterId(productAttributeBO.getUpdaterId());
        productAttributePo.setUpdaterName(productAttributeBO.getUpdaterName());

        productAttributePo.setNameDesc(productAttributeBO.getNameDesc());
        return productAttributePo;
    }

    /**
     * 转换商品规格值
     */
    public static List<ProductAttributeValuePO> productAttributeValueConverter(ProductAttributeBO productAttributeBO, Long attributeId){
        List<ProductAttributeValueInfoBO> valueInfoBOList = productAttributeBO.getValueInfoBOList();
        List<ProductAttributeValuePO> poList = new LinkedList<>();
        if(CollectionUtil.isEmpty(valueInfoBOList)){
            return poList;
        }
        for(ProductAttributeValueInfoBO valueInfoBO : valueInfoBOList){
            ProductAttributeValuePO po = new ProductAttributeValuePO();
            // 规格主键id
            po.setRelAttributeId(attributeId);
            // 规格值
            po.setAttributeValue(valueInfoBO.getAttributeValue());

            po.setCreatorId(valueInfoBO.getCreatorId());
            po.setCreatorName(valueInfoBO.getCreatorName());
            po.setUpdaterId(valueInfoBO.getUpdaterId());
            po.setUpdaterName(valueInfoBO.getUpdaterName());

            poList.add(po);
        }
        return poList;
    }

}
