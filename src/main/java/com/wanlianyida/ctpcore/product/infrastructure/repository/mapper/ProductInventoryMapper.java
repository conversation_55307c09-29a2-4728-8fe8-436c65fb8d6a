package com.wanlianyida.ctpcore.product.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.ctpcore.product.domain.model.bo.inventory.OrderProductInventoryBO;
import com.wanlianyida.ctpcore.product.infrastructure.repository.po.ProductInventoryPO;

/**
 * <p>
 * 商品库存表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
public interface ProductInventoryMapper extends BaseMapper<ProductInventoryPO> {

    /**
     * 更新库存数量
     */
    int updateInventoryQuantity(OrderProductInventoryBO bo);

    /**
     * 更新预扣数量
     */
    int updateWithholdingQuantity(OrderProductInventoryBO bo);

}
