package com.wanlianyida.ctpcore.product.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductViewLogBO;
import com.wanlianyida.ctpcore.product.domain.model.bo.ProductViewTimeDayBO;
import com.wanlianyida.ctpcore.product.domain.model.condition.ProductViewLogListCondition;
import com.wanlianyida.ctpcore.product.domain.model.entity.ProductViewLogEntity;
import com.wanlianyida.ctpcore.product.domain.repository.IProductViewLogRepository;
import com.wanlianyida.ctpcore.product.infrastructure.repository.mapper.ProductViewLogMapper;
import com.wanlianyida.ctpcore.product.infrastructure.repository.po.ProductViewLogPO;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <p>
 * 用户浏览商品记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Service
public class ProductViewLogServiceImpl implements IProductViewLogRepository {

    @Resource
    private ProductViewLogMapper pcProductViewLogMapper;

    @Override
    public Boolean addProductViewLog(ProductViewLogEntity productViewLogEntity) {
        ProductViewLogPO productViewLog = BeanUtil.copyProperties(productViewLogEntity, ProductViewLogPO.class);
        return pcProductViewLogMapper.insert(productViewLog) > 0;
    }

    @Override
    public List<ProductViewLogBO> queryViewLogListPage(ProductViewLogListCondition condition) {
        return pcProductViewLogMapper.getProductCollectListByUserId(condition);
    }

    @Override
    public int selectCount(ProductViewLogListCondition condition) {
        return pcProductViewLogMapper.selectCount(condition);
    }

    @Override
    public List<ProductViewTimeDayBO> selectCntByViewTimeDay(List<String> list, String userBaseId) {
        return pcProductViewLogMapper.selectCntByViewTimeDay(list, userBaseId);
    }

    @Override
    public int insertOnUpdate(ProductViewLogEntity productViewLogEntity) {
        return pcProductViewLogMapper.insertOnUpdate(BeanUtil.copyProperties(productViewLogEntity, ProductViewLogPO.class));
    }


}
