package com.wanlianyida.ctpcore.product.infrastructure.util;

import com.wanlianyida.ctpcore.product.infrastructure.constant.FieldConstant;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.enums.NumberBizTypeEnum;
import com.wanlianyida.framework.ctpcore.utils.IdUtil;
import com.wanlianyida.framework.ctpcore.utils.NumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ProductIdGenerator {

    @Resource
    private RedisService redisService;

    @Resource
    private NumberGenerator numberGenerator;

    @Resource
    private IdUtil idUtil;

    private static final String CTP_SPU_CODE = "ctp_spu_code";
    private static final String CTP_SKU_CODE = "ctp_sku_code";
    private static final String SERVICE_NAME = "ctp_core_product";


    public String generateAuditCode() {
        return numberGenerator.getBizId(NumberBizTypeEnum.AUDI.getCode());
    }

    /**
     *  SPU（10位）：P + 9位自增码（起始值100000000）
     *  P1000000001
     * @return
     */
    public String generateSPUNumber() {
        String key =  "ng:code:"+SERVICE_NAME+":"+CTP_SPU_CODE;
        if(!redisService.hasKey(key)){
            redisService.increment(key,100000000);
        }
        String sequence =  numberGenerator.generateBusNo(SERVICE_NAME, CTP_SPU_CODE,  9);
        return String.format("%s%s",  NumberBizTypeEnum.SPU.getCode(), sequence);
    }

    /**
     * SKU（16位）：SPU + 6位自增码（起始值100000）
     * P100000001100001
     * @param spuCode
     * @return
     */
    public String generateProductId(String spuCode) {
        String key = "ng:code:"+SERVICE_NAME+":"+CTP_SKU_CODE+":"+spuCode;
        if(!redisService.hasKey(key)){
            redisService.increment(key,100000);
        }
        return numberGenerator.generateFixedCodeNumber(SERVICE_NAME, CTP_SKU_CODE, spuCode, 6);
    }

    /**
     * 雪花ID
     * @return 雪花ID
     */
    public Long generateSnowId() {
        return idUtil.generateId(FieldConstant.SERVER_NAME);
    }

}
