package com.wanlianyida.fssbase.content.domain.model.condition;

import lombok.Data;

import java.util.List;

/**
 * 广告位查询
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
public class ChAdSpaceCondition {
    /**
     * 主键
     */
    private Long id;

    /**
     * 主键集合
     */
    private List<Long> ids;

    /**
     * 调用标识
     */
    private String callIndex;


    /**
     * 名称
     */
    private String name;


    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 站点ID
     */
    private Long relSiteId;


    /**
     * 平台编号 20商贸门户
     */
    private String platformCode;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer deleted;
}
