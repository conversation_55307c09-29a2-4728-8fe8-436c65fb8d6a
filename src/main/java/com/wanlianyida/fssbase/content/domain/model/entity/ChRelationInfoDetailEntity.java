package com.wanlianyida.fssbase.content.domain.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/12/07/17:24
 */
@Data
public class ChRelationInfoDetailEntity {

    /**
     * 主键ID
     */
    private Long infoDetailId;

    /**
     * 内容ID
     */
    @TableField("rel_info_id")
    private Long relInfoId;

    /**
     * 内容详情
     */
    @TableField("content_text")
    private String contentText;


    /**
     * 启用状态（0停用 1启用）
     */
    @TableField("enable_status")
    private Short enableStatus;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField("deleted")
    private Short deleted;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建人名字
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新人名字
     */
    @TableField("updater_name")
    private String updaterName;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 内容摘要
     */
    @TableField("content_summary")
    private String contentSummary;
}
