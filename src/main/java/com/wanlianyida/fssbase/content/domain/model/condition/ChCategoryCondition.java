package com.wanlianyida.fssbase.content.domain.model.condition;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/22/11:44
 */
@Data
public class ChCategoryCondition {

    /**
     * 站点id
     */
    private Long relSitId;

    /**
     * 父分类id(一级分类默认0)
     */
    private Long parentId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 业务大类 10资讯分类 20帮助 30平台规则40公告
     */
    private String bussCategory;

    /**
     * 调用别名
     */
    private String callAlias;

    /**
     * 启用状态（0停用 1启用）
     */
    private Integer enableStatus;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;


    /**
     * 平台编号 100商贸门户 200物流业务官网 210物流公司官网
     */
    private String platformCode;
}
