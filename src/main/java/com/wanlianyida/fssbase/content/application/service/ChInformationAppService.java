package com.wanlianyida.fssbase.content.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.content.application.assembler.ChInformationAssemble;
import com.wanlianyida.fssbase.content.domain.model.bo.ChInformationBo;
import com.wanlianyida.fssbase.content.domain.model.bo.ChInformationDetailBO;
import com.wanlianyida.fssbase.content.domain.model.condition.ChGroupParamCondition;
import com.wanlianyida.fssbase.content.domain.model.condition.ChInformationCondition;
import com.wanlianyida.fssbase.content.domain.model.condition.ChInformationDetailCondition;
import com.wanlianyida.fssbase.content.domain.model.entity.ChCategoryCenterEntity;
import com.wanlianyida.fssbase.content.domain.model.entity.ChInformationEntity;
import com.wanlianyida.fssbase.content.domain.repository.IChInformationRepository;
import com.wanlianyida.fssbase.content.domain.service.ChCategoryCenterDomainService;
import com.wanlianyida.fssbase.content.domain.service.ChInformationDomainService;
import com.wanlianyida.fssbase.content.infrastructure.constant.ContentConstants;
import com.wanlianyida.fssbase.content.infrastructure.enums.AuditStatusFlagEnum;
import com.wanlianyida.fssbase.content.infrastructure.exchange.EsContentExchangeService;
import com.wanlianyida.fssbase.content.interfaces.model.command.*;
import com.wanlianyida.fssbase.content.interfaces.model.dto.*;
import com.wanlianyida.fssbase.content.interfaces.model.query.ChInformationListQuery;
import com.wanlianyida.fssbase.content.interfaces.model.query.NoticePopListQuery;
import com.wanlianyida.search.command.CtpContentCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @since 2024/11/21/19:20
 */
@Slf4j
@Service
public class ChInformationAppService {


    @Resource
    private IChInformationRepository chInformationRepository;

    @Resource
    private ChInformationDomainService informationDomainService;

    @Resource
    private ChCategoryCenterDomainService chCategoryCenterDomainService;

    @Resource
    private EsContentExchangeService esContentExchangeService;

    @Resource
    private RedisService redisService;

    /**
     * 列表缓存
     * @param query
     * @return
     */
    @Cacheable(cacheNames = ContentConstants.INFO_CACHE, keyGenerator = ContentConstants.KEY_GENERATOR,
            cacheManager = ContentConstants.CACHE_MANAGER, sync = true)
    public ResultMode<List<ChInformationListUserDTO>> listCache(PagingInfo<ChInformationListQuery> query) {
        ResultMode<List<ChInformationListDTO>> listResultMode = this.listNoCache(query);
        List<ChInformationListUserDTO> chInformationListUserDTOList = BeanUtil.copyToList(listResultMode.getModel(), ChInformationListUserDTO.class);
        return ResultMode.successPageList(chInformationListUserDTOList, listResultMode.getTotal());
    }

    public ResultMode<List<ChInformationListDTO>> listNoCache(PagingInfo<ChInformationListQuery> query) {
        ChInformationListQuery filterModel = query.getFilterModel();
        ChInformationCondition condition = BeanUtil.copyProperties(filterModel, ChInformationCondition.class);
        List<Long> catogoryIdList = new ArrayList<>();
        if (condition.getRelCategoryId() != null) {
            catogoryIdList = chCategoryCenterDomainService.selectChildrenDeptById(condition.getRelCategoryId());
        }
        //分页拦截
        Page<ChInformationListDTO> page = PageHelper.startPage(query.currentPage, query.pageLength, query.getCountTotal());
        List<ChInformationEntity> list = informationDomainService.queryCondition(condition, catogoryIdList);
        List<ChInformationListDTO> result = BeanUtil.copyToList(list, ChInformationListDTO.class);
        return ResultMode.successPageList(result, (int) page.getTotal());
    }
    public ResultMode<List<ChInformationListDTO>> selectUnionData(PagingInfo<ChInformationListQuery> query) {
        ChInformationListQuery filterModel = query.getFilterModel();
        ChInformationCondition condition = BeanUtil.copyProperties(filterModel, ChInformationCondition.class);

        // 获取relCategoryIds，第一个类别为大宗类别
        List<Long> relCategoryIds = condition.getRelCategoryIds();
        if (CollectionUtil.isEmpty(relCategoryIds)) {
            return ResultMode.successPageList(Collections.emptyList(), 0);
        }

        Map<Long, List<Long>> fatherSonList = chCategoryCenterDomainService.selectChildrenDeptUndeletedByIdBatch(relCategoryIds);
        if (CollectionUtil.isEmpty(fatherSonList)) {
            return ResultMode.successPageList(Collections.emptyList(), 0);
        }
        // 分页查询
        Page<ChInformationListDTO> page = PageHelper.startPage(query.currentPage, query.pageLength, query.getCountTotal());

        // 组装查询条件
        Long firstCategoryId = CollectionUtil.getFirst(relCategoryIds);
        List<ChGroupParamCondition> chGroupParamConditions = buildChGroupParamConditions(fatherSonList, firstCategoryId);

        // 查询数据
        List<ChInformationEntity> chInformationEntities = chInformationRepository.selectUnionData(chGroupParamConditions);
        return ResultMode.successPageList(
                BeanUtil.copyToList(chInformationEntities, ChInformationListDTO.class),
                (int) page.getTotal()
        );
    }

    /**
     * 构建ChGroupParamCondition列表
     *
     * @param fatherSonList    父子类别映射
     * @param firstCategoryId  第一个类别ID
     * @return ChGroupParamCondition列表
     */
    private List<ChGroupParamCondition> buildChGroupParamConditions(Map<Long, List<Long>> fatherSonList, Long firstCategoryId) {
        List<ChGroupParamCondition> conditions = new ArrayList<>();

        List<Long> firstCategoryValues = fatherSonList.get(firstCategoryId);
        if (CollectionUtil.isNotEmpty(firstCategoryValues)) {
            conditions.add(createChGroupParamCondition(firstCategoryValues, 1, 2));
        }

        List<Long> remainingValues = fatherSonList.entrySet().stream()
                .filter(entry -> !entry.getKey().equals(firstCategoryId))
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(remainingValues)) {
            conditions.add(createChGroupParamCondition(remainingValues, 2, null));
        }

        return conditions;
    }



    private ChGroupParamCondition createChGroupParamCondition(List<Long> categoryIds, int index,Integer limit) {
        ChGroupParamCondition condition = new ChGroupParamCondition();
        condition.setCategoryIds(categoryIds);
        condition.setIndex(index);
        condition.setLimit(limit);
        return condition;
    }


    @Cacheable(cacheNames = ContentConstants.INFO_CACHE, keyGenerator = ContentConstants.KEY_GENERATOR,
            cacheManager = ContentConstants.CACHE_MANAGER, sync = true)
    public ResultMode<ChInformationDetailDTO> detailById(String infoId) {
        ChInformationDetailBO chInformationBo = informationDomainService.detailById(ChInformationAssemble.buildDetailCondition(infoId, null));
        ChInformationDetailDTO chInformationDetailDTO = BeanUtil.copyProperties(chInformationBo, ChInformationDetailDTO.class);
        return ResultMode.success(chInformationDetailDTO);
    }

    @Cacheable(cacheNames = ContentConstants.INFO_CACHE, keyGenerator = ContentConstants.KEY_GENERATOR,
            cacheManager = ContentConstants.CACHE_MANAGER, sync = true)
    public ResultMode<ChInformationDetailUserDTO> detailByIdUser(ChInformationDetailCondition condition) {
        ChInformationDetailBO chInformationDetailBO = informationDomainService.detailById(condition);
        ChInformationDetailUserDTO chInformationDetailDTO = BeanUtil.copyProperties(chInformationDetailBO, ChInformationDetailUserDTO.class);
        chInformationDetailDTO.setCategoryId(String.valueOf(chInformationDetailBO.getRelCategoryId()));
        return ResultMode.success(chInformationDetailDTO);
    }


    @Async("myAsyncThreadPool")
    public Boolean updatePv(Long infoId) {
        return informationDomainService.updateByInfoId(infoId);
    }


    @CacheEvict(cacheNames = ContentConstants.INFO_CACHE, cacheManager = ContentConstants.CACHE_MANAGER, allEntries = true)
    public String add(ChinformationAddCommand addCommand) {
        //写入mysql
        String infoId = informationDomainService.add(BeanUtil.copyProperties(addCommand, ChInformationBo.class));
        if (StringUtils.isNotBlank(infoId)) {
            //写入es,保证数据库成功
            CtpContentCommand ctpContentCommand = ChInformationAssemble.setInfoEs(infoId,
                    addCommand.getContentText(), addCommand.getContentSummary());
            ResultMode resultMode = esContentExchangeService.saveContent(ctpContentCommand);
           if (!resultMode.isSucceed()) {
               log.error("写入es文本失败msg:" + resultMode);
           }
        }
        return infoId;
    }

    @CacheEvict(cacheNames = ContentConstants.INFO_CACHE, cacheManager = ContentConstants.CACHE_MANAGER, allEntries = true)
    public Boolean update(ChinformationUpdateCommand updateCommand) {
        ChInformationBo chInformationEntity = BeanUtil.copyProperties(updateCommand, ChInformationBo.class);
        if (informationDomainService.update(chInformationEntity)) {
            CtpContentCommand ctpContentCommand = ChInformationAssemble.setInfoEs(updateCommand.getInfoId(),
                    updateCommand.getContentText(), updateCommand.getContentSummary());
            ResultMode resultMode = esContentExchangeService.saveContent(ctpContentCommand);
            if (!resultMode.isSucceed()) {
                log.error("更新es文本失败msg:" + resultMode);
            }
        }
        return true;
    }


    @CacheEvict(cacheNames = ContentConstants.INFO_CACHE, cacheManager = ContentConstants.CACHE_MANAGER, allEntries = true)
    public Boolean batchDelete(ChInformationBatchDeleteCommand deleteCommand) {
        ChInformationEntity chInformationEntity = new ChInformationEntity();
        chInformationEntity.setInfoIds(deleteCommand.getInfoIds().stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));
        return informationDomainService.batchDelete(chInformationEntity);
    }

    @CacheEvict(cacheNames = ContentConstants.INFO_CACHE, cacheManager = ContentConstants.CACHE_MANAGER, allEntries = true)
    public Boolean batchAudit(ChInformationAuditDeleteCommand deleteCommand) {
        if (deleteCommand.getInfoIds().isEmpty()) {
            return false;
        }
        ChInformationEntity chInformationEntity = new ChInformationEntity();
        BeanUtil.copyProperties(deleteCommand, chInformationEntity);
        chInformationEntity.setInfoIds(deleteCommand.getInfoIds().stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));

        // 审核时间
        chInformationEntity.setAuditDate(new Date());
        return informationDomainService.batchAudit(chInformationEntity);
    }

    @Cacheable(cacheNames = ContentConstants.INFO_CACHE, keyGenerator = ContentConstants.KEY_GENERATOR,
            cacheManager = ContentConstants.CACHE_MANAGER, sync = true)
    public ResultMode<Map<String, List<ChInformationListDTO>>> homepage(Map<String, Map<String, String>> map) {
        List<ChCategoryCenterEntity> chCategoryCenterEntities = chCategoryCenterDomainService.queryListUser(map);
        Map<String, List<ChInformationListDTO>> ResMap = new HashMap<>();
        chCategoryCenterEntities.forEach(e -> {
            List<Long> catogoryIdList = new ArrayList<>();
            if ( e.getCategoryId() != null) {
                //子类 + 自己的id
                catogoryIdList.addAll(chCategoryCenterDomainService.selectChildrenDeptById( e.getCategoryId()));
            }
            //拼接查询内容条件
            ChInformationCondition condition = new ChInformationCondition();
            condition.setAuditStatus(AuditStatusFlagEnum.AUDIT_SUCCESS.getCode());
            condition.setBussCategory(e.getBussCategory());
            condition.setSource(1);
            //分页拦截
            String pageSize = map.get(e.getCallAlias() + "_" + e.getBussCategory()).get("pageSize");
            PageHelper.startPage(1, Integer.parseInt(pageSize), null);
            List<ChInformationEntity> list = informationDomainService.queryCondition(condition, catogoryIdList);
            List<ChInformationListDTO> result = BeanUtil.copyToList(list, ChInformationListDTO.class);
            ResMap.put(e.getCallAlias()+"_"+ e.getBussCategory(), result);
        });
        return ResultMode.success(ResMap);
    }

    /**
     * 查询公告弹窗列表
     * @param query
     * @return
     */
    public ResultMode<NoticePopDTO> queryNoticePopList(NoticePopListQuery query) {
        return informationDomainService.queryNoticePopList(query);
    }

    /**
     * 确认弹窗
     * @param command
     * @return
     */
    public ResultMode<Boolean> confirmNoticePop(NoticePopCommand command) {
        return informationDomainService.confirmNoticePop(command);
    }
}
