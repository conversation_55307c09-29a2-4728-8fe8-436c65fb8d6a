package com.wanlianyida.fssbase.content.application.assembler;

import com.wanlianyida.fssbase.content.domain.model.condition.ChInformationDetailCondition;
import com.wanlianyida.fssbase.content.domain.model.entity.ChCategoryCenterEntity;
import com.wanlianyida.search.command.CtpContentCommand;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/04/14:34
 */
public class ChInformationAssemble {

    public static List<Long> getCategoryList( List<ChCategoryCenterEntity> chCategoryCenterEntities) {

        return chCategoryCenterEntities.stream()
                .map(ChCategoryCenterEntity::getCategoryId)
                .collect(Collectors.toList());
    }

    public static CtpContentCommand setInfoEs(String infoId, String addCommand, String summary) {
        CtpContentCommand ctpContentCommand = new CtpContentCommand();
        ctpContentCommand.setId(infoId);
        ctpContentCommand.setContentText(addCommand);
        ctpContentCommand.setContentSummary(summary);
        ctpContentCommand.setContentType("ch_information_content");
        return ctpContentCommand;
    }

    public static ChInformationDetailCondition buildDetailCondition(String infoId, Integer auditStatus) {
        ChInformationDetailCondition chInformationDetailCondition = new ChInformationDetailCondition();
        chInformationDetailCondition.setInfoId(Long.valueOf(infoId));
        chInformationDetailCondition.setAuditStatus(auditStatus); // 审核状态
        return chInformationDetailCondition;
    }

    public static ChInformationDetailCondition buildConditionCallAlias(String callAlias, String bussCategory) {
        ChInformationDetailCondition chInformationDetailCondition = new ChInformationDetailCondition();
        chInformationDetailCondition.setCallAlias(callAlias);
        chInformationDetailCondition.setBussCategory(bussCategory);
        return chInformationDetailCondition;
    }
}
