package com.wanlianyida.fssbase.content.interfaces.model.query;

import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/11/22/13:42
 */
@Data
public class ChCategoryListQuery {

    /**
     * 站点ID
     */
    private Long relSiteId;


    /**
     * 平台编号 20商贸门户
     */
    private String platformCode;

    /**
     * 分类名称
     */
    private String categoryName;


    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 业务大类 10资讯分类 20帮助 30平台规则40公告
     */
    @NotBlank(message = "业务大类不允许为空，业务大类 10资讯分类 20帮助 30平台规则40公告")
    private String bussCategory;

    /**
     * 调用别名
     */
    private String callAlias;

    /**
     * '启用状态（0停用 1启用）
     */
    private Integer enableStatus;


}
