package com.wanlianyida.fssbase.content.interfaces.model.dto;

import lombok.Data;

/**
 * 站点资源DTO
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
public class ChSiteResourceDTO {
    /**
     * id
     */
    private Integer id;

    /**
     * 站点ID
     */
    private Long relSiteId;

    /**
     * 主模块编号
     */
    private String moduleCode;

    /**
     * 导航菜单名称
     */
    private String menuName;

    /**
     * 导航菜单编号
     */
    private String menuCode;

    /**
     * 映射字段中文名
     */
    private String mappingFieldName;

    /**
     * 映射字段英文名
     */
    private String mappingFieldCode;

    /**
     * 图片标题
     */
    private String imageTitle;

    /**
     * "配置类型 （10弹窗 20链接 30图片 40文本 50视频)
     */
    private Short configType;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 描述内容
     */
    private String description;

    /**
     * 状态(0停用 1启用)
     */
    private Short enableStatus;

    /**
     * 平台编号 20商贸门户
     */
    private String platformCode;
}
