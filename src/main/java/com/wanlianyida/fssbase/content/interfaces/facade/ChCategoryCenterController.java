package com.wanlianyida.fssbase.content.interfaces.facade;

import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.content.application.service.ChCategoryAppService;
import com.wanlianyida.fssbase.content.interfaces.model.command.ChCategoryAddCommand;
import com.wanlianyida.fssbase.content.interfaces.model.command.ChCategoryDeleteCommand;
import com.wanlianyida.fssbase.content.interfaces.model.command.ChCategoryUpdateCommand;
import com.wanlianyida.fssbase.content.interfaces.model.dto.ChCategoryListDTO;
import com.wanlianyida.fssbase.content.interfaces.model.query.ChCategoryListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 分类管理
 * <AUTHOR>
 * @since 2024/11/21/19:18
 */
@RestController
@RequestMapping("/category")
public class ChCategoryCenterController  {

    @Resource
    private ChCategoryAppService chCategoryAppService;

    /**
     * 分类列表
     *
     * @param query
     * @return
     */
    @PostMapping("/list")
    public ResultMode list(@Validated @RequestBody ChCategoryListQuery query) {
        List<ChCategoryListDTO> result = chCategoryAppService.list(query);
        return ResultMode.success(result);
    }


    /**
     *  新增
     *
     * @param command
     * @return
     */
    @PostMapping("/add")
    public ResultMode add(@Validated @RequestBody ChCategoryAddCommand command) {
        if (chCategoryAppService.add(command)) {
            return ResultMode.success();
        }
        return ResultMode.fail();
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/update")
    public ResultMode<Boolean> update(@Validated @RequestBody ChCategoryUpdateCommand updateCommand) {
        if (chCategoryAppService.update(updateCommand)) {
            return ResultMode.success();
        }
        return ResultMode.fail();
    }


    /**
     * 根据id删除
     * @param deleteCommand
     * @return
     */
    @PostMapping("/deleteById")
    public ResultMode deleteById(@RequestBody ChCategoryDeleteCommand deleteCommand) {
        if (chCategoryAppService.deleteById(deleteCommand)) {
            return ResultMode.success();
        }
        return ResultMode.fail();
    }


}
