package com.wanlianyida.fssbase.content.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/11/23/11:42
 */
@Data
public class ChInformationListDTO {

    /**
     * 内容ID
     */
    private String infoId;

    /**
     * 站点ID
     */
    private Long relSitId;

    /**
     * 类别ID
     */
    private Long relCategoryId;

    /**
     * 业务大类 10资讯分类 20帮助 30平台规则40公告
     */
    private String bussCategory;

    /**
     * 标签列表
     */
    private String tagsList;

    /**
     * 标题
     */
    private String articleTitle;

    /**
     * 文章来源
     */
    private String articleSource;

    /**
     * 文章作者
     */
    private String articleWriter;

    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date articleTime;

    /**
     * "评论开关 1 开 0关
     */
    private Short commentsAllow;

    /**
     * 显示顺序
     */
    private Integer sortNumber;

    /**
     * 审核状态（10待审核 20审核通过 30审核驳回）
     */
    private Short auditStatus;

    /**
     * 启用状态（0停用 1启用）
     */
    private Short enableStatus;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Short deleted;


    /**
     * 平台编号 100商贸门户 200物流业务官网 210物流公司官网
     */
    private String platformCode;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 调用别名
     */
    private String callAlias;

    /**
     * 内容摘要
     */
    private String contentSummary;

    /**
     * 内容封面图
     */
    private String imageCoverUrl;

    /**
     * 分类名称
     */
    private String categoryName;


    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditDate;


    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;



    /**
     * 审核意见
     */
    private String auditRemark;

    /**
     * 热门标识(0 否 1 是)
     */
    private Integer hotTag;
}
