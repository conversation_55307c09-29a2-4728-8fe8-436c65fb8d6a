package com.wanlianyida.fssbase.content.infrastructure.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName: ContentNacosConfig
 * @description: Content公共nacos配置类
 * @author: zhang<PERSON><PERSON>
 * @date: 2025年07月07日
 * @version: 1.0
 */
@Data
@Configuration
@RefreshScope
public class ContentNacosConfig {

    /**
     * 轮播图,配置校验站内跳转链接前缀
     */
    @Value("${banner.jumpUrlArray:}")
    private String jumpUrlArray;

}
