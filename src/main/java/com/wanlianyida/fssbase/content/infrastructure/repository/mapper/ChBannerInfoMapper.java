package com.wanlianyida.fssbase.content.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.fssbase.content.domain.model.condition.ChBannerInfoCondition;
import com.wanlianyida.fssbase.content.domain.model.entity.ChBannerInfoAllEntity;
import com.wanlianyida.fssbase.content.infrastructure.repository.po.ChBannerInfoPO;

import java.util.List;

/**
 * <p>
 * 轮播图表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-21
 */
public interface ChBannerInfoMapper extends BaseMapper<ChBannerInfoPO> {

    List<ChBannerInfoAllEntity> queryPage(ChBannerInfoCondition condition);
}
