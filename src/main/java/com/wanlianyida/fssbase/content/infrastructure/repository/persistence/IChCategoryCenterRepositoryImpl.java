package com.wanlianyida.fssbase.content.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wanlianyida.fssbase.content.domain.model.condition.ChAdSpaceCondition;
import com.wanlianyida.fssbase.content.domain.model.condition.ChCategoryCondition;
import com.wanlianyida.fssbase.content.domain.model.condition.ChInformationCondition;
import com.wanlianyida.fssbase.content.domain.model.entity.ChAdSpaceEntity;
import com.wanlianyida.fssbase.content.domain.model.entity.ChCategoryCenterEntity;
import com.wanlianyida.fssbase.content.domain.model.entity.ChInformationEntity;
import com.wanlianyida.fssbase.content.domain.repository.IChCategoryCenterRepository;
import com.wanlianyida.fssbase.content.infrastructure.constant.ContentConstants;
import com.wanlianyida.fssbase.content.infrastructure.enums.DeletedFlagEnum;
import com.wanlianyida.fssbase.content.infrastructure.enums.EnableStatusFlagEnum;
import com.wanlianyida.fssbase.content.infrastructure.exception.ContentException;
import com.wanlianyida.fssbase.content.infrastructure.exception.ContentExceptionEnum;
import com.wanlianyida.fssbase.content.infrastructure.repository.mapper.ChCategoryCenterMapper;
import com.wanlianyida.fssbase.content.infrastructure.repository.mapper.ChInformationMapper;
import com.wanlianyida.fssbase.content.infrastructure.repository.po.ChCategoryCenterPo;
import com.wanlianyida.fssbase.content.infrastructure.repository.po.ChInformationPO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/11/22/10:43
 */
@Service
public class IChCategoryCenterRepositoryImpl  implements IChCategoryCenterRepository {


    @Resource
    private ChCategoryCenterMapper chCategoryCenterMapper;

    @Resource
    private IChInformationRepositoryImpl chInformationRepository;

    @Resource
    private ChInformationMapper chInformationMapper;


    @Override
    public List<ChCategoryCenterEntity> queryList(ChCategoryCondition condition) {
        LambdaQueryWrapper<ChCategoryCenterPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ObjUtil.isNotNull(condition.getRelSitId()), ChCategoryCenterPo::getRelSitId, condition.getRelSitId());
        queryWrapper.eq(ObjUtil.isNotNull(condition.getParentId()), ChCategoryCenterPo::getParentId, condition.getParentId());
        queryWrapper.like(StrUtil.isNotBlank(condition.getCategoryName()), ChCategoryCenterPo::getCategoryName, condition.getCategoryName());
        queryWrapper.eq(ObjUtil.isNotNull(condition.getEnableStatus()), ChCategoryCenterPo::getEnableStatus, condition.getEnableStatus());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getBussCategory()), ChCategoryCenterPo::getBussCategory, condition.getBussCategory());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getCallAlias()), ChCategoryCenterPo::getCallAlias, condition.getCallAlias());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getPlatformCode()), ChCategoryCenterPo::getPlatformCode, condition.getPlatformCode());

        //删除状态为未删除
        queryWrapper.eq(ChCategoryCenterPo::getDeleted, DeletedFlagEnum.UN_DELETED.getCode());
        return BeanUtil.copyToList(chCategoryCenterMapper.selectList(queryWrapper), ChCategoryCenterEntity.class);
    }

    /**
     * 首页底部查询
     * @param map
     * @return
     */
    @Override
    public List<ChCategoryCenterEntity> queryListUser(Map<String, Map<String, String>> map) {
        LambdaQueryWrapper<ChCategoryCenterPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(ChCategoryCenterPo::getBussCategory, ChCategoryCenterPo::getCallAlias, ChCategoryCenterPo::getCategoryId);
        //or (A and B)
        for (String key : map.keySet()) {
            Map<String, String> condition = map.get(key);
            queryWrapper.or(i -> i.eq(ChCategoryCenterPo::getBussCategory, condition.get("bussCategory"))
                    .eq(ChCategoryCenterPo::getCallAlias, condition.get("callAlias"))
                    .eq(ChCategoryCenterPo::getEnableStatus, EnableStatusFlagEnum.ENABLE.getCode()));
        }
        List<ChCategoryCenterPo> chCategoryCenterPos = chCategoryCenterMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(chCategoryCenterPos, ChCategoryCenterEntity.class);
    }

    @Override
    public Boolean add(ChCategoryCenterEntity chCategoryCenterEntity) {
        ChCategoryCenterPo chCategoryCenterPo = BeanUtil.copyProperties(chCategoryCenterEntity, ChCategoryCenterPo.class);
        return chCategoryCenterMapper.insert(chCategoryCenterPo) > ContentConstants.NUM_0;
    }

    /**
     * 支持父级分类修改
     * 分类状态修改，需要同步修改文章状态
     * 父类状态修改，联动子类修改
     * @param chCategoryCenterEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(ChCategoryCenterEntity chCategoryCenterEntity) {
        ChCategoryCenterPo chCategoryCenterPo = BeanUtil.copyProperties(chCategoryCenterEntity, ChCategoryCenterPo.class);

        ChCategoryCenterPo newParentCategory = chCategoryCenterMapper.selectById(chCategoryCenterEntity.getParentId());
        ChCategoryCenterPo oldCategory = chCategoryCenterMapper.selectById(chCategoryCenterPo.getCategoryId());
        //启用状态修改，需要更新文章表状态
        if (!oldCategory.getEnableStatus().equals(chCategoryCenterEntity.getEnableStatus())) {
            //分类 + 子类
            List<Long> catogoryIdList = getIdList(chCategoryCenterEntity);
            catogoryIdList.add(chCategoryCenterEntity.getCategoryId());

            LambdaUpdateWrapper<ChInformationPO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.set(ChInformationPO::getEnableStatus, chCategoryCenterEntity.getEnableStatus())
                    .in(ChInformationPO::getRelCategoryId, catogoryIdList);
            chInformationMapper.update(null, lambdaUpdateWrapper);
        }

        //父级如果发生修改，更新子集信息，否则不处理
        if (!Objects.equals(oldCategory.getParentId(), chCategoryCenterEntity.getParentId()) && Objects.nonNull(newParentCategory)) {
            String newAncestors = newParentCategory.getAncestors() + "," + newParentCategory.getCategoryId();
            String oldAncestors = oldCategory.getAncestors();
            chCategoryCenterPo.setAncestors(newAncestors);
            updateDeptChildren(chCategoryCenterPo.getCategoryId(), newAncestors, oldAncestors);
        }
        //当前 + 子集分类状态
        if (!oldCategory.getEnableStatus().equals(chCategoryCenterEntity.getEnableStatus())) {
            List<Long> collect = chCategoryCenterMapper.selectChildrenDeptById(chCategoryCenterEntity.getCategoryId()).stream()
                    .map(ChCategoryCenterPo::getCategoryId).collect(Collectors.toList());

            if (!collect.isEmpty()) {
                LambdaUpdateWrapper<ChCategoryCenterPo> wrapperEnableStatus = new LambdaUpdateWrapper<>();
                wrapperEnableStatus.set(ChCategoryCenterPo::getEnableStatus, chCategoryCenterEntity.getEnableStatus())
                        .eq(ChCategoryCenterPo::getDeleted, DeletedFlagEnum.UN_DELETED.getCode())
                        .in(ChCategoryCenterPo::getCategoryId, collect);
                chCategoryCenterMapper.update(null, wrapperEnableStatus);
            }
        }

        //更新历史文章分类名称
        if (!oldCategory.getCategoryName().equals(chCategoryCenterEntity.getCategoryName())) {
            LambdaUpdateWrapper<ChInformationPO> poLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            poLambdaUpdateWrapper.set(ChInformationPO::getCategoryName, chCategoryCenterEntity.getCategoryName())
                    .eq(ChInformationPO::getRelCategoryId, chCategoryCenterPo.getCategoryId());
            chInformationMapper.update(null, poLambdaUpdateWrapper);
        }

        LambdaUpdateWrapper<ChCategoryCenterPo> wrapper = new LambdaUpdateWrapper<>();

        wrapper.eq(ChCategoryCenterPo::getDeleted, DeletedFlagEnum.UN_DELETED.getCode())
                .eq(ChCategoryCenterPo::getCategoryId, chCategoryCenterPo.getCategoryId());

        return chCategoryCenterMapper.update(chCategoryCenterPo, wrapper) > ContentConstants.NUM_0;
    }


    @Override
    public ChCategoryCenterEntity getChCategoryById (Long id)
    {
        return BeanUtil.copyProperties(chCategoryCenterMapper.selectById(id), ChCategoryCenterEntity.class);
    }


    private List<Long> getIdList(ChCategoryCenterEntity chCategoryCenterEntity) {
        List<ChCategoryCenterPo> chCategoryCenterPos = chCategoryCenterMapper.selectChildrenDeptById(chCategoryCenterEntity.getCategoryId());
        List<Long> catogoryIdList = chCategoryCenterPos.stream()
                .map(ChCategoryCenterPo::getCategoryId)
                .collect(Collectors.toList());
        return catogoryIdList;
    }

    /**
     * 修改子元素关系
     * @param categoryId
     * @param newAncestors
     * @param oldAncestors
     */
    public void updateDeptChildren(Long categoryId, String newAncestors, String oldAncestors)
    {
        List<ChCategoryCenterPo> children = chCategoryCenterMapper.selectChildrenDeptById(categoryId);
        for (ChCategoryCenterPo child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty())
        {
            chCategoryCenterMapper.updateDeptChildren(children);
        }
    }



    @Override
    public Boolean deleteById(ChCategoryCenterEntity chCategoryCenterEntity) {
        Long categoryId = chCategoryCenterEntity.getCategoryId();
        if (categoryId == null) {
            throw new ContentException(ContentExceptionEnum.DATA_NOT_EIXT.getMsg());
        }
        ChInformationCondition chInformationCondition = new ChInformationCondition();
        chInformationCondition.setRelCategoryId(categoryId);
        List<Long> catogoryIdList = getIdList(chCategoryCenterEntity);
        catogoryIdList.add(categoryId);
        List<ChInformationEntity> chInformationEntities = chInformationRepository.queryCondition(chInformationCondition, catogoryIdList);
        //分类 + 子类 有内容不允许删除
        if (!chInformationEntities.isEmpty()) {
            throw new ContentException(ContentExceptionEnum.HIVE_CONTENT.getMsg());
        }
        //删除分类 + 子类
        LambdaUpdateWrapper<ChCategoryCenterPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ChCategoryCenterPo::getDeleted, DeletedFlagEnum.DELETED.getCode())
                .in(ChCategoryCenterPo::getCategoryId, catogoryIdList);

        return chCategoryCenterMapper.update(null, wrapper) > ContentConstants.NUM_0;
    }

    @Override
    public List<ChAdSpaceEntity> queryList(ChAdSpaceCondition condition) {
        return Collections.emptyList();
    }

    @Override
    public List<ChAdSpaceEntity> queryListByIds(List<Long> ids) {
        return Collections.emptyList();
    }

    /**
     * 查询调名称是否存在
     *
     * @param chCategoryCenterEntity
     */
    @Override
    public Boolean verifyName(ChCategoryCenterEntity chCategoryCenterEntity){
        if (StrUtil.isBlank(chCategoryCenterEntity.getCategoryName())) {
            return false;
        }
        ChCategoryCenterPo chCategoryCenterPo = BeanUtil.copyProperties(chCategoryCenterEntity, ChCategoryCenterPo.class);
        LambdaQueryWrapper<ChCategoryCenterPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(chCategoryCenterPo.getCategoryName()), ChCategoryCenterPo::getCategoryName, chCategoryCenterPo.getCategoryName())
                .eq(ChCategoryCenterPo::getDeleted, DeletedFlagEnum.UN_DELETED.getCode())
                .eq(ChCategoryCenterPo::getBussCategory, chCategoryCenterPo.getBussCategory())
                .ne(ObjUtil.isNotNull(chCategoryCenterPo.getCategoryId()), ChCategoryCenterPo::getCategoryId, chCategoryCenterPo.getCategoryId());
        return chCategoryCenterMapper.selectCount(queryWrapper) > ContentConstants.NUM_0;
    }


    /**
     * 校验名称
     * @param chCategoryCenterEntity
     * @return
     */
    public Boolean verifyCallAlias(ChCategoryCenterEntity chCategoryCenterEntity){
        if (StrUtil.isBlank(chCategoryCenterEntity.getCallAlias())) {
            return false;
        }
        ChCategoryCenterPo chCategoryCenterPo = BeanUtil.copyProperties(chCategoryCenterEntity, ChCategoryCenterPo.class);
        LambdaQueryWrapper<ChCategoryCenterPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(chCategoryCenterPo.getCallAlias()), ChCategoryCenterPo::getCallAlias, chCategoryCenterPo.getCallAlias())
                .eq(ChCategoryCenterPo::getDeleted, DeletedFlagEnum.UN_DELETED.getCode())
                .eq(ChCategoryCenterPo::getBussCategory, chCategoryCenterPo.getBussCategory())
                .ne(ObjUtil.isNotNull(chCategoryCenterPo.getCategoryId()), ChCategoryCenterPo::getCategoryId, chCategoryCenterPo.getCategoryId());
        return chCategoryCenterMapper.selectCount(queryWrapper) > ContentConstants.NUM_0;
    }

    /**
     * 查询子集
     * @param relCategoryId
     * @return
     */
    public  List<ChCategoryCenterEntity> selectChildrenDeptById(Long relCategoryId) {
        List<ChCategoryCenterPo> chCategoryCenterPos = chCategoryCenterMapper.selectChildrenDeptUndeletedById(relCategoryId);
        return BeanUtil.copyToList(chCategoryCenterPos, ChCategoryCenterEntity.class);
    }

    public  List<ChCategoryCenterEntity> selectChildrenDeptUndeletedByIdBatch(List<Long> relCategoryIds) {
        List<ChCategoryCenterPo> chCategoryCenterPos = chCategoryCenterMapper.selectChildrenDeptUndeletedByIdBatch(relCategoryIds);
        return BeanUtil.copyToList(chCategoryCenterPos, ChCategoryCenterEntity.class);
    }
}
