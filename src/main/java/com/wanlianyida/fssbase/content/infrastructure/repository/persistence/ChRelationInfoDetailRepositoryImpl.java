package com.wanlianyida.fssbase.content.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wanlianyida.fssbase.content.domain.model.entity.ChRelationInfoDetailEntity;
import com.wanlianyida.fssbase.content.domain.repository.ChRelationInfoDetailRepository;
import com.wanlianyida.fssbase.content.infrastructure.repository.mapper.ChRelationInfoDetailMapper;
import com.wanlianyida.fssbase.content.infrastructure.repository.po.ChRelationInfoDetailPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/12/07/17:21
 */
@Service
public class ChRelationInfoDetailRepositoryImpl implements ChRelationInfoDetailRepository {

    @Resource
    private ChRelationInfoDetailMapper chRelationInfoDetailMapper;

    @Override
    public ChRelationInfoDetailEntity getInfoDetailById (Long infoId)
    {
        LambdaQueryWrapper<ChRelationInfoDetailPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ChRelationInfoDetailPO::getRelInfoId, infoId);
        ChRelationInfoDetailPO chRelationInfoDetailPO = chRelationInfoDetailMapper.selectOne(queryWrapper);
        return BeanUtil.copyProperties(chRelationInfoDetailPO, ChRelationInfoDetailEntity.class);
    }
}
