package com.wanlianyida.fssbase.userauth.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wanlianyida.basemdm.api.model.dto.MdmOperatorDTO;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.userauth.application.assembler.PlatformAssembler;
import com.wanlianyida.fssbase.userauth.domain.model.entity.PlatformInfo;
import com.wanlianyida.fssbase.userauth.domain.service.PlatformInfoDomainService;
import com.wanlianyida.fssbase.userauth.domain.service.RoleDomainService;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthException;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthExceptionEnum;
import com.wanlianyida.fssbase.userauth.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.fssbase.userauth.infrastructure.exchange.RoleExchangeService;
import com.wanlianyida.fssbase.userauth.infrastructure.po.PlatformSystemInfoPO;
import com.wanlianyida.fssuserauth.api.enums.RoleTypeEnum;
import com.wanlianyida.fssuserauth.api.model.command.UmUserpermissionCommand;
import com.wanlianyida.fssuserauth.api.model.dto.RoleAddCommand;
import com.wanlianyida.fssuserauth.api.model.platform.command.PlatformInfoAddCommand;
import com.wanlianyida.fssuserauth.api.model.platform.command.PlatformInfoUpdateCommand;
import com.wanlianyida.fssuserauth.api.model.platform.dto.PlatformInfoDTO;
import com.wanlianyida.fssuserauth.api.model.platform.query.PlatformInfoQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台信息 appService
 *
 * <AUTHOR>
 * @date 2025-07-11 15:17:59
 */
@Service
public class PlatformInfoAppService {

    @Resource
    private PlatformInfoDomainService domainService;

    @Resource
    private MdmExchangeService mdmExchangeService;

    @Resource
    private RoleExchangeService roleExchangeService;

    @Resource
    private RoleDomainService roleDomainService;

    @Resource
    private UmUserpermissionAppService umUserpermissionAppService;

    @Resource
    private UserRoleAppService userRoleAppService;

    public ResultMode<List<PlatformInfoDTO>> queryPage(final PlatformInfoQuery query) {
        Page<PlatformInfo> page = domainService.queryPage(query);
        List<PlatformInfo> dos = coverUsername(page.getRecords());
        return ResultMode.successPageList(PlatformAssembler.INSTANCE.dosToDtos(dos), (int) page.getTotal());
    }

    public ResultMode<List<PlatformInfoDTO>> queryList(final PlatformInfoQuery query) {
        List<PlatformInfo> list = coverUsername(domainService.queryList(query));
        return ResultMode.success(PlatformAssembler.INSTANCE.dosToDtos(list));
    }

    /**
     * 转换对象
     *
     * @return 平台信息集合
     */
    public List<PlatformInfo> coverUsername(List<PlatformInfo> dos) {
        if (dos == null || dos.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> ids = new HashSet<>();
        for (PlatformInfo dto : dos) {
            if (StrUtil.isNotBlank(dto.getSuperAdminId())) {
                ids.add(Long.parseLong(dto.getSuperAdminId()));
            }
        }
        List<PlatformInfo> list = new ArrayList<>(dos.size());
        List<MdmOperatorDTO> dtos = mdmExchangeService.getMapByIds(ids);
        Map<String, String> map = dtos.stream().collect(Collectors.toMap(
                po -> String.valueOf(po.getId()), MdmOperatorDTO::getOperatorName));


        for (PlatformInfo dto : dos) {
            String superAdminId = dto.getSuperAdminId();
            // 避免直接修改原始对象，新建一个副本或临时对象
            PlatformInfo platformInfo = BeanUtil.copyProperties(dto, PlatformInfo.class);
            // 设置用户名
            if (ObjectUtil.isNotEmpty(map)) {
                platformInfo.setSuperAdminName(map.get(superAdminId));
            }
            list.add(platformInfo);
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(PlatformInfoAddCommand addCommand) {
        // 检查 systemCode 是否存在
        String systemCode = addCommand.getSystemCode();
        if (domainService.checkPlatformCodeExist(systemCode)) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.PLATFORM_CODE_EXIST);
        }
        domainService.add(addCommand);
        // 平台类型为管理端则初始化超级管理员
        initAdmin(addCommand.getPlatformType(), addCommand.getSystemCode(), addCommand.getSuperAdminId());
    }

    /**
     * 初始化创建超级管理员和管理员
     * @param platformType 平台类型
     * @param systemCode 平台编码
     * @param superAdminId 超级管理员id
     */
    public void initAdmin(String platformType, String systemCode, String superAdminId) {
        // 平台类型为管理端则初始化超级管理员
        if (platformType.contains("admin")) {
            // 获取超级管理员角色id
            Long superAdminRoleId = getSuperAdminRoleId(systemCode);
            // 创建管理员角色
            createAdminRole(systemCode);
            // 绑定用户角色
            if (StringUtils.isNotBlank(superAdminId)) {
                List<UmUserpermissionCommand> commandList = new ArrayList<>();
                UmUserpermissionCommand command = new UmUserpermissionCommand();
                command.setAccountId(superAdminId);
                command.setPermissionId(superAdminRoleId);
                commandList.add(command);
                userRoleAppService.batchInsert(commandList);
            }
        }
    }

    private Long getSuperAdminRoleId(String systemCode) {
        String roleType = RoleTypeEnum.SUPER_ADMIN.getCode();
        Long roleId = roleDomainService.getRoleByTypeAndSysType(roleType, systemCode);
        // 新增一个超级管理员信息
        if (ObjectUtil.isEmpty(roleId)) {
            boolean addResult = roleDomainService.platformAddRole(new RoleAddCommand()
                    .setName("超级管理员")
                    .setType(roleType)
                    .setSysType(systemCode));
            if (addResult) {
                roleId = roleDomainService.getRoleByTypeAndSysType(roleType, systemCode);
            }else {
                throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.SUPERADMIN_ROLE_CREATE_ERROR);
            }
        }
        return roleId;
    }

    private void createAdminRole(String systemCode) {
        Long adminId = roleDomainService.getRoleByTypeAndSysType(RoleTypeEnum.ADMIN.getCode(), systemCode);
        // 新增一个管理员信息
        if (ObjectUtil.isEmpty(adminId)) {
            roleDomainService.platformAddRole(new RoleAddCommand()
                    .setName("管理员")
                    .setType(RoleTypeEnum.ADMIN.getCode())
                    .setSysType(systemCode));
        }else {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.ADMIN_ROLE_CREATE_ERROR);
        }
    }

    public void update(PlatformInfoUpdateCommand updateCommand) {
        domainService.update(updateCommand);
        // 平台类型为管理端则初始化超级管理员
        initAdmin(updateCommand.getPlatformType(), updateCommand.getSystemCode(), updateCommand.getSuperAdminId());
    }

    public PlatformInfoDTO getById(Long id) {
        PlatformInfo platformInfo = domainService.getById(id);
        if (ObjectUtil.isEmpty(platformInfo)) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.PLATFORM_NOT_EXIST);
        }
        // 聚合用户信息
        if (StrUtil.isNotBlank(platformInfo.getSuperAdminId())) {
            MdmOperatorDTO operatorDTO = mdmExchangeService.getOperatorById(String.valueOf(platformInfo.getSuperAdminId()));
            if (ObjectUtil.isNotEmpty(operatorDTO)) {
                platformInfo.setSuperAdminName(operatorDTO.getOperatorName());
            }
        }
        return BeanUtil.copyProperties(platformInfo, PlatformInfoDTO.class);
    }

    public void delete(Long id) {
        domainService.delete(id);
    }

    public List<PlatformInfoDTO> listBySystemCodes(List<String> systemCodes) {
        List<PlatformSystemInfoPO> poList = domainService.listBySystemCodes(systemCodes);
        return PlatformAssembler.INSTANCE.posToDtos(poList);
    }
}
