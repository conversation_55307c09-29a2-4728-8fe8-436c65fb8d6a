package com.wanlianyida.fssbase.userauth.application.service;

import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.fssbase.userauth.domain.service.PlatformInfoDomainService;
import com.wanlianyida.fssbase.userauth.domain.service.UserResourceDomainService;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthException;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthExceptionEnum;
import com.wanlianyida.fssuserauth.api.model.command.AuthorizationClearCommand;
import com.wanlianyida.fssuserauth.api.model.dto.AuthorizationCommand;
import com.wanlianyida.fssuserauth.api.model.query.PlatformUserResourceQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.wanlianyida.fssbase.userauth.infrastructure.constant.AuthRedisConstants.userResourceRedisKey;

/**
 * 鉴权appService
 *
 * <AUTHOR>
 * @date 2025-06-27 10:32
 */
@Slf4j
@RefreshScope
@Service
public class AuthorizationAppService {

    @Resource
    private UserResourceDomainService userResourceDomainService;

    @Resource
    private PlatformInfoDomainService platformInfoDomainService;

    @Resource
    private RedisService redisService;

    // 临时逻辑 平台管理上线后替换
    @Value("${auth.systype:70}")
    private String authSystype;

    /**
     * 鉴权
     *
     * @param dto 鉴权参数
     */
    public void hasPermission(final AuthorizationCommand dto) {

        // 查看是否开启鉴权
        String sysType = dto.getSysType();
        if (!platformInfoDomainService.checkAuth(sysType)) {
            return;
        }

        // 1.查询用户系统资源
        PlatformUserResourceQuery query = new PlatformUserResourceQuery()
                .setSysType(sysType)
                .setUserId(dto.getUserId())
                .setCompanyId(dto.getCompanyId());
        List<String> userUrls = userResourceDomainService.getUserSystemResourceUrls(query);
        // 2.校验用户系统资源
        boolean has = userUrls.contains(dto.getResourceUrl());
        log.info("用户：{} 鉴权请求：{}, 鉴权结果：has={}", query.getUserId(), dto, has);
        if (!has) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.USER_NOT_AUTH);
        }
    }

    /**
     * 清理
     *
     * @param command 清除指令
     */
    public void clear(AuthorizationClearCommand command) {
        log.warn("用户：{} 清理鉴权缓存，指令：{}", command.getUserId(), command);
        String redisKey = userResourceRedisKey(command.getSysType(), command.getCompanyId(), command.getUserId());
        boolean remove = redisService.remove(redisKey);
        if (!remove) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.AUTH_CACHE_CLEAR_FAIL);
        }
    }
}
