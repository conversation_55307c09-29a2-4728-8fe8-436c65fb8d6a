package com.wanlianyida.fssbase.userauth.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.userauth.domain.model.condition.UmNodePermissionCondition;
import com.wanlianyida.fssbase.userauth.domain.model.entity.UmNodePermission;
import com.wanlianyida.fssbase.userauth.domain.service.UmNodePermissionDomainService;
import com.wanlianyida.fssuserauth.api.model.command.UmNodePermissionCommand;
import com.wanlianyida.fssuserauth.api.model.query.UmNodePermissionQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 节点角色表 Controller
 *
 * <AUTHOR>
 * @date 2025-03-29
 */
@Slf4j
@Service
public class UmNodePermissionAppService {

    @Resource
    private UmNodePermissionDomainService umNodePermissionDomainService;

    /**
     * 列表查询
     * @param query 查询参数
     * @return {@link ResultMode}<{@link UmNodePermission}>
     */
    public List<UmNodePermission> queryList(UmNodePermissionQuery query) {
        log.info("queryList#列表查询->{}", JSONUtil.toJsonStr(query));
        UmNodePermissionCondition condition = new UmNodePermissionCondition();
        BeanUtil.copyProperties(query, condition, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return umNodePermissionDomainService.queryList(condition);

    }

    /**
     * 新增
     * @param command
     * @return Long
     */
    public Long insert(UmNodePermissionCommand command) {
        log.info("insert#新增->{}", JSONUtil.toJsonStr(command));
        UmNodePermission entity = new UmNodePermission();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return umNodePermissionDomainService.insert(entity);
    }

    /**
     * 修改
     * @param command
     * @return Long
     */
    public Long update(UmNodePermissionCommand command) {
        log.info("update#修改->{}", JSONUtil.toJsonStr(command));
        UmNodePermission entity = new UmNodePermission();
        BeanUtil.copyProperties(command, entity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        return umNodePermissionDomainService.update(entity);
    }

}
