package com.wanlianyida.fssbase.userauth.application.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.userauth.application.assembler.RoleAssembler;
import com.wanlianyida.fssbase.userauth.domain.model.entity.Role;
import com.wanlianyida.fssbase.userauth.domain.service.RoleDomainService;
import com.wanlianyida.fssbase.userauth.domain.service.SystemDomainService;
import com.wanlianyida.fssbase.userauth.domain.service.RoleResourceDomainService;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthException;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthExceptionEnum;
import com.wanlianyida.fssuserauth.api.enums.RoleTypeEnum;
import com.wanlianyida.fssuserauth.api.model.dto.RoleAddCommand;
import com.wanlianyida.fssuserauth.api.model.dto.RoleDTO;
import com.wanlianyida.fssuserauth.api.model.dto.RoleUpdateCommand;
import com.wanlianyida.fssuserauth.api.model.query.PlatformRolePageQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthExceptionEnum.ROLE_NOT_EXIST;

/**
 * 角色app层service
 *
 * <AUTHOR>
 * @date 2025-05-13 17:28
 */
@Service
public class RoleAppService {

    @javax.annotation.Resource
    private RoleDomainService roleDomainService;

    @javax.annotation.Resource
    private SystemDomainService systemDomainService;

    @javax.annotation.Resource
    private RoleResourceDomainService roleResourceDomainService;

    public void create(RoleAddCommand command) {
        if (StrUtil.isNotBlank(command.getCode())) {
            if (roleDomainService.existsBySysTypeAndCode(command.getSysType(), command.getCode())) {
                throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.ROLE_CODE_EXIST);
            }
        }
        if (roleDomainService.existsBySysTypeAndNameAndOptionalCompanyId(command.getSysType(), command.getName(), command.getCompanyId())) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.ROLE_NAME_EXIST);
        }
        if (roleDomainService.platformAddRole(command)) {
            systemDomainService.incySystemVersion(command.getSysType());
        }
    }

    public ResultMode<List<RoleDTO>> pageByPlatform(final PlatformRolePageQuery query) {
        Page<Role> page = roleDomainService.pageByPlatform(query);
        return ResultMode.successPageList(RoleAssembler.INSTANCE.dosToDtos(page.getRecords()), (int) page.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdAndSysType(final Long roleId, final String sysType) {
        Role role = roleDomainService.getByIdAndSysType(roleId, sysType);
        if (role == null) {
            throw new FssBaseUserAuthException(ROLE_NOT_EXIST);
        }
        if (RoleTypeEnum.SUPER_ADMIN.getCode().equals(role.getType())) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.SUPER_ADMIN_CANT_DELETE);
        }
        if (RoleTypeEnum.ADMIN.getCode().equals(role.getType())) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.ADMIN_CANT_DELETE);
        }
        if (roleDomainService.deleteById(roleId)) {
            roleResourceDomainService.deleteByRoleId(roleId);
        }
        systemDomainService.incySystemVersion(sysType);
    }

    public void updateById(final RoleUpdateCommand command) {
        Role role = roleDomainService.getByIdAndSysType(command.getRoleId(), command.getSysType());
        if (role == null) {
            throw new FssBaseUserAuthException(ROLE_NOT_EXIST);
        }
        if (RoleTypeEnum.SUPER_ADMIN.getCode().equals(role.getType())) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.SUPER_ADMIN_CANT_EDIT);
        }
        if (RoleTypeEnum.ADMIN.getCode().equals(role.getType())) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.ADMIN_CANT_EDIT);
        }
        role.setName(command.getName());
        role.setCode(command.getCode());
        role.setDesc(command.getDesc());
        role.setUpdateId(command.getCreateUserId());
        roleDomainService.updateById(role);
        systemDomainService.incySystemVersion(command.getSysType());
    }

    public List<String> findSysTypeByRoleIds(List<Integer> roleIds) {
        return roleDomainService.findSysTypeByRoleIds(roleIds);
    }
}
