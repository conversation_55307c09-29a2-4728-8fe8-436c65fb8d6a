package com.wanlianyida.fssbase.userauth.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.fsscommon.utils.EncDecHelpUtil;
import com.wanlianyida.framework.fsscommon.utils.EncryptUtils;
import com.wanlianyida.fssbase.userauth.domain.model.condition.*;
import com.wanlianyida.fssbase.userauth.domain.model.entity.*;
import com.wanlianyida.fssbase.userauth.domain.service.*;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthException;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthExceptionEnum;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.enums.UserAuthEnums;
import com.wanlianyida.fssuserauth.api.model.command.TenantAuthRelationCommand;
import com.wanlianyida.fssuserauth.api.model.command.TenantDiverseInfoCommand;
import com.wanlianyida.fssuserauth.api.model.command.UmLogininfoCommand;
import com.wanlianyida.fssuserauth.api.model.dto.TenantDiverseInfoDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UmFunctionDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UserAccountInfoDTO;
import com.wanlianyida.fssuserauth.api.model.query.TenantDiverseInfoQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 租户授权关系 AppService
 * 一个方法逻辑关联多张表
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Slf4j
@Service
public class TenantAuthRelationAppService {

    @Resource
    private TenantDiverseInfoDomainService tenantDiverseInfoDomainService;
    @Resource
    private UmLogininfoDomainService umLogininfoDomainService;
    @Resource
    private UmUserpermissionDomainService umUserpermissionDomainService;
    @Resource
    private UserSystemRelDomainService userSystemRelDomainService;
    @Resource
    private UmPermissionfunctionsDomainService umPermissionfunctionsDomainService;
    @Resource
    private UmFunctionDomainService umFunctionDomainService;

    /**
     * 租户创建+初始化权限（创建租户+账号+租户角色关系+租户平台关系）
     *
     * @param command
     * @return Long
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(TenantAuthRelationCommand command) {
        log.info("register#租户创建->{}", JSONUtil.toJsonStr(command));
        try {
            Long tenantDiverseInfoId = null;
            // 租户信息：tenantId+category已存在跳过
            if (command.getTenantDiverseInfoCommand() != null) {
                TenantDiverseInfoCondition condition = new TenantDiverseInfoCondition();
                condition.setTenantId(command.getTenantDiverseInfoCommand().getTenantId());
                condition.setCategory(command.getTenantDiverseInfoCommand().getCategory());
                if (CollUtil.isEmpty(tenantDiverseInfoDomainService.queryList(condition))) {
                    TenantDiverseInfo tenantDiverseInfoEntity = new TenantDiverseInfo();
                    BeanUtil.copyProperties(command.getTenantDiverseInfoCommand(), tenantDiverseInfoEntity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    tenantDiverseInfoId = tenantDiverseInfoDomainService.insert(tenantDiverseInfoEntity);
                }
            }
            // 租户账号信息：loginName已存在跳过
            if (command.getUmLogininfoCommand() != null) {
                UmLogininfo umLogininfoEntity = new UmLogininfo();
                BeanUtil.copyProperties(command.getUmLogininfoCommand(), umLogininfoEntity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                UmLogininfoCondition condition = new UmLogininfoCondition();
                condition.setLoginName(umLogininfoEntity.getLoginName());
                if (CollUtil.isEmpty(umLogininfoDomainService.queryList(condition))) {
                    try {
                        // 前端对称加密密码解密后用自定义key进行MD5加密
                        String aesDecode = EncryptUtils.aesDecrypt(umLogininfoEntity.getPassword(), EncryptUtils.AES_KEY);
                        String md5Ecode = EncDecHelpUtil.md5Encode(aesDecode, umLogininfoEntity.getSingleKey());
                        umLogininfoEntity.setPassword(md5Ecode);
                    } catch (Exception e) {
                        log.error("register#密码加密不规范->{}", umLogininfoEntity.getPassword());
                        throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.PASSWORD_IMPROPER_ENCRYPTION);
                    }
                    umLogininfoDomainService.insert(umLogininfoEntity);
                }
            }
            // 租户角色关系：先删后增
            if (CollUtil.isNotEmpty(command.getUmUserpermissionCommandList())) {
                UmUserpermissionCondition condition = new UmUserpermissionCondition();
                condition.setAccountId(CollUtil.getFirst(command.getUmUserpermissionCommandList()).getAccountId());
                List<UmUserpermission> umUserpermissionList = umUserpermissionDomainService.queryList(condition);
                if (CollUtil.isNotEmpty(umUserpermissionList)) {
                    umUserpermissionDomainService.batchDelete(umUserpermissionList.stream().map(UmUserpermission::getUsePermissionId).collect(Collectors.toList()));
                }
                List<UmUserpermission> entityList = BeanUtil.copyToList(command.getUmUserpermissionCommandList(), UmUserpermission.class, CopyOptions.create().ignoreNullValue().ignoreError());
                umUserpermissionDomainService.batchInsert(entityList);
            }
            // 租户平台关系：增量新增
            if (command.getUserSystemRelCommand() != null) {
                UserSystemRel userSystemRelEntity = new UserSystemRel();
                BeanUtil.copyProperties(command.getUserSystemRelCommand(), userSystemRelEntity, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                UserSystemRelCondition userSystemRelCondition = new UserSystemRelCondition();
                userSystemRelCondition.setTenantId(userSystemRelEntity.getTenantId());
                userSystemRelCondition.setTenantCategory(userSystemRelEntity.getTenantCategory());
                List<UserSystemRel> userSystemRelList = userSystemRelDomainService.queryList(userSystemRelCondition);
                if (CollUtil.isEmpty(userSystemRelList)) {
                    if (userSystemRelList.stream().noneMatch(umUserSystemRel -> umUserSystemRel.getSystemCode().equals(userSystemRelEntity.getSystemCode()))) {
                        userSystemRelDomainService.insert(userSystemRelEntity);
                    }
                }
            }
            return tenantDiverseInfoId;
        } catch (FssBaseUserAuthException e) {
            throw e;
        } catch (Exception e) {
            log.error("register#租户创建异常->", e);
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.CREATE_TENANT_ERR);
        }
    }

    /**
     * 逻辑删除租户+物理删除租户角色关系、租户平台关系
     *
     * @param delCommand
     * @return Long
     */
    public Long deleteTenant(TenantDiverseInfoCommand delCommand) {
        log.info("deleteTenent#删除租户及授权->{}", JSONUtil.toJsonStr(delCommand));
        try {
            // 逻辑删除租户
            TenantDiverseInfoCondition condition = new TenantDiverseInfoCondition();
            condition.setTenantId(delCommand.getTenantId());
            condition.setCategory(delCommand.getCategory());
            List<TenantDiverseInfo> tenantDiverseInfoList = tenantDiverseInfoDomainService.queryList(condition);
            if (CollUtil.isEmpty(tenantDiverseInfoList)) {
                return 0L;
            }
            TenantDiverseInfo tenantDiverseInfoEntity = CollUtil.getFirst(tenantDiverseInfoList);
            if (tenantDiverseInfoEntity == null) {
                return 0L;
            }
            Long id = tenantDiverseInfoEntity.getId();
            tenantDiverseInfoDomainService.deleteById(id);
            // 物理删除租户角色关系
            UmUserpermissionCondition umUserpermissionCondition = new UmUserpermissionCondition();
            umUserpermissionCondition.setAccountId(delCommand.getTenantId());
            List<UmUserpermission> umUserpermissionList = umUserpermissionDomainService.queryList(umUserpermissionCondition);
            if (CollUtil.isNotEmpty(umUserpermissionList)) {
                umUserpermissionDomainService.batchDelete(umUserpermissionList.stream().map(UmUserpermission::getUsePermissionId).collect(Collectors.toList()));
            }
            // 物理删除租户平台关系
            UserSystemRelCondition userSystemRelCondition = new UserSystemRelCondition();
            userSystemRelCondition.setTenantId(delCommand.getTenantId());
            userSystemRelCondition.setTenantCategory(delCommand.getCategory());
            List<UserSystemRel> userSystemRelList = userSystemRelDomainService.queryList(userSystemRelCondition);
            if (CollUtil.isNotEmpty(userSystemRelList)) {
                userSystemRelDomainService.deleteById(CollUtil.getFirst(userSystemRelList).getId());
            }
            return id;
        } catch (Exception e) {
            log.error("register#逻辑删除租户异常->", e);
            throw new FssBaseUserAuthException("逻辑删除租户异常");
        }
    }


    /**
     * 获取租户授权
     *
     * @param query 查询参数
     * @return {@link ResponseMessage}<{@link TenantDiverseInfoDTO}>
     */
    public List<UmFunctionDTO> getUserFunctions(TenantDiverseInfoQuery query) {
        log.info("getUserFunctions#获取租户授权->{}", JSONUtil.toJsonStr(query));
        if (StrUtil.equals(UserAuthEnums.TenantCategoryEnum.PERSONAL.getCode(), query.getCategory())) {
            if (StrUtil.hasBlank(query.getTenantId(), query.getCategory())) {
                throw new FssBaseUserAuthException("用户ID和类别不能为空");
            }
        }
        if (StrUtil.equals(UserAuthEnums.TenantCategoryEnum.COMPANY.getCode(), query.getCategory())) {
            if (StrUtil.hasBlank(query.getCompanyId(), query.getCategory())) {
                throw new FssBaseUserAuthException("员工企业ID和类别不能为空");
            }
        }
        if (StrUtil.equals(UserAuthEnums.TenantCategoryEnum.EMPLOYEE.getCode(), query.getCategory())) {
            if (StrUtil.hasBlank(query.getTenantId(), query.getCompanyId(), query.getCategory())) {
                throw new FssBaseUserAuthException("员工ID和员工企业ID和类别不能为空");
            }
        }
        // 校验租户存在
        TenantDiverseInfoCondition tenantDiverseInfoCondition = new TenantDiverseInfoCondition();
        tenantDiverseInfoCondition.setTenantId(query.getTenantId());
        List<TenantDiverseInfo> tenantDiverseInfoList = tenantDiverseInfoDomainService.queryList(tenantDiverseInfoCondition);
        if (CollUtil.isEmpty(tenantDiverseInfoList)) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.USER_NOT_EXIST);
        }
        // 入参租户类别是个人：代表此时租户只是注册了，不属于任何企业：1.获取个人角色 2.获取个人角色对应的功能 3.获取功能对应的菜单按钮
        List<UmFunctionDTO> functionsFinal = new ArrayList<>();
        if (StrUtil.equals(UserAuthEnums.TenantCategoryEnum.PERSONAL.getCode(), query.getCategory())) {
            functionsFinal = getFunctions(query);
        }
        // 入参租户类别是企业：代表此时租户是企业的管理员：1.获取企业角色 2.获取企业角色对应的功能 3.获取功能对应的菜单按钮
        if (StrUtil.equals(UserAuthEnums.TenantCategoryEnum.COMPANY.getCode(), query.getCategory())) {
            functionsFinal = getFunctions(query);
        }
        // 入参租户类别是员工：代表此时租户是企业的普通员工：1.获取企业操作员角色 2.获取企业操作员角色对应的功能 3.获取功能对应的菜单按钮 4.获取企业的角色的功能对应的菜单按钮 5.两种菜单按钮集合取交集
        if (StrUtil.equals(UserAuthEnums.TenantCategoryEnum.EMPLOYEE.getCode(), query.getCategory())) {
            List<UmFunctionDTO> functionsEmployee = getFunctions(query);
            List<UmFunctionDTO> functionsCompany = getFunctions(query);
            Set<UmFunctionDTO> functionsSet = CollUtil.intersectionDistinct(functionsEmployee, functionsCompany);
            functionsFinal = CollUtil.newArrayList(functionsSet);
        }
        return functionsFinal;
    }

    private List<UmFunctionDTO> getFunctions(TenantDiverseInfoQuery query) {
        log.info("getFunctions#获取租户授权->{}", JSONUtil.toJsonStr(query));
        UmUserpermissionCondition umUserpermissionCondition = new UmUserpermissionCondition();
        umUserpermissionCondition.setAccountId(query.getTenantId());
        List<UmUserpermission> umUserpermissionList = umUserpermissionDomainService.queryList(umUserpermissionCondition);
        if (CollUtil.isEmpty(umUserpermissionList)) {
            return Collections.emptyList();
        }
        UmPermissionfunctionsCondition umPermissionfunctionsCondition = new UmPermissionfunctionsCondition();
        umPermissionfunctionsCondition.setPermissionIdList(umUserpermissionList.stream().map(UmUserpermission::getPermissionId).collect(Collectors.toList()));
        List<UmPermissionfunctions> umPermissionfunctionsList = umPermissionfunctionsDomainService.queryList(umPermissionfunctionsCondition);
        if (CollUtil.isEmpty(umPermissionfunctionsList)) {
            return Collections.emptyList();
        }
        List<Long> funcIdList = umPermissionfunctionsList.stream().map(UmPermissionfunctions::getFuncId).collect(Collectors.toList());
        UmFunctionCondition umFunctionCondition = new UmFunctionCondition();
        umFunctionCondition.setFuncIdList(funcIdList);
        umFunctionCondition.setSysType(query.getSystemCode());
        List<UmFunction> umFunctionList = umFunctionDomainService.queryList(umFunctionCondition);
        if (CollUtil.isEmpty(umFunctionList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(umFunctionList, UmFunctionDTO.class, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
    }

    /**
     * 租户登录验密
     *
     * @param command
     * @return {@link UserAccountInfoDTO }
     */
    public UserAccountInfoDTO loginCheckPwd(UmLogininfoCommand command) {
        log.info("loginCheckPwd#租户登录验密->{}", JSONUtil.toJsonStr(command));
        if (StrUtil.isBlank(command.getLoginName())) {
            throw new FssBaseUserAuthException("用户名不能为空");
        }
        // 校验租户存在
        TenantDiverseInfoCondition tenantDiverseInfoCondition = new TenantDiverseInfoCondition();
        tenantDiverseInfoCondition.setTenantId(command.getTenantId());
        List<TenantDiverseInfo> tenantDiverseInfoList = tenantDiverseInfoDomainService.queryList(tenantDiverseInfoCondition);
        if (CollUtil.isEmpty(tenantDiverseInfoList)) {
            throw new FssBaseUserAuthException("用户信息不存在");
        }
        TenantDiverseInfo tenantDiverseInfo = CollUtil.getFirst(tenantDiverseInfoList);
        // 查租户账号
        UmLogininfoCondition umLogininfoCondition = new UmLogininfoCondition();
        umLogininfoCondition.setTenantId(tenantDiverseInfo.getTenantId());
        umLogininfoCondition.setCreateSourse(UserAuthEnums.CreateSourseEnum.PC.getCode());
        List<UmLogininfo> umLogininfoList = umLogininfoDomainService.queryList(umLogininfoCondition);
        if (CollUtil.isEmpty(umLogininfoList)) {
            throw new FssBaseUserAuthException("用户账号信息不存在");
        }
        UmLogininfo umLogininfo = CollUtil.getFirst(umLogininfoList);
        if (StrUtil.isNotBlank(command.getPassword())) {
            try {
                // 解密验密
                String decode = EncryptUtils.aesDecrypt(command.getPassword(), EncryptUtils.AES_KEY);
                decode = EncDecHelpUtil.md5Encode(decode,umLogininfo.getSingleKey());
                String decodeDB = umLogininfo.getPassword();
                if (!StrUtil.equals(decode, decodeDB)) {
                    throw new FssBaseUserAuthException("密码输入错误");
                }
            } catch (FssBaseUserAuthException e) {
                throw e;
            } catch (Exception e) {
                log.error("loginCheckPwd#验密异常->", e);
                throw new FssBaseUserAuthException("验密异常");
            }
        }
        UserAccountInfoDTO userAccountInfoDTO = new UserAccountInfoDTO();
        userAccountInfoDTO.setTenantId(tenantDiverseInfo.getTenantId());
        userAccountInfoDTO.setCategory(tenantDiverseInfo.getCategory());
        userAccountInfoDTO.setSystemCode(tenantDiverseInfo.getSystemCode());
        userAccountInfoDTO.setLoginId(umLogininfo.getLoginId());
        userAccountInfoDTO.setLoginName(umLogininfo.getLoginName());
        userAccountInfoDTO.setPwdStrength(umLogininfo.getPwdStrength());
        userAccountInfoDTO.setTelephone(umLogininfo.getTelephone());
        return userAccountInfoDTO;
    }

    /**
     * 获取租户账号信息
     *
     * @param tenantId
     * @return {@link UserAccountInfoDTO }
     */
    public UserAccountInfoDTO getLoginInfo(String tenantId) {
        log.info("getLoginInfo#获取租户账号信息->{}", tenantId);
        // 查租户
        TenantDiverseInfoCondition tenantDiverseInfoCondition = new TenantDiverseInfoCondition();
        tenantDiverseInfoCondition.setTenantId(tenantId);
        List<TenantDiverseInfo> tenantDiverseInfoList = tenantDiverseInfoDomainService.queryList(tenantDiverseInfoCondition);
        if (CollUtil.isEmpty(tenantDiverseInfoList)) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.USER_NOT_EXIST);

        }
        TenantDiverseInfo tenantDiverseInfo = CollUtil.getFirst(tenantDiverseInfoList);
        // 查租户账号
        UmLogininfoCondition umLogininfoCondition = new UmLogininfoCondition();
        umLogininfoCondition.setTenantId(tenantId);
        umLogininfoCondition.setCreateSourse(UserAuthEnums.CreateSourseEnum.PC.getCode());
        List<UmLogininfo> umLogininfoList = umLogininfoDomainService.queryList(umLogininfoCondition);
        if (CollUtil.isEmpty(umLogininfoList)) {
            throw new FssBaseUserAuthException(FssBaseUserAuthExceptionEnum.USER_LOGIN_NOT_EXIST);
        }
        UmLogininfo umLogininfo = CollUtil.getFirst(umLogininfoList);
        UserAccountInfoDTO userAccountInfoDTO = new UserAccountInfoDTO();
        userAccountInfoDTO.setTenantId(tenantDiverseInfo.getTenantId());
        userAccountInfoDTO.setCategory(tenantDiverseInfo.getCategory());
        userAccountInfoDTO.setSystemCode(tenantDiverseInfo.getSystemCode());
        userAccountInfoDTO.setLoginId(umLogininfo.getLoginId());
        userAccountInfoDTO.setLoginName(umLogininfo.getLoginName());
        userAccountInfoDTO.setPwdStrength(umLogininfo.getPwdStrength());
        userAccountInfoDTO.setTelephone(umLogininfo.getTelephone());
        return userAccountInfoDTO;
    }

    /**
     * 租户改密
     *
     * @param command
     * @return String
     */
    public void changePwd(UmLogininfoCommand command) {
        log.info("changePwd#租户改密->{}", JSONUtil.toJsonStr(command));
        if (StrUtil.hasBlank(command.getPassword(), command.getNewPassword(), command.getConfirmNewPassword())) {
            throw new FssBaseUserAuthException("原密码、新密码、确认新密码不能为空");
        }
        // 查租户账号
        UmLogininfoCondition umLogininfoCondition = new UmLogininfoCondition();
        umLogininfoCondition.setTenantId(command.getTenantId());
        umLogininfoCondition.setCreateSourse(UserAuthEnums.CreateSourseEnum.PC.getCode());
        List<UmLogininfo> umLogininfoList = umLogininfoDomainService.queryList(umLogininfoCondition);
        if (CollUtil.isEmpty(umLogininfoList)) {
            // 兼容旧版本
            umLogininfoCondition.setTenantId(null);
            umLogininfoCondition.setLoginName(command.getLoginName());
            umLogininfoCondition.setCreateSourse(null);
            List<UmLogininfo> umLoginNameList = umLogininfoDomainService.queryList(umLogininfoCondition);
            if (CollUtil.isEmpty(umLoginNameList)) {
                throw new FssBaseUserAuthException("用户账号信息不存在");
            }
            umLogininfoList = umLoginNameList;
        }
        UmLogininfo umLogininfo = CollUtil.getFirst(umLogininfoList);
        try {
            // 解密验密
            String decodeOld = EncryptUtils.aesDecrypt(command.getPassword(), EncryptUtils.AES_KEY);
            log.info("changePwd#解密明文->{}", JSONUtil.toJsonStr(decodeOld));
            decodeOld = EncDecHelpUtil.md5Encode(decodeOld, umLogininfo.getSingleKey());
            log.info("changePwd#明文加密->{}", JSONUtil.toJsonStr(decodeOld));
            String decodeDB = umLogininfo.getPassword();
            log.info("changePwd#数据库原密码->{}", JSONUtil.toJsonStr(decodeDB));
            String decodeNew = EncryptUtils.aesDecrypt(command.getNewPassword(), EncryptUtils.AES_KEY);
            String decodeNewConfirm = EncryptUtils.aesDecrypt(command.getConfirmNewPassword(), EncryptUtils.AES_KEY);
            if (!StrUtil.equals(decodeNew, decodeNewConfirm)) {
                throw new FssBaseUserAuthException("新密码与确认密码不一致");
            }
            log.info("changePwd#解密验密->{}", JSONUtil.toJsonStr(decodeOld));
            if (!StrUtil.equals(decodeOld, decodeDB)) {
                throw new FssBaseUserAuthException("原密码输入错误");
            }
            UmLogininfo umLogininfoUpdate = new UmLogininfo();
            umLogininfoUpdate.setLoginId(umLogininfo.getLoginId());
            umLogininfoUpdate.setPassword(EncDecHelpUtil.md5Encode(decodeNew,umLogininfo.getSingleKey()));
            umLogininfoUpdate.setPasswordModifyTime(DateUtil.date());
            umLogininfoUpdate.setPwdStrength(command.getPwdStrength());
            umLogininfoDomainService.update(umLogininfoUpdate);
        } catch (FssBaseUserAuthException e) {
            throw e;
        } catch (Exception e) {
            log.error("changePwd#修改密码异常->", e);
            throw new FssBaseUserAuthException("修改密码异常");
        }
    }

    /**
     * 租户重置密码
     *
     * @param command
     * @return String
     */
    public void resetPwd(UmLogininfoCommand command) {
        log.info("resetPwd#租户重置密码->{}", JSONUtil.toJsonStr(command));
        if (StrUtil.hasBlank(command.getNewPassword(), command.getConfirmNewPassword())) {
            throw new FssBaseUserAuthException("新密码、确认新密码不能为空");
        }
        // 查租户账号
        UmLogininfoCondition umLogininfoCondition = new UmLogininfoCondition();
        umLogininfoCondition.setTenantId(command.getTenantId());
        umLogininfoCondition.setCreateSourse(UserAuthEnums.CreateSourseEnum.PC.getCode());
        List<UmLogininfo> umLogininfoList = umLogininfoDomainService.queryList(umLogininfoCondition);
        if (CollUtil.isEmpty(umLogininfoList)) {
            // 兼容旧版本
            umLogininfoCondition.setTenantId(null);
            umLogininfoCondition.setLoginName(command.getLoginName());
            List<UmLogininfo> umLoginNameList = umLogininfoDomainService.queryList(umLogininfoCondition);
            if (CollUtil.isEmpty(umLoginNameList)) {
                throw new FssBaseUserAuthException("用户账号信息不存在");
            }
            umLogininfoList = umLoginNameList;
        }
        UmLogininfo umLogininfo = CollUtil.getFirst(umLogininfoList);
        try {
            // 解新密验新密
            String decodeNew = EncryptUtils.aesDecrypt(command.getNewPassword(), EncryptUtils.AES_KEY);
            String decodeNewConfirm = EncryptUtils.aesDecrypt(command.getConfirmNewPassword(), EncryptUtils.AES_KEY);
            if (!StrUtil.equals(decodeNew, decodeNewConfirm)) {
                throw new FssBaseUserAuthException("新密码与确认密码不一致");
            }
            UmLogininfo umLogininfoUpdate = new UmLogininfo();
            umLogininfoUpdate.setLoginId(umLogininfo.getLoginId());
            umLogininfoUpdate.setPassword(EncDecHelpUtil.md5Encode(decodeNew,umLogininfo.getSingleKey()));
            umLogininfoUpdate.setPasswordModifyTime(DateUtil.date());
            umLogininfoUpdate.setPwdStrength(command.getPwdStrength());
            umLogininfoDomainService.update(umLogininfoUpdate);
        } catch (FssBaseUserAuthException e) {
            throw e;
        } catch (Exception e) {
            log.error("resetPwd#重置密码异常->", e);
            throw new FssBaseUserAuthException("重置密码异常");
        }
    }

}
