package com.wanlianyida.fssbase.userauth.infrastructure.repository.mapper;

import com.wanlianyida.fssbase.userauth.domain.model.condition.UmPermissioninfoCondition;
import com.wanlianyida.fssbase.userauth.domain.model.entity.UmPermissioninfo;
import com.wanlianyida.fssuserauth.api.model.dto.UserRoleNameDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 角色组管理表,角色编码 Mapper
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Mapper
public interface UmPermissioninfoMapper {

	/**
	 * 条件查询列表
	 *
	 * @param contition
	 * @return {@link List}<{@link UmPermissioninfoCondition}>
	 */
	List<UmPermissioninfo> queryByCondition(UmPermissioninfoCondition contition);

	/**
     * 新增
     *
     * @param entity
     */
	void insertSelective(UmPermissioninfo entity);

	/**
	 * 根据主键修改
	 *
	 * @param entity
	 */
	void updateByPrimaryKeySelective(UmPermissioninfo entity);

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link UmPermissioninfo}
	 */
	UmPermissioninfo selectByPrimaryKey(@Param("id") Long id);

	/**
	 * 物理删除
	 *
	 * @param id
	 */
	void deleteById(@Param("id") Long id);


	List<String> getRoleIdsByRoleName(@Param("roleName")String roleName);

	List<UserRoleNameDTO> queryRolesByUserIds(@Param("userIds")List<String> userIds);

	List<String> queryUserByRoleId(@Param("roleId") Integer roleId);

}