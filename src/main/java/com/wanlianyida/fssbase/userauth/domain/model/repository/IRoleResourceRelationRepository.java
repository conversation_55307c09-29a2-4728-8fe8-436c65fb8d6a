package com.wanlianyida.fssbase.userauth.domain.model.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wanlianyida.fssbase.userauth.infrastructure.po.PlatformUmPermissionfunctionsPO;

import java.util.List;

/**
 * 角色资源关系仓储定义
 *
 * <AUTHOR>
 * @date 2025-05-13 14:56:31
 */
public interface IRoleResourceRelationRepository extends IService<PlatformUmPermissionfunctionsPO> {

    List<Long> listResoureIdsByRoleIds(List<Long> roleIds);

    boolean batchSave(Long roleId, List<Long> resourceIds, String userId);

    boolean batchUpdate(Long roleId, List<Long> resourceIds, String userId);

    boolean batchDelete(Long roleId, List<Long> resourceIds);

    boolean batchDelete(Long roleId);

    boolean removeByRoleId(Long roleId);

}
