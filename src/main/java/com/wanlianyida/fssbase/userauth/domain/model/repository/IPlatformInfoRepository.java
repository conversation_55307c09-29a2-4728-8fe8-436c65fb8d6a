package com.wanlianyida.fssbase.userauth.domain.model.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wanlianyida.fssbase.userauth.infrastructure.po.PlatformSystemInfoPO;
import com.wanlianyida.fssuserauth.api.model.platform.query.PlatformInfoQuery;

import java.util.List;

/**
 * 平台信息Repository
 *
 * <AUTHOR>
 * @date 2025-07-11 15:08:56
 */
public interface IPlatformInfoRepository extends IService<PlatformSystemInfoPO> {

    Page<PlatformSystemInfoPO> queryPage(PlatformInfoQuery query);

    List<PlatformSystemInfoPO> queryList(PlatformInfoQuery query);

    void delete(Long id);

    boolean checkAuth(String platformCode);

    boolean checkPlatformCodeExist(String platformCode);

    List<PlatformSystemInfoPO> listBySystemCodes(List<String> systemCodes);
}
