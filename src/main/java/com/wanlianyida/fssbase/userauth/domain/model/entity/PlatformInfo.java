package com.wanlianyida.fssbase.userauth.domain.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 平台信息 DTO
 *
 * <AUTHOR>
 * @date 2025-07-11 14:50:21
 */
@Data
@Accessors(chain = true)
public class PlatformInfo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台编码
     */
    private String systemCode;

    /**
     * 平台名称
     */
    private String systemName;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 平台描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否默认显示 10-是 20-否
     */
    private String defaultShowArea;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 平台地址
     */
    private String url;

    /**
     * 平台获取初始化信息路径
     */
    private String dataInit;

    /**
     * 鉴权状态[0-不启用,1-启用]
     */
    private String authStatus;

    /**
     * 有效标志[0-无效,1-有效]
     */
    private String validFlag;

    /**
     * 删除标志[0-否,1-是]
     */
    private String delFlag;

    /**
     * 超级管理员id
     */
    private String superAdminId;

    /**
     * 超级管理员名称
     */
    private String superAdminName;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedDate;


    public static PlatformInfo create(Long id, String platformType, String systemCode,
                                      String systemName, String url, String superAdminId,
                                      String authStatus, String description, String validFlag, String creatorId,
                                      String updaterId, String delFlag){
        LocalDateTime now = LocalDateTime.now();
        return new PlatformInfo()
                .setId(id)
                .setPlatformType(platformType)
                .setSystemCode(systemCode)
                .setSystemName(systemName)
                .setUrl(url)
                .setSuperAdminId(superAdminId)
                .setAuthStatus(authStatus)
                .setDescription(description)
                .setValidFlag(validFlag)
                .setDelFlag(delFlag)
                .setCreatorId(creatorId)
                .setCreatedDate(now)
                .setUpdaterId(updaterId)
                .setUpdatedDate(now);
    }
}
