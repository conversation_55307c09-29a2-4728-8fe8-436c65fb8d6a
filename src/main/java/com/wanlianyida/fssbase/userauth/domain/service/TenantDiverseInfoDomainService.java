package com.wanlianyida.fssbase.userauth.domain.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.userauth.domain.model.condition.TenantDiverseInfoCondition;
import com.wanlianyida.fssbase.userauth.domain.model.entity.TenantDiverseInfo;
import com.wanlianyida.fssbase.userauth.domain.model.repository.TenantDiverseInfoRepository;
import com.wanlianyida.fssbase.userauth.infrastructure.exception.FssBaseUserAuthException;
import com.wanlianyida.fssuserauth.api.model.command.TenantUpdateStatusCommand;
import com.wanlianyida.fssuserauth.api.model.dto.TenantDiverseInfoDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 租户多元化信息表 DomainService
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Service
public class TenantDiverseInfoDomainService {

    @Resource
    private TenantDiverseInfoRepository tenantDiverseInfoRepository;

    /**
     * 分页查询
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link TenantDiverseInfoDTO}>
     */
    public PageInfo<TenantDiverseInfoDTO> queryPage(PagingInfo<TenantDiverseInfoCondition> pagingInfo) {
        return tenantDiverseInfoRepository.queryPage(pagingInfo);
    }

    /**
     * 列表查询
     * @param condition 查询参数
     * @return {@link ResultMode}<{@link TenantDiverseInfo}>
     */
    public List<TenantDiverseInfo> queryList(TenantDiverseInfoCondition condition) {
        return tenantDiverseInfoRepository.queryList(condition);
    }

    /**
     * 新增
     * @param entity
     * @return Long
     */
    public Long insert(TenantDiverseInfo entity) {
        if (StrUtil.hasBlank(entity.getTenantId(),entity.getCategory())) {
            throw new FssBaseUserAuthException("用户ID和类别不能为空");
        }
        TenantDiverseInfoCondition condition = new TenantDiverseInfoCondition();
        condition.setTenantId(entity.getTenantId());
        condition.setCategory(entity.getCategory());
        if (CollUtil.isNotEmpty(tenantDiverseInfoRepository.queryList(condition))) {
            throw new FssBaseUserAuthException("用户ID下类别已存在");
        }
        return tenantDiverseInfoRepository.insertSelective(entity);
    }

    /**
     * 修改
     * @param entity
     * @return Long
     */
    public Long update(TenantDiverseInfo entity) {
        return tenantDiverseInfoRepository.updateByPrimaryKeySelective(entity);
    }

    /**
     * 逻辑删除
     * @param id
     * @return Long
     */
    public Long deleteById(Long id) {
        return tenantDiverseInfoRepository.deleteById(id);
    }

    public void deleteByOperatorId(String operatorId){
        tenantDiverseInfoRepository.deleteByOperatorId(operatorId);
    }

    public void modifyStatus(TenantUpdateStatusCommand command){
        tenantDiverseInfoRepository.modifyStatus(command);
    }

    /**
     * 主键查询
     * @param id
     * @return
     */
    public TenantDiverseInfo getById(Long id) {
        return tenantDiverseInfoRepository.selectByPrimaryKey(id);
    }

}
