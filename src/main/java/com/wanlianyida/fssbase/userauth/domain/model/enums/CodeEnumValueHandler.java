package com.wanlianyida.fssbase.userauth.domain.model.enums;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @Description: 枚举校验注解处理类
 * @Author: Mindy
 * @Date: 2023/3/15
 * @Param:
 * @return:
 */
public class CodeEnumValueHandler implements ConstraintValidator<CodeEnumValue, Object> {

    private String[] strValues;
    private int[] intValues;


    @Override
    public void initialize(CodeEnumValue constraintAnnotation) {

        strValues = constraintAnnotation.strValues();
        intValues = constraintAnnotation.intValues();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {

        if (value instanceof String) {

            for (String s : strValues) {
                if (s.equals(value)) {
                    return true;
                }
            }
        } else if (value instanceof Integer) {

            for (int s : intValues) {
                if (s == ((Integer) value).intValue()) {
                    return true;
                }
            }
        }

        return false;
    }


}
