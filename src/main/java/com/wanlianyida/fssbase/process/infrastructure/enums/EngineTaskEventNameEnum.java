package com.wanlianyida.fssbase.process.infrastructure.enums;

import lombok.Getter;

/**
 * TaskEvent 的 eventName 枚举值
 * **create**
 * 任务创建时触发（分配给人或组之前）。
 * **assign**
 * 任务被分配给具体用户时触发。
 * **complete**
 * 任务完成时触发（用户提交或自动完成）。
 * **delete**
 * 任务被删除时触发（如流程终止或任务被取消）。
 * **update**
 * 任务属性（如优先级、描述）被修改时触发。
 * **timeout**
 * 任务因超时未处理而触发（需配置定时器边界事件）。
 * 其他相关事件（非TaskEvent但可能关联）
 * **start/end**：流程实例或活动的开始/结束事件（属于Execution事件，非TaskEvent）。
 * **migrate**：任务因流程迁移被重新分配时触发。
 */

@Getter
public enum EngineTaskEventNameEnum {

    /**任务创建时触发（分配给人或组之前）。在Execution.start之后 **/
    CREATE("create"),

    /**任务被分配给具体用户时触发。**/
    ASSIGNMENT("assignment"),

    /**任务完成时触发（用户提交或自动完成），在Execution.end之前**/
    COMPLETE("complete")

    ;

    private final String name;

    EngineTaskEventNameEnum(String name) {
        this.name = name;
    }
}
