package com.wanlianyida.fssbase.process.infrastructure.enums;

import lombok.Getter;

/**
 * 审核状态
 */
@Getter
public enum TaskAuditStatusEnum {

    STATUS_10(10, "待审"),
    STATUS_20(20, "审核通过"),
    STATUS_30(30, "审核不通过"),
    ;

    private final Integer status;
    private final String desc;

    TaskAuditStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
