package com.wanlianyida.fssbase.process.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.fssbase.process.infrastructure.repository.po.ActRuTaskPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20 08:52:46
 */
public interface ActRuTaskMapper extends BaseMapper<ActRuTaskPO> {

    /**
     * 查询用户待办任务
     * @param userId
     * @return
     */
    List<ActRuTaskPO> queryTaskByUserId(@Param("userId")String userId);
}
