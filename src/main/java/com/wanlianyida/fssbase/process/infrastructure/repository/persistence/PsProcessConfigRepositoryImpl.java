package com.wanlianyida.fssbase.process.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wanlianyida.fssbase.process.domain.model.condition.ProcessConfigCondition;
import com.wanlianyida.fssbase.process.domain.model.entity.PsProcessConfigEntity;
import com.wanlianyida.fssbase.process.domain.repository.PsProcessConfigRepository;
import com.wanlianyida.fssbase.process.infrastructure.repository.mapper.PsProcessConfigMapper;
import com.wanlianyida.fssbase.process.infrastructure.repository.po.PsProcessConfigPO;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
public class PsProcessConfigRepositoryImpl implements PsProcessConfigRepository {

    @Resource
    PsProcessConfigMapper psProcessConfigMapper;

    @Override
    public List<PsProcessConfigEntity> queryList(ProcessConfigCondition condition) {
        LambdaQueryWrapper<PsProcessConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotEmpty(condition.getProcessNo()), PsProcessConfigPO::getProcessNo, condition.getProcessNo());
        queryWrapper.in(CollUtil.isNotEmpty(condition.getProcessNoList()), PsProcessConfigPO::getProcessNo, condition.getProcessNoList());
        return BeanUtil.copyToList(psProcessConfigMapper.selectList(queryWrapper), PsProcessConfigEntity.class);
    }

    @Override
    public PsProcessConfigEntity getByCondition(ProcessConfigCondition condition) {
        LambdaQueryWrapper<PsProcessConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(condition.getId()), PsProcessConfigPO::getId, condition.getId());
        queryWrapper.eq(StrUtil.isNotEmpty(condition.getProcessNo()), PsProcessConfigPO::getProcessNo, condition.getProcessNo());
        queryWrapper.in(CollUtil.isNotEmpty(condition.getProcessNoList()), PsProcessConfigPO::getProcessNo, condition.getProcessNoList());
        return  BeanUtil.toBean(psProcessConfigMapper.selectOne(queryWrapper), PsProcessConfigEntity.class);
    }

    @Override
    public int insert(PsProcessConfigEntity entity) {
        if (entity != null) {
            PsProcessConfigPO po = new PsProcessConfigPO();
            BeanUtil.copyProperties(entity, po);
            return psProcessConfigMapper.insert(po);
        }
        return 0;
    }

    @Override
    public int update(PsProcessConfigEntity entity) {
        if (entity != null) {
            PsProcessConfigPO po = new PsProcessConfigPO();
            BeanUtil.copyProperties(entity, po);
            return psProcessConfigMapper.updateById(po);
        }
        return 0;
    }
}
