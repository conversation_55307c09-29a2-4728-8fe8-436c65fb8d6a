package com.wanlianyida.fssbase.process.infrastructure.converter;

import com.wanlianyida.fssbase.process.domain.model.entity.PsProcessInstanceEntity;
import org.camunda.bpm.engine.delegate.DelegateExecution;

public class ProcessInstanceConverter {
    public static PsProcessInstanceEntity toProcessInstanceEntity(DelegateExecution execution) {
        PsProcessInstanceEntity entity = new PsProcessInstanceEntity();
        entity.setProcessInstanceId(execution.getProcessInstanceId());
        entity.setProcessName(entity.getProcessName());
//        entity.set
        return entity;
    }
}
