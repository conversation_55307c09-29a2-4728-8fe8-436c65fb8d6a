package com.wanlianyida.fssbase.process.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 任务列表查询返回值
 */
@Data
public class PsTaskListResDTO {

    private Long id;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义key
     */
    private String processDefinitionKey;
    /**
     * 任务定义key
     */
    private String taskDefinitionKey;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 关联业务key
     */
    private String businessKey;

    /**
     * 任务状态[10-未处理,20-已处理]
     */
    private String taskStatus = "10";

    /**
     * 任务完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;

    /**
     * 任务完成结果[10-通过,20-未通过]
     */
    private String completeResult;

    /**
     * 任务完成用户id
     */
    private String completeUserId;

    /**
     * 提交用户id
     */
    private String submitUserId;

    /**
     * 任务完成用户名称
     */
    private String completeUserName;

    /**
     * 提交用户名称
     */
    private String submitUserName;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 流程开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processStartTime;

    private String taskCategory1;

    private String taskCategory2;

    private String taskCategory3;

    private String taskTitle1;

    private String taskTitle2;

    private String taskTitle3;

    private String taskDesc;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;
}
