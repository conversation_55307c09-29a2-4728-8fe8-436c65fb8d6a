package com.wanlianyida.fssbase.process.interfaces.facade;

import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.process.application.service.FlowOperateAppService;
import com.wanlianyida.fssbase.process.application.service.FlowQueryAppService;
import com.wanlianyida.fssbase.process.application.service.TaskAppService;
import com.wanlianyida.fssbase.process.interfaces.facade.command.CompleteTaskCommand;
import com.wanlianyida.fssbase.process.interfaces.facade.command.CreateDeploymentCommand;
import com.wanlianyida.fssbase.process.interfaces.facade.command.ProcessStartCommand;
import com.wanlianyida.fssbase.process.interfaces.facade.command.SubmitAuditCommand;
import com.wanlianyida.fssbase.process.interfaces.facade.dto.*;
import com.wanlianyida.fssbase.process.interfaces.facade.query.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/flow")
public class FlowController {
    @Resource
    FlowQueryAppService flowQueryAppService;
    @Resource
    FlowOperateAppService flowOperateAppService;
    @Resource
    TaskAppService taskAppService;

    /**
     * 创建流程（根据流程编号）
     */
    @PostMapping("/startProcess")
    ResultMode<ProcessInstanceDTO> startProcess(@RequestBody @Valid ProcessStartCommand command) {
        return flowOperateAppService.startProcess(command);
    }

    /**
     * 提交审批任务
     */
    @PostMapping("/submitAudit")
    ResultMode<SubmitAuditResDTO> submitAudit(@RequestBody @Valid SubmitAuditCommand command) {
        return flowOperateAppService.submitAudit(command);
    }

    /**
     * 执行用户任务
     */
    @PostMapping("/completeTask")
    ResultMode<CompleteTaskResDTO> completeTask(@RequestBody @Valid CompleteTaskCommand command) {
        return flowOperateAppService.completeTask(command);
    }

    /**
     * 查询节点信息
     */
    @PostMapping("/queryAuditTaskList")
    ResultMode<AuditNodeResDTO> queryAuditTaskList(@RequestBody @Valid AuditTaskListQuery query) {
        return taskAppService.queryAuditTaskList(query);
    }


    /**
     * 查询节点表单信息
     */
    @PostMapping("/queryTaskForm")
    ResultMode<TaskFormResDTO> queryTaskForm(@RequestBody @Valid TaskFormQuery query){
        return taskAppService.queryTaskForm(query);
    }

    /**
     * 查询我的用户任务
     */
    @PostMapping("/queryTaskList")
    ResultMode<List<PsTaskListResDTO>> queryTaskList(@RequestBody @Valid PagingInfo<PsTaskListQuery> query) {
        return taskAppService.queryTaskList(query);
    }


    /**
     * 查询任务列表
     */
    @PostMapping("/queryTaskPage")
    ResultMode<List<PsTaskDTO>> queryTaskPage(@RequestBody @Valid PagingInfo<PsTaskQuery> query) {
        return taskAppService.queryTaskPage(query);
    }


    /**
     * 查询审批记录
     */
    @PostMapping("/queryAuditHistory")
    ResultMode<List<PsAuditRecordResDTO>> queryAuditHistory(@RequestBody @Valid PagingInfo<PsAuditRecordListQuery> query) {
        return taskAppService.queryAuditHistory(query);
    }

    /**
     * 部署流程
     */
    @PostMapping("/deployProcess")
    ResultMode<CreateDeploymentDTO> deployProcess(@RequestBody @Valid CreateDeploymentCommand command) {
        return flowOperateAppService.createDeployment(command);
    }

    /**
     * 获取流程实例详情
     */
    @GetMapping("/getProcessXml/{processInstanceId}")
    ResultMode<ProcessDetailDTO> getProcessXml(@PathVariable("processInstanceId") String processInstanceId) {
        return flowQueryAppService.queryRuntimeProcessDetail(processInstanceId, false);
    }

    /**
     * 查询运行中的流程详情
     */
    @PostMapping("/getRuntimeProcessDetail")
    ResultMode<ProcessDetailDTO> queryRuntimeProcessDetail(@RequestBody @Valid ProcessDetailQueryDTO queryDTO) {
        return flowQueryAppService.queryRuntimeProcessDetail(queryDTO.getProcessInstanceId(), queryDTO.getHasHighLightXml());
    }

}
