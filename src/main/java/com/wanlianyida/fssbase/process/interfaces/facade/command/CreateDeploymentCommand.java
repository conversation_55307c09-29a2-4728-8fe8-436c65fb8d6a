package com.wanlianyida.fssbase.process.interfaces.facade.command;

import com.wanlianyida.fssbase.process.interfaces.facade.command.resource.DeployResource;
import lombok.Data;

import java.util.List;

@Data
public class CreateDeploymentCommand {
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 部署名称
     */
    private String deployName;

    /**
     * 部署xml
     */
    private String xml;

    /**
     * 资源文件 form表单和dmn决策
     */
    private List<DeployResource> resources;
}
