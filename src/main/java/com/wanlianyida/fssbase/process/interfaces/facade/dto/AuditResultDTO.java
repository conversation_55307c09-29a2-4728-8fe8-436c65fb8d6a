package com.wanlianyida.fssbase.process.interfaces.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuditResultDTO {
    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 关联业务key
     */
    private String businessKey;

    /**
     * 任务完成结果[10-通过,20-未通过]
     */
    private String completeResult;

    /**
     * 任务完成用户id
     */
    private String completeUserId;

    /**
     * 任务完成用户名称
     */
    private String completeUserName;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 完成时间戳
     */
    private Long completeTimestamp;
}
