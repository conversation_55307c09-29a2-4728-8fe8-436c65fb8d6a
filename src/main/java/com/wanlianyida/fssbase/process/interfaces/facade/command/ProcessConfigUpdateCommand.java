package com.wanlianyida.fssbase.process.interfaces.facade.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotNull;

@Data
public class ProcessConfigUpdateCommand {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 流程类别 审批流程10 业务编排20
     */
    @NotNull(message = "流程类别不能为空")
    private String definitionCategory;

    /**
     * 流程编号
     */
    @NotNull(message = "流程编号不能为空")
    private String processNo;

    /**
     * 流程名称
     */
    @NotNull(message = "流程名称不能为空")
    private String processName;

    /**
     * 流程定义key
     */
    @NotNull(message = "流程定义key不能为空")
    private String definitionKey;

    /**
     * 流程定义名称
     */
    @NotNull(message = "流程定义名称不能为空")
    private String definitionName;

    /**
     * 平台类型 大宗商贸10 物流20
     */
    @NotNull(message = "平台类型不能为空")
    private String platformCode;

    /**
     * 备注
     */
    private String description;


    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

}
