package com.wanlianyida.fssbase.process.interfaces.facade.dto;

import lombok.Data;

import java.util.Map;

@Data
public class ProcessInstanceDTO {

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义id
     */
    private String processDefinitionId;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 自定义变量
     */
    private Map<String, Object> customVar;
}
