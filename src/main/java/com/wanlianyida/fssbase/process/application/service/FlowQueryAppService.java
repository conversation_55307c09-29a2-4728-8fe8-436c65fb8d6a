package com.wanlianyida.fssbase.process.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.framework.fsscommon.entity.PagingInfo;
import com.wanlianyida.framework.fsscommon.entity.ResultMode;
import com.wanlianyida.fssbase.process.application.assembler.AuditAssembler;
import com.wanlianyida.fssbase.process.domain.repository.HistoryRepo;
import com.wanlianyida.fssbase.process.domain.repository.ProcessInstanceRepo;
import com.wanlianyida.fssbase.process.domain.service.FlowDomainService;
import com.wanlianyida.fssbase.process.infrastructure.constant.VarConstant;
import com.wanlianyida.fssbase.process.infrastructure.util.ProcessUtil;
import com.wanlianyida.fssbase.process.interfaces.facade.dto.*;
import com.wanlianyida.fssbase.process.interfaces.facade.query.*;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstanceQuery;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ActivityInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.task.TaskQuery;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 任务处理
 */
@Component
@Slf4j
public class FlowQueryAppService {

    @Resource
    TaskService taskService;

    @Resource
    RuntimeService runtimeService;

    @Resource
    HistoryService historyService;

    @Resource
    FlowDomainService flowDomainService;

    @Resource
    RepositoryService repositoryService;

    @Resource
    ProcessInstanceRepo processInstanceRepo;

    @Resource
    HistoryRepo historyRepo;

    /**
     * 查询用户任务
     */
    public ResultMode<List<TaskWithVarDTO>> queryTaskPageList(PagingInfo<TaskPageQuery> pageInfo) {
        // 参数校验
        ResultMode<?> checkRes = pageQueryValidation(pageInfo);
        if (!checkRes.isSucceed()) {
            return ResultMode.fail(checkRes.getMessage());
        }
        // 组装查询对象
        TaskQuery taskQuery = createTaskQuery(pageInfo.getFilterModel());
        long total = taskQuery.count();
        List<Task> taskList = taskQuery.listPage(pageInfo.getPageLength() * (pageInfo.getCurrentPage() - 1), pageInfo.getPageLength());

        List<TaskWithVarDTO> taskDtoList = BeanUtil.copyToList(taskList, TaskWithVarDTO.class);
        // 获取流程变量
        completeTaskVars(taskDtoList);
        // 获取businessKey
        completeBusinessKeys(taskDtoList);
        return ResultMode.successPageList(taskDtoList, (int)total);
    }

    /**
     * 查询流程实例
     */
    public ResultMode<List<ProcessInstanceDTO>> queryHistoryInstanceList(PagingInfo<HistoryInstancePageQuery> pageInfo) {
        HistoryInstancePageQuery query = pageInfo.getFilterModel();
        HistoricProcessInstanceQuery instanceQuery = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionKey(query.getProcessKey());
        // 高级搜索
        ProcessUtil.fillAdvanceQuery(instanceQuery, query);
        long total = instanceQuery.count();
        List<HistoricProcessInstance> instances = instanceQuery.orderByProcessInstanceStartTime().desc()
                .listPage(pageInfo.getPageLength() * (pageInfo.getCurrentPage() - 1), pageInfo.getPageLength());
        List<ProcessInstanceDTO> ansList = AuditAssembler.toProcessInstanceDtoList(instances);
        // 流程变量
        completeProcessInstanceVars(ansList);
        return ResultMode.successPageList(ansList, (int)total);
    }

    /**
     * 查询流程实例变量-运行中和已完成
     */
    public ResultMode<PsVarsDTO> queryProcessVars(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance != null) {
            // 运行中的
            String processDefinitionId = processInstance.getProcessDefinitionId();
            ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);
            Map<String, Object> varList = processInstanceRepo.getProcessVar(processInstanceId);
            PsVarsDTO varDTO = PsVarsDTO.builder()
                    .customVar(varList)
                    .businessKey(processInstance.getBusinessKey())
                    .definitionId(processInstance.getProcessDefinitionId())
                    .definitionKey(processDefinition.getKey())
                    .definitionVersion(processDefinition.getVersion())
                    .processInstanceId(processInstanceId)
                    .build();
            return ResultMode.success(varDTO);
        } else {
            // 查询已完成流程的变量信息
            return queryHistoryProcessVars(processInstanceId);
        }
    }

    private ResultMode<PsVarsDTO> queryHistoryProcessVars(String processInstanceId) {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        Map<String, Object> vars = historyRepo.getHistoricProcessInstanceVar(processInstanceId);
        PsVarsDTO varDTO = PsVarsDTO.builder()
                .customVar(vars)
                .businessKey(processInstance.getBusinessKey())
                .definitionId(processInstance.getProcessDefinitionId())
                .definitionKey(processInstance.getProcessDefinitionKey())
                .definitionVersion(processInstance.getProcessDefinitionVersion())
                .processInstanceId(processInstanceId)
                .build();
        return ResultMode.success(varDTO);
    }

    private ResultMode<?> pageQueryValidation(PagingInfo<TaskPageQuery> pageInfo) {
        return ResultMode.success();
    }

    public ResultMode<ProcessDetailDTO> queryRuntimeProcessDetail(String processInstanceId, boolean hasHighLight) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (processInstance == null) {
            return ResultMode.fail("流程不存在");
        }

        String processDefinitionId = processInstance.getProcessDefinitionId();
        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);

        // 当前tasks
        List<Task> activeTaskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();

        // 获取当前活动节点
        ActivityInstance activityInstance = runtimeService.getActivityInstance(processInstanceId);
        List<ActivityInstance> activityInstanceList = Lists.newArrayList();
        getActivityInstances(activityInstance, activityInstanceList);

        // 解析 BPMN 模型
        InputStream bpmnResource = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(), processDefinition.getResourceName());
        BpmnModelInstance bpmnModel = Bpmn.readModelFromStream(bpmnResource);
        String xml = Bpmn.convertToString(bpmnModel);
        String highLightXml = "";
        if (hasHighLight) {
            org.camunda.bpm.model.bpmn.instance.Process process = bpmnModel.getModelElementsByType(Process.class).iterator().next();
            // 高亮显示当前活动节点
            List<FlowNode> flowNodes = process.getFlowElements().stream()
                    .filter(FlowNode.class::isInstance).map(FlowNode.class::cast).collect(Collectors.toList());
            // 活动节点id
            List<String> activeActivityIds = activityInstanceList.stream().map(ActivityInstance::getActivityId).collect(Collectors.toList());
            for (FlowNode flowNode : flowNodes) {
                if (activeActivityIds.contains(flowNode.getId())) {
                    flowNode.setAttributeValue("id", flowNode.getId() + "-highlighted");
                }
            }
            highLightXml = Bpmn.convertToString(bpmnModel);
        }

        // 变量
        Map<String, Object> varList = processInstanceRepo.getProcessVar(processInstanceId);
        // 数据
        ProcessDetailDTO processDetailDTO = AuditAssembler.processDetailDTO(processInstance, activeTaskList, activityInstanceList, varList, xml, highLightXml);
        processDetailDTO.setIsEnd(0);
        return ResultMode.success(processDetailDTO);
    }

    /**
     * 根据部署id查询流程图
     */
    public ResultMode<PsSimpleFlowChartDTO> queryDeploymentFlowChart(PsSimpleFlowCharQuery query) {
        String deploymentId = query.getDeploymentId();
        String xml = getXmlByDeployId(deploymentId);
        if (StrUtil.isEmpty(xml)) {
            return ResultMode.fail("配置未找到");
        }
        PsSimpleFlowChartDTO flowChartDTO = new PsSimpleFlowChartDTO();
        flowChartDTO.setChartXml(xml);
        return ResultMode.success(flowChartDTO);
    }

    private String getXmlByDeployId(String deploymentId) {
        List<ProcessDefinition> processDefinitions = repositoryService
                .createProcessDefinitionQuery()
                .deploymentId(deploymentId)
                .list();
        if (CollUtil.isEmpty(processDefinitions)) {
            return null;
        }
        ProcessDefinition processDefinition = processDefinitions.get(0);
        // 解析 BPMN 模型
        InputStream bpmnResource = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(), processDefinition.getResourceName());
        BpmnModelInstance bpmnModel = Bpmn.readModelFromStream(bpmnResource);
        String xml = Bpmn.convertToString(bpmnModel);
        return ProcessUtil.removeAllNewlines(xml);
    }

    /**
     * 查询流程图
     */
    public ResultMode<PsFlowChartDTO> queryRunningFlowChart(PsFlowCharQuery flowCharQuery) {
        String processInstanceId = flowCharQuery.getProcessInstanceId();
        boolean hasHighLight = flowCharQuery.getHighlight() == 1;

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        if (processInstance == null) {
            // 已结束的从历史查询
            List<HistoricProcessInstance> list = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).list();
            if (CollUtil.isEmpty(list)) {
                return ResultMode.fail("流程实例不存在");
            }
            HistoricProcessInstance processInstance1 = list.get(0);
            String processDefinitionId = processInstance1.getProcessDefinitionId();
            List<ProcessDefinition> definitionList = repositoryService.createProcessDefinitionQuery().processDefinitionId(processDefinitionId).list();
            if (CollUtil.isEmpty(definitionList)) {
                return ResultMode.fail("流程定义不存在");
            }
            String deploymentId = definitionList.get(0).getDeploymentId();
            String xml = getXmlByDeployId(deploymentId);
            PsFlowChartDTO flowChartDTO = new PsFlowChartDTO();
            flowChartDTO.setChartXml(ProcessUtil.removeAllNewlines(xml));
            // 变量
            flowChartDTO.setCustomVar(historyRepo.getHistoricProcessInstanceVar(processInstanceId));
            return ResultMode.success(flowChartDTO);
        }

        String processDefinitionId = processInstance.getProcessDefinitionId();
        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);
        // 获取当前活动节点
        ActivityInstance activityInstance = runtimeService.getActivityInstance(processInstanceId);
        List<ActivityInstance> activityInstanceList = Lists.newArrayList();
        getActivityInstances(activityInstance, activityInstanceList);

        // 解析 BPMN 模型
        InputStream bpmnResource = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(), processDefinition.getResourceName());
        BpmnModelInstance bpmnModel = Bpmn.readModelFromStream(bpmnResource);
        String xml = Bpmn.convertToString(bpmnModel);
        String highLightXml = "";
        if (hasHighLight) {
            org.camunda.bpm.model.bpmn.instance.Process process = bpmnModel.getModelElementsByType(Process.class).iterator().next();
            // 高亮显示当前活动节点
            List<FlowNode> flowNodes = process.getFlowElements().stream()
                    .filter(FlowNode.class::isInstance).map(FlowNode.class::cast).collect(Collectors.toList());
            // 活动节点id
            List<String> activeActivityIds = activityInstanceList.stream().map(ActivityInstance::getActivityId).collect(Collectors.toList());
            for (FlowNode flowNode : flowNodes) {
                if (activeActivityIds.contains(flowNode.getId())) {
                    flowNode.setAttributeValue("id", flowNode.getId() + "-highlighted");
                }
            }
            highLightXml = Bpmn.convertToString(bpmnModel);
        }
        // 变量
        Map<String, Object> varList = processInstanceRepo.getProcessVar(processInstanceId);
        PsFlowChartDTO flowChartDTO = new PsFlowChartDTO();
        flowChartDTO.setChartXml(ProcessUtil.removeAllNewlines(xml));
        flowChartDTO.setHighLightXml(ProcessUtil.removeAllNewlines(highLightXml));
        flowChartDTO.setCustomVar(varList);
        return ResultMode.success(flowChartDTO);

    }

    private void getActivityInstances(ActivityInstance activityInstance, List<ActivityInstance> ansList) {
        ansList.add(activityInstance);
        for (ActivityInstance childInstance : activityInstance.getChildActivityInstances()) {
            getActivityInstances(childInstance, ansList);
        }
    }

    private TaskQuery createTaskQuery(TaskPageQuery pageQuery) {
        TaskQuery taskQuery = taskService.createTaskQuery();
        // 企业id
        if (StrUtil.isNotEmpty(pageQuery.getCompanyId())) {
            taskQuery.processVariableValueEquals(VarConstant.VAR_COMPANY_ID, pageQuery.getCompanyId());
        }
        // 候选人条件
        completeCandidateQuery(taskQuery, pageQuery.getCandidates());
        // 高级搜索条件
        if (CollUtil.isNotEmpty(pageQuery.getAdvanceQueryList())) {
            ProcessUtil.completeAdvanceQuery(taskQuery, pageQuery.getAdvanceQueryList());
        }
        // 排序
        taskQuery.orderByTaskCreateTime().desc();
        return taskQuery;
    }

    private void completeCandidateQuery(TaskQuery taskQuery, CandidateQuery candidateQuery) {
        // 候选人
        TaskQuery orQuery = taskQuery.or();
        if (CollUtil.isNotEmpty(candidateQuery.getUserList())) {
            for (String s : candidateQuery.getUserList()) {
                orQuery.taskCandidateUser(s);
            }
        }
        // 候选部门
        if (CollUtil.isNotEmpty(candidateQuery.getDepartmentList())) {
            orQuery.taskCandidateGroupIn(candidateQuery.getDepartmentList());
        }
        orQuery.endOr();
    }

    /**
     * 设置businessKey
     */
    private void completeBusinessKeys(List<TaskWithVarDTO> taskWithVarDTOList) {
        for (TaskWithVarDTO taskWithVarDTO : taskWithVarDTOList) {
            Object key = taskWithVarDTO.getCustomVar().get(VarConstant.VAR_BUSINESS_KEY);
            if (key != null) {
                taskWithVarDTO.setBusinessKey((String)key);
            }
        }
    }

    /**
     * 获取任务变量
     */
    private void completeTaskVars(List<TaskWithVarDTO> taskWithVarDtoList) {
        for (TaskWithVarDTO taskWithVarDTO : taskWithVarDtoList) {
            taskWithVarDTO.setCustomVar(processInstanceRepo.getProcessVar(taskWithVarDTO.getProcessInstanceId()));
        }
    }

    /**
     * 获取任务变量
     */
    private void completeProcessInstanceVars(List<ProcessInstanceDTO> instanceList) {
        for (ProcessInstanceDTO dto : instanceList) {
            dto.setCustomVar(historyRepo.getHistoricProcessInstanceVar(dto.getProcessInstanceId()));
        }
    }
}
