package com.wanlianyida.fssbase.process.domain.repository;

import com.wanlianyida.fssbase.process.domain.model.bo.PsTaskBO;
import com.wanlianyida.fssbase.process.domain.model.condition.PsTaskCondition;
import com.wanlianyida.fssbase.process.domain.model.condition.PsTaskDetailCondition;
import com.wanlianyida.fssbase.process.domain.model.condition.PsTaskQueryListCondition;
import com.wanlianyida.fssbase.process.domain.model.entity.PsTaskEntity;

import java.util.List;

/**
 * <p>
 * 任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface PsTaskRepository {
    void insert(PsTaskEntity psTaskEntity);

    void update(PsTaskEntity psTaskEntity, String taskId);

    PsTaskEntity getDetail(PsTaskDetailCondition condition);

    List<PsTaskEntity> queryList(PsTaskQueryListCondition condition);

    List<PsTaskBO> queryAllTaskPage(PsTaskCondition psTaskCondition);
}
