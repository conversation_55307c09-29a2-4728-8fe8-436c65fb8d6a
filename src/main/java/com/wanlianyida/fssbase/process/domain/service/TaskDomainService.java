package com.wanlianyida.fssbase.process.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.fssbase.process.domain.model.bo.CandidateBO;
import com.wanlianyida.fssbase.process.domain.model.bo.PsTaskBO;
import com.wanlianyida.fssbase.process.domain.model.bo.PsTaskQueryListBO;
import com.wanlianyida.fssbase.process.domain.model.condition.*;
import com.wanlianyida.fssbase.process.domain.model.entity.PsAuditRecordEntity;
import com.wanlianyida.fssbase.process.domain.model.entity.PsProcessInstanceEntity;
import com.wanlianyida.fssbase.process.domain.model.entity.PsTaskEntity;
import com.wanlianyida.fssbase.process.domain.repository.PsAuditRecordRepository;
import com.wanlianyida.fssbase.process.domain.repository.PsProcessInstanceRepository;
import com.wanlianyida.fssbase.process.domain.repository.PsTaskRepository;
import com.wanlianyida.fssbase.process.domain.repository.VarRepository;
import com.wanlianyida.fssbase.process.infrastructure.constant.VarConstant;
import com.wanlianyida.fssbase.process.infrastructure.converter.CandidateConverter;
import com.wanlianyida.fssbase.process.infrastructure.converter.TaskConverter;
import com.wanlianyida.fssbase.process.infrastructure.enums.EngineTaskEventNameEnum;
import com.wanlianyida.fssbase.process.infrastructure.enums.TaskStatusEnum;
import com.wanlianyida.fssbase.process.interfaces.facade.command.CompleteTaskCommand;
import com.wanlianyida.fssbase.process.interfaces.facade.query.PsAuditRecordListQuery;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.spring.boot.starter.event.TaskEvent;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Service
@Slf4j
public class TaskDomainService {

    @Resource
    PsTaskRepository psTaskRepository;
    @Resource
    PsProcessInstanceRepository psProcessInstanceRepository;
    @Resource
    VarRepository varRepository;
    @Resource
    PsAuditRecordRepository psAuditRecordRepository;

    /**
     * 更新任务表状态+插入审核流水
     * @param command command
     */
    public void updateTask(CompleteTaskCommand command, Long completeTimestamp) {
        Date completeDate = new Date(completeTimestamp);
        // 更新任务表
        PsTaskEntity updateEntity = new PsTaskEntity();
        updateEntity.setTaskStatus(TaskStatusEnum.TYPE_20.getType());
        updateEntity.setCompleteTime(completeDate);
        updateEntity.setCompleteResult(command.getCompleteResult());
        updateEntity.setCompleteUserId(command.getCompleteUserId());
        updateEntity.setCompleteUserName(command.getCompleteUserName());
        psTaskRepository.update(updateEntity, command.getTaskId());
        // 插入审核流水
        PsTaskEntity taskEntity = psTaskRepository.getDetail(PsTaskDetailCondition.builder().taskId(command.getTaskId()).build());
        PsAuditRecordEntity recordEntity = TaskConverter.toPsAuditRecordEntity(taskEntity, command);
        recordEntity.setCompleteTime(completeDate);
        psAuditRecordRepository.insert(recordEntity);
    }

    /**
     * 处理任务事件
     * @param delegateTask delegateTask
     */
    public void processTaskEvent(DelegateTask delegateTask) {
        if (EngineTaskEventNameEnum.CREATE.getName().equals(delegateTask.getEventName())) {
            processTaskCreate(delegateTask);
        }
        else if (EngineTaskEventNameEnum.COMPLETE.getName().equals(delegateTask.getEventName())) {
            processTaskComplete(delegateTask);
        }
    }

    /**
     * 查询任务列表
     * @param query query
     * @return List<PsTaskEntity>
     */
    public List<PsTaskEntity> queryTaskList(PsTaskQueryListBO query) {
        PsTaskQueryListCondition queryListCondition = BeanUtil.toBean(query, PsTaskQueryListCondition.class);
        return psTaskRepository.queryList(queryListCondition);
    }

    /**
     * 查询审批记录
     * @param query query
     * @return List<PsAuditRecordEntity>
     */
    public List<PsAuditRecordEntity> queryAuditHistory(PsAuditRecordListQuery query) {
        return psAuditRecordRepository.queryList(BeanUtil.toBean(query, PsAuditHistoryCondition.class));
    }

    /**
     * 处理任务创建事件
     * @param delegateTask delegateTask
     */
    private void processTaskCreate(DelegateTask delegateTask) {
        // 获取变量
        Map<String, Object> varMap = varRepository.getProcessRuntimeVar(delegateTask.getProcessInstanceId());
        // 设置当前任务候选人
        delegateTask.addCandidateUsers(getCandidateUsers(delegateTask.getProcessInstanceId(), varMap));

        // 处理任务表
        TaskEvent taskEvent = new TaskEvent(delegateTask);
        PsProcessInstanceQueryCondition condition = new PsProcessInstanceQueryCondition();
        condition.setProcessInstanceId(delegateTask.getProcessInstanceId());
        // 查询流程实例
        PsProcessInstanceEntity detail = psProcessInstanceRepository.getDetail(condition);
        // 组装任务数据
        PsTaskEntity entity = TaskConverter.toPsTaskEntity(taskEvent, detail, varMap);
        // 任务数据入库
        psTaskRepository.insert(entity);
        log.info("======用户任务新增===== {}", JSONUtil.toJsonStr(entity));
    }

    /**
     * 处理任务完成事件
     * @param delegateTask delegateTask
     */
    private void processTaskComplete(DelegateTask delegateTask) {
        PsTaskEntity updateEntity = new PsTaskEntity();
        updateEntity.setTaskStatus(TaskStatusEnum.TYPE_20.getType());
        updateEntity.setCompleteTime(new Date());
        psTaskRepository.update(updateEntity, delegateTask.getId());
        log.info("======用户任务更新完成===== {}", JSONUtil.toJsonStr(updateEntity));
    }

    /**
     * 获取候选人id列表
     * @param processInstId processInstId
     */
    private List<String> getCandidateUsers(String processInstId, Map<String, Object> varMap) {
        // 从变量中取候选人信息
        String candidates = (String)varMap.get(VarConstant.VAR_CANDIDATES);
        CandidateBO candidateBO = JSONUtil.toBean(candidates, CandidateBO.class);
        return CandidateConverter.toCandidateUserIdList(candidateBO);
    }

    /**
     * 查询任务列表分页
     * @param psTaskCondition
     * @return
     */
    public List<PsTaskBO> queryAllTaskPage(PsTaskCondition psTaskCondition) {
        return psTaskRepository.queryAllTaskPage(psTaskCondition);
    }
}
