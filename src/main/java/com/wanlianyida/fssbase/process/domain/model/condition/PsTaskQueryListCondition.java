package com.wanlianyida.fssbase.process.domain.model.condition;

import lombok.Data;

import java.util.List;

@Data
public class PsTaskQueryListCondition {
    /**
     * 任务id列表
     */
    private List<String> taskIdList;

    /**
     * 执行用户id
     */
    private String userId;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 流程编号列表
     */
    private List<String> processNoList;

    /**
     * 任务状态[10-未处理,20-已处理]
     */
    private String taskStatus;

    /**
     * 高级搜索
     */
    private List<AdvanceQueryCondition> advanceQueryList;
}
