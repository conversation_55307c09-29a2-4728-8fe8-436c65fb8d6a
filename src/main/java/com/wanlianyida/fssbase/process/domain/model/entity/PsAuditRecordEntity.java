package com.wanlianyida.fssbase.process.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 审批记录实体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
public class PsAuditRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 企业id
     */
    private String companyId;
    /**
     * 任务id
     */
    private String taskId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 关联业务key
     */
    private String businessKey;
    /**
     * 任务完成用户id
     */
    private String completeUserId;

    /**
     * 任务完成用户名称
     */
    private String completeUserName;

    /**
     * 任务完成时间
     */
    private Date completeTime;

    /**
     * 任务完成结果[10-通过,20-未通过]
     */
    private String completeResult;

    /**
     * 任务完成备注
     */
    private String completeComment;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 提交用户id
     */
    private String submitUserId;

    /**
     * 提交用户名称
     */
    private String submitUserName;

    /**
     * 任务状态[10-未处理,20-已处理]
     */
    private String taskStatus;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer deleted;


}
