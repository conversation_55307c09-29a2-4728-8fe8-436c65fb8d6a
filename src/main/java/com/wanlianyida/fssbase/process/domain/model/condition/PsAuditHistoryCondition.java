package com.wanlianyida.fssbase.process.domain.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PsAuditHistoryCondition {
    /**
     * 企业id
     */
    private String companyId;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 关联业务key
     */
    private String businessKey;
}
