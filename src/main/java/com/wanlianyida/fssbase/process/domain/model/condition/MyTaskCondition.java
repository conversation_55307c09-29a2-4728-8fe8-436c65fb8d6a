package com.wanlianyida.fssbase.process.domain.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询我的任务条件
 */

 @Data
 @Builder
 @AllArgsConstructor
 @NoArgsConstructor
public class MyTaskCondition {
    List<String> userIdList;

    List<String> groupIdList;

    List<String> processDefinitionKeyList;
}
