package com.wanlianyida.fssbase.process.domain.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PsActivityBO {
    /** 任务ID (act_hi_actinst.ID_) */
    private String activityId;

    /** 任务名称 (act_hi_actinst.ACT_NAME_) */
    private String activityName;

    /** 任务名称 (act_hi_actinst.ACT_TYPE_) */
    private String activityType;

    /** 任务名称 (act_hi_actinst.SEQUENCE_COUNTER_) */
    private String sequenceCounter;

    /** 流程定义ID (格式：key:version:randomId) */
    private String definitionId;

    /** 流程定义KEY (BPMN文件中的id属性) */
    private String definitionKey;

    /** 部署ID (对应ACT_RE_PROCDEF.DEPLOYMENT_ID_) */
    private String deploymentId;

    /** 部署版本号 (对应ACT_RE_PROCDEF.VERSION_) */
    private Integer deploymentVersion;

    /** 任务开始时间 (UTC时区存储) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /** 任务结束时间 (未完成时为空) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /** 任务负责人 (用户ID，需关联用户表扩展) */
    private String assignee;

    /** 任务状态：10进行中/20已挂起/30已完成 */
    private String activityStatus;

    /**流程实例id**/
    private String processInstanceId;

    private String businessKey;
}

