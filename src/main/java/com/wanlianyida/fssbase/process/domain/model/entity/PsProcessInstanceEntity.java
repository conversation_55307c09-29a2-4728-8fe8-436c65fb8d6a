package com.wanlianyida.fssbase.process.domain.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 流程实例实体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
public class PsProcessInstanceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程实例id
     */
    private Long id;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 流程定义名称
     */
    private String definitionName;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 关联业务key
     */
    private String businessKey;

    /**
     * 运行状态
     * @see com.wanlianyida.fssbase.process.infrastructure.enums.RunStatusFlagEnum
     */
    private Integer runStatus;

    /**
     * 提交用户id
     */
    private String submitUserId;

    /**
     * 提交用户名称
     */
    private String submitUserName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 创建用户名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer deleted;
}
