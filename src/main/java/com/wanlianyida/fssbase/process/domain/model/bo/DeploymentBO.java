package com.wanlianyida.fssbase.process.domain.model.bo;

import lombok.Data;

import java.util.Date;

@Data
public class DeploymentBO {

    /**
     * 流程类别 审批流程10 业务编排20
     */
    private String definitionCategory;


    /**
     * 流程定义名称
     */
    private String definitionName;


    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 部署id
     */
    private String deploymentId;

    /**
     * 部署版本
     */
    private String deployVersion;

    /**
     * 部署时间
     */
    private Date deployTime;

    /**
     * 部署状态
     */
    private Integer status;
}
