package com.wanlianyida.fssbase.process.domain.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

 @Data
 @Builder
 @AllArgsConstructor
 @NoArgsConstructor
public class ProcessStartBO {
    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 流程定义key
     */
    private String processDefinitionKey;

    /**
     * 业务变量
     */
    private Map<String, Object> customVar;
}
