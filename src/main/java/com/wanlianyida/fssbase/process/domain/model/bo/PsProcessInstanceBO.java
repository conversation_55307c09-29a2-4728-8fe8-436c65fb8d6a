package com.wanlianyida.fssbase.process.domain.model.bo;

import lombok.Data;

import java.util.Date;

@Data
public class PsProcessInstanceBO {

    /**
     * 流程实例id
     */
    private String processInstanceId;


    /**
     * 流程名称
     */
    private String processName;

    private String definitionKey;

    private String definitionId;

    private String deploymentId;

    private String deploymentVersion;


    /**
     * 当前活动节点id
     */
    private String activityId;

    /**
     * 当前活动节点名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 流程状态 10运行中 20挂起
     */
    private String state;

    /**
     * 是否结束 1是 0否
     */
    private Integer ended;
}
