package com.wanlianyida.fssbase.process.domain.model.bo;

import com.wanlianyida.fssbase.process.interfaces.facade.command.candidate.CandidateCommand;
import lombok.Data;

import java.util.Map;

import javax.validation.constraints.NotEmpty;

/**
 * 审批操作
 */
@Data
public class CompleteTaskBO {
    /**
     * 任务id
     */
    @NotEmpty(message = "任务id不能为空")
    private String taskId;

    /**
     * 任务完成结果[10-通过,20-未通过]
     */
    @NotEmpty(message = "任务完成结果不能为空")
    private String completeResult;

    /**
     * 完成用户id
     */
    @NotEmpty(message = "completeUserId不能为空")
    private String completeUserId;

    /**
     * 完成用户名称
     */
    @NotEmpty(message = "completeUserName不能为空")
    private String completeUserName;

    /**
     * 审批备注（可选）
     */
    private String completeComment;

    /**
     * 业务变量（可选）
     */
    private Map<String, Object> customVar;

    /**
     * 下一节点的：候选处理人/组（可选）
     */
    private CandidateCommand candidate;

    private Long completeTimestamp;
}
