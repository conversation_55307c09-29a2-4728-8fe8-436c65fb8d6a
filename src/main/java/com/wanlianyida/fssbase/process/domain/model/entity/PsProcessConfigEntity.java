package com.wanlianyida.fssbase.process.domain.model.entity;

import lombok.Data;

import java.util.Date;

/**
 * 流程配置表
 */
@Data
public class PsProcessConfigEntity {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 流程类别 审批流程10 业务编排20
     */
    private String definitionCategory;

    /**
     * 流程编号
     */
    private String processNo;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 流程定义名称
     */
    private String definitionName;

    /**
     * 平台类型 大宗商贸10 物流20
     */
    private String platformCode;

    /**
     * 备注
     */
    private String description;

    /**
     * 启用状态
     */
    private Integer enabledStatus;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;
}
