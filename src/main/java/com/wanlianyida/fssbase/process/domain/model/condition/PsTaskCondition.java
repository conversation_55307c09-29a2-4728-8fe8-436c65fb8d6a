package com.wanlianyida.fssbase.process.domain.model.condition;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PsTaskCondition {


    /**
     * 任务名称
     */
    private String taskName;


    /**
     * 流程发起开始时间
     */
    private Date processStartTimeBegin;

    /**
     * 流程发起结束时间
     */
    private Date processStartTimeEnd;


    /**
     * 任务接收开始时间
     */
    private Date taskCreateTimeBegin;


    /**
     * 任务接收结束时间
     */
    private Date taskCreateTimeEnd;


    /**
     * 任务办理开始时间
     */
    private Date taskCompleteTimeBegin;


    /**
     * 任务办理结束时间
     */
    private Date taskCompleteTimeEnd;


    /**
     * 任务状态 [10-待办,20-已办]
     */
    private String taskStatus;



    /**
     * 用户id
     */
    private List<String> userIds;

}
