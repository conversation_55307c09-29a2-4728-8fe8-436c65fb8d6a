package com.wanlianyida.fssbase.process.domain.model.bo;

import lombok.Data;

import java.util.Date;

@Data
public class PsTaskBO {

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务名称
     */
    private String taskName;


    /**
     * 业务key
     */
    private String businessKey;


    /**
     * 流程发起人
     */
    private String startUserId;


    /**
     * 流程发起时间
     */
    private Date processStartTime;


    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务审批人
     */
    private String taskUserId;


    /**
     * 任务开始时间
     */
    private Date taskStartTime;


    /**
     * 任务结束时间
     */
    private Date taskEndTime;


    /**
     * 流程状态
     */
    private String processStatus;


    /**
     * 流程编号
     */
    private String processNo;

}
