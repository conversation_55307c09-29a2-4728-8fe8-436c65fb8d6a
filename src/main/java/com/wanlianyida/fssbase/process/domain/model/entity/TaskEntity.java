package com.wanlianyida.fssbase.process.domain.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Map;

@Data
public class TaskEntity {
    private String id;

    private String executionId;

    private String processInstanceId;

    private String processDefinitionId;

    private String businessKey;

    private String name;

    private String taskDefinitionKey;

    private String owner;

    private String assignee;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Integer suspensionState;

    /**
     * 变量列表
     */
    private Map<String, Object> customVar;
}
