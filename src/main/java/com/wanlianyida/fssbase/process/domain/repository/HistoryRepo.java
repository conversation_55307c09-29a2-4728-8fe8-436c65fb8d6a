package com.wanlianyida.fssbase.process.domain.repository;

import com.wanlianyida.fssbase.process.domain.model.entity.ProcessInstanceEntity;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;

import java.util.List;
import java.util.Map;

public interface HistoryRepo {
    ProcessInstanceEntity getProcessInstance(String processInstanceId);

    ProcessInstanceEntity getProcessInstanceWithVar(String processInstanceId);

    HistoricProcessInstance getHistoricProcessInstance(String processInstanceId);

    Map<String, Object> getHistoricProcessInstanceVar(String processInstanceId);

    List<HistoricActivityInstance> getHistoricActivityInstance(String processInstanceId);
}
