package com.wanlianyida.fssbase.process.domain.model.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ActRuExecutionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 执行实例的唯一标识符
     */
    private String id;

    /**
     * 版本号，用于记录数据的修订版本
     */
    private Integer rev;

    /**
     * 根流程实例的 ID
     */
    private String rootProcInstId;

    /**
     * 流程实例的 ID
     */
    private String procInstId;

    /**
     * 业务键，用于关联业务数据
     */
    private String businessKey;

    /**
     * 父执行实例的 ID
     */
    private String parentId;

    /**
     * 流程定义的 ID
     */
    private String procDefId;

    /**
     * 上级执行实例的 ID
     */
    private String superExec;

    /**
     * 上级案例执行实例的 ID
     */
    private String superCaseExec;

    /**
     * 案例实例的 ID
     */
    private String caseInstId;

    /**
     * 活动的 ID
     */
    private String actId;

    /**
     * 活动实例的 ID
     */
    private String actInstId;

    /**
     * 表示执行实例是否处于活动状态的标志
     */
    private Boolean isActive;

    /**
     * 表示执行实例是否并发的标志
     */
    private Boolean isConcurrent;

    /**
     * 表示执行实例是否为作用域的标志
     */
    private Boolean isScope;

    /**
     * 表示执行实例是否为事件作用域的标志
     */
    private Boolean isEventScope;

    /**
     * 暂停状态
     */
    private Integer suspensionState;

    /**
     * 缓存的实体状态
     */
    private Integer cachedEntState;

    /**
     * 序列计数器
     */
    private Long sequenceCounter;

    /**
     * 租户 ID
     */
    private String tenantId;
}
