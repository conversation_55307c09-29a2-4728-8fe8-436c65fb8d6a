package com.wanlianyida.fssbase.process.domain.model.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 审批记录查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
public class PsAuditRecordListQueryBO {
    /**
     * 企业id
     */
    @NotEmpty(message = "企业id不能为空")
    private String companyId;


    /**
     * 流程编号
     */
    @NotEmpty(message = "流程编号不能为空")
    private String processNo;


    /**
     * 关联业务key
     */
    @NotEmpty(message = "业务id不能为空")
    private String businessKey;
}
