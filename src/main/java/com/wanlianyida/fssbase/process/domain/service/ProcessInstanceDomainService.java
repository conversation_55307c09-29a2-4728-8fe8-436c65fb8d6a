package com.wanlianyida.fssbase.process.domain.service;

import com.wanlianyida.fssbase.process.domain.model.condition.PsProcessInstanceQueryCondition;
import com.wanlianyida.fssbase.process.domain.model.entity.PsProcessInstanceEntity;
import com.wanlianyida.fssbase.process.domain.repository.PsProcessInstanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class ProcessInstanceDomainService {
    @Resource
    PsProcessInstanceRepository psProcessInstanceRepository;

    public void insertPsProcessInstance(PsProcessInstanceEntity entity) {
        psProcessInstanceRepository.insert(entity);
    }

    public void updatePsProcessInstance(PsProcessInstanceEntity entity, String procInstId) {
        psProcessInstanceRepository.update(entity, procInstId);
    }

    public PsProcessInstanceEntity getDetail(String procInstId) {
        return psProcessInstanceRepository.getDetail(PsProcessInstanceQueryCondition.builder().processInstanceId(procInstId).build());
    }

    public List<PsProcessInstanceEntity> getListByCondition(PsProcessInstanceQueryCondition condition) {
        return psProcessInstanceRepository.list(condition);
    }

}
