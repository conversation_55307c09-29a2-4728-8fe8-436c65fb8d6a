package com.wanlianyida.fssbase.process.domain.model.condition;

import lombok.Data;

@Data
public class AdvanceQueryCondition {
    /**
     * 变量key
     */
    private String fieldKey;

    /**
     * 操作符 eq, in, lt, gt, lte, gte, between
     * @see com.wanlianyida.fssbase.process.infrastructure.enums.OperatorEnum
     */
    private String operator;

    /**
     * 变量值
     */
    private Object searchValue;
}
