package com.wanlianyida.fssbase.process.domain.model.entity;

import lombok.Data;

import java.util.Date;

@Data
public class PsActivityConfigEntity {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 流程定义id
     */
    private String definitionId;

    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 流程部署id
     */
    private String deployId;

    /**
     * 节点id
     */
    private String activityId;

    /**
     * 节点名称
     */
    private String activityName;

    /**
     * 配置类型 10无 20角色 30岗位 40自定义
     */
    private String configType;

    /**
     * 是否为开始节点1是 0否
     */
    private Integer started;

    /**
     * 节点自定义参数
     */
    private String param;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;
}
