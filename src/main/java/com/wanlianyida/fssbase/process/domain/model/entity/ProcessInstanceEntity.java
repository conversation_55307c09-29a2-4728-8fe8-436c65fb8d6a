package com.wanlianyida.fssbase.process.domain.model.entity;

import lombok.Data;

import java.util.Map;

@Data
public class ProcessInstanceEntity {
    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程定义id
     */
    private String processDefinitionId;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 自定义变量
     */
    private Map<String, Object> customVar;
}
