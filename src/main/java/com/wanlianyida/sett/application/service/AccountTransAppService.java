package com.wanlianyida.sett.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.exter.api.finance.inter.PaycenterTradeInter;
import com.wanlianyida.exter.api.finance.model.command.CashOutCommand;
import com.wanlianyida.exter.api.finance.model.dto.CashOutDTO;
import com.wanlianyida.exter.api.finance.model.dto.FinanceResultMode;
import com.wanlianyida.framework.cache.lock.RedissonDistributedLocker;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.application.assembler.AccountTransAssembler;
import com.wanlianyida.sett.domain.model.bo.AccountTransBO;
import com.wanlianyida.sett.domain.model.condition.AccountTransCondition;
import com.wanlianyida.sett.domain.model.condition.SettOrderSettlementCondition;
import com.wanlianyida.sett.domain.model.condition.SettPaymentCondition;
import com.wanlianyida.sett.domain.model.entity.*;
import com.wanlianyida.sett.domain.service.*;
import com.wanlianyida.sett.infrastructure.constant.CommonConstant;
import com.wanlianyida.sett.infrastructure.enums.WithdrawalStatusEnum;
import com.wanlianyida.sett.infrastructure.properties.PlatformProperties;
import com.wanlianyida.sett.interfaces.model.command.AccountTransApplyCommand;
import com.wanlianyida.sett.interfaces.model.command.callback.BankWithdrawCallBackCommand;
import com.wanlianyida.sett.interfaces.model.dto.AccountTransApplyDetailDTO;
import com.wanlianyida.sett.interfaces.model.dto.AccountTransApplyListDTO;
import com.wanlianyida.sett.interfaces.model.dto.IdDTO;
import com.wanlianyida.sett.interfaces.model.dto.InvoiceApplyListDTO;
import com.wanlianyida.sett.interfaces.model.query.AccountTransListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

/**
 * 账户交易
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccountTransAppService {

    @Resource
    private AccountTransDomainService accountTransDomainService;
    @Resource
    private BankBackDomainService bankBackDomainService;
    @Resource
    private AccountTransAssembler accountTransAssembler;
    @Resource
    private PaycenterTradeInter paycenterTradeInter;
    @Resource
    private SettAcctAccountDomainService settAcctAccountDomainService;
    @Resource
    private PlatformProperties platformProperties;

    @Resource
    private SettOrderSettlementQueryDomainService settOrderSettlementQueryDomainService;

    @Resource
    private RedissonDistributedLocker redissonDistributedLocker;

    @Resource
    private SettPaymentDomainService settPaymentDomainService;

    /**
     * 账户交易列表查询
     */
    public ResultMode<List<AccountTransApplyListDTO>> listPage(PagingInfo<AccountTransListQuery> query) {
        AccountTransCondition condition = BeanUtil.copyProperties(query.getFilterModel(), AccountTransCondition.class);
        //分页拦截
        Page<InvoiceApplyListDTO> page = PageHelper.startPage(query.currentPage, query.pageLength, query.getCountTotal());
        page.setOrderBy("created_date desc");
        List<AccountTransApplyEntity> list = accountTransDomainService.queryCondition(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, AccountTransApplyListDTO.class), (int) page.getTotal());

    }

    /**
     * 提现
     */
    public ResultMode<IdDTO> cashOut(AccountTransApplyCommand command) {
        // 使用分布式锁防止并发操作同一个订单号
        String lockKey = CommonConstant.SUBMIT_CASH_RECEIVED_LOCK_PREFIX + command.getOrderNo();
        boolean lock = redissonDistributedLocker.tryLock(lockKey, -1, -1, TimeUnit.SECONDS);
        if (!lock) {
            return ResultMode.fail(); // 获取锁失败，返回操作失败结果
        }
        try {
            // 1、校验提现参数
            SettAcctCardBindEntity cardBindEntity = bankBackDomainService.getDefaultCard(command.getCompanyId(), command.getBankChannelName());
            if (cardBindEntity == null) {
                return ResultMode.fail("未绑定银行卡！");
            }
            if (!StrUtil.equals(command.getReceiverBankAccountNo(), cardBindEntity.getBankAccountNo())) {
                return ResultMode.fail("提现银行卡与绑定银行卡不一致！");
            }

            List<SettOrderSettlementEntity> settOrderSettlementEntities = settOrderSettlementQueryDomainService.queryCondition(SettOrderSettlementCondition.builder().orderNo(command.getOrderNo()).build());
            SettOrderSettlementEntity settOrderSettlementEntity = settOrderSettlementEntities.stream().findFirst().get();
            if (Arrays.asList(WithdrawalStatusEnum.STATUS_30.getStatus(),
                            WithdrawalStatusEnum.STATUS_40.getStatus(),
                            WithdrawalStatusEnum.STATUS_50.getStatus()).
                    contains(settOrderSettlementEntity.getWithdrawalStatus()))
                return ResultMode.fail("提现申请已处理！");

            // 2、数据封装
            AccountTransBO bo = accountTransAssembler.buildCashOutBO(command, cardBindEntity);

            // 3、银行提现
            AcctAccountEntity acctAccountEntity = settAcctAccountDomainService.getAcctAccountBySocialCreditCode(platformProperties.getSocialCreditCode());

            //步骤4 查询付款单信息
            List<SettPaymentEntity> settPaymentList = settPaymentDomainService.listByCondition(SettPaymentCondition.builder().orderNo(command.getOrderNo()).build());
            if (CollectionUtils.isEmpty(settPaymentList))
                return ResultMode.fail("未找到付款单信息！");
            String payerCompanyName = settPaymentList.get(0).getPayerCompanyName();
            CashOutCommand cashOutCommand = accountTransAssembler.buildBankCashOutCommand(bo.getApply(),
                    acctAccountEntity.getCompanyId(), "付款人:"+payerCompanyName);
            log.info("cashOut银行提现入参：{}", JSONUtil.toJsonStr(cashOutCommand));
            FinanceResultMode<CashOutDTO> financeResultMode = paycenterTradeInter.cashOut(cashOutCommand);
            log.info("cashOut银行提现返参：{}", JSONUtil.toJsonStr(financeResultMode));
            if (!financeResultMode.isSucceed()) {
                return ResultMode.fail("银行提现失败！");
            }
            // 4、数据存储
            accountTransDomainService.cashOut(bo);
            return ResultMode.success(IdDTO.builder().id(bo.getApply().getId().toString()).build());
        } catch (Exception e) {
            // 捕获异常并记录日志
            log.error("updateSettPayment线下支付上传支付凭证提交#异常:", e);
            return ResultMode.fail(e.getMessage()); // 返回操作失败结果
        } finally {
            redissonDistributedLocker.unlock(lockKey); // 释放分布式锁
        }
    }

    /**
     * 银行提现回调
     */
    public ResultMode withdrawCallBack(BankWithdrawCallBackCommand command) {
        accountTransDomainService.withdrawCallBack(accountTransAssembler.buildWithdrawCallBackEntity(command));
        return ResultMode.success();
    }

    /**
     * 提现详情
     */
    public ResultMode<AccountTransApplyDetailDTO> getDetail(IdQuery query) {
        // 查询提现详情
        AccountTransBO bo = accountTransDomainService.getDetail(query.getId());
        SettAcctCardBindEntity cardBindEntity = bankBackDomainService.getByBankAccountNo(bo.getApply().getReceiverBankAccountNo(), bo.getApply().getBankChannelName());
        // 数据封装
        return ResultMode.success(accountTransAssembler.buildAccountTransDetailDTO(bo, cardBindEntity));
    }

    /**
     * 记录下载电子回单数
     */
    public void counting(List<String> bankFlowNos) {
        accountTransDomainService.counting(bankFlowNos);
    }

    /**
     * 获取平台id
     */
    public String getPlatformId() {
        AcctAccountEntity acctAccountEntity = settAcctAccountDomainService.getAcctAccountBySocialCreditCode(platformProperties.getSocialCreditCode());
        if (acctAccountEntity == null) {
            return null;
        }
        return acctAccountEntity.getCompanyId();
    }

}
