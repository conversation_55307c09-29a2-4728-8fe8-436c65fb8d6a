package com.wanlianyida.sett.interfaces.model.command;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单结算商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
public class SettlementApplyProductCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;


    private Long orderSettlementDetailId;


    private Long orderProductId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商品编号
     */

    private String skuCode;

    /**
     * sku名称
     */

    private String skuName;

    /**
     * 结算数量
     */
    private BigDecimal smentQuantity;

    /**
     * 结算单价
     */
    private BigDecimal smentPrice;

    /**
     * 结算小计
     */
    private BigDecimal sementSubtotal;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 单位转换
     */
    private BigDecimal unitCon;


    /**
     * 申请结算单号
     */
    private String settApplyNo;

    /**
     * 提货单号
     */
    private String pickupNo;


    /**
     * 合同单价
     */
    private BigDecimal contractUnitPrice;


    /**
     * 发货数量
     */
    private BigDecimal shipmentQty;

    /**
     * 收货/提货数量
     */
    private BigDecimal receiveQty;

    /**
     * 调差
     */
    private BigDecimal adjustAmount;

    /**
     * 应付结算金额
     */
    private BigDecimal payableSettAmount;
}
