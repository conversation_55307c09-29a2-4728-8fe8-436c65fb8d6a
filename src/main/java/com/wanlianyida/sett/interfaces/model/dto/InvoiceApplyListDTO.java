package com.wanlianyida.sett.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票列表返回dto
 * <AUTHOR>
 **/

@Data
public class InvoiceApplyListDTO {


    @JsonSerialize(using = ToStringSerializer.class)
    private Long  id;

    /**
     * 申请单号
     */
    private String  applyNo;

    /**
     *买家公司ID
     */
    private String buyerCompanyId;

    /**
     *买家公司名称
     */
    private String buyerCompanyName;

    /**
     *卖家公司ID
     */
    private String sellerCompanyId;

    /**
     *买家公司名称
     */
    private String sellerCompanyName;

    /**
     * 发票号码
     */
    private String  invoiceNo;

    /**
     * 发票数量
     */
    private Integer  invoiceQty;

    /**
     *结算金额
     */
    private BigDecimal settAmount;

    /**
     *价税总额
     */

    private BigDecimal  totalAmount;

    /**
     *不含税总额
     */
    private BigDecimal  withoutTaxTotalAmount;

    /**
     *税额
     */
    private BigDecimal  taxTotalAmount;

    /**
     *开票状态
     */
    private String issueInvoiceStatus;

    /**
     *创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
