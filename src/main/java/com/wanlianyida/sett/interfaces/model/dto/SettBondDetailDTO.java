package com.wanlianyida.sett.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/24 09:38
 * @description: TODO
 */
@Data
public class SettBondDetailDTO {

    /**
     * 保证金id
     */
    private String id;

    /**
     * 保证金单据号
     */
    private String paymentNo;

    /**
     * 业务单据号
     */
    private String orderNo;

    /**
     * 交易类型[10-预付款；20-尾款；30-询比价保证金]
     */
    private Integer transactionType;

    /**
     * 应付保证金金额
     */
    private BigDecimal payableAmount;

    /**
     * 实付保证金金额
     */
    private BigDecimal paidAmount;

    /**
     * 保证金状态[110-待付款；120-待确认收款；130-已付款；140-确认收款驳回]
     */
    private Integer paymentStatus;

    /**
     * 付款公司名称
     */
    private String payerCompanyName;

    /**
     * 付款方银行账号
     */
    private String payerBankAccountNumber;

    /**
     * 付款方银行联行号
     */
    private String payerBankUnionNumber;

    /**
     * 付款方开户名称
     */
    private String payerAccountName;

    /**
     * 付款方银行名称
     */
    private String payerBankName;


    /**
     * 收款方开户名称
     */
    private String receiverAccountName;

    /**
     * 收款方银行账号
     */
    private String receiverBankAccountNumber;

    /**
     * 收款方银行名称
     */
    private String receiverBankName;

    /**
     * 收款方银行联行号
     */
    private String receiverBankUnionNumber;

    /**
     * 收款公司名称
     */
    private String receiverCompanyName;

    /**
     * 付款方式[20-银行承兑30-商业承兑40-电汇(电子钱包)50-电汇(银行账户)]
     */
    private String paymentMethod;

    /**
     * 付款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paymentTime;

    /**
     * 确认收款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiptConfirmTime;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 银行流水号
     */
    private String bankFlowNo;

    /**
     * 平台流水号
     */
    private String platformFlowNo;

    /**
     * 支付失败原因/驳回原因
     */
    private String payFailReason;

    /**
     * 付款凭证
     */
    private List<SettPaymentAttachment> settPaymentAttachmentList;

    /**
     * 付款单对应多个附件
     */
    @Data
    public static class SettPaymentAttachment {
        /**
         * 附件url
         */
        private String fileUrl;

        /**
         * 附件名
         */
        private String fileName;
    }

}
