package com.wanlianyida.sett.interfaces.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 账户表对应的PO类
 */
@Data
public class SettAcctAccountBalanceDTO {

    private Long id; // // 账户id

    private Long certId; // 证书id

    private String companyId; // 公司id

    private String companyName; // 公司名称

    private String bankAccountNo; // 银行账号

    private String bankChannelName; // 银行渠道名称

    private String bankChannelCode; // 银行渠道编码，

    private Integer accountStatus; // 账户状态 [10-未开通,20-开通中,30-开通成功,40-开通失败]

    private BigDecimal totalAmount; // 总额

    private BigDecimal usableAmount; // 可用余额

    private BigDecimal frozenAmount; // 冻结余额

    /**
     * 认证状态 [10-未上传,20-待审核,30-审核不通过,40-已上传（审核通过]
     */
    private Integer certStatus;

    private String errorMessage; // 错误信息
}
