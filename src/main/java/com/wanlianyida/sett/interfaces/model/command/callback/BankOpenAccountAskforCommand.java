package com.wanlianyida.sett.interfaces.model.command.callback;

import com.wanlianyida.sett.domain.model.condition.BankCardCondition;
import com.wanlianyida.sett.domain.model.condition.OrganizationCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * BankOpenAccountAskforCommand
 *
 * <AUTHOR>
 * @since 2025/4/24
 */

@Data
public class BankOpenAccountAskforCommand {

    /**
     * 会员证件号
     * 必用
     * 个人：身份证，公司：社保信用代码
     */
    @NotBlank(message = "社会统一信用代码")
    private String companySocialCreditCode;

    /**
     * 法人名称
     * 必用
     */
    @NotBlank(message = "法人名称不能为空")
    private String legalPersonName;

    /**
     * 法人证件号
     * 必用
     */
    @NotBlank(message = "法人证件号不能为空")
    private String legalPersonIdentity;

    /**
     * 法人手机号
     * 必用
     */
    @NotBlank(message = "法人手机号不能为空")
    private String legalPersonMobile;


    /**
     * 银行渠道
     * 必用：例如（JSB：江苏银行）
     */
    @NotBlank(message = "银行渠道不能为空")
    private String bankChannelCode;


    /**
     * 签约渠道：10-app 20-平台H5网页 30-公众号 40-小程序
     * 必用
     */
    @NotBlank(message = "签约渠道不能为空")
    private String signChannel;


    //绑卡对象
    private BankCardCondition bankCard;


    // 组织机构对象
    private OrganizationCondition organization;


    @ApiModelProperty("联系人集合")
    @NotNull(message = "联系人集合不能为空")
    @Size(min = 1, message = "联系人集合不能为空")
    private List<SettAccApplicantCommand> contactsDetailList;




}