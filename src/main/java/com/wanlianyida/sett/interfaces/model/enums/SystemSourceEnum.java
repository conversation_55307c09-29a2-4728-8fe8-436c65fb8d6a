package com.wanlianyida.sett.interfaces.model.enums;

import lombok.Getter;

/**
 * 系统请求来源枚举
 * <AUTHOR>
 */
@Getter
public enum SystemSourceEnum {
    USER("user", "用户端"),
    PLATFORM("platform", "平台端"),
    PORTAL("portal", "门户端"),
    ;

    private final String code;
    private final String desc;
    SystemSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
