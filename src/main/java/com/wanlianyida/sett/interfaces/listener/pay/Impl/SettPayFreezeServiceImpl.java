package com.wanlianyida.sett.interfaces.listener.pay.Impl;

import com.wanlianyida.sett.application.service.SettPaymentFreezeRecordAppService;
import com.wanlianyida.sett.interfaces.listener.pay.PaymentOptMsgHandleService;
import com.wanlianyida.sett.interfaces.model.command.mq.SettPaymentOptMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 冻结
 * <AUTHOR>
 * @since 2025/05/19/16:02
 */
@Slf4j
@Service("settPayFreezeService")
public class SettPayFreezeServiceImpl implements PaymentOptMsgHandleService {


    @Resource
    private SettPaymentFreezeRecordAppService settPaymentFreezeRecordAppService;


    @Override
    public Boolean consume(SettPaymentOptMessage settPaymentOptMessage) {
        log.info("settPayUnFreezeService" + settPaymentOptMessage);
        return settPaymentFreezeRecordAppService.paymentFreeze(settPaymentOptMessage);
    }


}
