package com.wanlianyida.sett.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.application.service.SettPaymentAppService;
import com.wanlianyida.sett.domain.service.mq.InitSettlementService;
import com.wanlianyida.sett.interfaces.model.command.ConfirmReceivedCommand;
import com.wanlianyida.sett.interfaces.model.command.ProcessUnionPayCommand;
import com.wanlianyida.sett.interfaces.model.command.SettPaymentCommand;
import com.wanlianyida.sett.interfaces.model.command.mq.OrderStatusUpdMessage;
import com.wanlianyida.sett.interfaces.model.dto.PaymentCheckBalanceDTO;
import com.wanlianyida.sett.interfaces.model.dto.PaymentDetailDTO;
import com.wanlianyida.sett.interfaces.model.dto.SettPaymentDTO;
import com.wanlianyida.sett.interfaces.model.dto.SettPaymentDetailDTO;
import com.wanlianyida.sett.interfaces.model.query.PaymentCheckBalanceQuery;
import com.wanlianyida.sett.interfaces.model.query.SettPaymentDetailQuery;
import com.wanlianyida.sett.interfaces.model.query.SettPaymentListQuery;
import com.wanlianyida.sett.interfaces.model.query.SettPaymentQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 付款单接口
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@RestController
@RequestMapping("/settPayment")
@Validated
public class SettPaymentController {

    @Resource
    private SettPaymentAppService settPaymentAppService;

    @Resource(name = "orderFinishService")
    private InitSettlementService initSettlementService;

    /**
     * 付款单列表分页查询
     */
    @PostMapping("/querySettPaymentPage")
    public ResultMode<List<SettPaymentDTO>> querySettPaymentPage(@Validated @RequestBody PagingInfo<SettPaymentListQuery> query) {
        return settPaymentAppService.querySettPaymentPage(query);
    }

    /**
     * 付款单详情页查询
     */
    @PostMapping("/querySettPaymentDetail")
    public ResultMode<SettPaymentDetailDTO> querySettPaymentDetail(@Validated @RequestBody SettPaymentDetailQuery query) {
        return settPaymentAppService.querySettPaymentDetail(query);
    }

    /**
     * 买家---提交付款单
     */
    @PostMapping("/updateSettPayment")
    public ResultMode<Void> updateSettPayment(@RequestBody @Valid SettPaymentCommand command) {
        return settPaymentAppService.updateSettPayment(command);
    }

    /**
     * 确定收款操作
     */
    @PostMapping("/confirmPaymentReceived")
    public ResultMode<Void> confirmPaymentReceived(@RequestBody @Validated ConfirmReceivedCommand command) {
        return settPaymentAppService.pmtRecdConfOpt(command);
    }


    @PostMapping("/queryPaymentDetail")
    public ResultMode<PaymentDetailDTO> queryPaymentDetail(@Validated @RequestBody SettPaymentQuery query) {
        return settPaymentAppService.getDetail(query);
    }

    @PostMapping("/freezeApplyRequest")
    public void freezeApplyRequest(@Validated @RequestBody OrderStatusUpdMessage orderStatusUpdMessage) {
        initSettlementService.consume(orderStatusUpdMessage);
    }

    /**
     * （谨慎使用此接口）删除所有数据--测试清除数据使用此接口
     */
    @PostMapping("/deleteAllSettPay")
    public ResultMode<Boolean> deleteAllSettPay() {
        return settPaymentAppService.deleteAllSettPay();
    }

    /**
     * 校验余额是否充足
     */
    @PostMapping("/paymentCheckBalance")
    public ResultMode<PaymentCheckBalanceDTO> paymentCheckBalance(@Validated @RequestBody PaymentCheckBalanceQuery query) {
        return settPaymentAppService.paymentCheckBalance(query);
    }

    /**
     * 根据平台流水号查询付款单信息
     *
     * @param query 订单查询参数，包含平台流水号
     * @return SettPaymentDTO 付款单信息
     */
    @PostMapping("/getSettPaymentByPlatformFlowNo")
    public ResultMode<SettPaymentDTO> getSettPaymentByPlatformFlowNo(@Validated @RequestBody SettPaymentQuery query) {
        return settPaymentAppService.getSettPaymentByPlatformFlowNo(query);
    }

    /**
     * 银联支付提交
     */
    @PostMapping("/processUnionPay")
    public ResultMode<Void> processUnionPay(@Validated @RequestBody ProcessUnionPayCommand command) {
        return settPaymentAppService.processUnionPay(command);
    }

}
