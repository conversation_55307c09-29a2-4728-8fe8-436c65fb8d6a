package com.wanlianyida.sett.domain.service;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.sett.domain.model.condition.PaymentCheckBalanceCondition;
import com.wanlianyida.sett.domain.model.condition.SettAcctAccountCondition;
import com.wanlianyida.sett.domain.model.entity.AcctAccountEntity;
import com.wanlianyida.sett.domain.repository.SettAcctAccountRepository;
import com.wanlianyida.sett.interfaces.model.dto.SettAcctAccountDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * @since 2025-04-28
 */
@Slf4j
@Service
public class SettAcctAccountDomainService {

    @Resource
    private SettAcctAccountRepository settAcctAccountRepository;



    public ResultMode<List<SettAcctAccountDTO>> querySettAcctAccountList(SettAcctAccountCondition condition) {
        condition.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        return settAcctAccountRepository.querySettAcctAccountList(condition);
    }

    /**
     * 根据社会信用代码查询企业账户信息
     */
    public AcctAccountEntity getAcctAccountBySocialCreditCode(String socialCreditCode) {
        return settAcctAccountRepository.getAcctAccountBySocialCreditCode(socialCreditCode);
    }

    /**
     * 根据银行渠道编号查询账户信息
     */
    public AcctAccountEntity getAcctAccountByBankChannelCode(PaymentCheckBalanceCondition condition) {
        return settAcctAccountRepository.getAcctAccountByBankChannelCode(condition);
    }
}
