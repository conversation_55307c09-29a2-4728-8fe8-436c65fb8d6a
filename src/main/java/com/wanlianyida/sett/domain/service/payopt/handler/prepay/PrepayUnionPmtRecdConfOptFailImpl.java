package com.wanlianyida.sett.domain.service.payopt.handler.prepay;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.domain.model.bo.payopt.ConfirmReceivedBO;
import com.wanlianyida.sett.domain.model.bo.payopt.PrepayPmtRecdConfOptBO;
import com.wanlianyida.sett.domain.model.entity.SettPaymentEntity;
import com.wanlianyida.sett.domain.repository.SettPaymentRepository;
import com.wanlianyida.sett.domain.service.SettOrderSettlementQueryDomainService;
import com.wanlianyida.sett.domain.service.payopt.PmtRecdConfOptService;
import com.wanlianyida.sett.infrastructure.enums.SettPaymentEnum;
import com.wanlianyida.sett.infrastructure.enums.TransactionTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 线下预付款确认收款
 * <AUTHOR>
 * @since 2025/05/23/11:11
 */
@Service("prepayUnionPmtRecdConfOptFail")
public class PrepayUnionPmtRecdConfOptFailImpl extends PmtRecdConfOptService<PrepayPmtRecdConfOptBO> {


    @Resource
    private SettOrderSettlementQueryDomainService settlementQueryDomainService;

    @Resource
    private SettPaymentRepository paymentRepository;

    @Override
    public Class<PrepayPmtRecdConfOptBO> optParamObj() {
        return PrepayPmtRecdConfOptBO.class;
    }

    @Override
    protected ResultMode<Void> checkParam(ConfirmReceivedBO receivedBO) {
        if (!receivedBO.getPaymentStatus().equals(SettPaymentEnum.PAYMENT_STATUS_140.getCode()) || !receivedBO.getTransactionType().equals(TransactionTypeEnum.TRANSACTION_TYPE_10.getType())) {
            return ResultMode.fail("参数错误");
        }
        return ResultMode.success();
    }


    @Override
    protected void assemblePayOptData(PrepayPmtRecdConfOptBO bo, ConfirmReceivedBO receivedBO) {
        //付款单表
        SettPaymentEntity settPaymentEntity = assemblePaymentUpdCommon(bo);
        settPaymentEntity.setPaymentStatus(SettPaymentEnum.PAYMENT_STATUS_140.getCode());
        settPaymentEntity.setReceiptConfirmTime(null);
        // 失败原因
        settPaymentEntity.setPayFailReason(receivedBO.getPayFailReason());
        bo.setSettPaymentUpdateEntity(settPaymentEntity);
    }

    @Override
    protected Boolean handlePayOpt(PrepayPmtRecdConfOptBO bo) {
        return paymentRepository.prePaymentFail(bo);
    }


}
