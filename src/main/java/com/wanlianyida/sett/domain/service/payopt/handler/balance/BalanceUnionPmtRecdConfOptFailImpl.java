package com.wanlianyida.sett.domain.service.payopt.handler.balance;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.domain.assembler.PmtRecdConfOptServiceAssembler;
import com.wanlianyida.sett.domain.model.bo.payopt.BalancePmtRecdConfOptBO;
import com.wanlianyida.sett.domain.model.bo.payopt.ConfirmReceivedBO;
import com.wanlianyida.sett.domain.model.entity.SettPaymentEntity;
import com.wanlianyida.sett.domain.repository.SettPaymentRepository;
import com.wanlianyida.sett.domain.service.SettPaymentDeductionDetailDomainService;
import com.wanlianyida.sett.domain.service.SettPaymentDeductionDomainService;
import com.wanlianyida.sett.domain.service.payopt.PmtRecdConfOptService;
import com.wanlianyida.sett.infrastructure.enums.SettPaymentEnum;
import com.wanlianyida.sett.infrastructure.enums.TransactionTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 线下尾款收款确认处理
 * <AUTHOR>
 * @since 2025/05/23/11:11
 */
@Service("balanceUnionPmtRecdConfOptFail")
public class BalanceUnionPmtRecdConfOptFailImpl extends PmtRecdConfOptService<BalancePmtRecdConfOptBO> {


    @Resource
    private SettPaymentDeductionDomainService settPaymentDeductionDomainService;

    @Resource
    private SettPaymentDeductionDetailDomainService settPaymentDeductionDetailDomainService;

    @Resource
    private SettPaymentRepository paymentRepository;

    @Override
    public Class<BalancePmtRecdConfOptBO> optParamObj() {
        return BalancePmtRecdConfOptBO.class;
    }

    @Override
    protected ResultMode<Void> checkParam(ConfirmReceivedBO receivedBO) {
        if (!receivedBO.getPaymentStatus().equals(SettPaymentEnum.PAYMENT_STATUS_140.getCode()) || !receivedBO.getTransactionType().equals(TransactionTypeEnum.TRANSACTION_TYPE_20.getType())) {
            return ResultMode.fail("参数错误");
        }
        return ResultMode.success();
    }


    @Override
    protected void assemblePayOptData(BalancePmtRecdConfOptBO bo, ConfirmReceivedBO receivedBO) {
        //付款单表
        SettPaymentEntity settPaymentQuery = bo.getSettPaymentQuery();
        SettPaymentEntity settPaymentEntity = assemblePaymentUpdCommon(bo);
        settPaymentEntity.setPaymentStatus(SettPaymentEnum.PAYMENT_STATUS_140.getCode());
        // 失败原因
        settPaymentEntity.setPayFailReason(receivedBO.getPayFailReason());
        bo.setSettPaymentUpdateEntity(settPaymentEntity);
        //处理结算相关数据
        bo.setSettOrderSettlementDetailUpdateEntity(PmtRecdConfOptServiceAssembler.buildSettOrderSettlementDetailEntity(settPaymentQuery));

    }




    @Override
    protected Boolean handlePayOpt(BalancePmtRecdConfOptBO bo) {
        return paymentRepository.BalancePaymentFail(bo);
    }


}
