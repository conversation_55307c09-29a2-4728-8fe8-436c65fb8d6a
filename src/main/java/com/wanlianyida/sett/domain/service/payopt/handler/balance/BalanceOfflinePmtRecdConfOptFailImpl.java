package com.wanlianyida.sett.domain.service.payopt.handler.balance;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.domain.assembler.PmtRecdConfOptServiceAssembler;
import com.wanlianyida.sett.domain.model.bo.payopt.BalancePmtRecdConfOptBO;
import com.wanlianyida.sett.domain.model.bo.payopt.ConfirmReceivedBO;
import com.wanlianyida.sett.domain.model.entity.SettPaymentEntity;
import com.wanlianyida.sett.domain.repository.SettPaymentRepository;
import com.wanlianyida.sett.domain.service.payopt.PmtRecdConfOptService;
import com.wanlianyida.sett.infrastructure.enums.SettPaymentEnum;
import com.wanlianyida.sett.infrastructure.enums.TransactionTypeEnum;
import com.wanlianyida.sett.infrastructure.exception.CtpCoreSettExceptionEnum;
import org.springframework.stereotype.Service;

import java.util.Date;

import javax.annotation.Resource;

/**
 * 线下尾款收款确认处理--驳回
 * <AUTHOR>
 * @since 2025/05/23/11:11
 */
@Service("balanceOfflinePmtRecdConfOptFail")
public class BalanceOfflinePmtRecdConfOptFailImpl extends PmtRecdConfOptService<BalancePmtRecdConfOptBO> {



    @Resource
    private SettPaymentRepository paymentRepository;

    @Override
    public Class<BalancePmtRecdConfOptBO> optParamObj() {
        return BalancePmtRecdConfOptBO.class;
    }

    @Override
    protected ResultMode<Void> checkParam(ConfirmReceivedBO receivedBO) {
        if (!receivedBO.getPaymentStatus().equals(SettPaymentEnum.PAYMENT_STATUS_150.getCode()) || !receivedBO.getTransactionType().equals(TransactionTypeEnum.TRANSACTION_TYPE_20.getType())) {
            return ResultMode.fail(CtpCoreSettExceptionEnum.ERROR_ORDER_STATUS_NOT_RECEIPT.getCode(),
                    CtpCoreSettExceptionEnum.ERROR_ORDER_STATUS_NOT_RECEIPT.getMsg());
        }
        return ResultMode.success();
    }


    @Override
    protected void assemblePayOptData(BalancePmtRecdConfOptBO bo, ConfirmReceivedBO receivedBO) {
        //付款单表
        SettPaymentEntity settPaymentQuery = bo.getSettPaymentQuery();
        SettPaymentEntity settPaymentEntity = assemblePaymentUpdCommon(bo);
        settPaymentEntity.setPaymentStatus(SettPaymentEnum.PAYMENT_STATUS_150.getCode());
        settPaymentEntity.setReceiptConfirmTime(new Date());
        settPaymentEntity.setPayFailReason(receivedBO.getPayFailReason());
        bo.setSettPaymentUpdateEntity(settPaymentEntity);
        //处理结算相关数据
        bo.setSettOrderSettlementDetailUpdateEntity(PmtRecdConfOptServiceAssembler.buildSettOrderSettlementDetailFailEntity(settPaymentQuery));

    }




    @Override
    protected Boolean handlePayOpt(BalancePmtRecdConfOptBO bo) {
        return paymentRepository.BalancePaymentFail(bo);
    }


}
