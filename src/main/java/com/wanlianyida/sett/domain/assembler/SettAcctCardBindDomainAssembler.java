package com.wanlianyida.sett.domain.assembler;

import com.wanlianyida.sett.domain.model.bo.SettAcctAccountBO;
import com.wanlianyida.sett.domain.model.condition.BankAcctCardBindCondition;
import com.wanlianyida.sett.domain.model.entity.SettAcctCardBindEntity;
import com.wanlianyida.sett.domain.model.entity.SettAcctOpenApplyEntity;
import com.wanlianyida.sett.infrastructure.enums.BindStatusEnum;
import com.wanlianyida.sett.infrastructure.enums.OverallStatusEnum;
import com.wanlianyida.sett.infrastructure.enums.ProtocolAgreeStatusEnum;
import org.springframework.stereotype.Component;

@Component
public class SettAcctCardBindDomainAssembler {

    /**
     * 创建账户银行卡绑定实体
     *
     * @param condition
     * @return
     */
    public SettAcctCardBindEntity buildAcctCardBindEntity(BankAcctCardBindCondition condition, SettAcctAccountBO account, SettAcctOpenApplyEntity openApply) {
        SettAcctCardBindEntity settAcctCardBindEntity = new SettAcctCardBindEntity();
        settAcctCardBindEntity.setAccountId(String.valueOf(account.getId()));
        settAcctCardBindEntity.setBusinessId(String.valueOf(openApply.getId()));
        settAcctCardBindEntity.setCompanyId(account.getCompanyId());
        settAcctCardBindEntity.setCompanyName(account.getCompanyName());
        settAcctCardBindEntity.setCompanySocialCreditCode(account.getSocialCreditCode());
        settAcctCardBindEntity.setBankUnionNumber(condition.getBankUnionNumber()); //银联号
        settAcctCardBindEntity.setBankAccountType(10);
        settAcctCardBindEntity.setAccountName(condition.getAccountName());
        settAcctCardBindEntity.setBankCode(condition.getBankCode());
        settAcctCardBindEntity.setBankName(condition.getBankName());
        settAcctCardBindEntity.setBranchName(condition.getBranchName());
        settAcctCardBindEntity.setBankAccountNo(condition.getBankAccountNo());
        settAcctCardBindEntity.setDefaultFlag(ProtocolAgreeStatusEnum.NO.getStatus());
        settAcctCardBindEntity.setBindStatus(BindStatusEnum.STATUS_10.getStatus());
        settAcctCardBindEntity.setOverallStatus(OverallStatusEnum.STATUS_20.getStatus());
        settAcctCardBindEntity.setBankChannelCode(condition.getBankChannelCode());
        return settAcctCardBindEntity;
    }
}
