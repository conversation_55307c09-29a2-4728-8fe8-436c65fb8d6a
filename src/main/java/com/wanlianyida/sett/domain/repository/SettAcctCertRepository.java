package com.wanlianyida.sett.domain.repository;

import com.wanlianyida.sett.domain.model.entity.SettAcctCertEntity;

/**
 * <p>
 * 账户证书表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface SettAcctCertRepository {

    Long save(SettAcctCertEntity entity);

    SettAcctCertEntity getAcctCertById(Long id);

    Boolean deleteById(Long id);
    void updateAcctCertById(SettAcctCertEntity settAcctCert);
    SettAcctCertEntity queryAcctCertByCompanyIdAndAccountNo(String companyId, String bankAccountNo);
    SettAcctCertEntity queryAcctCertByCompanyIdAndAccountId(String companyId, String accountId);
}
