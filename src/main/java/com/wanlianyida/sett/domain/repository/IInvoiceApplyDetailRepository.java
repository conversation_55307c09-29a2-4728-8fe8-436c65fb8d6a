package com.wanlianyida.sett.domain.repository;

import com.wanlianyida.sett.domain.model.condition.InvoiceApplyDetailCondition;
import com.wanlianyida.sett.domain.model.entity.InvoiceApplyDetailEntity;

import java.util.List;

/**
 * 发票申请单明细
 * <AUTHOR>
*/
public interface IInvoiceApplyDetailRepository {
    /**
     * 批量新增
     */
    void batchInsert(List<InvoiceApplyDetailEntity> list);

    /**
     * 根据发票申请单号物理删除
     */
    void deleteByApplyNo(String applyNo);

    /**
     * 获取发票申请明细
     */
    List<InvoiceApplyDetailEntity> queryCondition(InvoiceApplyDetailCondition condition);
}
