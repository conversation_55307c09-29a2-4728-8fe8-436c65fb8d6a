package com.wanlianyida.sett.domain.model.condition;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BankLargeOrderBackCondition {
    /**
     * 错误消息
     */
    private String errorMessage;
    /**
     * 流水号---付款单号
     */
    @NotBlank(message = "流水号不允许为空")
    private String serialNumber;
    /**
     * 状态(10-待交易,20-已提交,30-支付成功,40-支付失败)
     */
    @NotBlank(message = "状态不允许为空")
    private Integer status;
    /**
     * 交易时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    private String transDate;
    /**
     * 交易编码--银行流水号
     */
    private String tradeNo;
}
