package com.wanlianyida.sett.domain.model.bo;

import com.wanlianyida.sett.domain.model.entity.*;
import lombok.Data;

import java.util.List;

/**
 * 创建发票申请单BO
 * <AUTHOR>
 */
@Data
public class CreateInvoiceBO {

    /**
     * 结算申请单更新实体
     */
    private List<SettSettlementApplyEntity> settlementApplyListUpdate;

    /**
     * 发票申请单新增实体
     */
    private InvoiceApplyEntity invoiceApplyInsert;

    /**
     * 发票集合新增列表
     */
    private List<InvoiceEntity> invoiceListInsert;

    /**
     * 发票附件
     */
    private List<SettPaymentAttachmentEntity> attachmentEntityListInsert;

    /**
     * 发票申请明细
     */
    private List<InvoiceApplyDetailEntity> invoiceApplyDetailListInsert;
}
