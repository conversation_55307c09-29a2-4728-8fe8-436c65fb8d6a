package com.wanlianyida.sett.domain.model.bo;

import com.wanlianyida.sett.domain.model.condition.OrganizationCondition;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

/**
 * BankOpenAccountAskforBo
 *
 * <AUTHOR>
 * @since 2025/4/28
 */

@Data
public class BankOpenAccountAskforBo {

    private String memberCode;

    private String memberName;

    /**
     * 会员证件号
     * 必用
     * 个人：身份证，公司：社保信用代码
     */
    @NotBlank(message = "社会统一信用代码")
    private String memberCertNo;

    /**
     * 法人名称
     * 必用
     */
    @NotBlank(message = "法人名称不能为空")
    private String legalPersonName;

    /**
     * 法人证件号
     * 必用
     */
    @NotBlank(message = "法人证件号不能为空")
    private String legalPersonIdentity;

    /**
     * 法人手机号
     * 必用
     */
    @NotBlank(message = "法人手机号不能为空")
    private String legalPersonMobile;

    /**
     * 会员类型
     * 必用：10企业，20个人
     */
    @NotBlank(message = "会员类型不能为空")
    private String memberType;


    /**
     * 银行渠道
     * 必用：例如（JSB：江苏银行）
     */
    @NotBlank(message = "银行渠道不能为空")
    private String channel;

    /**
     * 平台ID
     * 必用：例如：2
     */
    @NotBlank(message = "平台ID不能为空")
    private String platformId;

    /**
     * 平台名称
     * 必用：例如：万联易达物资科技有限公司
     */
    @NotBlank(message = "平台名称不能为空")
    private String platformName;

    /**
     * 签约渠道：10-app 20-平台H5网页 30-公众号 40-小程序
     * 必用
     */
    @NotBlank(message = "签约渠道不能为空")
    private String signChannel;

    //卡对象
    private BankCard bankCard;


    // 组织机构对象
    private OrganizationCondition organization;

    @Data
    public static class BankCard implements Serializable {
        /**
         * 银行卡号
         * 必用
         */
        @NotBlank(message = "银行卡号不能为空")
        private String cardNo;

        /**
         * 银行联行号
         * 必用
         */
        @NotBlank(message = "银行联行号不能为空")
        private String bankBranchNo;

        /**
         * 开户名称
         * 必用
         */
        @NotBlank(message = "开户行名称不能为空")
        private String bankName;

        /**
         * 支行名称
         * 必用
         */
        @NotBlank(message = "支行名称不能为空")
        private String branchName;

        private String superBankNo;

    }

}
