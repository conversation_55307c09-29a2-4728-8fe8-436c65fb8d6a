package com.wanlianyida.sett.domain.model.bo;

import com.wanlianyida.sett.domain.model.entity.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SettlementHandleOldBO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountsAmount;

    /**
     * 商品总金额
     */
    private BigDecimal productTotalAmount;

    /**
     * 是否预付:0-否,1-是
     */
    private Boolean prepayFlag;

    /**
     * 预付金额
     */
    private BigDecimal prepayAmount;

    /**
     * 预付款比例
     */
    private BigDecimal prepayRatio;

    /**
     * 运费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 应结算金额
     */
    private BigDecimal shouldSmentAmount;

    /**
     * 已结算金额
     */
    private BigDecimal smentAmount;

    /**
     * 剩余结算金额
     */
    private BigDecimal remainAmount;

    /**
     * 结算状态:10待结算 20 部分结算 30已结算
     */
    private Integer smentStatus;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;


    /**
     * 买家id
     */
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    private String buyerCompanyName;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;


    /**
     * 卖家公司名称
     */
    private String sellerCompanyName;

    /**
     * 提现状态10-未体现,20-待提现,30-提现中,40-已提现
     */
    private Integer withdrawalStatus;

    /**
     * 提现时间
     */
    private Date withdrawalTime;

    /**
     * 订单结算明细表
     */
    private List<SettOrderSettlementDetailEntity> orderSettlementDetailList;


    private SettSettlementApplyEntity settlementApply;


    private List<SettSettlementApplyDetailEntity> settlementApplyDetailList;

    /**
     * 结算商品列表
     */
    private List<SettSettlementApplyProductEntity> settlementApplyProductList;


}