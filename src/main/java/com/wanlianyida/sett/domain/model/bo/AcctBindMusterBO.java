package com.wanlianyida.sett.domain.model.bo;

import com.wanlianyida.sett.domain.model.entity.*;
import com.wanlianyida.sett.interfaces.model.command.callback.SettAccApplicantCommand;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AcctBindMusterBO {

    //账户表
    private AcctAccountEntity acctAccountEntity;

    //账户开户申请表
    private SettAcctOpenApplyEntity settAcctOpenApplyEntity;

    //绑卡表
    private SettAcctCardBindEntity settAcctCardBindEntity;

    //账户表
    private SettAcctAccountEntity settAcctAccountEntityForUpdate;

    // 联系人对象
    private List<SettAccApplicantEntity> contactsDetailListForInsert;
}
