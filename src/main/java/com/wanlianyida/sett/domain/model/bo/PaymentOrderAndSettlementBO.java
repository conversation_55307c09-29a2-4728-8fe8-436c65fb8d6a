package com.wanlianyida.sett.domain.model.bo;

import com.wanlianyida.sett.domain.model.entity.SettOrderSettlementDetailEntity;
import com.wanlianyida.sett.domain.model.entity.SettOrderSettlementEntity;
import com.wanlianyida.sett.domain.model.entity.SettPaymentEntity;
import com.wanlianyida.sett.domain.model.entity.SettSettlementApplyEntity;
import lombok.Data;

@Data
public class PaymentOrderAndSettlementBO {


    private SettPaymentEntity settPaymentEntity;

    private SettOrderSettlementEntity settOrderSettlementEntity;

    private SettSettlementApplyEntity settSettlementApplyEntity;

    private SettOrderSettlementDetailEntity settOrderSettlementDetailEntity;

}
