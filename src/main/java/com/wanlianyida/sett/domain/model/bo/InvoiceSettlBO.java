package com.wanlianyida.sett.domain.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Veriosn 1.0
 **/

@Data
public class InvoiceSettlBO {
    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 结算单号
     */
    private String settApplyNo;

    /**
     *买家id
     */
    private String buyerCompanyId;
    /**
     * 买家
     */
    private String buyerCompanyName;

    /**
     *卖家id
     */
    private String sellerCompanyId;

    /**
     * 卖家
     */
    private String sellerCompanyName;

    /**
     * 结算金额
     */
    private BigDecimal settTotalAmount;

    /**
     * 结算确认状态
     */
    private Integer settStatus;

    /**
     * 结算付款状态
     */
    private Integer payStatus;

    /**
     * 发票登记状态
     */
    private Integer invoiceRegStatus;

    /**
     * 结算单创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

}
