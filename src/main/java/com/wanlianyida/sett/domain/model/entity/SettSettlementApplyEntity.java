package com.wanlianyida.sett.domain.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class SettSettlementApplyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 申请结算单号
     */
    private String settApplyNo;

    /**
     * 卖家公司id
     */
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    private String sellerCompanyName;

    /**
     * 买家公司id
     */
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    private String buyerCompanyName;

    /**
     * 结算总金额
     */
    private BigDecimal settTotalAmount;

    /**
     * 结算确认状态[10-待买家确认,20-买家驳回,30-已确认]
     */
    private Integer settStatus;

    /**
     * 结算付款总金额
     */
    private BigDecimal payTotalAmount;

    /**
     * 结算付款状态[10待买家付款,20待卖家确认收款,30已付款]
     */
    private Integer payStatus;

    /**
     * 发票登记状态[0-待登记,1-已登记]
     */
    private Integer invoiceRegStatus;

    /**
     * 预付总额
     */
    private BigDecimal prepaidTotalAmount;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 买家确认人id
     */
    private String buyerConfirmorId;

    /**
     * 买家确认时间
     */
    private Date buyerConfirmTime;

    /**
     * 卖家确认人id
     */
    private String sellerConfirmorId;

    /**
     * 卖家确认时间
     */
    private Date sellerConfirmTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 结算日期
     */
    private Date settTime;


    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 文件url
     */
    private String fileUrl;


    /**
     * 附件删除列表
     */
    private List<Long> deleteFileList;


    // 结算确认单附件类型：40卖家结算确认单，41买家结算确认单
    private String fileType;

}