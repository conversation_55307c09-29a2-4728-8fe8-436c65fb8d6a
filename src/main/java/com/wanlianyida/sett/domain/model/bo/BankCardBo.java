package com.wanlianyida.sett.domain.model.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BankCardBo {

    /**
     * 银行卡号
     * 必用
     */
    @NotBlank(message = "银行卡号不能为空")
    private String cardNo;

    /**
     * 银行联行号
     * 必用
     */
    @NotBlank(message = "银行联行号不能为空")
    private String bankBranchNo;

    /**
     * 开户名称
     * 必用
     */
    @NotBlank(message = "开户行名称不能为空")
    private String bankName;

    /**
     * 支行名称
     * 必用
     */
    @NotBlank(message = "支行名称不能为空")
    private String branchName;

    private String superBankNo;
}
