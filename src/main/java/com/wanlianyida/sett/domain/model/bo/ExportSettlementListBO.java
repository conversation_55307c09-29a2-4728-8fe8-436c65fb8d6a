package com.wanlianyida.sett.domain.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ExportSettlementListBO {

    /**
     * 结算单号
     */
    private String settApplyNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 买家公司ID
     */
    private String buyerCompanyId;

    /**
     * 买家
     */
    private String buyerCompanyName;

    /**
     * 卖家公司ID
     */
    private String sellerCompanyId;

    /**
     * 卖家
     */
    private String sellerCompanyName;

    /**
     * 提货单号
     */
    private String pickupNo;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 单位转换
     */
    private BigDecimal unitCon;

    /**
     * 合同单价
     */
    private BigDecimal contractUnitPrice;

    /**
     * 发货数量
     */
    private BigDecimal shipmentQty;

    /**
     * 收货/提货数量
     */
    private BigDecimal receiveQty;

    /**
     * 结算数量
     */
    private BigDecimal smentQuantity;

    /**
     * 结算单价
     */
    private BigDecimal smentPrice;

    /**
     * 结算小计
     */
    private BigDecimal sementSubtotal;

    /**
     * 调差
     */
    private BigDecimal adjustAmount;

    /**
     * 应付结算金额
     */
    private BigDecimal payableSettAmount;

    /**
     * 结算总额（结算总金额）
     */
    private BigDecimal settTotalAmount;

    /**
     * 结算付款总额（结算付款总金额）
     */
    private BigDecimal payTotalAmount;

    /**
     * 结算预付总额（预付总额）
     */
    private BigDecimal prepaidTotalAmount;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 结算确认状态[10-待买家确认,20-买家驳回,30-已确认]
     */
    private Integer settStatus;

    /**
     * 结算付款状态[10待买家付款,20待卖家确认收款,30已付款]
     */
    private Integer payStatus;

    /**
     * 发票登记状态[10-待登记,20-已登记]
     */
    private Integer invoiceRegStatus;
}
