package com.wanlianyida.sett.domain.model.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class ExportSettlementListCondition {

    // 结算单号
    private String settApplyNo;

    // 买家
    private String buyerCompanyName;

    // 卖家
    private String sellerCompanyName;

    // 开票申请状态
    private String invoiceRegStatus;

    // 结算确认状态
    private String settStatus;

    // 开始创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdDateStart;

    // 结束创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createdDateEnd;

    // 结算申请单ID
    private List<Long> ids;

    // 企业类型：10卖家，20买家
    private Integer companyType;

    // 买家/卖家公司id
    private String companyId;

    // 是否导出明细字段[0-否,1-是]
    private Integer detailField = 1;

    // 接口请求来源 [10-用户端,20-平台端]
    private Integer source;
}
