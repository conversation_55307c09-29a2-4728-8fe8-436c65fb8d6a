package com.wanlianyida.sett.infrastructure.exception;

import lombok.Getter;

/**
 * 获取大额订单号相关 error code
 */
@Getter
public enum PaymentErrorCode {
    ERROR_10001("U10001","付款单信息查询失败"),
    ERROR_10002("U10002","调用金融获取大额订单号失败"),
    ERROR_10003("U10003","付款银行登记信息新增失败"),
    ERROR_10004("U10004","调用金融转账接口失败"),
    ;
    private String code;

    private String msg;

    PaymentErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
