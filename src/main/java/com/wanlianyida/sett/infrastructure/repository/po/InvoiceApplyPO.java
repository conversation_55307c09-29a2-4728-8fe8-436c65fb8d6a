package com.wanlianyida.sett.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 发票申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22 20:41:47
 */
@Getter
@Setter
@TableName("sett_inv_invoice_apply")
public class InvoiceApplyPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId("id")
    private Long id;

    /**
     * 申请单号
     */
    @TableField("apply_no")
    private String applyNo;

    /**
     * 买家公司id
     */
    @TableField("buyer_company_id")
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    @TableField("buyer_company_name")
    private String buyerCompanyName;

    /**
     * 卖家公司id
     */
    @TableField("seller_company_id")
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    @TableField("seller_company_name")
    private String sellerCompanyName;

    /**
     * 发票号码
     */
    @TableField("invoice_no")
    private String invoiceNo;

    /**
     * 发票数量
     */
    @TableField("invoice_qty")
    private Integer invoiceQty;

    /**
     * 结算金额
     */
    @TableField("sett_amount")
    private BigDecimal settAmount;

    /**
     * 价税总额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 不含税总额
     */
    @TableField("without_tax_total_amount")
    private BigDecimal withoutTaxTotalAmount;

    /**
     * 税额总额
     */
    @TableField("tax_total_amount")
    private BigDecimal taxTotalAmount;

    /**
     * 开票状态[10-待买家确认,20-买家驳回,30-已开票]
     */
    @TableField("issue_invoice_status")
    private String issueInvoiceStatus;

    /**
     * 退回原因
     */
    @TableField("return_reason")
    private String returnReason;

    /**
     * 创建用户id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;
}
