package com.wanlianyida.sett.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 付款冻结记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@TableName("sett_pay_freeze_record")
public class SettPaymentFreezeRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 付款单号
     */
    @TableField("payment_no")
    private String paymentNo;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 冻结金额
     */
    @TableField("freeze_amount")
    private BigDecimal freezeAmount;

    /**
     * 冻结时间
     */
    @TableField("freeze_time")
    private LocalDateTime freezeTime;

    /**
     * 解冻时间
     */
    @TableField("unfreeze_time")
    private LocalDateTime unfreezeTime;

    /**
     * 状态[10-冻结成功 20-冻结失败 30-解冻成功 40-解冻失败]
     */
    @TableField("status")
    private Integer status;

    /**
     * 冻结状态[10-未冻结 20-冻结成功 30-冻结失败]
     */
    @TableField("freeze_status")
    private Integer freezeStatus;

    /**
     * 解冻状态[10-未解冻 20-解冻成功 30-解冻失败]
     */
    @TableField("unfreeze_status")
    private Integer unfreezeStatus;

    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private LocalDateTime createDate;

    /**
     * 修改人ID
     */
    @TableField("updater_id")
    private String updateId;

    /**
     * 修改时间
     */
    @TableField("updated_date")
    private LocalDateTime updateDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 账户ID
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 银行渠道编码
     */
    @TableField("bank_channel_code")
    private String bankChannelCode;


}
