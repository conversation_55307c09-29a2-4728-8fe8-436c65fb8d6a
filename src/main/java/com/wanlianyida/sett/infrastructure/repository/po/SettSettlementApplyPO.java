package com.wanlianyida.sett.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@TableName("sett_sett_apply")
public class SettSettlementApplyPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请结算单号
     */
    @TableField("sett_apply_no")
    private String settApplyNo;

    /**
     * 卖家公司id
     */
    @TableField("seller_company_id")
    private String sellerCompanyId;

    /**
     * 卖家公司名称
     */
    @TableField("seller_company_name")
    private String sellerCompanyName;

    /**
     * 买家公司id
     */
    @TableField("buyer_company_id")
    private String buyerCompanyId;

    /**
     * 买家公司名称
     */
    @TableField("buyer_company_name")
    private String buyerCompanyName;

    /**
     * 结算总金额
     */
    @TableField("sett_total_amount")
    private BigDecimal settTotalAmount;

    /**
     * 结算确认状态[10-待买家确认,20-买家驳回,30-已确认]
     */
    @TableField("sett_status")
    private Integer settStatus;

    /**
     * 结算付款总金额
     */
    @TableField("pay_total_amount")
    private BigDecimal payTotalAmount;

    /**
     * 结算付款状态[10待买家付款,20待卖家确认收款,30已付款]
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 发票登记状态[10-待登记,20-已登记]
     */
    @TableField("invoice_reg_status")
    private Integer invoiceRegStatus;

    /**
     * 预付总额
     */
    @TableField("prepaid_total_amount")
    private BigDecimal prepaidTotalAmount;

    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 买家确认人id
     */
    @TableField("buyer_confirmor_id")
    private String buyerConfirmorId;

    /**
     * 买家确认时间
     */
    @TableField("buyer_confirm_time")
    private Date buyerConfirmTime;

    /**
     * 卖家确认人id
     */
    @TableField("seller_confirmor_id")
    private String sellerConfirmorId;

    /**
     * 卖家确认时间
     */
    @TableField("seller_confirm_time")
    private Date sellerConfirmTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 退回原因
     */
    @TableField("return_reason")
    private String returnReason;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 结算日期
     */
    @TableField("sett_time")
    private Date settTime;

}