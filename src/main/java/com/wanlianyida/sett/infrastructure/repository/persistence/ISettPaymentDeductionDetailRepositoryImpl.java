package com.wanlianyida.sett.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wanlianyida.sett.domain.model.condition.SettPaymentDeductionDetailCondition;
import com.wanlianyida.sett.domain.model.entity.SettPaymentDeductionDetailEntity;
import com.wanlianyida.sett.domain.repository.ISettPaymentDeductionDetailRepository;
import com.wanlianyida.sett.infrastructure.repository.mapper.SettPaymentDeductionDetailMapper;
import com.wanlianyida.sett.infrastructure.repository.po.SettPaymentDeductionDetailPO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/04/26/15:30
 */
@Service
public class ISettPaymentDeductionDetailRepositoryImpl implements ISettPaymentDeductionDetailRepository {

    @Resource
    private SettPaymentDeductionDetailMapper settPaymentDeductionDetailMapper;

    @Override
    public Boolean batchInsert(List<SettPaymentDeductionDetailEntity> SettPaymentOffsetDetailEntityList) {
        return null;
    }

    @Override
    public Boolean batchDelete(SettPaymentDeductionDetailEntity SettPaymentOffsetDetailEntity) {
        return null;
    }

    @Override
    public List<SettPaymentDeductionDetailEntity> queryCondition(SettPaymentDeductionDetailCondition condition) {
        return Collections.emptyList();
    }

    @Override
    public Boolean updateByCondition(SettPaymentDeductionDetailCondition condition, SettPaymentDeductionDetailEntity SettPaymentOffsetDetailEntity) {
        return null;
    }

    @Override
    public Long add(SettPaymentDeductionDetailEntity SettPaymentOffsetDetailEntity) {
        return 0L;
    }

    @Override
    public SettPaymentDeductionDetailEntity detailById(Long id) {
        return null;
    }

    @Override
    public SettPaymentDeductionDetailEntity detailByCondition(SettPaymentDeductionDetailCondition condition) {
        LambdaQueryWrapper<SettPaymentDeductionDetailPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(condition.getId()),SettPaymentDeductionDetailPO::getId, condition.getId());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getOrderNo()),SettPaymentDeductionDetailPO::getOrderNo, condition.getOrderNo());
        queryWrapper.eq(ObjUtil.isNotNull(condition.getDeductionType()),SettPaymentDeductionDetailPO::getDeductionType, condition.getDeductionType());
        SettPaymentDeductionDetailPO settPaymentDeductionDetailPO = settPaymentDeductionDetailMapper.selectOne(queryWrapper);
        return BeanUtil.toBean(settPaymentDeductionDetailPO, SettPaymentDeductionDetailEntity.class);
    }

    @Override
    public void batchUpdateById(SettPaymentDeductionDetailEntity SettPaymentOffsetDetailEntity, List<Long> idList) {

    }

    @Override
    public void insert(SettPaymentDeductionDetailEntity entity) {
        settPaymentDeductionDetailMapper.insert(BeanUtil.toBean(entity, SettPaymentDeductionDetailPO.class));
    }
}
