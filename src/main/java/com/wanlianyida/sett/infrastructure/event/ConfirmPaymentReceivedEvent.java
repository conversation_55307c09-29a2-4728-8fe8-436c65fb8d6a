package com.wanlianyida.sett.infrastructure.event;

/**
 * LargeOrderCallbackEvent
 * 银行回调
 *
 * <AUTHOR>
 * @since 2025/4/3
 */
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfirmPaymentReceivedEvent {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     *  120
     */
    private Integer paymentStatus;

    /**
     * 交易类型[10-预付款20-尾款]
     */
    private Integer transactionType;

    /**
     * 付款方式[10-银行转账20-银行承兑30-商业承兑50-电汇]
     */
    private String paymentMethod;



}
