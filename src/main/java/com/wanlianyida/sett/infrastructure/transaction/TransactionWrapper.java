package com.wanlianyida.sett.infrastructure.transaction;

import com.wanlianyida.framework.ctpcommon.entity.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

@Component
@Slf4j
public class TransactionWrapper {

    @Autowired
    private TransactionTemplate transactionTemplate;

    public TransactionWrapper() {
    }

    public <T> T transaction(Callback<T> callback) {
        return this.transactionTemplate.execute((status) -> {
            try {
                return callback.execute();
            } catch (BaseException bizException) {
                log.warn("business exception caught in transaction:", bizException);
                status.setRollbackOnly();
                throw bizException;
            } catch (Throwable throwable) {
                log.error("TransactionWrapper transaction unknown error:", throwable);
                status.setRollbackOnly();
                throw new BaseException("transaction callback exception");
            }
        });
    }

    public void transaction(Runnable runnable) {
        this.transactionTemplate.execute((status) -> {
            try {
                runnable.run();
                return true;
            } catch (BaseException bizException) {
                log.warn("business exception caught in transaction ", bizException);
                status.setRollbackOnly();
                throw bizException;
            } catch (Throwable throwable) {
                log.error("TransactionWrapper transaction unknown error ", throwable);
                status.setRollbackOnly();
                throw new BaseException("transaction runnable exception");
            }
        });
    }


}

