package com.wanlianyida.sett.infrastructure.constant;

/**
 * 付款相关
 */
public abstract class PaymentConstants {

    // 渠道江苏银行
    public static final String PAYMENT_CHANNEL_BOJS = "BOJS";
    // 渠道银联
    public static final String PAYMENT_CHANNEL_CUP = "CUP";

    // 大额订单
    public static final String PAYMENT_CALLBACK_URL = "/inner/payStatus/largeOrderback";

    // 银联回调地址
    public static final String PAYMENT_UNION_PAY_CALLBACK_URL = "/inner/payStatus/unionPayCallBack";

    // 收款方银行名称
    public static final String PAYMENT_RECEIVER_BANK_OF_JIANGSU = "江苏银行";

    // 买家付款
    public static final String PAYMENT_TYPE_BUYER_PAY = "BUYER_PAY";

    // 买家修改付款信息
    public static final String PAYMENT_TYPE_BUYER_UPDATE_PAYMENT_INFO = "BUYER_UPDATE_PAYMENT_INFO";

    // 卖家收款驳回
    public static final String PAYMENT_TYPE_SELLER_REJECT = "SELLER_REJECT";

    // 卖家确认收款
    public static final String PAYMENT_TYPE_SELLER_CONFIRM = "SELLER_CONFIRM";

    // 银行反馈付款失败
    public static final String PAYMENT_TYPE_BANK_FEEDBACK_PAYMENT_FAIL = "BANK_FEEDBACK_PAYMENT_FAIL";

    // 银行确认到账
    public static final String PAYMENT_TYPE_BANK_CONFIRM_RECEIVED = "BANK_CONFIRM_RECEIVED";



}
