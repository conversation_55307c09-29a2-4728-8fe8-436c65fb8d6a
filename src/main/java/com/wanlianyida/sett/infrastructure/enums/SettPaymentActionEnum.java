package com.wanlianyida.sett.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 付款单操作
 */
@Getter
public enum SettPaymentActionEnum {

    ACTION_10("10","付款成功冻结处理","settPayFreezeService"),
    ACTION_20("20","订单完成解冻处理","settPayUnFreezeService"),
    ACTION_30("30","银行回调处理","autoConfirmPaymentService"),
    ACTION_40("40","结算确认处理","settlementApplyConfirmService"),
    ;

    private String action;

    private String desc;

    private String handlerName;

    SettPaymentActionEnum(String action, String desc, String handlerName) {
        this.action = action;
        this.desc = desc;
        this.handlerName = handlerName;
    }

    /**
     * 查询 action 处理器
     * @param action
     * @return
     */
    public static String getHandlerByAction(String action){
        if(StrUtil.isBlank(action)){
            return null;
        }
        Optional<SettPaymentActionEnum> first = Arrays.stream(values()).filter(a -> StrUtil.equals(a.getAction(), action)).findFirst();
        if(first.isPresent()){
            return first.get().getHandlerName();
        }
        return null;
    }

    public static String getDescByHandlerName(String handlerName){
        if(StrUtil.isBlank(handlerName)){
            return null;
        }
        Optional<SettPaymentActionEnum> first = Arrays.stream(values()).filter(a -> StrUtil.equals(a.getHandlerName(), handlerName)).findFirst();
        if(first.isPresent()){
            return first.get().getDesc();
        }
        return null;
    }

}
