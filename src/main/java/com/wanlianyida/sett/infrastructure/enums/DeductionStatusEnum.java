package com.wanlianyida.sett.infrastructure.enums;

import lombok.Getter;

@Getter
public enum DeductionStatusEnum {
    //是否无需结算:0-否 1-是
    DEDUCTION_STATUS_10(10, "已取消/未生效"),
    DEDUCTION_STATUS_20(20, "已生效/抵扣中"),
    DEDUCTION_STATUS_30(30, "已核销/已抵扣"),
    ;
    private final Integer status;
    private final String desc;


    DeductionStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
