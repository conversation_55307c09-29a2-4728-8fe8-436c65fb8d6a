package com.wanlianyida.process.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.process.interfaces.model.command.SubmitAutoAuditCommand;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2025/03/06/17:58
 */
public class AutoAuditRuleAppAssembler {

    public static SubmitAutoAuditCommand buildCondition(Map<String, Object> allVariablesOutput) {
        SubmitAutoAuditCommand autoAuditCommand = BeanUtil.toBean(allVariablesOutput, SubmitAutoAuditCommand.class);
        Map<String, Object> customVar = new HashMap<>();
        if (allVariablesOutput.containsKey("companyIdList") && allVariablesOutput.get("companyIdList") != null) {
            customVar.put("companyIdList", allVariablesOutput.get("companyIdList"));
        }
        if (allVariablesOutput.containsKey("productCategoryIdList") && allVariablesOutput.get("productCategoryIdList") != null) {
            customVar.put("productCategoryIdList", allVariablesOutput.get("productCategoryIdList"));
        }
        if (allVariablesOutput.containsKey("contentCategoryIdList") && allVariablesOutput.get("contentCategoryIdList") != null) {
            customVar.put("contentCategoryIdList", allVariablesOutput.get("contentCategoryIdList"));
        }
        autoAuditCommand.setCustomVar(customVar);
        return autoAuditCommand;
    }
}
