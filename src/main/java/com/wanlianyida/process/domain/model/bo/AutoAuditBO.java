package com.wanlianyida.process.domain.model.bo;

import com.wanlianyida.support.api.model.dto.BdAutoAuditRuleListDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/03/19:05
 */
@Data
public class AutoAuditBO {

    /**
     * 配置id
     */
    private String configId;

    /**
     * 业务id
     */
    private String busiId;

    /**
     * '审核类型 字典值=auto_audit_type'
     */
    private String auditType;

    /**
     * 场景类型 字典值=auto_audit_scene_type
     */
    private String sceneType;

    /**
     * 公司id
     */
    private List<String> companyIdList;

    /**
     * 商品三级分类id
     */
    private List<String> productCategoryIdList;

    /**
     * 内容一级分类
     */
    private List<String> contentCategoryIdList;

    /**
     * 是否发送消息
     */
    private Boolean autoAuditRuleCheckStatus;

    /**
     * 规则列表
     */
    private List<BdAutoAuditRuleListDTO> listDTOList;
}
