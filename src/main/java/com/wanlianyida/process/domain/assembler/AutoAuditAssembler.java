package com.wanlianyida.process.domain.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.process.domain.model.bo.AutoAuditBO;
import com.wanlianyida.process.domain.model.bo.AutoAuditRuleValueBO;
import com.wanlianyida.process.infrastructure.enums.AutoAuditRuleTypeEnum;
import com.wanlianyida.support.api.model.dto.BdAutoAuditRuleListDTO;
import com.wanlianyida.support.api.model.query.AutoAuditConfigListQuery;

public class AutoAuditAssembler {

    public static AutoAuditConfigListQuery buildProjectDetailCondition(AutoAuditBO autoAuditBO) {
        AutoAuditConfigListQuery auditConfigListQuery = BeanUtil.copyProperties(autoAuditBO, AutoAuditConfigListQuery.class);
        auditConfigListQuery.setAutoAudit(1);
        return auditConfigListQuery;
    }

    public static AutoAuditRuleValueBO buildDetailCondition(AutoAuditBO autoAuditBO, BdAutoAuditRuleListDTO dto) {
        AutoAuditRuleValueBO autoAuditRuleValueBO = new AutoAuditRuleValueBO();
        if (AutoAuditRuleTypeEnum.ACTION_10.getCode().equals(dto.getRuleType())) {
            autoAuditRuleValueBO.setBusiId(autoAuditBO.getCompanyIdList());
        } else if (AutoAuditRuleTypeEnum.ACTION_20.getCode().equals(dto.getRuleType())) {
            autoAuditRuleValueBO.setBusiId(autoAuditBO.getProductCategoryIdList());
        } else {
            autoAuditRuleValueBO.setBusiId(autoAuditBO.getContentCategoryIdList());
        }
        autoAuditRuleValueBO.setRuleDetailBusiIdList(dto.getBusiIdList());
        return autoAuditRuleValueBO;
    }
}
