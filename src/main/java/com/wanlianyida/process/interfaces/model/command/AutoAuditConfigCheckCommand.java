package com.wanlianyida.process.interfaces.model.command;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/03/19:13
 */
@Data
public class AutoAuditConfigCheckCommand {


    /**
     * 审核配置id
     */
    private String configId;

    /**
     * '审核类型 字典值=auto_audit_type'
     */
    private String auditType;

    /**
     * 场景类型 字典值=auto_audit_scene_type
     */
    private String sceneType;

    /**
     * 业务id
     */
    private String busiId;

    /**
     * 公司id
     */
    private List<String> companyId;

    /**
     * 商品三级分类id
     */
    private List<String> productCategoryId;

    /**
     * 内容一级分类
     */
    private List<String> contentCategoryId;

}
