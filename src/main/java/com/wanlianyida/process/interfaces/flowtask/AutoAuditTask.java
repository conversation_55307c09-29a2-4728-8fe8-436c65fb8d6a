package com.wanlianyida.process.interfaces.flowtask;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.process.application.assembler.AutoAuditRuleAppAssembler;
import com.wanlianyida.process.application.service.AutoAuditAppService;
import com.wanlianyida.process.infrastructure.constant.AutoAuditConstant;
import com.wanlianyida.process.infrastructure.enums.AuditResultEnum;
import com.wanlianyida.process.infrastructure.event.EventPublisher;
import com.wanlianyida.process.interfaces.model.command.SubmitAutoAuditCommand;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskHandler;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

@Configuration
@Slf4j
public class AutoAuditTask {

    @Resource
    private AutoAuditAppService autoAuditAppService;

    @Resource
    private EventPublisher eventPublisher;

    @Bean
    @ExternalTaskSubscription(topicName = AutoAuditConstant.CTP_AUTO_AUDIT_CHECK_RULE,
            processDefinitionKeyIn = {AutoAuditConstant.CTP_BF_AUTO_AUDIT},
            lockDuration = 5000)
    public ExternalTaskHandler checkRuleExternalTaskHandler() {
        return (ExternalTask externalTask, ExternalTaskService externalTaskService) -> {
            Map<String, Object> allVariablesOutput = externalTask.getAllVariables();
            log.info("checkRuleExternalTaskHandler#请求#{}", JSONUtil.toJsonStr(allVariablesOutput));
            SubmitAutoAuditCommand autoAuditCommand = AutoAuditRuleAppAssembler.buildCondition(allVariablesOutput);
            Map<String, Object> variablesInput = new HashMap<>();
            variablesInput.put("exp_audit", AuditResultEnum.AUDIT_FAIL.getCode());
            log.info("checkRuleExternalTaskHandler#校验规则请求#{}", JSONUtil.toJsonStr(autoAuditCommand));
            if (Boolean.TRUE.equals(autoAuditAppService.checkRule(autoAuditCommand))) {
                variablesInput.put("exp_audit", AuditResultEnum.AUDIT_SUCCESS.getCode());
            }
            log.info("checkRuleExternalTaskHandler#规则校验结果提交#{}", JSONUtil.toJsonStr(variablesInput));
            externalTaskService.complete(externalTask, variablesInput);
        };
    }

    @Bean
    @ExternalTaskSubscription(topicName = AutoAuditConstant.CTP_AUTO_AUDIT,
            processDefinitionKeyIn = {AutoAuditConstant.CTP_BF_AUTO_AUDIT},
            lockDuration = 5000)
    public ExternalTaskHandler sendMqExternalTaskHandler() {
        return (ExternalTask externalTask, ExternalTaskService externalTaskService) -> {
            Map<String, Object> allVariablesOutput = externalTask.getAllVariables();
            log.info("sendMqExternalTaskHandler#请求#{}", JSONUtil.toJsonStr(allVariablesOutput));
            SubmitAutoAuditCommand autoAuditCommand = AutoAuditRuleAppAssembler.buildCondition(allVariablesOutput);
            Map<String, Object> variables = new HashMap<>();
            variables.put("exp_audit", AuditResultEnum.AUDIT_FAIL.getCode());
            log.info("sendMqExternalTaskHandler#请求--转对象#{}", JSONUtil.toJsonStr(allVariablesOutput));
            if (Boolean.TRUE.equals(eventPublisher.sendAutoAuditMsg(autoAuditCommand))) {
                variables.put("exp_audit", AuditResultEnum.AUDIT_SUCCESS.getCode());
            }
            log.info("sendMqExternalTaskHandler#提交#{}", JSONUtil.toJsonStr(variables));
            externalTaskService.complete(externalTask, variables);
        };
    }
}
