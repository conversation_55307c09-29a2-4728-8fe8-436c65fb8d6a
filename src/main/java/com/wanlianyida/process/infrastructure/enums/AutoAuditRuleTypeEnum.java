package com.wanlianyida.process.infrastructure.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/03/04/19:43
 */
@Getter
public enum AutoAuditRuleTypeEnum {

        ACTION_10("10","商家类型"),
        ACTION_20("20","商品品类"),
        ACTION_30("30","资讯类别"),
        ACTION_40("40","帮助分类"),
        ACTION_50("50","公告分类"),
            ;

    private String code;

    private String desc;


    AutoAuditRuleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
