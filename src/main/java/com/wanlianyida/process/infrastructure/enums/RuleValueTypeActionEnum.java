package com.wanlianyida.process.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 规则类型_规则值
 */
@Getter
public enum RuleValueTypeActionEnum {

    // 规则类型_规则值
    ACTION_10_10("10_10","商家类型_全部","ruleValueAllService"),
    ACTION_10_20("10_20","商家类型_内部商家","ruleValueCompanyInnerService"),
    ACTION_10_30("10_30","商家类型_仅含指定商家","ruleValueIncludeService"),
    ACTION_10_40("10_40","商家类型_仅排除含指定商家","ruleValueExcludeService"),
    ACTION_20_10("20_10","商品品类_全部","ruleValueAllService"),
    ACTION_20_20("20_20","商品品类_指定品类","ruleValueIncludeService"),
    ACTION_30_10("30_10","资讯类别_全部","ruleValueAllService"),
    ACTION_30_20("30_20","资讯类别_指定品类","ruleValueIncludeService"),
    ACTION_40_10("40_10","帮助分类_全部","ruleValueAllService"),
    ACTION_40_20("40_20","帮助分类_指定品类","ruleValueIncludeService"),
    ACTION_50_10("50_10","公告分类_全部","ruleValueAllService"),
    ACTION_50_20("50_20","公告分类_指定品类","ruleValueIncludeService"),

    ;


    private String action;

    private String desc;

    private String handlerName;

    RuleValueTypeActionEnum(String action, String desc, String handlerName) {
        this.action = action;
        this.desc = desc;
        this.handlerName = handlerName;
    }

    /**
     * 查询 action 处理器
     * @param action
     * @return
     */
    public static String getHandlerByAction(String action){
        if(StrUtil.isBlank(action)){
            return null;
        }
        Optional<RuleValueTypeActionEnum> first = Arrays.stream(values()).filter(a -> StrUtil.equals(a.getAction(), action)).findFirst();
        if(first.isPresent()){
            return first.get().getHandlerName();
        }
        return null;
    }

}
