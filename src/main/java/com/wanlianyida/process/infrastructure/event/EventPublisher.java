package com.wanlianyida.process.infrastructure.event;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.process.infrastructure.constant.RocketMqConstant;
import com.wanlianyida.process.infrastructure.enums.AutoAuditTypeEnum;
import com.wanlianyida.process.interfaces.model.command.SubmitAutoAuditCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 事件发布器
 */
@Slf4j
@Component
public class EventPublisher {


    @Resource
    private MqEventPublisher mqEventPublisher;

    /**
     *  destination :  topic:tag   可以不指定tag
     *  destination = topic:auto_audit_${audit_type}_${scene_type}
     * 发送mq消息
     * @param sendAutoAuditMsgBO
     */
    public Boolean sendAutoAuditMsg(SubmitAutoAuditCommand sendAutoAuditMsgBO){
        log.info("自动审核消息发送消息入参:{}", JSONUtil.toJsonStr(sendAutoAuditMsgBO));
        String destination = RocketMqConstant.CTP_AUTO_AUDIT_TOPIC +  ":" + AutoAuditTypeEnum.getHandlerByAction(sendAutoAuditMsgBO.getProcessNo());
        SendReceipt sendReceipt =  mqEventPublisher.publishNormalMessage(destination,
                MqEventMessage.buildEventMessage(destination, sendAutoAuditMsgBO));
        log.info("自动审核消息发送消息返回结果 to topic {} sendReceipt={}", destination, sendReceipt);
        return sendReceipt != null;
    }

}
