package com.wanlianyida.process.infrastructure.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/01/13/13:31
 */
@Getter
public enum CtpCoreProcessExceptionEnum {

    ERROR_ORDER_STATUS_NOT_RECEIPT("BCTP0301", "未查询到数据"),
    ERROR_ORDER_STATUS_NOT_OPEN("BCTP0302", "自动审核配置未开启"),
    ;

    private String code;

    private String msg;

    CtpCoreProcessExceptionEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
