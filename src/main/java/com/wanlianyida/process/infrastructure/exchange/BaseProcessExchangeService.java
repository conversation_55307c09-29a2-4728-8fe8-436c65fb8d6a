package com.wanlianyida.process.infrastructure.exchange;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbaseprocess.api.inter.ProcessInter;
import com.wanlianyida.fssbaseprocess.api.model.command.ProcessCreateCommand;
import com.wanlianyida.fssbaseprocess.api.model.command.ProcessStartCommand;
import com.wanlianyida.fssbaseprocess.api.model.dto.ProcessInstanceDTO;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/03/04/14:48
 */
@Slf4j
@Service
public class BaseProcessExchangeService {

    @Resource
    private ProcessInter processInter;

    public ResponseMessage<ProcessInstanceDTO> createProcess(ProcessCreateCommand command) {
        try {
            log.info("创建流程实例：{}", command);
            ResponseMessage<ProcessInstanceDTO> process = processInter.createProcess(command);
            log.info("创建流程实例：{}", process);
            return process;
        } catch (Exception e) {
            log.error("创建流程实例error：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public ResultMode<ProcessInstanceDTO> startProcess(ProcessStartCommand command) {
        log.info("创建流程实例参数：{}", command);
        try {
            ResponseMessage<ProcessInstanceDTO> res = processInter.startProcess(command);
            log.info("创建流程实例返回值：{}", res);
            if (res.isSucceed() && res.getModel() != null && StrUtil.isNotEmpty(res.getModel().getProcessInstanceId())) {
                // 启动成功
                return ResultMode.success(res.getModel());
            }
        } catch (Exception e) {
            log.error("创建流程实例异常", e);
        }
        // 启动失败
        log.error("流程启动失败");
        return ResultMode.fail();
    }
}
