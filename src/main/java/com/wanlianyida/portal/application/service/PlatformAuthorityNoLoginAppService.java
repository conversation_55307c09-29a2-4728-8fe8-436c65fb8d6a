package com.wanlianyida.portal.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserInfoQuery;
import com.wanlianyida.basicdata.model.command.PlatformUmLoginCommand;
import com.wanlianyida.basicdata.model.dto.PlatformCmSystemparameterDTO;
import com.wanlianyida.basicdata.model.dto.PlatformCompanyAccountDTO;
import com.wanlianyida.basicdata.model.dto.PlatformEdunewsarticlesDTO;
import com.wanlianyida.basicdata.model.query.PlatformCmSystemparameterQuery;
import com.wanlianyida.basicdata.model.query.PlatformEdunewsarticlesQuery;
import com.wanlianyida.basicdata.model.query.PlatformUmLogininfoQuery;
import com.wanlianyida.basicdata.model.query.PlatformUmUserbaseinfoQuery;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.constant.CommonRedisConstants;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.IdUtil;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.fssuserauth.api.enums.UserAuthEnums;
import com.wanlianyida.fssuserauth.api.model.command.UmLogininfoCommand;
import com.wanlianyida.fssuserauth.api.model.dto.UmLogininfoDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UserAccountInfoDTO;
import com.wanlianyida.fssuserauth.api.model.query.UmLogininfoQuery;
import com.wanlianyida.partner.api.inter.PersonalAccountInter;
import com.wanlianyida.partner.api.model.command.SwitchCompanyCommand;
import com.wanlianyida.partner.api.model.dto.SwitchCompanyDTO;
import com.wanlianyida.portal.infrastructure.constant.Constant;
import com.wanlianyida.portal.infrastructure.exechange.BasicDataExchangeService;
import com.wanlianyida.portal.infrastructure.exechange.MdmExchangeService;
import com.wanlianyida.portal.infrastructure.exechange.UserAuthExchangeService;
import com.wanlianyida.portal.infrastructure.utils.BizLogUtilService;
import com.wanlianyida.portal.interfaces.dto.UserLoginDTO;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 其他非登录接口信息 AppService
 *
 * <AUTHOR>
 * @date 2024-11-30
 */
@Slf4j
@Service
public class PlatformAuthorityNoLoginAppService {

    @Resource
    private BasicDataExchangeService basicDataExchangeService;
    @Resource
    private UserAuthExchangeService userAuthExchangeService;
    @Resource
    private MdmExchangeService mdmExchangeService;
    @Resource
    private IdUtil idUtil;
    @Resource
    private PersonalAccountInter personalAccountInter;
    @Resource
    private RedisService redisService;



    /**
     * 其他协议信息
     *
     * @param query
     * @return {@link PlatformEdunewsarticlesDTO}
     */
    public ResultMode<PlatformEdunewsarticlesDTO> getDetail(PlatformEdunewsarticlesQuery query) {
        return basicDataExchangeService.getDetail(query);
    }

    /**
     * 其他协议信息
     *
     * @param query
     * @return {@link PlatformEdunewsarticlesDTO}
     */
    public ResultMode<List<PlatformEdunewsarticlesDTO>> getDetailListById(PlatformEdunewsarticlesQuery query) {
        return basicDataExchangeService.getDetailListById(query);
    }

    /**
     * 系统参数
     *
     * @param query
     * @return {@link PlatformCmSystemparameterDTO}
     */
    public ResultMode<List<PlatformCmSystemparameterDTO>> platformCmSystemparameterPaging(PlatformCmSystemparameterQuery query) {
        ResultMode<List<PlatformCmSystemparameterDTO>> result = basicDataExchangeService.platformCmSystemparameterPaging(query);
        if (result != null && CollUtil.isNotEmpty(result.getModel())) {
            return ResultMode.success(result.getModel());
        }
        return ResultMode.success();
    }

    /**
     * 获取图形验证码：无需用户名
     *
     * @return {@link String}
     */
    public ResultMode<List<String>> getNumberVerificationCode() {
        ResultMode<List<String>> result = basicDataExchangeService.getNumberVerificationCode();
        if (result != null && CollUtil.isNotEmpty(result.getModel())) {
            return ResultMode.success(result.getModel());
        }
        return ResultMode.fail("查询图形码失败");
    }

    /**
     * 手机号重复校验
     *
     * @param query
     * @return {@link Boolean}
     */
    public ResultMode<Boolean> repeatAssociatorTelephoneCheck(PlatformUmLogininfoQuery query) {
        return basicDataExchangeService.repeatAssociatorTelephoneCheck(query);
    }

    /**
     * 获取短信验证码：要求输入图形码
     *
     * @param query
     * @return {@link String}
     */
    public ResultMode<String> getSignUpSmsVerificationCode(PlatformUmUserbaseinfoQuery query) {
        return basicDataExchangeService.getSignUpSmsVerificationCode(query);
    }

    /**
     * 获取短信验证码：无锡输入图形码
     *
     * @param query
     * @return {@link String}
     */
    public ResultMode<String> getMessageValidateCode(PlatformUmUserbaseinfoQuery query) {
        return basicDataExchangeService.getMessageValidateCode(query);
    }

    /**
     * 校验短信验证码
     *
     * @param query
     * @return {@link String}
     */
    public ResultMode<String> checkMessageValidateCode(PlatformUmUserbaseinfoQuery query) {
        return basicDataExchangeService.checkMessageValidateCode(query);
    }

    /**
     * 校验图形验证码
     *
     * @param query
     * @return {@link String}
     */
    public ResultMode<String> checkGraphValidateCode(PlatformUmUserbaseinfoQuery query) {
        return basicDataExchangeService.checkGraphValidateCode(query);
    }

    /**
     * 校验用户名重复
     *
     * @param query
     * @return {@link Boolean}
     */
    public ResultMode<Boolean> repeatLoginNameCheck(PlatformUmLogininfoQuery query) {
        return basicDataExchangeService.repeatLoginNameCheck(query);
    }

    /**
     * 用户端统一登录
     *
     * @param command
     * @return {@link Boolean}
     */
    public ResultMode<UserLoginDTO> login(PlatformUmLoginCommand command, HttpServletRequest request) {
        ResultMode<UserLoginDTO> userLoginDTOResultMode = new ResultMode<>();
        if (StrUtil.isBlank(command.getLoginType())) {
            command.setLoginType(UserAuthEnums.LoginTypeEnum.PWD.getCode());
        }
        ResultMode<SwitchCompanyDTO> rs = null;
        String userId = null;
        boolean oldLogin = true;
        MdmUserInfoDTO mdmUserInfoDTO = null;
        if (StrUtil.equals(command.getLoginType(), UserAuthEnums.LoginTypeEnum.PWD.getCode())) {
            ResultMode<UmLogininfoDTO> umLogininfoDTOResultMode = getUmLogininfoDTO(command);
            if (!umLogininfoDTOResultMode.isSucceed()) {
                return ResultMode.fail(umLogininfoDTOResultMode.getMessage());
            }
            UmLogininfoDTO umLogininfoDTO = umLogininfoDTOResultMode.getModel();
            if (StrUtil.isNotBlank(umLogininfoDTO.getTenantId())) {
                ResultMode<MdmUserInfoDTO> mdmCmdUserInfoDTOResultMode = getMdmCmdUserInfoDTOById(umLogininfoDTO);
                if (!mdmCmdUserInfoDTOResultMode.isSucceed()) {
                    return ResultMode.fail(mdmCmdUserInfoDTOResultMode.getMessage());
                }
                mdmUserInfoDTO = mdmCmdUserInfoDTOResultMode.getModel();
                if (StrUtil.equals(mdmUserInfoDTO.getEnableFlag(), UserAuthEnums.YesNoEnum.NO.getCode())) {
                    return ResultMode.fail("您的账号已被大宗商品交易平台停用，请联系该平台客服");
                }
                oldLogin = false;
            }
        }
        String loginName = command.getLoginName();
        if (StrUtil.equals(command.getLoginType(), UserAuthEnums.LoginTypeEnum.SMS.getCode())) {
            if (!Validator.isMobile(loginName)) {
                return ResultMode.fail("手机号格式不正确");
            }
            ResultMode<MdmUserInfoDTO> mdmCmdUserInfoDTOBuPhoneResultMode = getMdmCmdUserInfoDTOByPhone(command);
            if (!mdmCmdUserInfoDTOBuPhoneResultMode.isSucceed()) {
                return ResultMode.fail(mdmCmdUserInfoDTOBuPhoneResultMode.getMessage());
            }
            mdmUserInfoDTO = mdmCmdUserInfoDTOBuPhoneResultMode.getModel();
            if (StrUtil.equals(mdmUserInfoDTO.getEnableFlag(), UserAuthEnums.YesNoEnum.NO.getCode())) {
                return ResultMode.fail("您的账号已被大宗商品交易平台停用，请联系该平台客服");
            }
            oldLogin = false;
        }
        if (!oldLogin) {
            UmLogininfoCommand unCommand = new UmLogininfoCommand();
            BeanUtil.copyProperties(command, unCommand);
            unCommand.setMessageValidateCode(command.getSmsCode());
            unCommand.setGraphValidateCode(command.getVerifyCode());
            unCommand.setGraphValidateCodeRedisKey(loginName);
            userLoginDTOResultMode = userAuthExchangeService.loginNew(unCommand, mdmUserInfoDTO, request);
            // 限制登录频率：true登录失败，false登录成功
            Boolean loginResultFlag = false;
            if (!userLoginDTOResultMode.isSucceed()) {
                loginResultFlag = true;
            }
            PlatformUmLoginCommand model = new PlatformUmLoginCommand();
            BeanUtil.copyProperties(unCommand, model);
            model.setLoginResultFlag(loginResultFlag);
            ResultMode<String> resultMode = basicDataExchangeService.verifyLoginErrorPolicy(model);
            if (!resultMode.isSucceed()) {
                return ResultMode.fail(resultMode.getMessage());
            }
            // 限制登录频率无拦截正常向下走
            if (!userLoginDTOResultMode.isSucceed()) {
                return userLoginDTOResultMode;
            }
            // 调用切换企业接口：要设置token传递下去
            SwitchCompanyCommand switchCompanyCommand = new SwitchCompanyCommand();
            switchCompanyCommand.setScene(UserAuthEnums.SwitchCompanySceneEnum.LOGIN.getCode());
            rs = personalAccountInter.switchCompanyWithToken(userLoginDTOResultMode.getModel().getToken(), switchCompanyCommand);
            log.info("login#调用切换企业接口结果，rs->{}", JSONUtil.toJsonStr(rs));
        }
        if (oldLogin) {
            // 短信登录的，最后走旧登录时将密码传过去，短信码在此处直接验证
            if (StrUtil.equals(command.getLoginType(), UserAuthEnums.LoginTypeEnum.SMS.getCode())) {
                if (StrUtil.isBlank(command.getSmsCode())) {
                    return ResultMode.fail("短信验证码不能为空");
                }
                //验短信码--platform
                PlatformUmUserbaseinfoQuery querySms = new PlatformUmUserbaseinfoQuery();
                querySms.setTelephone(loginName);
                querySms.setMessageValidateCode(command.getSmsCode());
                ResultMode<String> resultModeSms = basicDataExchangeService.checkMessageValidateCode(querySms);
                if (!resultModeSms.isSucceed()) {
                    return ResultMode.fail(resultModeSms.getCode(), resultModeSms.getMessage());
                }
                // 查账号的密码
                ResultMode<UmLogininfoDTO> umLogininfoDTOResultMode = getUmLogininfoDTO(command);
                if (!umLogininfoDTOResultMode.isSucceed()) {
                    return ResultMode.fail(umLogininfoDTOResultMode.getMessage());
                }
                command.setPassword(umLogininfoDTOResultMode.getModel().getPassword());
            }
            PlatformUmLoginCommand info = new PlatformUmLoginCommand();
            BeanUtil.copyProperties(command, info);
            userLoginDTOResultMode = basicDataExchangeService.login(info);
            if (!userLoginDTOResultMode.isSucceed()) {
                return userLoginDTOResultMode;
            }

            // 调用切换企业接口：要设置token传递下去
            SwitchCompanyCommand switchCompanyCommand = new SwitchCompanyCommand();
            switchCompanyCommand.setScene(UserAuthEnums.SwitchCompanySceneEnum.LOGIN.getCode());
            rs = personalAccountInter.switchCompanyWithToken(userLoginDTOResultMode.getModel().getToken(), switchCompanyCommand);
            log.info("login#调用切换企业接口结果，rs->{}", JSONUtil.toJsonStr(rs));
            // 老账号也要记录登录日志--log
            UserAccountInfoDTO userAccountInfoDTO = new UserAccountInfoDTO();
            userAccountInfoDTO.setLoginName(loginName);
            userAccountInfoDTO.setTenantId(userLoginDTOResultMode.getModel().getUserBaseId());
            if (rs.isSucceed()){
                SwitchCompanyDTO rsModel = rs.getModel();
                if (ObjectUtil.isNotEmpty(rsModel)){
                    userAccountInfoDTO.setTenantId(userId);
                }
            }
            BizLogUtilService.sendLogBizMsg(userAccountInfoDTO, request);
        }


        UserLoginDTO loginDTO = userLoginDTOResultMode.getModel();
        // 缓存用户操作时间(登录后有调platform接口会有缓存校验)
        basicDataExchangeService.redisLastOperationTime(loginDTO.getUserBaseId());
        if (rs.isSucceed()){
            SwitchCompanyDTO rsModel = rs.getModel();
            if (ObjectUtil.isNotEmpty(rsModel)){
                userId = rsModel.getUserId();
            }
        }
        // 只处理userId不为空的场景。物流登录后userId拿不到
        if (StrUtil.isNotEmpty(loginDTO.getToken()) && StrUtil.isNotEmpty(userId)){
            loginDTO = processToken(loginDTO, userId);
            // 保存登录token
            String redisKey = CommonRedisConstants.REDIS_TOKENINFO_INFO_KEY + userId;
            redisService.set(redisKey, loginDTO.getToken());
        }

        return ResultMode.success(loginDTO);
    }


    /**
     * 处理token
     * @param loginDTO
     * @param userId
     * @return
     */
    public UserLoginDTO processToken(UserLoginDTO loginDTO,String userId) {
        UserLoginDTO userLoginDTO = new UserLoginDTO();
        String tokenStr = loginDTO.getToken();
        // 解析JWT Claims
        Claims claims = JwtUtil.getClaimByToken(tokenStr);
        String subject = claims.getSubject();
        TokenInfo bean = JSONUtil.toBean(subject, TokenInfo.class);
        if (ObjectUtil.isNotEmpty(bean)){
            bean.setUserId(userId);
            String tokenInfoString = JSON.toJSONString(bean);
            String newToken = JwtUtil.generateToken(tokenInfoString);
            userLoginDTO.setToken(newToken);
            return userLoginDTO;
        }
        return null;
    }

    /**
     * 用户端注册静默免密码登录
     * 直接生成token返回
     *
     * @param tokenInfo
     * @param request
     * @return {@link ResultMode }<{@link UserLoginDTO }>
     */
    public ResultMode<UserLoginDTO> initLogin(TokenInfo tokenInfo, HttpServletRequest request) {
        /** 俩参数调用方补全
         tokenInfo.setUserBaseId("用户主数据的id");
         tokenInfo.setLoginName("规则生成的用户名");
         **/
        tokenInfo.setClentId(idUtil.generateId(Constant.CTP_ORCH_PORTAL).toString());
        return userAuthExchangeService.initLogin(tokenInfo, request);
    }

    /**
     * 信用代码查企业管理员账号及资质的手机号联系人
     *
     * @param socialCreditCode
     * @return {@link PlatformCompanyAccountDTO}
     */
    public ResultMode<PlatformCompanyAccountDTO> getCompanyAccount(String socialCreditCode) {
        return basicDataExchangeService.getCompanyAccount(socialCreditCode);
    }

    /**
     * 获取图形验证码：需要用户名
     *
     * @return {@link String}
     */
    public ResultMode<List<String>> getVerificationCode(PlatformUmLogininfoQuery query) {
        ResultMode<List<String>> result = basicDataExchangeService.getVerificationCode(query);
        if (result != null && CollUtil.isNotEmpty(result.getModel())) {
            return ResultMode.success(result.getModel());
        }
        return ResultMode.fail("查询图形码失败");
    }

    public ResultMode resetPW(UmLogininfoCommand command) {
        return userAuthExchangeService.resetPwd(command);
    }

    private ResultMode<UmLogininfoDTO> getUmLogininfoDTO(PlatformUmLoginCommand command) {
        UmLogininfoDTO umLogininfoDTO;
        UmLogininfoQuery query = new UmLogininfoQuery();
        query.setLoginName(command.getLoginName());
        query.setCreateSourse("1");
        ResultMode<UmLogininfoDTO> loginNameResultMode = userAuthExchangeService.getLoginInfo(query);
        if (!loginNameResultMode.isSucceed()) {
            /** 不支持手机号密码登录
             query.setLoginName(null);
             query.setTelephone(command.getLoginName());
             ResultMode<UmLogininfoDTO> phoneResultMode = userAuthExchangeService.getLoginInfo(query);
             if (!phoneResultMode.isSucceed()) {
             return ResultMode.fail("用户名不存在");
             } else {
             umLogininfoDTO = phoneResultMode.getModel();
             }
             **/
            return ResultMode.fail("用户名不存在");
        }
        umLogininfoDTO = loginNameResultMode.getModel();
        return ResultMode.success(umLogininfoDTO);
    }

    private ResultMode<MdmUserInfoDTO> getMdmCmdUserInfoDTOById(UmLogininfoDTO umLogininfoDTO) {
        MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
        mdmUserInfoQuery.setId(Long.valueOf(umLogininfoDTO.getTenantId()));
        ResultMode<MdmUserInfoDTO> cmdUserInfoResultMode = mdmExchangeService.getMdmCmdUserInfo(mdmUserInfoQuery);
        if (!cmdUserInfoResultMode.isSucceed() || cmdUserInfoResultMode.getModel() == null) {
            return ResultMode.fail("用户不存在");
        }
        return ResultMode.success(cmdUserInfoResultMode.getModel());
    }

    private ResultMode<MdmUserInfoDTO> getMdmCmdUserInfoDTOByLoginName(PlatformUmLoginCommand command) {
        MdmUserInfoQuery mdmUserInfoQuery = new MdmUserInfoQuery();
        mdmUserInfoQuery.setLoginName(command.getLoginName());
        ResultMode<MdmUserInfoDTO> cmdUserInfoResultMode = mdmExchangeService.getMdmCmdUserInfo(mdmUserInfoQuery);
        if (!cmdUserInfoResultMode.isSucceed() || cmdUserInfoResultMode.getModel() == null) {
            return ResultMode.fail("用户名不存在");
        }
        return ResultMode.success(cmdUserInfoResultMode.getModel());
    }

    public ResultMode<MdmUserInfoDTO> getMdmCmdUserInfoDTOByPhone(PlatformUmLoginCommand command) {
        MdmUserInfoQuery queryUser = new MdmUserInfoQuery();
        queryUser.setMobile(command.getLoginName());
        ResultMode<MdmUserInfoDTO> responseMessageCmdUserInfo = mdmExchangeService.getMdmCmdUserInfo(queryUser);
        if (!responseMessageCmdUserInfo.isSucceed() || responseMessageCmdUserInfo.getModel() == null) {
            return ResultMode.fail("手机号不存在");
        }
        return ResultMode.success(responseMessageCmdUserInfo.getModel());
    }
}
