package com.wanlianyida.portal.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.portal.infrastructure.constant.MqConstant;
import com.wanlianyida.portal.interfaces.command.StatisticsCollectCommand;
import com.wanlianyida.portal.interfaces.dto.StatisticsCollectDTO;
import com.wanlianyida.support.api.inter.StatisticsCountInter;
import com.wanlianyida.support.api.model.command.StatisticsCountCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/03/13
 *  pv uv统计
 */
@Service
@Slf4j
public class StatisticsCountAppService {
    @Resource
    private StatisticsCountInter statisticsCountInter;
    @Resource
    private MqEventPublisher mqEventPublisher;

    /**
     * 添加pv uv
     * @param countCommand
     * @return {@link ResultMode }<{@link Void }>
     */
    public ResultMode<Void> addStatisticsCount(StatisticsCountCommand countCommand){
        return statisticsCountInter.addStatisticsCount(countCommand);
    }

    /**
     * 发送统计信息数据到后端
     */
    public ResultMode<Void> statisticCollect(StatisticsCollectCommand command){
        StatisticsCollectDTO collectDTO = BeanUtil.toBean(command, StatisticsCollectDTO.class);
        collectDTO.setId(UUID.fastUUID().toString(true));
        collectDTO.setOperateTime(DateUtil.now());
        MqEventMessage.EventMessage<StatisticsCollectDTO> eventMessage = MqEventMessage.buildEventMessage(MqConstant.BI_STATISTICS_TOPIC, collectDTO);
        log.info("info={}", JSONUtil.toJsonStr(eventMessage));
        mqEventPublisher.syncSendNormalMessage(eventMessage);
        return ResultMode.success();
    }
}
