package com.wanlianyida.portal.application.service.markettrends;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.portal.infrastructure.exechange.BaseBiExchangeService;
import com.wanlianyida.portal.interfaces.dto.AdsCtpsPlatCommodityPriceInfoHfDTO;
import com.wanlianyida.portal.interfaces.query.CtpsPlatCommodityPriceInfoQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AdsCtpsPlatCommodityPriceInfoHfAppService {

    @Autowired
    private BaseBiExchangeService baseBiExchangeService;

    public ResultMode<List<AdsCtpsPlatCommodityPriceInfoHfDTO>> queryPageList(PagingInfo<CtpsPlatCommodityPriceInfoQuery> pagingInfo) {
        return baseBiExchangeService.queryPageList(pagingInfo);
    }

    public ResultMode<AdsCtpsPlatCommodityPriceInfoHfDTO> querySynTime() {
        return baseBiExchangeService.querySynTime();
    }
}
