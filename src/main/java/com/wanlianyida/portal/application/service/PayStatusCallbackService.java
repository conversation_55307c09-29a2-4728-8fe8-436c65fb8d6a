package com.wanlianyida.portal.application.service;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.portal.infrastructure.exechange.PayStatusCallbackExchangeService;
import com.wanlianyida.sett.api.model.command.BankLargeOrderBackCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class PayStatusCallbackService {

    @Resource
    private PayStatusCallbackExchangeService payStatusCallbackExchangeService;

    /**
     * 大额订单回调接口
     */
    public ResultMode<Void> payStatusCallback(BankLargeOrderBackCommand command) {
        return payStatusCallbackExchangeService.payStatusCallback(command);
    }
}
