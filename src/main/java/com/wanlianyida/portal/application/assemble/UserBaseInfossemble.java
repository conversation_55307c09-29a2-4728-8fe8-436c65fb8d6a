package com.wanlianyida.portal.application.assemble;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.portal.interfaces.util.CtpBeanUtil;

/**
 * <AUTHOR>
 * @since 2024/11/25/13:27
 */
public class UserBaseInfossemble {


    public static Object setAddBaseInfo(Object command)
    {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        CtpBeanUtil.setProperty(command, "creatorId", StrUtil.isNotEmpty(tokenInfo.getUserBaseId()) ? tokenInfo.getUserBaseId() : null);
        CtpBeanUtil.setProperty(command, "creatorName", StrUtil.isNotEmpty(tokenInfo.getLoginName()) ? tokenInfo.getLoginName() : null);
        return command;
    }

    public static Object setUpdateBaseInfo(Object command)
    {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        CtpBeanUtil.setProperty(command, "updaterId", StrUtil.isNotEmpty(tokenInfo.getUserBaseId()) ? tokenInfo.getUserBaseId() : null);
        CtpBeanUtil.setProperty(command, "updaterName", StrUtil.isNotEmpty(tokenInfo.getLoginName()) ? tokenInfo.getLoginName() : null);
        return command;
    }
}
