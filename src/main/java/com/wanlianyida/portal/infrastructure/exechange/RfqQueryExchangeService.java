package com.wanlianyida.portal.infrastructure.exechange;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.utils.PagingUtil;
import com.wanlianyida.framework.ctpcommon.utils.ResultModeUtil;
import com.wanlianyida.portal.interfaces.dto.RfqBidPageListPortalDTO;
import com.wanlianyida.portal.interfaces.dto.RfqDetailPortalDTO;
import com.wanlianyida.portal.interfaces.dto.RfqProductPortalDTO;
import com.wanlianyida.portal.interfaces.dto.TxRfqOrderDTO;
import com.wanlianyida.portal.interfaces.query.RfqBidPageListPortalQuery;
import com.wanlianyida.portal.interfaces.query.RfqDetailPortalQuery;
import com.wanlianyida.transaction.api.inter.RfqQueryInter;
import com.wanlianyida.transaction.api.model.dto.RfqDetailDTO;
import com.wanlianyida.transaction.api.model.query.RfqBidPageListQuery;
import com.wanlianyida.transaction.api.model.query.RfqDetailQuery;
import com.wanlianyida.transaction.api.model.query.RfqProductApiQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/06/17/17:00
 */
@Service
@Slf4j
public class RfqQueryExchangeService {

    @Resource
    private RfqQueryInter rfqQueryInter;
    /**
     * 询比价单详情
     */
    public ResultMode<RfqDetailPortalDTO> queryRfqDetail(RfqDetailPortalQuery pageQuery) {
        RfqDetailQuery rfqDetailQuery = BeanUtil.copyProperties(pageQuery, RfqDetailQuery.class);
        ResultMode<RfqDetailDTO> rfqDetailDTOResultMode = rfqQueryInter.queryRfqDetail(rfqDetailQuery);
        return ResultModeUtil.convertResultMode(rfqDetailDTOResultMode, RfqDetailPortalDTO.class);
    }

    /**
     * 询比价单详情-出价记录
     **/
    public ResultMode<List<RfqBidPageListPortalDTO>> queryRfqBidPageList(PagingInfo<RfqBidPageListPortalQuery> pageQuery) {
        PagingInfo<RfqBidPageListQuery> rfqBidPageListQueryPagingInfo = PagingUtil.convertPagingInfo(pageQuery, RfqBidPageListQuery.class);
        return ResultModeUtil.convertResultModeList(rfqQueryInter.queryRfqBidPageList(rfqBidPageListQueryPagingInfo), RfqBidPageListPortalDTO.class);
    }

    public ResultMode<TxRfqOrderDTO> queryDetailRfqOrder(RfqDetailQuery pageQuery) {
        return ResultModeUtil.convertResultMode(rfqQueryInter.queryDetailRfqOrder(pageQuery), TxRfqOrderDTO.class);
    }

    public ResultMode<List<RfqProductPortalDTO>> queryRfqProductList(PagingInfo<RfqDetailPortalQuery> pageQuery) {
        PagingInfo<RfqProductApiQuery> pageInfo = PagingUtil.convertPagingInfo(pageQuery, RfqProductApiQuery.class);
        return ResultModeUtil.convertResultModeList(rfqQueryInter.queryRfqProductList(pageInfo), RfqProductPortalDTO.class);
    }
}
