package com.wanlianyida.portal.interfaces.inter;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.dto.BannerDTO;
import com.wanlianyida.portal.infrastructure.exechange.BaseContentExchangeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 轮播图
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@RestController
@RequestMapping("/content/banner")
public class BannerController {

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    /**
     * 查询列表（首页）
     *
     * @param relAdId rel 广告 ID
     * @return {@link ResultMode }<{@link List }<{@link BannerDTO }>>
     */
    @GetMapping("/queryList/{id}")
    public ResultMode<List<BannerDTO>> queryList(@PathVariable("id") Long relAdId){
        return baseContentExchangeService.bannerInfoInterQueryList(relAdId);

    }
}
