//package com.wanlianyida.portal.interfaces.inter.callback;
//
//import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
//import com.wanlianyida.portal.application.service.BankCallbackAppService;
//import com.wanlianyida.sett.api.model.command.BankCardBindCallBackCommand;
//import com.wanlianyida.sett.api.model.command.BankOpenCallBackCommand;
//import com.wanlianyida.sett.api.model.command.BankOpenInfoQueryCommand;
//import com.wanlianyida.sett.api.model.command.BankWithdrawCallBackCommand;
//import com.wanlianyida.sett.api.model.dto.BankOpenInfoQueryResDTO;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 银行回调通知接口
// */
//@RestController
//@RequestMapping("/inner/bank")
//public class BankCallbackController {
//
//    @Resource
//    BankCallbackAppService bankCallbackAppService;
//
//
//
//    /**
//     * 银行提现回调
//     */
//    @PostMapping("/withdrawCallback")
//    public ResultMode<Void> withdrawCallback(@RequestBody @Validated BankWithdrawCallBackCommand command) {
//        return bankCallbackAppService.withdrawCallBack(command);
//    }
//
//
//    /**
//     * 银行开户回调
//     */
//    @PostMapping("/bankOpenCallback")
//    public ResultMode<Void> bankOpenCallback(@RequestBody @Validated BankOpenCallBackCommand command) {
//        return bankCallbackAppService.bankOpenCallback(command);
//    }
//
//    /**
//     * 银行绑卡回调接口
//     */
//    @PostMapping("/bindCardCallback")
//    public ResultMode<Void> bindCardCallback(@RequestBody @Validated BankCardBindCallBackCommand command) {
//        return bankCallbackAppService.bindCardCallback(command);
//    }
//
//    /**
//     * 查询开户信息接口
//     */
//    @PostMapping("/bankOpenInfoQuery")
//    public ResultMode<BankOpenInfoQueryResDTO> bankOpenInfoQuery(@RequestBody @Validated BankOpenInfoQueryCommand command) {
//        return bankCallbackAppService.bankOpenInfoQuery(command);
//    }
//
//}
