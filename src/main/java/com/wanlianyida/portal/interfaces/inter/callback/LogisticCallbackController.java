//package com.wanlianyida.portal.interfaces.inter.callback;
//
//import com.alibaba.fastjson.JSON;
//import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
//import com.wanlianyida.order.api.model.command.LogisticsQuotationCallbackRequest;
//import com.wanlianyida.order.api.model.command.QuotationShipStatusCallbackCommand;
//import com.wanlianyida.order.api.model.command.WaybillCallbackCommand;
//import com.wanlianyida.portal.infrastructure.exechange.OrderExchangeService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 物流回调通知接口
// */
//@RestController
//@RequestMapping("/inner/lgi")
//@Slf4j
//public class LogisticCallbackController {
//
//    @Resource
//    OrderExchangeService orderExchangeService;
//
//    /**
//     * 报价成功状态回调
//     */
//    @PostMapping("/quotation/callback")
//    public ResultMode<Object> quotationCallback(@RequestBody LogisticsQuotationCallbackRequest command) {
//        log.info("[LogisticCallbackController.quotationCallback->报价回调] ,request:{}", JSON.toJSONString(command));
//        return orderExchangeService.quotationCallback(command);
//    }
//
//    /**
//     * 生成运单、运单签收回调
//     */
//    @PostMapping("/waybill/callback")
//    public ResultMode<Void> waybillCallback(@RequestBody WaybillCallbackCommand command) {
//        return orderExchangeService.waybillCallback(command);
//    }
//
//    /**
//     * 询价单发货状态回调
//     */
//    @PostMapping("/quotationShipStatus/callback")
//    public ResultMode<Void> quotationShipStatusCallBack(@RequestBody QuotationShipStatusCallbackCommand command) {
//        return orderExchangeService.quotationShipStatusCallBack(command);
//    }
//
//}
