package com.wanlianyida.portal.interfaces.inter;

import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.dto.ShopContactsDTO;
import com.wanlianyida.partner.api.model.dto.ShopDTO;
import com.wanlianyida.partner.api.model.dto.UmShopDTO;
import com.wanlianyida.partner.api.model.query.queryShopHallByPageQuery;
import com.wanlianyida.portal.application.service.ShopAppService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/partner/um/shop")
public class ShopController {
    @Resource
    private ShopAppService appService;



    /**
     * 根据店铺id/公司id 查询店铺信息
     *
     * @param shopId 店铺id
     * @return {@code ResultMode<UmShopDTO> }
     */
    @PostMapping("/shopDetailByShopIdOrCompanyId")
    public ResultMode<ShopDTO> shopDetailByShopIdOrCompanyId(@RequestParam(value = "shopId", required = false) Long shopId,
                                                             @RequestParam(value = "companyId", required = false) String companyId) {
        return appService.shopDetailByShopIdOrCompanyId(shopId,companyId);
    }

    /**
     * 批量获取店铺联系方式
     *
     * @param shopIds 店铺id列表
     * @return {@code ResultMode<String> }
     */
    @PostMapping("/getShopContactsList")
    public ResultMode<List<ShopContactsDTO>> getShopContactsList(@RequestParam List<Long> shopIds) {
        return appService.getShopContactsList(shopIds);
    }

    /**
     * 店铺大厅分页查询
     *
     * @param pagingInfo
     * @return
     */
    @PostMapping("/queryShopHallByPage")
    public ResultMode queryShopHallByPage(@RequestBody PagingInfo<queryShopHallByPageQuery> pagingInfo){
        PageInfo<UmShopDTO> umShopDTOPageInfo = appService.queryShopHallByPage(pagingInfo);
        return ResultMode.successPageList(umShopDTOPageInfo.getList(),(int)umShopDTOPageInfo.getTotal());
    }
}
