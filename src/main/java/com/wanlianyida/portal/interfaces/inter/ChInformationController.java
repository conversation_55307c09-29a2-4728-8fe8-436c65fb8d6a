package com.wanlianyida.portal.interfaces.inter;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbasecontent.api.model.command.*;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationDetailUserDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationListDTO;
import com.wanlianyida.fssbasecontent.api.model.dto.ChInformationListUserDTO;
import com.wanlianyida.fssbasecontent.api.model.query.ChInformationListQuery;
import com.wanlianyida.portal.application.service.ChInformationService;
import com.wanlianyida.portal.infrastructure.exechange.BaseContentExchangeService;
import com.wanlianyida.portal.interfaces.command.NoticePopCommand;
import com.wanlianyida.portal.interfaces.dto.NoticePopDTO;
import com.wanlianyida.portal.interfaces.query.NoticePopListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 *  内容管理
 * <AUTHOR>
 * @since 2024/11/21/19:19
 */
@RestController
@RequestMapping("/content/information")
public class ChInformationController {

    @Resource
    private ChInformationService chInformationService;

    @Resource
    private BaseContentExchangeService baseContentExchangeService;

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping("/listPage")
    public ResultMode<List<ChInformationListDTO>> list(@Validated @RequestBody PagingInfo<ChInformationListQuery> query) {
        return baseContentExchangeService.chInformationInterList(query);
    }

    @PostMapping("/listPageUser")
    public ResultMode<List<ChInformationListUserDTO>> listPageUser(@Validated @RequestBody PagingInfo<ChInformationListQuery> query) {
        return baseContentExchangeService.chInformationInterListUser(query);
    }
    @PostMapping("/selectUnionData")
    public ResultMode<List<ChInformationListDTO>> selectUnionData(@Validated @RequestBody PagingInfo<ChInformationListQuery> query) {
        return baseContentExchangeService.selectUnionData(query);
    }
    /**
     * 内容详情 后台
     * @param infoId
     * @return
     */
    @GetMapping("/detailById")
    public ResultMode<ChInformationDetailDTO> detailById(@RequestParam(name = "infoId", required = true) String infoId,
                                                         @RequestParam(name = "callAlias",required = false)  String callAlias,
                                                         @RequestParam(name = "bussCategory",required = false)  String bussCategory){
        return chInformationService.detailById(infoId, callAlias, bussCategory);
    }

    /**
     * 内容详情 门户
     * @param infoId
     * @return
     */
    @GetMapping("/detailByIdUser")
    ResultMode<ChInformationDetailUserDTO> detailByIdUser(@RequestParam(name = "infoId") String infoId){
        return baseContentExchangeService.chInformationInterDetailByIdUser(infoId);
    }

    /**
     * 关于我们
     * @param callAlias
     * @return
     */
    @GetMapping("/detailByCallAlias")
    public ResultMode<ChInformationDetailUserDTO> detailByCallAlias(
            @RequestParam(name = "callAlias")  String callAlias,
            @RequestParam(name = "bussCategory") String bussCategory) {
        return baseContentExchangeService.chInformationInterDetailByCallAlias(callAlias, bussCategory);
    }
    /**
     * 新增
     * @param addCommand
     * @return
     */
    @PostMapping("/add")
    public ResultMode add(@Validated @RequestBody ChinformationAddICommand addCommand) {
        return baseContentExchangeService.chInformationInterAdd(addCommand);
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/update")
    public ResultMode update(@RequestBody ChinformationUpdateICommand updateCommand){
        return baseContentExchangeService.chInformationInterUpdate(updateCommand);
    }


    /**
     * 批量删除内容
     * @param deleteCommand
     * @return
     */
    @PostMapping("/batchDelete")
    public ResultMode<Boolean> batchDelete(@RequestBody ChInformationBatchDeleteICommand deleteCommand) {
        return baseContentExchangeService.chInformationInterBatchDelete(deleteCommand);
    }
    /**
     * 批量审核
     * @param auditCommand
     * @return
     */
    @PostMapping("/batchAudit")
    public ResultMode<Boolean> batchAudit(@RequestBody ChInformationAuditDeleteICommand auditCommand) {
        return baseContentExchangeService.chInformationInterBatchAudit(auditCommand);
    }

    @PostMapping("/homepage")
    ResultMode<Map<String, List<ChInformationListDTO>>> homepage(@RequestBody  Map<String, Map<String, String>> map) {
        return baseContentExchangeService.chInformationInterHomepage(map);
    }


    /**
     * 公告弹窗查询
     * @param query
     * @return
     */
    @PostMapping("/query-notice-pop-list")
    public ResultMode<NoticePopDTO> queryNoticePopList(@RequestBody NoticePopListQuery query) {
        return baseContentExchangeService.queryNoticePopList(query);
    }

    /**
     * 公告弹窗确认
     * @param
     * @return
     */
    @PostMapping("/confirm-notice-pop")
    public ResultMode<Boolean> confirmNoticePop(@RequestBody NoticePopCommand command) {
        return baseContentExchangeService.confirmNoticePop(command);
    }

}
