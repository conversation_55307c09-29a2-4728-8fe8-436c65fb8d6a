package com.wanlianyida.portal.interfaces.inter;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.portal.application.service.BdPricingTypeConfigAppService;
import com.wanlianyida.support.api.model.dto.BdPricingTypeConfigDTO;
import com.wanlianyida.support.api.model.query.BdPricingTypeConfigQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/pricingType")
public class BdPricingTypeConfigController {

    @Resource
    private BdPricingTypeConfigAppService pricingTypeConfigAppService;

    /**
     * 查询
     */
    @PostMapping("/query")
    public ResultMode<List<BdPricingTypeConfigDTO>> query(@RequestBody BdPricingTypeConfigQuery query) {
        return pricingTypeConfigAppService.query(query);
    }

}
