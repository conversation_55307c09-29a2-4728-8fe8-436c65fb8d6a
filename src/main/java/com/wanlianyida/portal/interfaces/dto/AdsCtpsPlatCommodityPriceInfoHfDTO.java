package com.wanlianyida.portal.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.ctpcommon.model.dto.FacadeBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AdsCtpsPlatCommodityPriceInfoHfDTO extends FacadeBaseDTO implements Serializable {

//    /**
//     * 周期类型 | 日、周、月
//     */
//    @ApiModelProperty("周期类型 | 日、周、月")
//    private String trType;
//
//    /**
//     * 时间范围值
//     */
//    @ApiModelProperty("时间范围值")
//    private String trValue;
//
//    /**
//     * 三级品类名称
//     */
//    @ApiModelProperty("三级品类名称")
//    private String cateName;

//    /**
//     * 产地编码
//     */
//    @ApiModelProperty("产地编码")
//    private String prdCode;

//    /**
//     * 品种编码
//     */
//    @ApiModelProperty("品种编码")
//    private String breedCode;

//    /**
//     * 材质编码
//     */
//    @ApiModelProperty("材质编码")
//    private String mqCode;

//    /**
//     * 规格编码
//     */
//    @ApiModelProperty("规格编码")
//    private String scCode;

    /**
     * 产地名称
     */
    @ApiModelProperty("产地名称")
    private String prdName;

    /**
     * 产地简称
     */
    @ApiModelProperty("产地简称")
    private String prdShortName;

//    /**
//     * 品种名称
//     */
//    @ApiModelProperty("品种名称")
//    private String breedName;
//
//    /**
//     * 品种简称
//     */
//    @ApiModelProperty("品种简称")
//    private String breedShortName;

//    /**
//     * 材质名称
//     */
//    @ApiModelProperty("材质名称")
//    private String mqName;
//
//    /**
//     * 材质简称
//     */
//    @ApiModelProperty("材质简称")
//    private String mqShortName;

//    /**
//     * 规格名称
//     */
//    @ApiModelProperty("规格名称")
//    private String scName;
//
//    /**
//     * 规格简称
//     */
//    @ApiModelProperty("规格简称")
//    private String scShortName;

    /**
     * 单位名称
     */
    @ApiModelProperty("单位名称")
    private String unitName;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    private BigDecimal dataValue;

    /**
     * 涨跌值
     */
    @ApiModelProperty("涨跌值")
    private BigDecimal chgValue;

    /**
     * 涨跌幅
     */
    @ApiModelProperty("涨跌幅")
    private BigDecimal chgRate;

    /**
     * 数据同步时间(datetime)
     */
    @ApiModelProperty("数据同步时间(datetime)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date synTime;


}
