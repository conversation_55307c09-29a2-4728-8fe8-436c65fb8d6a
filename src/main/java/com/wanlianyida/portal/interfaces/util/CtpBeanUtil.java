package com.wanlianyida.portal.interfaces.util;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @since 2025/02/10/10:36
 */
public class CtpBeanUtil {
    public static void setProperty(Object bean, String propertyName, Object value) {
        try {
            PropertyDescriptor pd = new PropertyDescriptor(propertyName, bean.getClass());
            Method writeMethod = pd.getWriteMethod();
            if (writeMethod != null) {
                writeMethod.invoke(bean, value);
            } else {
                System.out.println("No setter found for property: " + propertyName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
