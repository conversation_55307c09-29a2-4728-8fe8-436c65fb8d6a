package com.wanlianyida.portal.interfaces.util;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @since 2025/02/10/10:36
 */
public class UserBaseInfoUtil {
    public static void setProperty(Object bean, String propertyName, Object value) {
        try {
            PropertyDescriptor pd = new PropertyDescriptor(propertyName, bean.getClass());
            Method writeMethod = pd.getWriteMethod();
            if (writeMethod != null) {
                writeMethod.invoke(bean, value);
            } else {
                System.out.println("No setter found for property: " + propertyName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Object setAddBaseInfo(Object command)
    {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        setProperty(command, "creatorId", StrUtil.isNotEmpty(tokenInfo.getUserBaseId()) ? tokenInfo.getUserBaseId() : null);
        setProperty(command, "creatorName", StrUtil.isNotEmpty(tokenInfo.getLoginName()) ? tokenInfo.getLoginName() : null);
        return command;
    }

    public static Object setUpdateBaseInfo(Object command)
    {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        setProperty(command, "updaterId", StrUtil.isNotEmpty(tokenInfo.getUserBaseId()) ? tokenInfo.getUserBaseId() : null);
        setProperty(command, "updaterName", StrUtil.isNotEmpty(tokenInfo.getLoginName()) ? tokenInfo.getLoginName() : null);
        return command;
    }
}
