package com.wanlianyida.user.application.assembler;

import cn.hutool.core.date.DateUtil;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.support.api.model.command.MsgLogOperationCmd;
import com.wanlianyida.support.api.model.enums.LogOperationRecordEnum;

public class OperatorAssembler {
    /**
     * 创建用户解绑日志
     * @param bizId
     * @param content
     * @return
     */
    public static MsgLogOperationCmd createUserAuthAuditLog(String bizId, String operateType, String content) {
        MsgLogOperationCmd result = new MsgLogOperationCmd();
        result.setBizId(bizId);
        result.setBizType(LogOperationRecordEnum.CTP_PARTNER_USER_AUTH_AUDIT.getBizType());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        result.setUserBaseId(tokenInfo.getUserId());
        result.setUserName(tokenInfo.getUsername());
        result.setUserAccount(tokenInfo.getLoginName());
        result.setOperateTime(DateUtil.date());
        result.setOperateType(operateType);
        result.setOperateContent(content);
        return result;
    }
}
