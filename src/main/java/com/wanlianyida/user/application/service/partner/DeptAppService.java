package com.wanlianyida.user.application.service.partner;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.inter.DeptManageInter;
import com.wanlianyida.partner.api.model.command.DeptAddCommand;
import com.wanlianyida.partner.api.model.command.DeptDeleteCommand;
import com.wanlianyida.partner.api.model.command.DeptUpdateCommand;
import com.wanlianyida.partner.api.model.dto.DeptConditionDTO;
import com.wanlianyida.partner.api.model.dto.OrgAndDeptTreeDTO;
import com.wanlianyida.partner.api.model.query.DeptListQuery;
import com.wanlianyida.partner.api.model.query.DeptTreeQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class DeptAppService {
   @Resource
   private DeptManageInter deptManageInter;

    /**
     * 部门新增
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> add(DeptAddCommand command) {
        return deptManageInter.add(command);
    }

    /**
     * 部门更新
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> update(DeptUpdateCommand command) {
        return deptManageInter.update(command);
    }

    /**
     * 部门删除
     *
     * @param command 命令
     * @return {@link ResultMode }<{@link Boolean }>
     */
    public ResultMode<Boolean> delete(DeptDeleteCommand command) {
        return deptManageInter.delete(command);
    }

    /**
     * 构建部门树
     *
     * @param query 查询
     * @return {@link ResultMode }<{@link OrgAndDeptTreeDTO }>
     */
    public ResultMode<OrgAndDeptTreeDTO> buildDeptTree(DeptTreeQuery query) {
        log.info("部门树查询参数：{}", query);
        return deptManageInter.buildDeptTree(query);
    }

    /**
     * 查询部门列表
     *
     * @param query query
     * @return {@code ResultMode<UmDeptDTO> }
     */
    public ResultMode<List<DeptConditionDTO>>queryByCondition(@RequestBody DeptListQuery query){
        return deptManageInter.queryByCondition(query);
    }

    /**
     * 查询当前登录人的部门信息
     *
     * @return {@code ResultMode<UmDeptDTO> }
     */
    public ResultMode<List<DeptConditionDTO>> getDepartmentsByMemberId(){
        return deptManageInter.getDepartmentsByMemberId();
    }

    public ResultMode<OrgAndDeptTreeDTO> buildDeptTreeByCompanyName(DeptTreeQuery query) {
        return deptManageInter.buildDeptTreeByCompanyName(query);
    }
}
