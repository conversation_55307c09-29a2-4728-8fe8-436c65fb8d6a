package com.wanlianyida.user.application.service.transaction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.*;
import com.wanlianyida.framework.ctpcommon.enums.AutoAuditTypeEnum;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.order.api.inter.OrderCommandInter;
import com.wanlianyida.order.api.inter.PurOrderQueryInter;
import com.wanlianyida.order.api.model.command.CompanyCommand;
import com.wanlianyida.order.api.model.command.PurOrderCreateCommand;
import com.wanlianyida.order.api.model.command.SellerProductCommand;
import com.wanlianyida.order.api.model.dto.PurOrderMappingDTO;
import com.wanlianyida.partner.api.inter.UmShopInter;
import com.wanlianyida.partner.api.model.dto.ShopContactsDTO;
import com.wanlianyida.process.api.inter.AutoAuditConfigInter;
import com.wanlianyida.process.api.model.command.SubmitAutoAuditCommand;
import com.wanlianyida.process.api.model.dto.SubmitAutoAuditDTO;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.transaction.api.inter.PurchaseInter;
import com.wanlianyida.transaction.api.model.command.PurchaseProductCommand;
import com.wanlianyida.transaction.api.model.command.PurchasePublishCommand;
import com.wanlianyida.transaction.api.model.command.PurchaseUpdateCommand;
import com.wanlianyida.transaction.api.model.command.PurposeOrderCommand;
import com.wanlianyida.transaction.api.model.dto.*;
import com.wanlianyida.transaction.api.model.query.CheckCalmSupplierQuery;
import com.wanlianyida.transaction.api.model.query.PurchaseListQuery;
import com.wanlianyida.transaction.api.model.query.PurchaseSupplierQuery;
import com.wanlianyida.user.application.assembler.PurchaseAssembler;
import com.wanlianyida.user.infrastructure.enums.PurchaseScopeEnum;
import com.wanlianyida.user.infrastructure.exchange.*;
import com.wanlianyida.user.interfaces.model.command.PurchasePurposeOrderCommand;
import com.wanlianyida.user.interfaces.model.dto.ShopDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月09日 19:47
 */
@Slf4j
@Service
public class PurchaseAppService {

    @Resource
    private PurchaseInter purchaseInter;
    @Resource
    private PurchaseExchangeService purchaseExchangeService;
    @Resource
    private ProductExchangeService productExchangeService;
    @Resource
    private OrderCommandInter orderCommandInter;
    @Resource
    private PurOrderQueryInter purOrderQueryInter;
    @Resource
    private PurOrderExchangeService purOrderExchangeService;
    @Resource
    private UmShopInter umShopInter;
    @Resource
    private ShopExchangeService shopExchangeService;
    @Resource
    private CompanyExchangeService companyExchangeService;
    @Resource
    private AutoAuditConfigInter autoAuditConfigInter;

    public ResultMode publish(PurchasePublishCommand command) {
        ResultMode<String> resultMode = purchaseInter.publish(command);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getMessage());
        }
        String id = resultMode.getModel();
        log.info("发布求购，返回结果：{}",id);
        List<String> categoryId3List = command.getProductList().stream()
                .map(PurchaseProductCommand::getCategoryId3)
                .map(String::valueOf)
                .collect(Collectors.toList());

        if (StrUtil.isNotEmpty(id)) {
            AutoAuditTypeEnum autoAuditTypeEnum = AutoAuditTypeEnum.TYPE_6;
            if(PurchaseScopeEnum.SCOPE_20.getCode().equals(command.getPurchaseScope())) {
                autoAuditTypeEnum = AutoAuditTypeEnum.TYPE_17;
            }
            // 检查自动审核配置
            checkAutoAuditConfig(id, categoryId3List, autoAuditTypeEnum);
        }

        return resultMode;
    }

    public ResultMode<?> update(PurchaseUpdateCommand command) {
        return purchaseInter.update(command);
    }

    public ResultMode<List<PurchaseListDTO>> pageCondition(PagingInfo<PurchaseListQuery> pageQuery) {
        return purchaseInter.pageCondition(pageQuery);
    }

    public ResultMode<PurchaseDetailDTO> queryDetail(IdQuery query) {
        ResultMode<PurchaseDetailDTO> result = purchaseInter.queryDetail(query);
        if (result == null || !result.isSucceed() || result.getModel() == null) {
            return result;
        }

        // 历史bug修复,展示填写的联系人信息
//        PurchaseDetailDTO model = result.getModel();
//        List<PurchaseQuotationDTO> quotationList = Optional.ofNullable(model.getQuotationList()).orElse(Collections.emptyList());
//        if (quotationList.isEmpty()) {
//            return result;
//        }
//
//        List<Long> shopIdList = quotationList.stream()
//                .map(PurchaseQuotationDTO::getShopId)
//                .filter(Objects::nonNull)
//                .distinct()
//                .collect(Collectors.toList());
//
//        if (shopIdList.isEmpty()) {
//            log.info("【查询店铺联系人】店铺id为空或为0，不查询");
//            clearContactInfo(quotationList);
//            return result;
//        }
//
//        ResultMode<List<ShopContactsDTO>> shopContactsResult = umShopInter.getShopContactsList(shopIdList);
//        if (shopContactsResult == null || !shopContactsResult.isSucceed() || CollectionUtil.isEmpty(shopContactsResult.getModel())) {
//            log.info("【查询店铺联系人】失败或为空，不设置联系人信息");
//            clearContactInfo(quotationList);
//            return result;
//        }
//        List<ShopContactsDTO> contactsDTOList = shopContactsResult.getModel();
//        log.info("【查询店铺联系人】成功，开始设置联系人信息：{}", contactsDTOList);
//        Map<Long, ShopContactsDTO> shopContactsMap = contactsDTOList.stream()
//                .collect(Collectors.toMap(ShopContactsDTO::getId, Function.identity(), (existing, replacement) -> existing));
//        setContactInfo(quotationList, shopContactsMap);
        appendShopInfoToProduct(result.getModel());
        return result;
    }

    public ResultMode<PurchaseStatisticsDTO> statistics() {
        return purchaseInter.statistics();
    }

    /**
     * 生成意向单
     */
    public ResultMode<?> purposeOrder(PurchasePurposeOrderCommand command) {
        log.info("【生成意向单】入参：{}", command);
        // 报价单信息
        QuotationDetailDTO quotationDetail = purchaseExchangeService.queryQuotationDetail(command.getId());
        if (quotationDetail == null) {
            return ResultMode.fail("当前报价单不存在");
        }
//        Calendar instance = Calendar.getInstance();
//        instance.setTime(new Date());
//        instance.set(Calendar.HOUR_OF_DAY, 0);
//        instance.set(Calendar.MINUTE, 0);
//        instance.set(Calendar.SECOND, 0);
//        instance.set(Calendar.MILLISECOND, 0);
        if (quotationDetail.getValidDate().before(new Date()) || quotationDetail.getQuotationStatus().equals(20)) {
            return ResultMode.fail("报价已失效，无法下单");
        }
        // 求购单信息
        PurchaseEmptyDetailDTO purchaseDetail = purchaseExchangeService.queryEmptyDetail(quotationDetail.getRelPurchaseId());
        if (purchaseDetail == null) {
            return ResultMode.fail("当前求购单不存在");
        }
        // 过滤无需下单的商品
        quotationDetail.setProductList(quotationDetail.getProductList().stream().filter(e -> {
            return e.getOfferProductData() != null && command.getProductIdList().contains(e.getOfferProductData().getId());
        }).collect(Collectors.toList()));
        // 商品sku信息
        List<ProductSkuDetailDTO> productSkuDetailList = productExchangeService.querySkuDetailBatch(quotationDetail.getProductList().stream().map(e -> e.getOfferProductData().getSkuCode()).collect(Collectors.toList()));
        // 生成意向单
        PurOrderCreateCommand createCommand = PurchaseAssembler.purposeOrder(quotationDetail, purchaseDetail, productSkuDetailList);
        // 卖家店铺信息
        appendSellerInfo(createCommand);
        log.info("【生成意向单】调用接口参：{}", createCommand);
        ResultMode<List<String>> result = orderCommandInter.createPurOrder(createCommand);
        log.info("【生成意向单】响应参数：{}", result);
        if (!result.isSucceed() || CollectionUtil.isEmpty(result.getModel())) {
            throw new BaseException("生成意向单失败");
        }
        List<PurOrderMappingDTO> mappingList = purOrderExchangeService.querySkuMapping(result.getModel());
        PurposeOrderCommand purposeOrderCommand = PurchaseAssembler.getPurposeOrderCommand(command, quotationDetail, mappingList);
        return purchaseInter.purposeOrder(purposeOrderCommand);
    }

    public ResultMode<PurchaseEmptyDetailDTO> queryPurchaseDetail(IdQuery query) {
        return purchaseInter.queryPurchaseDetail(query);
    }

    public ResultMode<?> stopPurchase(IdCommand command) {
        return purchaseInter.stopPurchase(command);
    }

    private void appendShopInfoToProduct(PurchaseDetailDTO detailDTO) {
        if (CollUtil.isEmpty(detailDTO.getQuotationList())) return;
        for (PurchaseQuotationDTO quotationDTO : detailDTO.getQuotationList()) {
            if (CollUtil.isEmpty(quotationDTO.getProductList())) continue;
            for (PurchaseProductDetailDTO productDto : quotationDTO.getProductList()) {
                productDto.setShopId(quotationDTO.getShopId());
                productDto.setShopName(quotationDTO.getShopName());
            }
        }
    }

    /**
     * 填充企业和店铺信息
     *
     * @param command
     */
    private void appendSellerInfo(PurOrderCreateCommand command) {
        List<SellerProductCommand> products = command.getPorderList();
        products.forEach(e -> {
            // 填充店铺信息
            appendShopData(e.getSellerInfo());
        });
    }

    /**
     * 填充店铺信息
     */
    private void appendShopData(CompanyCommand companyCommand) {
        if (companyCommand == null) {
            return;
        }
        ShopDTO shopDTO = shopExchangeService.getShopInfo(companyCommand.getCompanyId());
        companyCommand.setShopId(shopDTO.getShopId());
        companyCommand.setShopName(shopDTO.getShopName());
        companyCommand.setShopLinkName(shopDTO.getShopLinkName());
        companyCommand.setShopLinkTelephone(shopDTO.getShopLinkTelephone());
    }

    private void clearContactInfo(List<PurchaseQuotationDTO> quotationList) {
        for (PurchaseQuotationDTO dto : quotationList) {
            dto.setLinkmanName(null);
            dto.setLinkmanMobile(null);
        }
    }

    private void setContactInfo(List<PurchaseQuotationDTO> quotationList, Map<Long, ShopContactsDTO> shopContactsMap) {
        for (PurchaseQuotationDTO dto : quotationList) {
            ShopContactsDTO shopContactsDTO = shopContactsMap.get(dto.getShopId());
            if (shopContactsDTO != null) {
                dto.setLinkmanName(shopContactsDTO.getContacts());
                dto.setLinkmanMobile(shopContactsDTO.getPhone());
            } else {
                dto.setLinkmanName(null);
                dto.setLinkmanMobile(null);
            }
        }
    }

    /**
     * @param auditId
     * @param categoryId3List
     * @param auditTypeEnum
     */
    private void checkAutoAuditConfig(String auditId, List<String> categoryId3List, AutoAuditTypeEnum auditTypeEnum) {
        try {
            SubmitAutoAuditCommand auditCommand = new SubmitAutoAuditCommand();
            auditCommand.setProcessNo(auditTypeEnum.getCode());
            auditCommand.setBusinessKey(auditId);
            Map<String, Object> params = new HashMap<>();
            params.put("companyIdList", Arrays.asList(JwtUtil.getTokenInfo().getCompanyId().toString()));
            params.put("productCategoryIdList",categoryId3List);
            auditCommand.setCustomVar(params);

            ResultMode<SubmitAutoAuditDTO> autoAuditDTOResultMode = autoAuditConfigInter.submitAutoAudit(auditCommand);
            if (!autoAuditDTOResultMode.isSucceed()) {
                log.error("{}-调用审核服务失败：{}", auditTypeEnum.getDesc(), autoAuditDTOResultMode.getMessage());
            }
            log.info("{}-调用审核服务，入参：{}", auditTypeEnum.getDesc(), auditCommand);
        } catch (Exception e) {
            log.error("{}-调用审核服务失败:{}", auditTypeEnum.getDesc(), e);
        }
    }

    public ResultMode<List<PurchaseCompanyDTO>> queryPurchaseCompany(PurchaseSupplierQuery purchaseSupplierQuery) {
        return purchaseInter.queryPurchaseCompany(purchaseSupplierQuery);
    }

    public ResultMode<CheckCalmSupplierDTO> checkCalmSupplier(CheckCalmSupplierQuery checkCalmSupplierQuery) {
        return purchaseInter.checkCalmSupplier(checkCalmSupplierQuery);
    }

    public ResultMode<List<PurchaseProductDTO>> pageProductList(PagingInfo<IdQuery> pageQuery) {
        return purchaseInter.pageProductList(pageQuery);
    }
}
