package com.wanlianyida.user.application.service.settAcctCert;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.api.inter.SettAcctCertInter;
import com.wanlianyida.sett.api.model.command.SettAcctCertCommand;
import com.wanlianyida.sett.api.model.command.SettAcctCertUpdateCommand;
import com.wanlianyida.sett.api.model.dto.CallbackAcctCertDTO;
import com.wanlianyida.sett.api.model.dto.SettAcctCertDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ClassName: SettAcctCertService
 * @description:
 * @author: zhangZhen
 * @date: 2025年05月20日
 * @version: 1.0
 */

@Slf4j
@Service
public class SettAcctCertService {

    @Autowired
    private SettAcctCertInter settAcctCertInter;


    public ResultMode<Void> save(@RequestBody SettAcctCertCommand command){
        return settAcctCertInter.save(command);
    }

    public ResultMode<SettAcctCertDTO> getAcctCertById(Long id) {
        return settAcctCertInter.getAcctCertById(id);
    }

    public ResultMode<Void> removeById(Long id) {
        return settAcctCertInter.removeById(id);
    }

    public ResultMode<Void> updateAcctCertById(@RequestBody SettAcctCertUpdateCommand command){
        return settAcctCertInter.updateAcctCertById(command);
    }

    public ResultMode<Void> callbackUpdateAcctCertStatus(CallbackAcctCertDTO callbackAcctCertDTO) {
        return settAcctCertInter.callbackUpdateAcctCertStatus(callbackAcctCertDTO);
    }

//    public ResultMode<SettAcctCertDTO> queryAcctCertByCompanyIdAndAccountId(String companyId, String accountId) {
//        return settAcctCertInter.queryAcctCertByCompanyIdAndAccountId(companyId,accountId);
//    }
}
