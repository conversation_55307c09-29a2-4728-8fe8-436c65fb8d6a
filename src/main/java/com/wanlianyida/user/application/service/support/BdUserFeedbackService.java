package com.wanlianyida.user.application.service.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.support.api.model.command.*;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackAndAttachDTO;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackDTO;
import com.wanlianyida.support.api.model.dto.BdUserFeedbackListDTO;
import com.wanlianyida.support.api.model.query.BdUserFeedbackQuery;
import com.wanlianyida.user.infrastructure.exchange.BdUserFeedbackExchangeService;
import com.wanlianyida.user.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.user.infrastructure.exchange.SensitiveExchangeService;
import com.wanlianyida.user.interfaces.model.command.FeedbackCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Service
@Slf4j
public class BdUserFeedbackService {

    @Resource
    private BdUserFeedbackExchangeService bdUserFeedbackExchangeService;

    @Resource
    MdmExchangeService mdmExchangeService;

    @Resource
    private SensitiveExchangeService sensitiveExchangeService;

    public ResultMode add(FeedbackCommand command) {
        BdUserFeedbackAndAttachAddCommand andAttachAddCommand =  new BdUserFeedbackAndAttachAddCommand();
        BdUserFeedbackAddCommand bdUserFeedbackAddCommand = new BdUserFeedbackAddCommand();
        bdUserFeedbackAddCommand.setFeedbackType(command.getFeedbackType());
        bdUserFeedbackAddCommand.setFeedbackContent(command.getFeedbackContent());

        String ans = sensitiveExchangeService.checkWords(command.getFeedbackContent());
        if (!StrUtil.isBlank(ans)) {
            return ResultMode.fail("您当前提交的内容包含敏感词["+ ans+"]，请修改后再提交" );
        }

        String userId = JwtUtil.getTokenInfo().getUserId();
        MdmUserQuery query = new MdmUserQuery();
        log.info("根据用户ID获取手机号:{}",userId);
        query.setUserId(JwtUtil.getTokenInfo().getUserId());
        query.setUserBaseId(JwtUtil.getTokenInfo().getUserBaseId());
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.queryUserInfo(query);
        log.info("根据用户ID获取手机号返回值:{}",mdmUserInfoDTO);
        if(mdmUserInfoDTO !=null){
            bdUserFeedbackAddCommand.setFeedbackUserMobile(mdmUserInfoDTO.getMobile());
        }
        List<BdAttachmentAddCommand> attachmentAddCommandList = BeanUtil.copyToList(command.getAttachmentList(),BdAttachmentAddCommand.class);
        andAttachAddCommand.setBdUserFeedbackAddCommand(bdUserFeedbackAddCommand);
        andAttachAddCommand.setAttachmentAddCommandList(attachmentAddCommandList);
        return bdUserFeedbackExchangeService.add(andAttachAddCommand);
    }

    public ResultMode<List<BdUserFeedbackListDTO>> pageList(PagingInfo<BdUserFeedbackQuery> pagingInfo) {
        ResultMode<List<BdUserFeedbackDTO>> listResultMode = bdUserFeedbackExchangeService.pageList(pagingInfo);
        List<BdUserFeedbackDTO> list = listResultMode.getModel();
        if(CollUtil.isEmpty(list)){
            ResultMode.success();
        }
        List<BdUserFeedbackListDTO> bdUserFeedbackListDTOS = BeanUtil.copyToList(list, BdUserFeedbackListDTO.class);
        return ResultMode.successPageList(bdUserFeedbackListDTOS,listResultMode.getTotal());
    }

    public ResultMode<BdUserFeedbackAndAttachDTO> getDetail(@Valid BdUserFeedbackQuery query) {
        return bdUserFeedbackExchangeService.getDetail(query);
    }
}
