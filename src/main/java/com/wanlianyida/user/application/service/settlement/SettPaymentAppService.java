package com.wanlianyida.user.application.service.settlement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.inter.OcOrderReceiveMethodInter;
import com.wanlianyida.order.api.model.dto.OrderReceiveMethodDTO;
import com.wanlianyida.order.api.model.query.OrderReceiveMethodQuery;
import com.wanlianyida.partner.api.model.query.UmCompanyReceiptQuery;
import com.wanlianyida.sett.api.inter.SettPaymentBankRegInter;
import com.wanlianyida.sett.api.inter.SettPaymentCommandInter;
import com.wanlianyida.sett.api.inter.SettPaymentQueryInter;
import com.wanlianyida.sett.api.model.command.ConfirmReceivedCommand;
import com.wanlianyida.sett.api.model.command.SettPaymentCommand;
import com.wanlianyida.sett.api.model.dto.*;
import com.wanlianyida.sett.api.model.query.*;
import com.wanlianyida.user.application.assembler.SettPaymentAssembler;
import com.wanlianyida.user.infrastructure.enums.OrderReceiveMethodEnum;
import com.wanlianyida.user.infrastructure.enums.SettPaymentStatusEnum;
import com.wanlianyida.user.infrastructure.enums.TransactionTypeEnum;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.CompanyReceiptExchangeService;
import com.wanlianyida.user.infrastructure.exchange.SensitiveExchangeService;
import com.wanlianyida.user.interfaces.model.dto.ReceiverBankAccountNoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 付款单
 */
@Slf4j
@Service
public class SettPaymentAppService {

    @Resource
    private SettPaymentQueryInter settPaymentQueryInter;

    @Resource
    private SettPaymentCommandInter settPaymentCommandInter;

    @Resource
    private OcOrderReceiveMethodInter ocOrderReceiveMethodInter;

    @Resource
    private SettPaymentBankRegInter settPaymentBankRegInter;

    @Resource
    private CompanyReceiptExchangeService companyReceiptExchangeService;

    @Resource
    private SensitiveExchangeService sensitiveExchangeService;

    /**
     * 查询付款单分页列表
     *
     * @param param
     * @return
     */
    public ResultMode<List<SettPaymentDTO>> querySettPaymentPage(PagingInfo<SettPaymentListQuery> param) {
        return settPaymentQueryInter.querySettPaymentPage(param);
    }

    /**
     * 买家---提交之前查询付款单详情页信息
     */
    public ResultMode<SettPaymentBeforeSubmitDetailDTO> queryPaymentDetailsBeforeSubmit(@Validated @RequestBody SettPaymentDetailQuery query) {
        SettPaymentBeforeSubmitDetailDTO settPaymentBeforeSubmitDetailDTO = settPaymentQueryInter.queryPaymentDetailsBeforeSubmit(query).getModel();
        if (Objects.isNull(settPaymentBeforeSubmitDetailDTO))
            return ResultMode.fail(UserErrorCode.ERROR_20020.getCode(), UserErrorCode.ERROR_20020.getMsg());
        OrderReceiveMethodQuery receiveMethodQuery = BeanUtil.toBean(query, OrderReceiveMethodQuery.class);
        // 封装付款方式信息
        List<OrderReceiveMethodDTO> methodDTOList = ocOrderReceiveMethodInter.queryList(receiveMethodQuery).getModel();
        // 封装付款提交前的详情数据
        SettPaymentBeforeSubmitDetailDTO detailDTO = getDetailDTO(settPaymentBeforeSubmitDetailDTO, methodDTOList, SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod.class);
        // 对收款方式进行线上线下分类，并设置到详情对象中
        Map<String, List<SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod>> orderReceiveMethodMapList = getOnLineOrOfflinePayMethod(detailDTO.getOrderReceiveMethodList());
        // 设置到详情对象中
        detailDTO.setOrderReceiveMethodNewList(orderReceiveMethodMapList);
        return ResultMode.success(detailDTO);
    }

    /**
     * 根据交易类型和预付标识，处理并设置订单收款方式列表
     *
     * @param detailDTO 付款提交前的详情数据传输对象
     * @param methodDTOList                    原始订单收款方式列表
     * @return 处理后的付款提交前详情对象
     */
    private <T, R> T getDetailDTO(T detailDTO, List<OrderReceiveMethodDTO> methodDTOList, Class<R> orderReceiveMethodClass) {

        List<R> orderReceiveMethods = null;

        // 尾款点击付款页面时，付款方式需要与预付款一致
        if (Objects.equals(BeanUtil.getProperty(detailDTO, "transactionType"), TransactionTypeEnum.TRANSACTION_TYPE_20.getType())
                && Boolean.TRUE.equals(BeanUtil.getProperty(detailDTO, "prepayFlag"))) {
            Map<String, OrderReceiveMethodDTO> orderReceiveMethodMap = new LinkedHashMap<>();
            methodDTOList.forEach(orderReceiveMethodDTO -> {
                orderReceiveMethodMap.put(orderReceiveMethodDTO.getReceiveMethod(), orderReceiveMethodDTO);
            });
            // 根据预付款付款方式获取收款方式列表区分线上和线下
            Map<String, OrderReceiveMethodDTO> receiveMethodMap = getOrderReceiveMethodList(orderReceiveMethodMap, StrUtil.toString(BeanUtil.getProperty(detailDTO, "advancePaymentMethod")));
            if (CollUtil.isNotEmpty(receiveMethodMap)) {
                orderReceiveMethods = BeanUtil.copyToList(receiveMethodMap.values(), orderReceiveMethodClass);
            }
        } else {
            // 非尾款或无预付情况下，获取所有付款方式列表
            orderReceiveMethods = BeanUtil.copyToList(methodDTOList, orderReceiveMethodClass);
        }

        // 设置处理后的收款方式列表
        BeanUtil.setProperty(detailDTO, "orderReceiveMethodList", orderReceiveMethods);
        return detailDTO;
    }

    private Map<String, List<SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod>> getOnLineOrOfflinePayMethod(List<SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod> orderReceiveMethodList) {
        Map<String, List<SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod>> orderReceiveMethodMap = new HashMap<>();
        List<SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod> onlinePayMethodList = new ArrayList<>();
        List<SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod> offlinePayMethodList = new ArrayList<>();
        orderReceiveMethodList.forEach(node -> {
            if (Arrays.asList(OrderReceiveMethodEnum.PAY_MODE_60.getCode(),
                    OrderReceiveMethodEnum.PAY_MODE_40.getCode()).contains(String.valueOf(node.getReceiveMethod()))) { // 线上支付
                onlinePayMethodList.add(node);
            } else { // 银行承兑
                offlinePayMethodList.add(node);
            }
        });
        orderReceiveMethodMap.put("1", null);
        if (CollectionUtil.isNotEmpty(onlinePayMethodList)){
            onlinePayMethodList.sort(Comparator.comparing(SettPaymentBeforeSubmitDetailDTO.OrderReceiveMethod::getReceiveMethod));
            orderReceiveMethodMap.put("1", onlinePayMethodList);
        }
        orderReceiveMethodMap.put("2", null);
        if (CollectionUtil.isNotEmpty(offlinePayMethodList))
            orderReceiveMethodMap.put("2", offlinePayMethodList);
        return orderReceiveMethodMap;
    }

    /**
     * 根据付款方式过滤可用的收款方式
     *
     * @param orderReceiveMethodMap 收款方式映射（key: 收款方式编码）
     * @param paymentMethod         付款方式编码
     */
    private Map<String, OrderReceiveMethodDTO> getOrderReceiveMethodList(Map<String, OrderReceiveMethodDTO> orderReceiveMethodMap, String paymentMethod) {
        // 根据付款方式执行不同的业务逻辑
        switch (paymentMethod) {
            case "40":
            case "60": { // 银联支付/电子钱包：仅保留当前付款方式
                orderReceiveMethodMap.keySet().removeIf(key ->
                        !key.equals(paymentMethod)
                );
                break;
            }
            case "50":
            case "30":
            case "20": { // 银行承兑、商业承兑、电汇(银行账户)：移除电子钱包和银联支付方式
                orderReceiveMethodMap.keySet().removeIf(key ->
                        key.equals(OrderReceiveMethodEnum.PAY_MODE_60.getCode()) ||
                                key.equals(OrderReceiveMethodEnum.PAY_MODE_40.getCode())
                );
                break;
            }
        }

        return orderReceiveMethodMap;
    }

    /**
     * 付款单数据更新操作
     */
    public ResultMode<Void> updateSettPayment(@RequestBody @Valid SettPaymentCommand command) {
        return settPaymentCommandInter.updateSettPayment(command);
    }

    /**
     * 确定收款操作
     */
    public ResultMode<Void> confirmPaymentReceived(@RequestBody @Validated ConfirmReceivedCommand command) {
        // 敏感词检测
        if (StrUtil.isNotBlank(command.getPayFailReason())) {
            String ans = sensitiveExchangeService.checkWords(command.getPayFailReason());
            if (!Strings.isNullOrEmpty(ans)) {
                return ResultMode.fail(StrUtil.format(UserErrorCode.ERROR_20028.getMsg(), ans));
            }
        }

        return settPaymentCommandInter.confirmPaymentReceived(command);
    }

    /**
     * 买家---提交之后付款单详情页查询
     */
    public ResultMode<SettPaymentAfterSubmitDetailDTO> queryPaymentDetailsAfterSubmit(SettPaymentDetailQuery query) {
        return ResultMode.success(settPaymentQueryInter.queryPaymentDetailsAfterSubmit(query).getModel());
    }

    /**
     * 买家---提交之后修改付款单详情页查询
     */
    public ResultMode<SettPaymentAfterSubmitUpdateDetailDTO> queryPaymentDetailsAfterSubmitUpdate(SettPaymentDetailQuery query) {
        // 查询付款单修改后的详情数据
        SettPaymentAfterSubmitUpdateDetailDTO updateDetailDTO = settPaymentQueryInter.queryPaymentDetailsAfterSubmitUpdate(query).getModel();
        if (Objects.isNull(updateDetailDTO)) {
            return ResultMode.success(updateDetailDTO);
        }

        // 查询订单对应的收款方式信息
        OrderReceiveMethodQuery receiveMethodQuery = BeanUtil.toBean(query, OrderReceiveMethodQuery.class);
        List<OrderReceiveMethodDTO> methodDTOList = ocOrderReceiveMethodInter.queryList(receiveMethodQuery).getModel();

        // 若无可用收款方式，直接返回付款单详情
        if (CollectionUtil.isEmpty(methodDTOList)) {
            return ResultMode.success(updateDetailDTO);
        }

        // 收款方式
        String receiveMethod = methodDTOList.stream()
                .map(OrderReceiveMethodDTO::getReceiveMethod)
                .collect(Collectors.joining(StrPool.COMMA));
        updateDetailDTO.setReceiveMethod(receiveMethod);

        SettPaymentAfterSubmitUpdateDetailDTO detailDTO = getDetailDTO(updateDetailDTO, methodDTOList, SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod.class);
        // 对收款方式进行线上线下分类，并设置到详情对象中
        Map<String, List<SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod>> orderReceiveMethodMapList = getUpdateOnLineOrOfflinePayMethod(detailDTO.getOrderReceiveMethodList());
        // 设置到详情对象中
        detailDTO.setOrderReceiveMethodNewList(orderReceiveMethodMapList);

        return ResultMode.success(detailDTO);
    }

    private Map<String, List<SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod>> getUpdateOnLineOrOfflinePayMethod(List<SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod> orderReceiveMethodList) {
        Map<String, List<SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod>> orderReceiveMethodMap = new HashMap<>();
        List<SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod> onlinePayMethodList = new ArrayList<>();
        List<SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod> offlinePayMethodList = new ArrayList<>();
        orderReceiveMethodList.forEach(node -> {
            if (Arrays.asList(OrderReceiveMethodEnum.PAY_MODE_60.getCode(),
                    OrderReceiveMethodEnum.PAY_MODE_40.getCode()).contains(String.valueOf(node.getReceiveMethod()))) { // 线上支付
                onlinePayMethodList.add(node);
            } else { // 银行承兑
                offlinePayMethodList.add(node);
            }
        });
        if (CollectionUtil.isNotEmpty(onlinePayMethodList)){
            onlinePayMethodList.sort(Comparator.comparing(SettPaymentAfterSubmitUpdateDetailDTO.OrderReceiveMethod::getReceiveMethod));
            orderReceiveMethodMap.put("1", onlinePayMethodList);
        }
        if (CollectionUtil.isNotEmpty(offlinePayMethodList))
            orderReceiveMethodMap.put("2", offlinePayMethodList);
        return orderReceiveMethodMap;
    }

    /**
     * （谨慎使用此接口）删除所有数据--测试清除数据使用此接口
     *
     * @return
     */
    public ResultMode<Boolean> deleteAllSettPay() {
        return settPaymentCommandInter.deleteAllSettPay();
    }

    /**
     * 提交付款单
     */
    /**
     * 提交付款单处理逻辑
     * 根据不同的付款方式执行对应的业务操作：
     * - 电子钱包（40）：查询银行登记信息
     * - 银联支付（60）：待开发
     * - 银行承兑（20）、商业承兑（30）、电汇（50）：更新付款单信息
     *
     * @param command 付款单请求参数
     * @return 操作结果
     */
    public ResultMode<SettPaymentBankRegDTO> submitSettPayment(@Valid SettPaymentCommand command) {
        // 校验参数
        if (ObjectUtil.isEmpty(command)) {
            return ResultMode.fail("参数错误！");
        }

        // 根据付款方式执行不同的业务逻辑
        switch (command.getPaymentMethod()) {
            case "40": { // 电子钱包：查询银行登记信息
                // 校验必要参数
                if (ObjectUtil.isEmpty(command.getOrderNo()) || ObjectUtil.isEmpty(command.getTransactionType())) {
                    return ResultMode.fail("电子钱包参数错误！");
                }
                // 构造查询参数并调用接口获取银行登记详情
                SettPaymentBankRegQuery query = new SettPaymentBankRegQuery();
                query.setOrderNo(command.getOrderNo());
                query.setTransactionType(command.getTransactionType());
                query.setBankChannelCode(command.getBankChannelCode());
                ResultMode<SettPaymentBankRegDTO> resultMode = settPaymentBankRegInter.querySettPaymentBankRegDetail(query);
                log.info("查询银行登记详情：{}", resultMode);
                if (!resultMode.isSucceed()) {
                    return ResultMode.fail(resultMode.getMessage());
                }
                return resultMode;
            }
            case "60": { // 银联支付
                // 检查渠道编号是否存在
                if (ObjectUtil.isEmpty(command.getBankChannelCode())) {
                    return ResultMode.fail("银联支付渠道编号不能为空！");
                }
                // 构建银联支付指令并调用处理接口
                ResultMode<Void> resultMode = settPaymentCommandInter.processUnionPay(SettPaymentAssembler.buildProcessUnionPayCommand(command));
                if (!resultMode.isSucceed()) {
                    return ResultMode.fail(resultMode.getMessage());
                }
                break;
            }
            case "50":
            case "30":
            case "20": { // 银行承兑、商业承兑、电汇(银行账户)：更新付款单信息
                // 调用付款单更新操作接口
                ResultMode<Void> resultMode = settPaymentCommandInter.updateSettPayment(command);
                if (!resultMode.isSucceed()) {
                    return ResultMode.fail(resultMode.getMessage());
                }
                break;
            }
            default:
                return ResultMode.fail("付款方式错误！");
        }
        return ResultMode.success();
    }

    /**
     * 校验余额是否充足
     */
    public ResultMode<PaymentCheckBalanceDTO> paymentCheckBalance(PaymentCheckBalanceQuery query) {
        return settPaymentQueryInter.paymentCheckBalance(query);
    }

    /**
     * 查下银行账户
     */
    public ResultMode<ReceiverBankAccountNoDTO> receiverBankAccountNo() {
        ReceiverBankAccountNoDTO receiverBankAccountNoDTO = new ReceiverBankAccountNoDTO();
        receiverBankAccountNoDTO.setBankAccountInfo(companyReceiptExchangeService.queryPage(getUmCompanyReceiptQueryPagingInfo()).getModel());
        List<SettAcctAccountBalanceDTO> model = companyReceiptExchangeService.queryPayAccountBalanceDetailList(new SettAcctAccountBalanceQuery()).getModel();
        if (CollectionUtil.isNotEmpty(model)) {
            model.forEach(e -> {
                e.setAccountName(e.getCompanyName());
            });
            Map<String, List<SettAcctAccountBalanceDTO>> map = model.stream().collect(Collectors.groupingBy(SettAcctAccountBalanceDTO::getBankChannelCode));
            receiverBankAccountNoDTO.setWalletAccountBalance(map);
        }
        return ResultMode.success(receiverBankAccountNoDTO);
    }

    private static PagingInfo<UmCompanyReceiptQuery> getUmCompanyReceiptQueryPagingInfo() {
        PagingInfo<UmCompanyReceiptQuery> queryPagingInfo = new PagingInfo<>();
        queryPagingInfo.setCurrentPage(1);
        queryPagingInfo.setPageLength(1000);
        queryPagingInfo.setCountTotal(false);
        queryPagingInfo.setFilterModel(new UmCompanyReceiptQuery());
        return queryPagingInfo;
    }
}
