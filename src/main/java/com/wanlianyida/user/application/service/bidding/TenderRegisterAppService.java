package com.wanlianyida.user.application.service.bidding;

import com.wanlianyida.bidding.api.inter.TenderRegisterInter;
import com.wanlianyida.bidding.api.model.command.PerfectRegisterInfoCommand;
import com.wanlianyida.bidding.api.model.command.TenderConfirmCommand;
import com.wanlianyida.bidding.api.model.command.TenderRegisterCommand;
import com.wanlianyida.bidding.api.model.query.TenderConfirmListQuery;
import com.wanlianyida.bidding.api.model.query.TenderRegisterListQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TenderRegisterAppService {

    @Resource
    private TenderRegisterInter tenderRegisterInfo;


    /**
     * 确认列表
     * @param pagingInfo
     * @return
     */
    public ResultMode confirmList(PagingInfo<TenderConfirmListQuery> pagingInfo){
        return tenderRegisterInfo.confirmList(pagingInfo);
    }

    /**
     * 确认
     * @return
     */
    public ResultMode confirm(TenderConfirmCommand command){
        return tenderRegisterInfo.confirm(command);
    }

    /**
     * 报名列表
     * @return
     */
    public ResultMode registerList(PagingInfo<TenderRegisterListQuery> pagingInfo){
        return tenderRegisterInfo.registerList(pagingInfo);
    }


    /**
     * 报名列表
     * @return
     */
    public ResultMode unregisterList(PagingInfo<TenderRegisterListQuery> pagingInfo){
        return tenderRegisterInfo.unregisterList(pagingInfo);
    }


    /**
     * 详情
     * @return
     */
    public ResultMode tenderRegisterInfo(String id){
        return tenderRegisterInfo.tenderRegisterInfo(id);
    }

    /**
     * 报名
     * @param command
     */
    public ResultMode register(TenderRegisterCommand command){
        return tenderRegisterInfo.register(command);
    }

    /**
     * 完善报名信息
     * @param command
     */
    public ResultMode perfectRegisterInfo(PerfectRegisterInfoCommand command) {
        return tenderRegisterInfo.perfectRegisterInfo(command);
    }

}
