package com.wanlianyida.user.application.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.fssbaselog.api.model.dto.LogOperationRecordDTO;
import com.wanlianyida.fssbaselog.api.model.query.LogOperationRecordQuery;
import com.wanlianyida.user.infrastructure.exchange.LogExchangeService;
import com.wanlianyida.user.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.user.interfaces.model.command.OperateContentCommand;
import com.wanlianyida.user.interfaces.model.dto.OperationRecordLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @date 2024/12/05
 */
@Slf4j
@Service
public class OperateLogAppService {

    @Resource
    private LogExchangeService logExchangeService;

    @Resource
    private UploadExchangeService uploadExchangeService;

    /**
     * 操作日志分页查询
     *
     * @param query
     * @return
     */
    public ResultMode<List<LogOperationRecordDTO>> queryPage(PagingInfo<LogOperationRecordQuery> query) {
        return logExchangeService.queryOperationRecordPage(query);
    }

    public ResultMode<List<OperationRecordLogDTO>> queryRecordPage(PagingInfo<LogOperationRecordQuery> query) {
        ResultMode<List<LogOperationRecordDTO>> listResultMode = logExchangeService.queryOperationRecordPage(query);
        if (!listResultMode.isSucceed()) {
            return ResultMode.fail(listResultMode.getCode(), listResultMode.getMessage());
        }
        List<OperationRecordLogDTO> operationRecordLogDTOS = BeanUtil.copyToList(listResultMode.getModel(), OperationRecordLogDTO.class);
        operationRecordLogDTOS.forEach(item -> {
            String operateContent = item.getOperateContent();
            if (StrUtil.isNotBlank(operateContent)) {
                try {
                    OperateContentCommand operateContentCommand = JSONUtil.toBean(item.getOperateContent(), OperateContentCommand.class);
                    if (operateContentCommand != null && operateContentCommand.getDetailId() != null && operateContentCommand.getOperateContent() != null) {
                        if (operateContentCommand.getOperateContent().contains("fileUrl") && operateContentCommand.getOperateContent().contains("fileName")) {
                            List<OperationRecordLogDTO.Attachment> list = JSONUtil.toList(operateContentCommand.getOperateContent(), OperationRecordLogDTO.Attachment.class);
                            convertAttachmentUrls(list);
                            item.setAttachmentList(list);
                            item.setOperateContent("");
                        } else {
                            item.setOperateContent(operateContentCommand.getOperateContent());
                        }
                        item.setDetailId(operateContentCommand.getDetailId());
                    }
                } catch (Exception e) {
                    log.error("解析 operateContent 失败: {}", operateContent, e);
                }
            }
        });
        return ResultMode.successPageList(operationRecordLogDTOS, listResultMode.getTotal());
    }

    /**
     * 转换附件图片url
     */
    private void convertAttachmentUrls(List<OperationRecordLogDTO.Attachment> attachmentList) {
        if (IterUtil.isEmpty(attachmentList)) {
            return;
        }

        List<String> urls = attachmentList.stream().filter(att -> StrUtil.isNotBlank(att.getFileUrl()))
                .map(OperationRecordLogDTO.Attachment::getFileUrl).distinct().collect(Collectors.toList());
        Map<String, String> urlMap = uploadExchangeService.convertUrls(urls);
        if (MapUtil.isEmpty(urlMap)) {
            return;
        }

        attachmentList.stream().filter(att -> StrUtil.isNotBlank(att.getFileUrl()))
                .forEach(att -> att.setFileUrl(urlMap.get(att.getFileUrl())));
    }
}
