package com.wanlianyida.user.application.service.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.inter.ShopCartCommandInter;
import com.wanlianyida.order.api.model.command.AddShopCartCommand;
import com.wanlianyida.order.api.model.command.DelShopCartCommand;
import com.wanlianyida.order.api.model.command.DelShopCartNumCommand;
import com.wanlianyida.order.api.model.command.ShopProductCommand;
import com.wanlianyida.product.api.inter.ProductInter;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.query.ProductSkuQuery;
import com.wanlianyida.user.infrastructure.constant.Constant;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.ProductExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

/**
 * 购物车操作聚合成
 */
@Slf4j
@Component
public class ShopCartCommandAppService {

    @Resource
    private ShopCartCommandInter shopCartCommandInter;

    @Resource
    private ProductInter productInter;

    @Resource
    private RedisService redisService;

    @Resource
    private ProductExchangeService productExchangeService;


    /**
     * 添加购物车
     * @param command
     */
    public ResultMode addShopCart(AddShopCartCommand command) {
        log.info("addShopCart#添加购车请求参数：{}", JSONUtil.toJsonStr(command));
        // 添加购物车校验
//        ResultMode checkRes = checkAddShopCardValidation(command);
//        if (!checkRes.isSucceed()) {
//            return checkRes;
//        }
        return shopCartCommandInter.addShopCart(command);
    }

    /**
     * 删除购物车
     * @param command
     */
    public void delShopCart(List<DelShopCartCommand> command) {
        log.info("delShopCart#删除购车请求参数：{}", JSONUtil.toJsonStr(command));
        shopCartCommandInter.delShopCart(command);
    }


    /**
     * 删除购物车数量
     * @param command
     * @return
     */
    public ResultMode delShopCartNum(DelShopCartNumCommand command) {
        log.info("delShopCartNum#删除购车数量请求参数：{}", JSONUtil.toJsonStr(command));
        return shopCartCommandInter.delShopCartNum(command);
    }

    /**
     * 数据校验
     * @param command
     */
    private ResultMode checkData(AddShopCartCommand command) {
        /**
         * 1.校验当前时间是否在营业时间范围内。若是则进行下一步；若否则toast报错：非开市时间不可交易
         * 2.校验购买数量是否>0且<=sku数量。若是则进行下一步；若否则toast报错：购买数量最大为XX
         * 3.校验此用户是否企业认证通过且有贸易平台业务授权
         */
        //校验商品数据 1+价格 2+数量 3+商品状态
        ShopProductCommand shopProduct = command.getShopProduct();
        String skuCode = shopProduct.getSkuCode();
        ProductSkuQuery query = new ProductSkuQuery();
        query.setSkuCodeList(Arrays.asList(skuCode));
        ResultMode<List<ProductSkuDetailDTO>> resultMode = productInter.queryProductSkuDetail(query);
        if(ObjUtil.isNull(resultMode) || CollUtil.isEmpty(resultMode.getModel())){
            return ResultMode.fail("商品失效请刷新页面");
        }
        ProductSkuDetailDTO skuDetailDTO = resultMode.getModel().get(0);
        BigDecimal priceFee = Opt.ofNullable(skuDetailDTO.getPriceFee()).orElse(BigDecimal.ZERO);
        if(priceFee.compareTo(shopProduct.getPrice()) != 0){
            return ResultMode.fail("商品价格有变更请刷新页面");
        }
        //校验库存
        String sukStock = redisService.get(Constant.RedisKey.SUK_STOCK_PREFIX+skuCode);
        if(StrUtil.isBlank(sukStock)){
            return ResultMode.fail("商品库存为零");
        }
        BigDecimal decimalSukStock = new BigDecimal(sukStock);
        if(shopProduct.getPurchaseQuantity().compareTo(decimalSukStock) >0){
            return ResultMode.fail("购买数量最大为"+sukStock);
        }
        return ResultMode.success();
    }


    private ResultMode checkAddShopCardValidation(AddShopCartCommand addShopCartCommand) {
        /**
         * 校验购买数量>最小起订量， <库存数量
         */
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productExchangeService.queryProductSkuDetail(Lists.newArrayList(addShopCartCommand.getShopProduct().getSkuCode()));
        if (CollUtil.isEmpty(productSkuDetailDTOS)) {
            return ResultMode.fail(UserErrorCode.ERROR_10002.getCode(), UserErrorCode.ERROR_10002.getMsg());
        }
        ProductSkuDetailDTO productSkuDetailDTO = productSkuDetailDTOS.get(0);
        BigDecimal purchaseQuantity = addShopCartCommand.getShopProduct().getPurchaseQuantity();
        if (purchaseQuantity.compareTo(productSkuDetailDTO.getQuantity()) > 0 || purchaseQuantity.compareTo(productSkuDetailDTO.getMinQuantity()) < 0) {
            return ResultMode.fail(UserErrorCode.ERROR_10006.getCode(),
                    String.format(UserErrorCode.ERROR_10006.getMsg(),
                            productSkuDetailDTO.getMinQuantity().stripTrailingZeros().toPlainString(),
                            productSkuDetailDTO.getQuantity().stripTrailingZeros().toPlainString()));
        }
        return ResultMode.success();
    }
}
