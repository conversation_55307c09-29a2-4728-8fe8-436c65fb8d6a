package com.wanlianyida.user.application.service.partner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserInfoQuery;
import com.wanlianyida.basemsg.api.model.command.SystemMsgCommand;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.user.infrastructure.constant.Constant;
import com.wanlianyida.user.infrastructure.enums.PhoneCodeTypeEnum;
import com.wanlianyida.user.infrastructure.exception.CtpOrchUserException;
import com.wanlianyida.user.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.user.infrastructure.exchange.MsgExchangeService;
import com.wanlianyida.user.infrastructure.utils.OrgUtil;
import com.wanlianyida.user.interfaces.model.command.GetPhoneCodeCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import static com.wanlianyida.user.infrastructure.utils.CommonUtil.isPhoneFormatValid;

@Slf4j
@Service
public class CommonValidatorAppService {
    @Resource
    private RedisService redisService;
    @Resource
    private MsgExchangeService msgExchangeService;
    @Resource
    private MdmExchangeService mdmExchangeService;


    public ResultMode<String> getSendCode(GetPhoneCodeCommand command) {
        String phone = command.getPhone();
        String type = command.getType();
        Boolean isNeedQueryPhone = command.getIsNeedQueryPhone();

        if (StrUtil.isEmpty(type)) {
            return ResultMode.fail("手机号类型不能为空");
        }
        if (ObjectUtil.isEmpty(isNeedQueryPhone)) {
            return ResultMode.fail("是否需要查询手机号不能为空");
        }


        PhoneCodeTypeEnum typeEnum = PhoneCodeTypeEnum.getByCode(type);
        if (ObjectUtil.isNull(typeEnum)) {
            return ResultMode.fail("验证码类型错误");
        }

        if (isNeedQueryPhone) {
            // 查询用户手机号
            TokenInfo tokenInfo = JwtUtil.getTokenInfo();
            if (ObjectUtil.isNull(tokenInfo)) {
                return ResultMode.fail("当前用户未登录");
            }
            String loginName = tokenInfo.getLoginName();
            phone = queryUserPhone(loginName);
        }

        // 校验手机号格式
        isPhoneFormatValid(phone);

        // 缓存key
        String cacheKey = Constant.RedisKey.USER_CODE_PREFIX + typeEnum.name() + ":" + phone;
        // 获取手机号验证码
        String cacheValue = redisService.get(cacheKey);
        if (StrUtil.isNotBlank(cacheValue)) {
            // 截取下划线后边的内容
            long ttl = Long.parseLong(cacheValue.split("_")[1]);
            //当前时间戳-验证码发送的时间戳，如果小于60秒，则不给重复发送
            long leftTime = OrgUtil.getCurrentTimestamp() - ttl;
            if (leftTime < (1000 * 60)) {
                log.info("重复发送短信验证码，时间间隔:{}秒", leftTime);
                return ResultMode.fail("请勿重复发送短信验证码");
            }
        }
        // 获取验证码
        String code = OrgUtil.getRandomCode(6);
        // 拼接验证码
        String value = code + "_" + OrgUtil.getCurrentTimestamp();
        log.info("======================生成的验证码为:{}===================", code);
        // 存入缓存
        redisService.set(cacheKey, value, Constant.CODE_EXPIRED_5, TimeUnit.MILLISECONDS);
        log.info("======================验证码存入缓存，key:{}，value:{}===================", cacheKey, value);
        // 调用发送验证码的接口
        Set<String> phoneList = new HashSet<>();
        HashMap<String, String> paramsMap = new HashMap<>();
        paramsMap.put("code", code);
        phoneList.add(phone);

        sendMsg(typeEnum.getTemplateId(), "1", phoneList, paramsMap);

//        return ResultMode.success("验证码已发送至手机,请在手机上查看");
        return ResultMode.success(code);
    }

    /**
     * 发送消息
     *
     * @param templateId   模板id
     * @param type         类型 1短信 2站内信
     * @param receiverList 接收器列表
     * @param paramsMap    参数图
     * @return {@code ResultMode }
     */
    public ResultMode sendMsg(String templateId, String type, Set<String> receiverList, HashMap<String, String> paramsMap) {
        // 发送站内信通知
        SystemMsgCommand msgInfo = new SystemMsgCommand();
        //模板id
        msgInfo.setTemplateId(templateId);
        //模板参数
        JSONObject params = new JSONObject();
        paramsMap.forEach((param, value) -> params.put(param, value));
        msgInfo.setTemplateParameter(JSON.toJSONString(params));
        //消息区分：21大宗平台用户端,22大宗平台管理端
        msgInfo.setAppId(21);
        if (ObjectUtil.equal(type, "1")) {
            // 发短信
            msgInfo.setPhoneList(receiverList);
        } else {
            // 站内信
            msgInfo.setReceiverList(receiverList);
        }
        log.info("发送短信验证码参数：{}", JSON.toJSONString(msgInfo));
        //推送消息
        ResultMode send = msgExchangeService.send(msgInfo);
        if (!send.isSucceed()) {
            return ResultMode.fail(send.getMessage());
        }
        log.info("发送短信验证码结果：{}", send.getMessage());
        return send;
    }

    /**
     * 调用主数据查询用户联系方式
     *
     * @param loginName
     * @return
     */
    public String queryUserPhone(String loginName) {
        MdmUserInfoQuery query = new MdmUserInfoQuery();
        query.setLoginName(loginName);
        List<MdmUserInfoDTO> mdmUserInfoDTOS = mdmExchangeService.queryCmdUserList(query);
        return CollUtil.isNotEmpty(mdmUserInfoDTOS) ? mdmUserInfoDTOS.get(0).getMobile() : null;
    }

    public Boolean checkPhoneRegistered(String phone) {
        if (StrUtil.isEmpty(phone)) {
            throw new CtpOrchUserException("手机号码不能为空");
        }
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.queryByUserPhone(phone);
        if (ObjectUtil.isNotEmpty(mdmUserInfoDTO)) {
            return true;
        }
        return false;
    }
}
