package com.wanlianyida.user.application.service.product;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.inter.ProductCollectInter;
import com.wanlianyida.product.api.model.command.ProductCollectActionCommand;
import com.wanlianyida.product.api.model.command.ProductCollectAddCommand;
import com.wanlianyida.product.api.model.command.ProductCollectCancelCommand;
import com.wanlianyida.user.infrastructure.enums.CollectActionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户收藏店铺
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Service
@Slf4j
public class ProductCollectAppService {

    @Resource
    private ProductCollectInter productCollectInter;


    /**
     * 店铺收藏操作
     *
     * @param addCommand
     * @return {@link Boolean }
     */
    public ResultMode<?> productCollectAction(ProductCollectActionCommand addCommand) {
        if (CollectActionEnum.MUST_INPUT.getCode().equals(addCommand.getCollectActionType())) {
            return productCollectInter.addProductCollect(BeanUtil.copyProperties(addCommand, ProductCollectAddCommand.class));
        } else if (CollectActionEnum.NOT_MUST_INPUT.getCode().equals(addCommand.getCollectActionType())) {
            return productCollectInter.cancelCollection(BeanUtil.copyProperties(addCommand, ProductCollectCancelCommand.class));
        }
        return ResultMode.fail("操作类型错误");
    }


}
