package com.wanlianyida.user.application.service.product;

import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.inter.OutSaleReasonInter;
import com.wanlianyida.product.api.model.command.OutSaleReasonAddCommand;
import com.wanlianyida.product.api.model.command.OutSaleReasonChangeOrderCommand;
import com.wanlianyida.product.api.model.dto.OutSaleReasonDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 下架原因管理
 */
@Service
@Slf4j
public class OutSaleReasonAppService {

    @Resource
    private OutSaleReasonInter outSaleReasonInter;

    /**
     * 查询类表 sort_no正序
     */
    public ResultMode<List<OutSaleReasonDTO>> list() {
        return outSaleReasonInter.list();
    }

    public void add(OutSaleReasonAddCommand command) {
        outSaleReasonInter.add(command);
    }

    public void changeOrder(OutSaleReasonChangeOrderCommand command) {
        outSaleReasonInter.changeOrder(command);
    }

    public void delete(IdCommand command) {
        outSaleReasonInter.delete(command);
    }
}
