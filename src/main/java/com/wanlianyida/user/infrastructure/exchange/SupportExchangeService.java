package com.wanlianyida.user.infrastructure.exchange;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.AutoAuditConfigInter;
import com.wanlianyida.support.api.inter.PicLibraryInter;
import com.wanlianyida.support.api.inter.PlatformConfigInter;
import com.wanlianyida.support.api.model.command.PicLibraryAddCommand;
import com.wanlianyida.support.api.model.command.PicLibraryUpdateCommand;
import com.wanlianyida.support.api.model.dto.AutoAuditBiddingConfigListDTO;
import com.wanlianyida.support.api.model.dto.PicLibraryListDTO;
import com.wanlianyida.support.api.model.dto.PlatformConfigDTO;
import com.wanlianyida.support.api.model.query.AutoAuditBiddingConfigListQuery;
import com.wanlianyida.support.api.model.query.PicLibraryListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/03/12/15:38
 */
@Slf4j
@Component
public class SupportExchangeService {

    @Resource
    private PicLibraryInter picLibraryInter;

    @Resource
    private PlatformConfigInter platformConfigInter;

    @Resource
    private AutoAuditConfigInter autoAuditConfigInter;

    public ResultMode<List<AutoAuditBiddingConfigListDTO>> queryBiddingConfigList(AutoAuditBiddingConfigListQuery query) {
        return autoAuditConfigInter.queryBiddingConfigList(query);
    }

    /**
     * 新增
     * @param addCommand
     * @return
     */
    public ResultMode add(List<PicLibraryAddCommand> addCommand) {
        return picLibraryInter.add(addCommand);
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    public ResultMode update(PicLibraryUpdateCommand updateCommand) {
        return picLibraryInter.update(updateCommand);
    }


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    public ResultMode<List<PicLibraryListDTO>> list(PagingInfo<PicLibraryListQuery> query) {
        return picLibraryInter.list(query);
    }

    /**
     * 查询平台配置
     */
    public ResultMode<PlatformConfigDTO> getCommonConfig() {
        return platformConfigInter.getCommonConfig();
    }
}
