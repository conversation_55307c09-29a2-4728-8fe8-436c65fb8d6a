package com.wanlianyida.user.infrastructure.enums;

import lombok.Getter;

@Getter
public enum QualTypeEnum {

    SOCIAL_CREDIT_CODE("10", "营业执照"),
    LEGAL_CODE("20", "法人身份证"),
    ALl("99","营业执照/法人身份证");

    private final String desc;
    private final String code;

    QualTypeEnum(String code, String desc) {
        this.desc = desc;
        this.code = code;
    }
    public static QualTypeEnum getByCode(String code) {
        for (QualTypeEnum value : QualTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return QualTypeEnum.SOCIAL_CREDIT_CODE;
    }
}
