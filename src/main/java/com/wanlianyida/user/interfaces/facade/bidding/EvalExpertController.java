package com.wanlianyida.user.interfaces.facade.bidding;

import com.wanlianyida.bidding.api.model.command.EvalExpertCommand;
import com.wanlianyida.bidding.api.model.command.EvalExpertDTO;
import com.wanlianyida.bidding.api.model.command.EvalExpertUpdateCommand;
import com.wanlianyida.bidding.api.model.query.EvalExpertListQuery;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.user.application.service.bidding.EvalExpertAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 评标专家
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@RequestMapping("/eval/expert")
public class EvalExpertController {

    @Resource
    private EvalExpertAppService evalExpertAppService;


    /**
     * 批量创建评标专家
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/batchCreate")
    public ResultMode<?> batchCreate(@RequestBody @Validated EvalExpertCommand updateCommand) {
        return evalExpertAppService.batchCreate(updateCommand);
    }

    /**
     * 更新评标专家
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/updateEvalExpert")
    public ResultMode<Boolean> updateEvalExpert(@RequestBody @Validated EvalExpertUpdateCommand updateCommand) {
        return evalExpertAppService.update(updateCommand);
    }

    /**
     * 查询评标专家
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPageList")
    public ResultMode<List<EvalExpertDTO>> list(@RequestBody EvalExpertListQuery query) {
        return evalExpertAppService.list(query);
    }

}
