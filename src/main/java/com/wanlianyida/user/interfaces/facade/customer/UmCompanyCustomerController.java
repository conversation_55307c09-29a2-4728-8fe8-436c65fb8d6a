package com.wanlianyida.user.interfaces.facade.customer;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmCompanyCustomerCommand;
import com.wanlianyida.partner.api.model.dto.UmCompanyCustomerDTO;
import com.wanlianyida.partner.api.model.dto.UmSupplierCustomerDTO;
import com.wanlianyida.partner.api.model.query.UmCompanyCustomerQuery;
import com.wanlianyida.partner.api.model.query.UmSupplierCustomerQuery;
import com.wanlianyida.user.application.service.customer.UmCompanyCustomerAppService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 企业客户/供应商信息表 Controller
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@RequestMapping("/umCompanyCustomer")
@RestController
public class UmCompanyCustomerController {

	@Resource
    private UmCompanyCustomerAppService umCompanyCustomerAppService;

	/**
	 * 分页查询
	 * @param pagingInfo 分页查询参数
	 * @return {@link ResultMode}<{@link UmCompanyCustomerDTO}>
	 */
	@PostMapping("/queryPage")
	public ResultMode<List<UmCompanyCustomerDTO>> queryPage(@RequestBody PagingInfo<UmCompanyCustomerQuery> pagingInfo) {
		return umCompanyCustomerAppService.queryPage(pagingInfo);
	}

	/**
	 * 列表查询
	 * @param condition 查询参数
	 * @return {@link ResultMode}<{@link UmCompanyCustomerDTO}>
	 */
	@PostMapping("/queryList")
	public ResultMode<List<UmCompanyCustomerDTO>> queryList(@RequestBody UmCompanyCustomerQuery condition) {
		return umCompanyCustomerAppService.queryList(condition);
	}

    /**
	 * 新增
	 * @param bo
	 */
	@PostMapping("/insert")
	public ResultMode insert(@RequestBody UmCompanyCustomerCommand bo) {
		return umCompanyCustomerAppService.insert(bo);
	}

	/**
	 * 修改
	 * @param bo
	 */
	@PostMapping("/update")
	public ResultMode update(@RequestBody UmCompanyCustomerCommand bo) {
		return umCompanyCustomerAppService.update(bo);
	}

	/**
	 * 逻辑删除
	 * @param id
	 */
	@PostMapping("/delete/{id}")
	public ResultMode delete(@PathVariable("id") Long id) {
		return umCompanyCustomerAppService.delete(id);
	}


	/**
	 * 供应商列表查询
	 *
	 */
	@PostMapping("/querySupplierList")
	public ResultMode<List<UmSupplierCustomerDTO>> querySupplierList(@RequestBody UmSupplierCustomerQuery query) {
		return umCompanyCustomerAppService.querySupplierList(query);
	}

	/**
	 * 详情
	 */
	@PostMapping("/detail/{id}")
	public ResultMode<?> detail(@PathVariable("id") Long id) {
		return ResultMode.success(umCompanyCustomerAppService.detail(id));
	}
}
