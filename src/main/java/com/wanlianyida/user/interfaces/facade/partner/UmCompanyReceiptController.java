package com.wanlianyida.user.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmCompanyReceiptInsertCommand;
import com.wanlianyida.partner.api.model.command.UmCompanyReceiptUpdateCommand;
import com.wanlianyida.partner.api.model.dto.UmCompanyReceiptDTO;
import com.wanlianyida.partner.api.model.query.UmCompanyReceiptQuery;
import com.wanlianyida.user.application.service.partner.UmCompanyReceiptAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月31日 09:16
 */
@Api("企业收款信息")
@RestController
@RequestMapping("/company/receipt")
public class UmCompanyReceiptController {

    @Resource
    private UmCompanyReceiptAppService companyReceiptAppService;

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResultMode }
     */
    @PostMapping("/queryList")
    public ResultMode<List<UmCompanyReceiptDTO>> queryPage(@RequestBody PagingInfo<UmCompanyReceiptQuery> pagingInfo) {
        return companyReceiptAppService.queryPage(pagingInfo);
    }

    @ApiOperation("新增")
    @PostMapping("/add")
    public ResultMode<?> add(@RequestBody @Validated UmCompanyReceiptInsertCommand command) {
        return companyReceiptAppService.add(command);
    }

    @ApiOperation("编辑")
    @PostMapping("/update")
    public ResultMode<?> update(@RequestBody @Validated UmCompanyReceiptUpdateCommand command) {
        return companyReceiptAppService.update(command);
    }

    @ApiOperation("设为默认")
    @PostMapping("/setDefault")
    public ResultMode<?> setDefault(@RequestBody @Validated IdQuery query) {
        return companyReceiptAppService.setDefault(query);
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResultMode<?> delete(@RequestBody @Validated IdCommand command) {
        return companyReceiptAppService.delete(command);
    }
}
