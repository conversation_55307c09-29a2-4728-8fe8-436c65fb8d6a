package com.wanlianyida.user.interfaces.facade.partner;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.*;
import com.wanlianyida.partner.api.model.dto.CompanyAuditInfoDTO;
import com.wanlianyida.partner.api.model.dto.CompanyStatusDTO;
import com.wanlianyida.partner.api.model.dto.EnterpriseInfoDTO;
import com.wanlianyida.partner.api.model.dto.IdCardOcrDTO;
import com.wanlianyida.partner.api.model.query.CheckCompanyStatusQuery;
import com.wanlianyida.partner.api.model.query.CompanyAuditInfoQuery;
import com.wanlianyida.partner.api.model.query.EnterpriseQuery;
import com.wanlianyida.user.application.service.partner.EnterpriseAccountAppService;
import com.wanlianyida.user.interfaces.model.dto.UmEmployeeBindingRecordListDTO;
import com.wanlianyida.user.interfaces.model.query.EmployeeInviteRecordQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/** 企业账号管理
 * <AUTHOR>
 * @date 2025/03/28
 */
@RestController
@RequestMapping("/enterprise/account")
public class EnterpriseAccountController {
    @Resource
    private EnterpriseAccountAppService appService;
    /**
     * 创建公司
     * @param command
     * @return
     */
    @PostMapping("/createCompany")
    public ResultMode<String> createCompany(@RequestBody CreateCompanyCommand command) {
        return appService.createCompany(command);
    }

    /**
     *
     * 企业信息详情页
     * @param query
     * @return
     */
    @PostMapping("/getEntProfileDetailPage")
    public ResultMode<EnterpriseInfoDTO> getEntProfileDetailPage(@RequestBody EnterpriseQuery query) {
        return appService.getEntProfileDetailPage(query);
    }

    /**
     * 开通角色
     * @param command
     * @return
     */
    @PostMapping("/openRole")
    public ResultMode<Void> openRole(@RequestBody UmRoleCommand command) {
        return appService.openRole(command);
    }

    /**
     * 根据企业名称校验公司是否注册
     * @param query
     * @return
     */
    @PostMapping("/checkCompanyStatusByName")
    public ResultMode<CompanyStatusDTO> checkCompanyStatusByName(@RequestBody CheckCompanyStatusQuery query) {
        return appService.checkCompanyStatusByName(query);
    }

    @ApiOperation("分页查询邀请记录")
    @PostMapping("/pageInviteRecordList")
    public ResultMode<List<UmEmployeeBindingRecordListDTO>> pageInviteRecordList(@RequestBody @Validated PagingInfo<EmployeeInviteRecordQuery> pageQuery) {
        return appService.pageInviteRecordList(pageQuery);
    }

    /**
     * 根据企业信用代码查询企业状态
     * @param query
     * @return
     */
    @PostMapping("/checkCompanyStatusByLicenseNo")
    public ResultMode<CompanyInfoDTO> checkCompanyStatusByLicenseNo(@RequestBody CheckCompanyStatusQuery query) {
        return appService.checkCompanyStatusByLicenseNo(query);
    }

    /**
     * 变更授权委托书
     * @param command
     * @return
     */
    @PostMapping("/updatePowerOfAttorney")
    public ResultMode<Void> updatePowerOfAttorney(@RequestBody PowerOfAttorneyCommand command) {
        return appService.updatePowerOfAttorney(command);
    }

    /**
     * 变更工商信息
     * @param command
     * @return
     */
    @PostMapping("/updateBusinessLicenseInfo")
    public ResultMode<Void> updateBusinessLicenseInfo(@RequestBody @Validated BusinessLicenseInfoCommand command) {
        return appService.updateBusinessLicenseInfo(command);
    }

    /**
     * 企业邀请员工
     * @param command
     * @return
     */
    @PostMapping("/invite")
    public ResultMode<Void> invite(@RequestBody EmployeeInviteCommand command) {
        return appService.invite(command);
    }

    /**
     *  企业法人OCR识别
     */
    @PostMapping("/identityCardOcr")
    public ResultMode<IdCardOcrDTO> identityCardOcr(@RequestBody IdCardOcrCommand command) {
        return appService.identityCardOcr(command);
    }

    /**
     * 变更企业工商信息+ 法人信息
     */
    @PostMapping("/updateEnterpriseAndLegalInfo")
    public ResultMode<Void> updateEnterpriseAndLegalInfo(@RequestBody UpdateEnterpriseAndLegalInfoCommand command) {
        return appService.updateEnterpriseAndLegalInfo(command);
    }

    /**
     * 查询企业审核信息
     *
     * @param query
     * @return
     */
    @PostMapping("/query-company-audit-info")
    public ResultMode<CompanyAuditInfoDTO> queryCompanyAuditInfo(@RequestBody CompanyAuditInfoQuery query) {
        return appService.queryCompanyAuditInfo(query);
    }


}
