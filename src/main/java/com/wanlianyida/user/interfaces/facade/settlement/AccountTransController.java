package com.wanlianyida.user.interfaces.facade.settlement;

import com.wanlianyida.exter.api.finance.model.dto.DropdownItemDTO;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.api.model.command.AccountTransApplyCommand;
import com.wanlianyida.sett.api.model.dto.AccountTransApplyDetailDTO;
import com.wanlianyida.sett.api.model.dto.SettAcctTransApplyListDTO;
import com.wanlianyida.sett.api.model.query.AccountTransListQuery;
import com.wanlianyida.user.application.service.settlement.AccountTransAppService;
import com.wanlianyida.user.interfaces.model.dto.BankChannelDTO;
import com.wanlianyida.user.interfaces.model.dto.ElectronicReceiptDTO;
import com.wanlianyida.user.interfaces.model.dto.FundsChangeListDTO;
import com.wanlianyida.sett.api.model.dto.IdDTO;
import com.wanlianyida.user.interfaces.model.query.FundsChangeQuery;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api("账户交易")
@RestController
@RequestMapping("/accountTrans")
public class AccountTransController {

    @Resource
    private AccountTransAppService accountTransAppService;

    /**
     * 账户交易列表查询
     */
    @PostMapping("/listPage")
    public ResultMode<List<SettAcctTransApplyListDTO>> listPage(@Validated @RequestBody PagingInfo<AccountTransListQuery> query) {
        return accountTransAppService.listPage(query);
    }

    /**
     * 提现
     */
    @PostMapping("/cashOut")
    public ResultMode<IdDTO> cashOut(@RequestBody @Validated AccountTransApplyCommand command) {
        return accountTransAppService.cashOut(command);
    }

    /**
     * 提现详情
     */
    @PostMapping("/getDetail")
    public ResultMode<AccountTransApplyDetailDTO> getDetail(@RequestBody @Validated IdQuery query) {
        return accountTransAppService.getDetail(query);
    }

    /**
     * 获取支持的银行渠道
     */
    @PostMapping("/queryBankChannel")
    public ResultMode<List<BankChannelDTO>> queryBankChannel() {
        return accountTransAppService.queryBankChannel();
    }

    /**
     * 获取账户变动原因下拉列表（资金变动部分）
     */
    @PostMapping("/queryTradeType")
    public ResultMode<List<DropdownItemDTO>> queryTradeType() {
        return accountTransAppService.queryTradeType();
    }

    /**
     * 资金变动查询
     */
    @PostMapping("/queryFundsChangePage")
    public ResultMode<List<FundsChangeListDTO>> queryFundsChangePage(@Validated @RequestBody PagingInfo<FundsChangeQuery> query) {
        return accountTransAppService.queryFundsChangePage(query);
    }

    /**
     * 下载回单
     */
    @PostMapping("/downloadReceipt")
    public ResultMode<ElectronicReceiptDTO> downloadReceipt(@RequestBody List<String> tradNoList) {
        return accountTransAppService.downloadReceipt(tradNoList);
    }
}
