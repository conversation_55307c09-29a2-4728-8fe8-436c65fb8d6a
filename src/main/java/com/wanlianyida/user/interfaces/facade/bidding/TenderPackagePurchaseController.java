package com.wanlianyida.user.interfaces.facade.bidding;

import com.wanlianyida.bidding.api.model.command.PackagePurchaseCommand;
import com.wanlianyida.bidding.api.model.command.PackagePurchaseConfirmCommand;
import com.wanlianyida.bidding.api.model.dto.TenderPackagePurchaseDetailDTO;
import com.wanlianyida.bidding.api.model.dto.TenderPackagePurchaseListDTO;
import com.wanlianyida.bidding.api.model.query.PackagePurchaseQuery;
import com.wanlianyida.bidding.api.model.query.TenderPackagePurchasePageQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.user.application.service.bidding.TenderPackagePurchaseAppService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月18日 10:14
 */
@RestController
@RequestMapping("/tender/purchase")
public class TenderPackagePurchaseController {

    @Resource
    private TenderPackagePurchaseAppService packagePurchaseAppService;

    @PostMapping("/pageCondition")
    @ApiModelProperty("标书购买记录分页查询")
    public ResultMode<List<TenderPackagePurchaseListDTO>> pageCondition(@RequestBody @Validated PagingInfo<TenderPackagePurchasePageQuery> pageQuery) {
        return packagePurchaseAppService.pageCondition(pageQuery);
    }

    @PostMapping("/purchase")
    @ApiModelProperty("标书购买记录分页查询")
    public ResultMode<?> purchase(@RequestBody @Validated PackagePurchaseCommand command) {
        return packagePurchaseAppService.purchase(command);
    }

    @PostMapping("/confirm")
    @ApiModelProperty("标书购买记录分页查询")
    public ResultMode<?> confirm(@RequestBody @Validated PackagePurchaseConfirmCommand command) {
        return packagePurchaseAppService.confirm(command);
    }

    @PostMapping("/purchaseDetail")
    @ApiModelProperty("购买标书详情")
    public ResultMode<TenderPackagePurchaseDetailDTO> purchaseDetail(@RequestBody PackagePurchaseQuery query) {
        return packagePurchaseAppService.purchaseDetail(query);
    }
}
