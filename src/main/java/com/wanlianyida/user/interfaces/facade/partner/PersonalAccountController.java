package com.wanlianyida.user.interfaces.facade.partner;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.*;
import com.wanlianyida.partner.api.model.dto.*;
import com.wanlianyida.partner.api.model.query.QueryBoundInfoQuery;
import com.wanlianyida.partner.api.model.query.UmAccreditAuditQuery;
import com.wanlianyida.partner.api.model.query.UmEmployeeBindingRecordQuery;
import com.wanlianyida.user.application.service.partner.PersonalAccountAppService;
import com.wanlianyida.user.interfaces.model.command.ApplyToBeAdminCommand;
import com.wanlianyida.user.interfaces.model.command.HandOverAdminCommand;
import com.wanlianyida.user.interfaces.model.command.SwitchPlatformCommand;
import com.wanlianyida.user.interfaces.model.dto.CurrentCompanyDTO;
import com.wanlianyida.user.interfaces.model.dto.SwitchPlatformDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 个人账号管理
 *
 * <AUTHOR>
 * @date 2025/03/28
 */
@RestController
@RequestMapping("/um/personal/account")
public class PersonalAccountController {
    @Resource
    private PersonalAccountAppService appService;

    @ApiOperation("分页查询员工绑定记录")
    @PostMapping("/pageBindingRecordList")
    public ResultMode<List<UmEmployeeBindingRecordDTO>> pageBindingHistoryList(@RequestBody @Validated PagingInfo<UmEmployeeBindingRecordQuery> pageQuery) {
        return appService.pageBindingRecordList(pageQuery);
    }

    @ApiOperation("分页查询管理员绑定记录")
    @PostMapping("/pageManagerList")
    public ResultMode<List<UmAccreditAuditListDTO>> pageManagerList(@RequestBody @Validated PagingInfo<UmAccreditAuditQuery> pageQuery) {
        return appService.pageManagerList(pageQuery);
    }

    /**
     * 更换手机号
     *
     * @param command
     * @return
     */
    @PostMapping("/updateUserPhone")
    public ResultMode<Void> updateUserPhone(@RequestBody CmdUserInfoCommand command) {
        return appService.updateUserPhone(command);
    }

    /**
     * 绑定/更新用户邮箱
     *
     * @param command
     * @return
     */
    @PostMapping("/setUserEmailAddress")
    public ResultMode<Void> setUserEmailAddress(@RequestBody CmdUserInfoCommand command) {
        return appService.setUserEmailAddress(command);
    }

    /**
     * 实名认证
     *
     * @param command
     * @return
     */
    @PostMapping("/authenticateRealName")
    public ResultMode<AuthenticateRealNameResultDTO> authenticateRealName(@RequestBody AuthenticateRealNameCommand command) {
        return appService.authenticateRealName(command);
    }

    /**
     * 更新用户二要素信息（姓名和身份证号）
     *
     * @param command
     * @return
     */
    @PostMapping("/updateUserBasicTwoFactors")
    public ResultMode<Void> updateUserBasicTwoFactors(@RequestBody AuthenticateRealNameCommand command) {
        return appService.updateUserBasicTwoFactors(command);
    }

    /**
     * 申请成为管理员
     *
     * @param command
     * @return
     */
    @PostMapping("/applyToBeAdministrator")
    public ResultMode<Void> applyToBeAdministrator(@RequestBody ApplyToBeAdminCommand command) {
        return appService.applyToBeAdministrator(command);
    }

    /**
     * 管理员过户
     *
     * @param command
     * @return
     */
    @PostMapping("/handOverAdministrator")
    public ResultMode<Void> handOverAdministrator(@RequestBody HandOverAdminCommand command) {
        return appService.handOverAdministrator(command);
    }

    /**
     * 个人详情页查询
     *
     * @return
     */
    @PostMapping("/getPersonalDetailPage")
    public ResultMode<PersonalInfoDTO> getPersonalDetailPage() {
        return appService.getPersonalDetailPage();
    }


    /**
     * 切换企业
     *
     * @param command
     * @return 10-当前用户未绑定企业 20-切换企业成功 30-切换企业失败
     */
    @PostMapping("/switchCompany")
    public ResultMode<SwitchCompanyDTO> switchCompany(@RequestBody SwitchCompanyCommand command) {
        return appService.switchCompany(command);
    }

    /**
     * 员工处理邀请
     * @param command
     * @return
     */
    @PostMapping("/employeeInviteProcess")
    public ResultMode<Void> employeeInviteProcess(@RequestBody HandleInvitationCommand command) {
        return appService.employeeInviteProcess(command);
    }

    /**
     * 设置默认企业
     * @param command
     * @return
     */
    @PostMapping("/setDefaultCompany")
    public ResultMode<Void> setDefaultCompany(@RequestBody @Validated SetDefaultCompanyCommand command) {
        return appService.setDefaultCompany(command);
    }

    /**
     * 分页查询已绑定的企业信息
     *
     * @param pagingInfo
     * @return
     */
    @PostMapping("/queryBoundCompanyInfo")
    public ResultMode<List<BindCompanyDTO>> queryBoundCompanyInfo(@RequestBody PagingInfo<QueryBoundInfoQuery> pagingInfo) {
        return appService.queryBoundCompanyInfo(pagingInfo);
    }

    /**
     * 根据手机号查询用户是否实名认证
     * @param phone
     * @return
     */
    @GetMapping("/queryUserRealNameStatus{phone}")
    public ResultMode<Void> queryUserRealNameStatus(@PathVariable String phone){
        if (StrUtil.isEmpty(phone)){
            return ResultMode.fail("手机号不能为空");
        }
        return appService.queryUserRealNameStatus(phone);
    }

    /**
     * 获取当前用户所在企业信息
     * @return
     */
    @PostMapping("/getCurrentCompany")
    public ResultMode<CurrentCompanyDTO> getCurrentCompany(){
        return appService.getCurrentCompany();
    }

    @ApiOperation("待处理的邀请员工数量")
    @PostMapping("/countPendingInvitedEmployees")
    public ResultMode<Integer> countPendingInvitedEmployees(){
        return appService.countPendingInvitedEmployees();
    }


    @ApiOperation("切换平台")
    @PostMapping("/switchPlatform")
    public ResultMode<SwitchPlatformDTO> switchPlatform(@RequestBody SwitchPlatformCommand command){
        return appService.switchPlatform(command);
    }

    /**
     * 获取邀请码
     *
     * @param cmd 邀请码表
     * @return ResultMode<UmInviteCodeDTO>
     */
    @ApiOperation(value = "获取邀请码", notes = "获取邀请码")
    @PostMapping("/getInviteCode")
    public ResultMode<UmInviteCodeDTO> getInviteCode(@RequestBody UmInviteCodeCommand cmd){
        return appService.getInviteCode(cmd);
    }
}
