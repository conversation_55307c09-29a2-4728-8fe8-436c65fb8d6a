package com.wanlianyida.user.interfaces.facade.invoice;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.api.model.command.ConfirmInvoiceCommand;
import com.wanlianyida.sett.api.model.command.CreateInvoiceCommand;
import com.wanlianyida.sett.api.model.command.EditInvoiceCommand;
import com.wanlianyida.sett.api.model.command.ReturnInvoiceCommand;
import com.wanlianyida.sett.api.model.dto.*;
import com.wanlianyida.sett.api.model.query.*;
import com.wanlianyida.user.application.service.invoice.InvoiceAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/invoice")
@Validated
public class InvoiceController {

    @Resource
    private InvoiceAppService invoiceAppService;

    /**
     * 创建发票申请单
     */
    @PostMapping("/createInvoice")
    public ResultMode<Void> createInvoice(@RequestBody @Valid CreateInvoiceCommand command) {
        return invoiceAppService.createInvoice(command);
    }

    /**
     * 修改发票申请单
     */
    @PostMapping("/editInvoice")
    public ResultMode<Void> editInvoice(@RequestBody @Valid EditInvoiceCommand command) {
        return invoiceAppService.editInvoice(command);
    }

    /**
     * 确认收票
     */
    @PostMapping("/confirmInvoice")
    public ResultMode<Void> confirmInvoice(@RequestBody @Valid ConfirmInvoiceCommand command) {
        return invoiceAppService.confirmInvoice(command);
    }

    /**
     * 退回
     */
    @PostMapping("/returnInvoice")
    public ResultMode<Void> returnInvoice(@RequestBody @Valid ReturnInvoiceCommand command) {
        return invoiceAppService.returnInvoice(command);
    }

    /**
     * 发票列表查询
     */
    @PostMapping("/listPage")
    ResultMode<List<InvoiceApplyListDTO>> listPage(@Validated @RequestBody PagingInfo<InvoiceApplyListQuery> query){
        return  invoiceAppService.listPage(query);
    }

    /**
     * 发票明细查询
     */
    @GetMapping("/detailById")
    public ResultMode<InvoiceApplyDetailListDTO> detailByIdAdmin(@RequestParam(name = "id") @NotNull(message = "id不能为空") String id) {
        return invoiceAppService.detailById(id);
    }

    /**
     * 开票状态统计
     */
    @PostMapping("/statistics")
    public ResultMode<InvoiceApplyStatisticsDTO> statistics(@Validated @RequestBody InvoiceAppIyByIdListQuery query) {
        return invoiceAppService.statistics(query);
    }
    /**
     * 买家:企业筛选
     */
    @PostMapping("/buyersEnterprise")
    public ResultMode<List<InvoiceBuyersEnterpriseDTO>> buyersEnterprise(@RequestBody @Valid InvoiceBuyersEnterpriseQuery query){
        return invoiceAppService.buyersEnterprise(query);
    }

    /**
     * 卖家:企业筛选
     */
    @PostMapping("/sellerEnterprise")
    public ResultMode<List<InvoiceSellerEnterpriseDTO>> sellerEnterprise(@RequestBody @Valid InvoiceSellerEnterpriseQuery query){
        return invoiceAppService.sellerEnterprise(query);
    }

    /**
     * 发票是否号码重复校验
     */
    @PostMapping("/existInvoiceNo")
    public ResultMode<InvoiceItExistInvoiceNoDto> doesItExistInvoiceNo(@Validated @RequestBody InvoiceNoByIdListQuery query){
        return invoiceAppService.doesItExistInvoiceNo(query);
    }

    /**
     * 结算单/可移入单据分页列表
     *
     */
    @PostMapping("/invoiceSettByPageList")
    public ResultMode<List<InvoiceSettlListDTO>> settlByPageList(@Validated @RequestBody PagingInfo<InvoiceSettlListQuery> query){
        return invoiceAppService.invoiceSettByPageList(query);
    }

    /**
     * 开票/批量开票:查询结算单明细
     */
    @PostMapping("/batchInvoicing")
    public ResultMode<List<InvoiceBatchInvoicingDTO>> batchInvoicing(@Validated @RequestBody InvoiceInvoicingQuery query){
        return invoiceAppService.batchInvoicing(query);
    }
}
