package com.wanlianyida.user.interfaces.facade.user;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.user.application.service.user.UserPermissionFunctionsAppService;
import com.wanlianyida.user.interfaces.model.command.UserPermissionFunctionsCommand;
import com.wanlianyida.user.interfaces.model.dto.UserFunctionsDTO;
import com.wanlianyida.user.interfaces.model.query.UserPermissionFunctionsQuery;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/user/userPermFun")
public class UserPermissionFunctionsController {

    @Resource
    UserPermissionFunctionsAppService userPermissionFunctionsAppService;

    @PostMapping("/queryAssignedList")
    public ResultMode<List<UserFunctionsDTO>> queryAssignedList(@RequestBody UserPermissionFunctionsQuery query) {
        return userPermissionFunctionsAppService.queryAssignedList(query);
    }

    @PostMapping("/assignUserPermission")
    public ResultMode assignUserPermissions(@RequestBody UserPermissionFunctionsCommand userPermissionFunctionsCommand) {
        return userPermissionFunctionsAppService.assignUserPermissions(userPermissionFunctionsCommand);
    }

}
