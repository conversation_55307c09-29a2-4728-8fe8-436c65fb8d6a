package com.wanlianyida.user.interfaces.facade.base;

import com.wanlianyida.basemdm.api.model.dto.MdmBankInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmBankInfoQuery;
import com.wanlianyida.exter.api.finance.model.dto.BankBranchInfoDTO;
import com.wanlianyida.exter.api.finance.model.query.BankBranchInfoQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.application.service.base.BankInfoAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年05月09日 16:17
 */
@Api("银行信息")
@RestController
@RequestMapping("/bankInfo")
public class BankInfoController {

    @Resource
    private BankInfoAppService bankInfoAppService;

    @ApiOperation("根据银行编码查询")
    @RequestMapping("/queryByCode/{bankCode}")
    public ResponseMessage<MdmBankInfoDTO> queryByCode(@PathVariable("bankCode") String bankCode) {
        return bankInfoAppService.queryByCode(bankCode);
    }

    @ApiOperation("条件查询")
    @RequestMapping("/queryCondition")
    public ResponseMessage<List<MdmBankInfoDTO>> queryCondition(@RequestBody @Validated MdmBankInfoQuery query) {
        return bankInfoAppService.queryCondition(query);
    }

    @ApiOperation("查询银行联行号信息")
    @PostMapping("/queryBankBranchNo")
    public ResponseMessage<List<BankBranchInfoDTO>> queryBankBranchNo(@RequestBody @Validated PagingInfo<BankBranchInfoQuery> query) {
        return bankInfoAppService.queryBankBranchNo(query);
    }
}
