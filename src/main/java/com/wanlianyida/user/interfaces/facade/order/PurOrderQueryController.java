package com.wanlianyida.user.interfaces.facade.order;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.order.api.model.dto.PurOrderDetailDTO;
import com.wanlianyida.order.api.model.dto.PurOrderListDTO;
import com.wanlianyida.order.api.model.query.PurOrderDetailQuery;
import com.wanlianyida.order.api.model.query.PurOrderListQuery;
import com.wanlianyida.user.application.service.order.PurOrderQuerAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 意向单查询
 *
 * <AUTHOR>
 * @since 2024/11/25 15:09
 */
@RestController
@RequestMapping("/purOrderQuery")
public class PurOrderQueryController {

    @Resource
    private PurOrderQuerAppService purOrderQuerAppService;

    /**
     * 意向订单列表查询
     */
    @PostMapping("pageList")
    public ResultMode<List<PurOrderListDTO>> pageList(@RequestBody PagingInfo<PurOrderListQuery> pagingInfo) {
        return purOrderQuerAppService.pageList(pagingInfo);
    }

    /**
     * 意向订单详情查询
     */
    @PostMapping("detail")
    public ResultMode<PurOrderDetailDTO> detail(@RequestBody @Validated IdQuery query) {
        return purOrderQuerAppService.detail(query);
    }

    /**
     * 意向订单详情查询
     */
    @PostMapping("detailByNo")
    public ResultMode<PurOrderDetailDTO> detailByNo(@RequestBody @Validated PurOrderDetailQuery query) {
        return purOrderQuerAppService.detailByNo(query);
    }

}
