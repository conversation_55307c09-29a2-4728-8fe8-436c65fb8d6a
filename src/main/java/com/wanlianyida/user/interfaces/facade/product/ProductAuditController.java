package com.wanlianyida.user.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.AuditRejectReasonAddCommand;
import com.wanlianyida.product.api.model.command.AuditRejectReasonSortCommand;
import com.wanlianyida.product.api.model.dto.*;
import com.wanlianyida.product.api.model.query.AuditRejectReasonQuery;
import com.wanlianyida.product.api.model.query.AuditSkuListQuery;
import com.wanlianyida.user.application.service.product.ProductAuditAppService;
import com.wanlianyida.user.interfaces.model.command.AuditLogCancelCommand;
import com.wanlianyida.user.interfaces.model.query.AuditSpuListQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月27日 14:05
 */
@Api("商品审核")
@RestController
@RequestMapping("/product/audit")
public class ProductAuditController {
    @Resource
    private ProductAuditAppService auditAppService;

    @ApiModelProperty("查询spu审核列表")
    @PostMapping("/querySpuList")
    public ResultMode<List<AuditLogSpuListDTO>> querySpuList(@RequestBody @Validated PagingInfo<AuditSpuListQuery> query) {
        return auditAppService.querySpuList(query);
    }

    @ApiOperation("/审核列表(以sku纬度)")
    @PostMapping("/pageSkuCondition")
    public ResultMode<List<AuditLogSkuListDTO>> pageSkuCondition(@RequestBody PagingInfo<AuditSkuListQuery> pageQuery) {
        return auditAppService.pageSkuCondition(pageQuery);
    }

    @ApiOperation("/根据状态统计数据（sku）")
    @PostMapping("/statisticsAuditSkuList")
    public ResultMode<AuditStatisticsDTO> statisticsAuditSkuList(@RequestBody AuditSkuListQuery pageQuery) {
        return auditAppService.statisticsAuditSkuList(pageQuery);
    }

    @ApiOperation("审核详情")
    @PostMapping("/queryDetail")
    public ResultMode<AuditLogDetailDTO> queryDetail(@RequestBody @Validated IdCommand command) {
        return auditAppService.queryDetail(command);
    }

    @ApiOperation("审核撤销")
    @PostMapping("/cancel")
    public ResultMode audit(@RequestBody @Validated AuditLogCancelCommand command) {
        return auditAppService.cancel(command);
    }

    @ApiOperation("查询驳回原因列表")
    @PostMapping("/queryRejectReason")
    public ResultMode<List<AuditRejectReasonDTO>> queryRejectReason(@RequestBody @Validated AuditRejectReasonQuery query) {
        return auditAppService.queryRejectReason(query);
    }

    @ApiOperation("添加驳回原因")
    @PostMapping("/addRejectReason")
    public ResultMode addRejectReason(@RequestBody @Validated AuditRejectReasonAddCommand command) {
        return auditAppService.addRejectReason(command);
    }

    @ApiOperation("驳回原因重排序")
    @PostMapping("/reasonResort")
    public ResultMode reasonResort(@RequestBody @Validated AuditRejectReasonSortCommand command) {
        return auditAppService.reasonResort(command);
    }

    @ApiOperation("删除驳回原因")
    @PostMapping("/reasonDelete")
    public ResultMode reasonDelete(@RequestBody @Validated IdCommand command) {
        return auditAppService.reasonDelete(command);
    }
}
