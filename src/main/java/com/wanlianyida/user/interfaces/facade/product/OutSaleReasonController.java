package com.wanlianyida.user.interfaces.facade.product;

import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.OutSaleReasonAddCommand;
import com.wanlianyida.product.api.model.command.OutSaleReasonChangeOrderCommand;
import com.wanlianyida.product.api.model.dto.OutSaleReasonDTO;
import com.wanlianyida.user.application.service.product.OutSaleReasonAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@Api("下架原因管理接口")
@RestController
@RequestMapping("/outSaleReason")
public class OutSaleReasonController {

    @Resource
    OutSaleReasonAppService outSaleReasonAppService;

    @ApiOperation("查询下架原因列表")
    @PostMapping("/list")
    public ResultMode<List<OutSaleReasonDTO>> list() {
        return outSaleReasonAppService.list();
    }

    @ApiOperation("添加原因")
    @PostMapping("/add")
    public ResultMode<?> add(@RequestBody OutSaleReasonAddCommand command) {
        outSaleReasonAppService.add(command);
        return ResultMode.success();
    }

    @ApiOperation("操作排序")
    @PostMapping("/changeOrder")
    public ResultMode<?> changeOrder(@RequestBody OutSaleReasonChangeOrderCommand command) {
        outSaleReasonAppService.changeOrder(command);
        return ResultMode.success();
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public ResultMode<?> delete(@RequestBody IdCommand command) {
        outSaleReasonAppService.delete(command);
        return ResultMode.success();
    }

}
