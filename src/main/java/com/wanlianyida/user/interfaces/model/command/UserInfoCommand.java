package com.wanlianyida.user.interfaces.model.command;

import com.wanlianyida.user.interfaces.model.dto.UserPermissionDTO;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserInfoCommand {

    /**
     * 主键（partner um_company_member表主键）
     */
    private String id;

    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 登录账号
     */
    private String loginName;
    /**
     * 用户姓名
     */
    private String username;
    /**
     * 用户登录ID
     */
    private String loginId;
    /**
     * 用户基本信息表Id
     */
    private String userBaseId;
    /**
     * 初始密码
     */
    private String password;
    /**
     * 角色集合
     */
    private List<UserPermissionDTO> permissionInfoList;

    /**
     * 部门集合
     */
    private List<String> deptIds;

    /**
     * 岗位id
     */
    private String post;

    /**
     * 用户信息状态(1:有效, 2:无效)
     */
    private String userStatus;

    /**
     * 手机号
     */
    private String telephone;
    /**
     * 备注
     */
    private String remark;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @NotNull(message = "平台类型不能为空")
    private String platformType;

}
