package com.wanlianyida.user.interfaces.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wanlianyida.framework.ctpcommon.model.query.FacadeBaseQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 企业信息表 Query
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
public class APIUmCompanyQuery extends FacadeBaseQuery {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 统一社会信用代码
	 */
	private String socialCreditCode;

	/**
	 * 企业名称(全称)
	 */
	private String companyName;

	/**
	 * 精确查询企业名称(全称)
	 */
	private String fullMatchCompanyName;

	/**
	 * 企业简称
	 */
	private String companyShortName;

	/**
	 * 企业地址(省Code)
	 */
	private String province;

	/**
	 * 企业地址(省)
	 */
	private String provinceName;

	/**
	 * 企业地址(市Code)
	 */
	private String city;

	/**
	 * 企业地址(市)
	 */
	private String cityName;

	/**
	 * 企业地址(区/县Code)
	 */
	private String area;

	/**
	 * 企业地址(区/县)
	 */
	private String areaName;

	/**
	 * 企业地址(镇/街道Code)
	 */
	private String street;

	/**
	 * 企业地址(镇/街道)
	 */
	private String streetName;

	/**
	 * 企业详细地址
	 */
	private String addressDetail;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 执照类型:1000企业营业执照,1100个体工商户执照
	 */
	private String licenseType;

	/**
	 * 会员类型:10项目会员,20一般会员
	 */
	private String memberType;

	/**
	 * 申请来源:1大宗平台,2物流平台
	 */
	private String applySource;

	/**
	 * 实名认证状态:1未认证,2认证通过
	 */
	private String realnameStatus;

	/**
	 * 签章认证状态:1未认证,2认证通过
	 */
	private String signStatus;

	/**
	 * 企业类型:1发货企业,2个体工商,3其他,4物流企业
	 */
	private String companyType;

	/**
	 * 联系人
	 */
	private String contacts;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 主管税务机关
	 */
	private String taxAuthority;

	/**
	 * 业务所属企业id
	 */
	private String bizCompanyId;

	/**
	 * 企业业务配置状态:1已配置,0未配置
	 */
	private String bizSetStatus;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

	/**
	 * 查询企业名称
	 */
	private String searchCompanyName;

	/**
	 * 用户名
	 */
	private String loginName;

	/**
	 * 法人授权状态:1已授权,2未授权
	 */
	private String legalPersonAuthStatus;

	/**
	 * 审核状态:1待审核,2审核不通过,3审核通过
	 */
	private String aptitudeStatus;

	/**
	 * 区分标记:1企业认证审核，2商家入驻审核
	 */
	private String tabFlag;

	/**
	 * 企业id集合
	 */
	private List<String> companyIds;

	/**
	 * 平台类型 10用户端 20平台端
	 */
	public String platformType;

	/**
	 * 登录名模糊查询
	 */
	private String searchLoginName;

	/*
	  * 类型:10客户,20供应商
	 */
	private String customerType;

}
