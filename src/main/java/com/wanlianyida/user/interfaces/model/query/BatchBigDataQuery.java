package com.wanlianyida.user.interfaces.model.query;

import com.wanlianyida.framework.ctpcommon.model.query.FacadeBaseQuery;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @ClassName BatchBigDataQuery
 * @Description
 * <AUTHOR>
 * @Veriosn 1.0
 **/

@Data
public class BatchBigDataQuery extends FacadeBaseQuery {


    @NotNull(message = "queryList 不能为空")
    @Size(min = 1, message = "queryList 至少包含一条记录")
    private List<BigDataQuery> queryList;

}
