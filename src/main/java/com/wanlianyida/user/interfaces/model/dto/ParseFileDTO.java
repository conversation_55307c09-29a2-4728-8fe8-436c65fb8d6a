package com.wanlianyida.user.interfaces.model.dto;

import com.wanlianyida.framework.ctpcommon.model.dto.FacadeBaseDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 导入返回DTO
 */
@Data
public class ParseFileDTO extends FacadeBaseDTO {

    /**
     * 失败信息
     */
    private FailInfo failInfo;

    /**
     * 导入结果信息
     */
    private ImportResultInfo importResultInfo;

    /**
     * 校验通过的数据集合
     */
    private List<Map<String, Object>> successDataList;

    /**
     * 失败信息
     */
    @Data
   public static class FailInfo {
        /**
         * 失败得url
         */
        private String failUrl;

        /**
         * 失败文件名称
         */
        private String failFileName;
   }

    /**
     * 导入结果信息
     */
    @Data
    public static class ImportResultInfo {
        /**
         * 总共数据条数
         */
        private Integer totalCount;

        /**
         * 成功条数
         */
        private Integer successCount;

        /**
         * 失败条数
         */
        private Integer failCount;

        /**
         * 自动去重条数
         */
        private Integer deduplicationCount;
    }

}

