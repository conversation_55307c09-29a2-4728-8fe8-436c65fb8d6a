package com.wanlianyida.user.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.ctpcommon.model.dto.FacadeBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ParticipateUserDTO extends FacadeBaseDTO {

    @ApiModelProperty("询比价单号")
    private String rfqNo;

    @ApiModelProperty("询比价单标题")
    private String rfqTitle;

    @ApiModelProperty("发布方企业名称")
    private String publishCompanyName;

    @ApiModelProperty("报价方式[10-按单价报价,20-按总价报价]")
    private Integer quoteMethod;

    @ApiModelProperty("询价状态")
    private Integer rfqStatus;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("实际结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndTime;

    @ApiModelProperty("预计结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectEndTime;

    @ApiModelProperty("流标时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date abtbidTime;

    @ApiModelProperty("是否有保证金")
    private Integer depositFlag;

    @ApiModelProperty("出价标志[1-已出价,0-未出价]")
    private Integer bidFlag;

    @ApiModelProperty("保证金金额")
    private BigDecimal depositAmt;

    @ApiModelProperty("起拍价-起始价")
    private BigDecimal startPrice;

    @ApiModelProperty("成交价")
    private BigDecimal finalPrice;

    @ApiModelProperty("我的出价")
    private BigDecimal finalBidPrice;

    @ApiModelProperty("当前价")
    private BigDecimal currentPrice;

    @ApiModelProperty("中标标志[1-已中标,0-未中标]")
    private Integer winFlag;

    @ApiModelProperty("报名状态")
    private Integer regStatus;

    @ApiModelProperty("保证金状态")
    private Integer depositStatus;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("计价单位id")
    private Long pricingUnitId;

}
