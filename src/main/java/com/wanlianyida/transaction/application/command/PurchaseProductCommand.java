package com.wanlianyida.transaction.application.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月06日 16:46
 */
@Data
@ApiModel("求购商品表")
public class PurchaseProductCommand {

    @ApiModelProperty("1级品类ID")
    @NotNull(message = "1级品类ID不能为空")
    private Long categoryId1;

    @ApiModelProperty("1级品类名称")
    @NotBlank(message = "1级品类名称不能为空")
    private String categoryName1;

    @ApiModelProperty("2级品类ID")
    @NotNull(message = "2级品类ID不能为空")
    private Long categoryId2;

    @ApiModelProperty("2级品类名称")
    @NotBlank(message = "2级品类名称不能为空")
    private String categoryName2;

    @ApiModelProperty("3级品类ID")
    @NotNull(message = "3级品类ID不能为空")
    private Long categoryId3;

    @ApiModelProperty("3级品类名称")
    @NotBlank(message = "3级品类名称不能为空")
    private String categoryName3;

    @ApiModelProperty("品类图片地址")
    @NotBlank(message = "品类图片地址不能为空")
    private String picUrl;

    @ApiModelProperty("规格")
    @NotBlank(message = "规格不能为空")
    private String specificationName;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("采购数量")
    @NotNull(message = "采购数量不能为空")
    private BigDecimal purchaseCount;

    @ApiModelProperty("计量单位ID")
    @NotNull(message = "计量单位ID不能为空")
    private Integer relMeasurementUnitId;

    @ApiModelProperty("计量单位名称")
    @NotNull(message = "计量单位名称不能为空")
    private String relMeasurementUnit;

    @ApiModelProperty("其他说明")
    private String remark;
}
