package com.wanlianyida.transaction.domain.repository;

import com.wanlianyida.transaction.application.dto.RfqBidPageListDTO;
import com.wanlianyida.transaction.domain.model.condition.RfqBidPageListCondition;
import com.wanlianyida.transaction.domain.model.entity.TxRfqBidEntity;

import java.util.List;

public interface RfqBidRepository {

    // 新建仓储层-通用方法定义
    List<RfqBidPageListDTO> pageList(RfqBidPageListCondition condition);

    List<TxRfqBidEntity> listByCondition();

    TxRfqBidEntity getDetail();

    void insert();

    void updateByCondition();

    /**
     * 查询询价单下的最高报价
     * @param rfqNo
     * @return
     */
    TxRfqBidEntity queryMaxPriceBid(String rfqNo);

    /**
     * 根据询价单编号查询
     * @param rfqNo
     * @return
     */
    List<TxRfqBidEntity> queryByRfqNo(String rfqNo);
}
