package com.wanlianyida.transaction.domain.model.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 物流委托 Condition
 * <AUTHOR>
 */
@Data
public class LogisticsEntrustCondition {
    @ApiModelProperty("状态（10已发布 20已跟进 30已关闭）")
    private Integer documentStatus;

    @ApiModelProperty("起运地")
    private String startAddress;

    @ApiModelProperty("目的地")
    private String endAddress;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("发布时间-起")
    private Date startPublishDate;

    @ApiModelProperty("发布时间-止")
    private Date endPublishDate;

    @ApiModelProperty("企业ID（店铺ID）")
    private String companyId;
}
