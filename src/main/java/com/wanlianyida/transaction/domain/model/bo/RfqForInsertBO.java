package com.wanlianyida.transaction.domain.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.transaction.domain.model.entity.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RfqForInsertBO {
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("单号")
    private String rfqNo;

    @ApiModelProperty("询比价类型[10-销售,20-采购]")
    private Integer rfqType;

    @ApiModelProperty("询比价单模式[10-公开增价]")
    private Integer rfqMode;

    @ApiModelProperty("询比价单范围[10-定向]")
    private Integer rfqScope;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("预计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date expectEndTime;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("业务联系电话")
    private String contactPhone;

    @ApiModelProperty("自动延时标志[1-是,0-否]")
    private Integer autoDelayFlag;

    @ApiModelProperty("延时分钟")
    private Integer delayPeriodMinute;

    @ApiModelProperty("资质审核标志[1-是,0-否]")
    private Integer qualifyCheckFlag;

    @ApiModelProperty("保证金标志[1-是,0-否]")
    private Integer depositFlag;

    @ApiModelProperty("保证金金额")
    private BigDecimal depositAmt;

    @ApiModelProperty("报名截止标记")
    private Integer regDeadlineFlag;

    @ApiModelProperty("报名截止小时")
    private Integer regDeadlineHour;

    @ApiModelProperty("报价方式[10-按单价报价,20-按总价报价]")
    private Integer quoteMethod;

    @ApiModelProperty("起拍价")
    private BigDecimal startPrice;

    @ApiModelProperty("加价梯度")
    private BigDecimal priceStep;

    @ApiModelProperty("出价次数限制类型[10-无限制]")
    private Integer bidCountLimitType;

    @ApiModelProperty("最高价出价方式[10-公开,20-不公开]")
    private Integer maxPriceBidMethod;

    @ApiModelProperty("中标标准[10-一人中标]")
    private Integer winStandard;

    @ApiModelProperty("最低参与企业数")
    private Integer minJoinCount;

    @ApiModelProperty("参与标准[10-以报名企业数为准,20-以出价人数为准]")
    private Integer joinStandard;

    @ApiModelProperty("商品信息")
    private List<TxRfqProductEntity> productList;

    @ApiModelProperty("客户信息")
    private List<TxRfqCustomerEntity> customerList;

    @ApiModelProperty("收款方式信息")
    private List<TxRfqReceiveMethodEntity> receiverMethodList;

    @ApiModelProperty("商品sku属性信息")
    private List<TxRfqProductSkuAttrEntity> productSkuAttrList;
}
