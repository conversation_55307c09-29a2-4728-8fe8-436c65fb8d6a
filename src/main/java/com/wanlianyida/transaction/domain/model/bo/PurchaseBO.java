package com.wanlianyida.transaction.domain.model.bo;

import com.wanlianyida.transaction.domain.model.entity.PurchaseCompanyEntity;
import com.wanlianyida.transaction.domain.model.entity.PurchaseInfoEntity;
import com.wanlianyida.transaction.domain.model.entity.PurchaseProductEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月07日 16:16
 */
@Data
public class PurchaseBO {

    private PurchaseInfoEntity purchaseInfo;

    private List<PurchaseProductEntity> productList;

    /**
     * 定向求购客户列表
     */
    private List<PurchaseCompanyEntity> purchaseCompanyList;

    /**
     * 求购单号
     */
    private String purchaseNo;
}
