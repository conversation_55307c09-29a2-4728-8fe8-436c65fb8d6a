package com.wanlianyida.transaction.infrastructure.enums;

import lombok.Getter;

/**
 * 询比价单审核状态枚举 10待审核 20审核不通过 30审核通过
 * <AUTHOR>
 */
@Getter
public enum RfqAuditStatusEnum {
    NOT_AUDIT(10, "待审核"),
    AUDIT_PASS(30, "审核通过"),
    AUDIT_NOT_PASS(20, "审核不通过");

    private final Integer code;
    private final String desc;
    RfqAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static RfqAuditStatusEnum getByCode(Integer code) {
        for (RfqAuditStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return RfqAuditStatusEnum.AUDIT_PASS;
    }
}
