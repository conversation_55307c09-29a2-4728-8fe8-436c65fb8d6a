package com.wanlianyida.transaction.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 求购定向客户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-17 13:53:57
 */
@Getter
@Setter
@TableName("tx_purchase_company")
public class PurchaseCompanyPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 求购单号
     */
    @TableField("purchase_no")
    private String purchaseNo;

    /**
     * 定向求购客户公司id
     */
    @TableField("purchase_company_id")
    private String purchaseCompanyId;

    /**
     * 定向求购客户公司名称
     */
    @TableField("purchase_company_name")
    private String purchaseCompanyName;

    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;
}
