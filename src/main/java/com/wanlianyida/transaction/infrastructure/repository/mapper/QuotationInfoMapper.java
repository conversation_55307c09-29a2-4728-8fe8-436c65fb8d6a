package com.wanlianyida.transaction.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.transaction.application.dto.QuotationListDTO;
import com.wanlianyida.transaction.domain.model.bo.StatusStatisticsBO;
import com.wanlianyida.transaction.domain.model.condition.QuotationCondition;
import com.wanlianyida.transaction.domain.model.entity.QuotationProductEntity;
import com.wanlianyida.transaction.infrastructure.repository.po.QuotationInfoPO;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月06日 17:08
 */
public interface QuotationInfoMapper extends BaseMapper<QuotationInfoPO> {

    @MapKey("status")
    Map<Integer, StatusStatisticsBO> statistics(String companyId);

    List<QuotationListDTO> queryConditionList(QuotationCondition condition);

    int batchUpdate(List<QuotationProductEntity> list);
}
