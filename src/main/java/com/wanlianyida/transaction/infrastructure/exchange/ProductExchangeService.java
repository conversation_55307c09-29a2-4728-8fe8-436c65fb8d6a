package com.wanlianyida.transaction.infrastructure.exchange;

import cn.hutool.core.collection.CollUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.inter.ProductInter;
import com.wanlianyida.product.api.model.dto.ProductSkuDetailDTO;
import com.wanlianyida.product.api.model.dto.ProductSpuDetailDTO;
import com.wanlianyida.product.api.model.query.ProductSkuQuery;
import com.wanlianyida.product.api.model.query.ProductSpuQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Service
@Slf4j
public class ProductExchangeService {

    @Resource
    private ProductInter productInter;

    public List<ProductSkuDetailDTO> queryProductSkuDetail(List<String> skuCodeList) {
        ProductSkuQuery query = new ProductSkuQuery();
        query.setSkuCodeList(skuCodeList);
        ResultMode<List<ProductSkuDetailDTO>> skuDetail = productInter.queryProductSkuDetail(query);
        if (!skuDetail.isSucceed()){
            log.error("查询商品SKU详情失败:{}", skuDetail.getMessage());
            return null;
        }
        if (skuDetail.getModel() == null || CollUtil.isEmpty(skuDetail.getModel())){
            log.error("查询商品SKU详情为空");
            return null;
        }
        return skuDetail.getModel();
    }

    public List<ProductSpuDetailDTO> queryProductSpuDetail(List<String> spuCodeList) {
        ProductSpuQuery query = new ProductSpuQuery();
        query.setSpuCodeList(spuCodeList);
        ResultMode<List<ProductSpuDetailDTO>> skuDetail = productInter.queryProductSpuDetail(query);
        if (!skuDetail.isSucceed()){
            log.error("查询商品SPU详情失败:{}", skuDetail.getMessage());
            return null;
        }
        if (skuDetail.getModel() == null || CollUtil.isEmpty(skuDetail.getModel())){
            log.error("查询商品SpU详情为空");
            return null;
        }
        return skuDetail.getModel();
    }
}
