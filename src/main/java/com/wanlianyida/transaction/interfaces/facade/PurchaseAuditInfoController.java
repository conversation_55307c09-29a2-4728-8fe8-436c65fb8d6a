package com.wanlianyida.transaction.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.transaction.application.command.AuditPurchaseCommand;
import com.wanlianyida.transaction.application.dto.PurchaseAuditBasicInfoDTO;
import com.wanlianyida.transaction.application.dto.PurchaseAuditInfoPageDTO;
import com.wanlianyida.transaction.application.query.PurchaseAuditBasicInfoQuery;
import com.wanlianyida.transaction.application.query.PurchaseAuditInfoPageQuery;
import com.wanlianyida.transaction.application.service.PurchaseAuditInfoAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 求购审核
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchase/audit")
@Validated
public class PurchaseAuditInfoController {

    @Resource
    private PurchaseAuditInfoAppService auditInfoAppService;

    /**
     * 分页查询求购审核信息
     * @param query 分页参数
     */
    @PostMapping(value = {"/pageQuery"})
    public ResultMode<List<PurchaseAuditInfoPageDTO>> pageQuery(@RequestBody PagingInfo<PurchaseAuditInfoPageQuery> query) {
        return auditInfoAppService.pageQuery(query);
    }

    /**
     * 审核操作
     */
    @PostMapping(value = {"/audit"})
    public ResultMode<?> audit(@RequestBody @Valid AuditPurchaseCommand auditPurchaseCommand) {
        return auditInfoAppService.audit(auditPurchaseCommand);
    }

    /**
     * 审核单基本信息
     */
    @PostMapping(value = {"/basicInfo"})
    public ResultMode<PurchaseAuditBasicInfoDTO> basicInfo(@RequestBody @Valid PurchaseAuditBasicInfoQuery query) {
        return auditInfoAppService.basicInfo(query);
    }

}
