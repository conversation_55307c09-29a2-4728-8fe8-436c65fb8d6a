package com.wanlianyida.transaction.interfaces.listener;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wanlianyida.transaction.application.assembler.RfqAssembler;
import com.wanlianyida.transaction.application.command.RfqAuditCommand;
import com.wanlianyida.transaction.domain.model.bo.RfqOrderBO;
import com.wanlianyida.transaction.domain.model.bo.TxRfqOrderAuditBO;
import com.wanlianyida.transaction.domain.service.RfqDomainService;
import com.wanlianyida.transaction.infrastructure.constant.RocketMqConstant;
import com.wanlianyida.transaction.infrastructure.enums.RfqAuditStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 询比价自动审核消费者
 */
@Service
@Slf4j
@RocketMQMessageListener(
        namespace = "${rocketmq.push-consumer.name-space:}",
        consumerGroup = RocketMqConstant.RFQ_CONSUMER_GROUP,
        topic = RocketMqConstant.CTP_AUTO_AUDIT_TOPIC,
        tag = RocketMqConstant.RFQ_TAG)
public class RfqAudoAuditConsumer implements RocketMQListener {

    @Resource
    private RfqDomainService rfqDomainService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        ByteBuffer body = messageView.getBody();
        String messageBody = StandardCharsets.UTF_8.decode(body).toString();
        log.info("询比价自动审核接收到消息：{}", messageBody);
        ObjectMapper objectMapper = new ObjectMapper();
        String businessKey = "";
        try {
            JsonNode rootNode = objectMapper.readTree(messageBody);
            businessKey = rootNode.path("businessKey").asText(null);
            if (businessKey != null) {
                handleMessage(businessKey);
            }
            log.info("获取到的businessKey: {}", businessKey);
        } catch (Exception e) {
            log.error("询比价自动处理消息rocketmq失败：消息id：{}，业务id：{}，失败原因：{}", messageView.getMessageId(), businessKey, e);
            return ConsumeResult.FAILURE;
        }
        return ConsumeResult.SUCCESS;
    }

    private void handleMessage(String id) {
        RfqOrderBO rfqOrderBO = rfqDomainService.getRfqOrderBO(id);
        if (rfqOrderBO == null){
            return;
        }
        RfqAuditCommand command = new RfqAuditCommand();
        command.setRfqNo(id);
        command.setAuditStatus(RfqAuditStatusEnum.AUDIT_PASS.getCode());
        TxRfqOrderAuditBO auditBO = RfqAssembler.createAudit(command, true);
        rfqDomainService.auditRfq(auditBO);
    }
}
