package com.wanlianyida.transaction.interfaces.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.support.api.model.dto.BdDelayTaskDTO;
import com.wanlianyida.transaction.domain.service.RfqDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.List;

import javax.annotation.Resource;

/**
 * 询比价单监听
 */
@Service
@Slf4j
//@RocketMQMessageListener(
//        namespace = "${rocketmq.push-consumer.name-space:}",
//        consumerGroup = RocketMqConstant.RFQ_OPEN_DELAY_GROUP,
//        topic = RocketMqConstant.RFQ_OPEN_DELAY_TOPIC,
//        tag = RocketMqConstant.COMMON_TAG
//)
public class RfqDelayRocketMQConsumer implements RocketMQListener {

    @Resource
    private RfqDomainService rfqDomainService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("rocketMq消费#询比价单延时任务,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
        ByteBuffer body = messageView.getBody();
        if(messageView.getBody() == null){
            log.info("rocketMq消费#询比价单延时任务#没有获取到消息体,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
            return ConsumeResult.SUCCESS;
        }
        String messageBody = StandardCharsets.UTF_8.decode(body).toString();
        List<BdDelayTaskDTO> resultDto = JSONUtil.toList(messageBody, BdDelayTaskDTO.class);
        if (ObjectUtil.isNull(resultDto)) {
            log.info("rocketMq消费#询比价单延时任务#消费体转换失败,消息id={}，topic={}",messageView.getMessageId(),messageView.getTopic());
            return ConsumeResult.SUCCESS;
        } else {
            log.info("rocketMq消费#询比价单延时任务#消费体={},消息id={}，topic={}",resultDto,messageView.getMessageId(),messageView.getTopic());
        }

        try {
            rfqDomainService.delayHandle(resultDto);
            log.info("rocketMq消费#询比价单延时任务消费成功，消息体={}",resultDto);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("rocketMq消费#询比价单延时任务消费失败,消息体={}, error={}",resultDto, e);
            return ConsumeResult.FAILURE;
        }
    }

}
