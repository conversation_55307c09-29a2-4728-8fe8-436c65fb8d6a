package com.wanlianyida.bidding.domain.audit.model.bo;

import com.wanlianyida.bidding.domain.clarify.model.entity.ClarifyEntity;
import com.wanlianyida.bidding.domain.project.model.entity.ProjectEntity;
import com.wanlianyida.bidding.domain.project.model.entity.TenderPackageEntity;
import lombok.Data;

import java.util.List;

/**
 * 审批BO
 */
@Data
public class AuditBO {

    /**
     * 项目实体
     */
    private ProjectEntity projectEntity;

    /**
     * 标包实体
     */
    private TenderPackageEntity tenderPackageEntity;

    /**
     * 澄清实体
     */
    private ClarifyEntity clarify;

    /**
     * 项目下的所有标包
     */
    private List<TenderPackageEntity> tenderPackageEntityList;
}
