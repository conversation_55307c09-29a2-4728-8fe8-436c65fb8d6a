package com.wanlianyida.bidding.domain.audit.model.bo;

import com.wanlianyida.bidding.domain.audit.model.entity.TaskEntity;
import lombok.Data;

/**
 * 标包审批列表
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
public class PackageAuditListBO extends TaskEntity {

    /**
     * id
     */
    private Long packageId;

    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 采购方式 10公开竞谈 20邀请竞谈
     */
    private String purchaseMethod;

    /**
     * 招标负责人id
     */
    private String tenderLeaderId;

    /**
     * 招标负责人姓名
     */
    private String tenderLeaderName;

    /**
     * 项目id
     */
    private Long relProjectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目经理用户id
     */
    private String projectManagerId;

    /**
     * 项目经理用户名
     */
    private String projectManagerName;

    /**
     * 标包编号
     */
    private String tenderNo;

}
