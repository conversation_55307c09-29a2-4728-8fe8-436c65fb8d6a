package com.wanlianyida.bidding.domain.issue.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.bidding.domain.assembler.TenderRegisterAssembler;
import com.wanlianyida.bidding.domain.issue.model.entity.TenderPackageSupplierResponseEntity;
import com.wanlianyida.bidding.domain.issue.repository.TenderPackageInviteRepository;
import com.wanlianyida.bidding.domain.issue.repository.TenderPackageSupplierResponseRepository;
import com.wanlianyida.bidding.domain.project.model.condition.TenderPackageInviteCondition;
import com.wanlianyida.bidding.domain.project.model.entity.TenderPackageInviteEntity;
import com.wanlianyida.bidding.infrastructure.enums.PackagePurchaseResponseStatusEnum;
import com.wanlianyida.bidding.infrastructure.enums.TenderPackageStatus;
import com.wanlianyida.bidding.infrastructure.enums.TenderSubStageEnum;
import com.wanlianyida.bidding.infrastructure.event.EventPublisher;
import com.wanlianyida.framework.ctpcommon.entity.BaseException;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

@Service
public class TenderPackageInviteDomainService {
    @Resource
    private TenderPackageInviteRepository tenderPackageInviteRepository;

    @Resource
    private EventPublisher eventPublisher;

    @Resource
    private TenderPackageSupplierResponseRepository tenderPackageSupplierResponseRepo;

    public void batchCreate(List<TenderPackageInviteEntity> inviteEntityList) {
        tenderPackageInviteRepository.batchCreate(inviteEntityList);
    }


    public PageInfo<TenderPackageInviteEntity> queryPage(PagingInfo<TenderPackageInviteCondition> conditionPage) {
        TenderPackageInviteCondition condition = conditionPage.getFilterModel();
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        // 验证公司id
        if (ObjectUtil.isEmpty(tokenInfo.getCompanyId())){
            throw new RuntimeException("当前账号存在异常，未获取到公司id");
        }
        List<Integer> inviteStatusList = condition.getInviteStatusList();
        if (CollUtil.isEmpty(inviteStatusList)){
            throw new BaseException("邀请状态不能为空");
        }
        // 包含预邀请
        if (inviteStatusList.contains(TenderPackageStatus.STATUS_10.getStatus())){
            condition.setTenderSubStage(TenderSubStageEnum.REPLY.getCode());
        }

        // 获取公司id
        String companyId = tokenInfo.getCompanyId();
        condition.setCompanyId(companyId);
        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        List<TenderPackageInviteEntity> list = tenderPackageInviteRepository.queryPage(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        PageInfo<TenderPackageInviteEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    public TenderPackageInviteEntity queryDetail(TenderPackageInviteCondition condition) {
        if (ObjectUtil.isEmpty(condition.getId())) {
            return null;
        }

        // 查询邀请信息
        TenderPackageInviteEntity entity = tenderPackageInviteRepository.queryDetail(condition);
        if (ObjectUtil.isEmpty(entity)) {
            throw new RuntimeException("未查询到邀请信息");
        }

        return entity;
    }

    public TenderPackageInviteEntity queryDetailByRelTenderPackageId(TenderPackageInviteCondition condition) {
        if (ObjectUtil.isEmpty(condition.getRelTenderPackageId())) {
            return null;
        }
        condition.setSupplierId(JwtUtil.getTokenInfo().getCompanyId());
        // 查询邀请信息
        TenderPackageInviteEntity entity = tenderPackageInviteRepository.queryDetailByRelTenderPackageId(condition);
        if (ObjectUtil.isEmpty(entity)) {
            throw new RuntimeException("未查询到邀请信息");
        }

        return entity;
    }

    public Boolean invitationLetterProcess(TenderPackageInviteEntity entity) {
        if (ObjectUtil.isEmpty(entity.getId())) {
            throw new RuntimeException("id不能为空");
        }

        if (ObjectUtil.isEmpty(entity.getInviteStatus())){
            throw new RuntimeException("处理状态不能为空");
        }
        TenderPackageInviteCondition condition = BeanUtil.copyProperties(entity,
                TenderPackageInviteCondition.class);

        // 查询邀请信息
        condition.setSupplierId(JwtUtil.getTokenInfo().getCompanyId());
        TenderPackageInviteEntity inviteEntity = tenderPackageInviteRepository.queryDetailByRelTenderPackageId(condition);
        if (ObjectUtil.isEmpty(inviteEntity)) {
            throw new RuntimeException("未查询到邀请信息");
        }

        // 校验邀请函处理状态
        Integer inviteStatus = inviteEntity.getInviteStatus();
        if (inviteStatus.equals(TenderPackageStatus.STATUS_20.getStatus()) ||
                inviteStatus.equals(TenderPackageStatus.STATUS_30.getStatus())) {
            throw new RuntimeException("已处理邀请函");
        }

        if (inviteStatus.equals(TenderPackageStatus.STATUS_40.getStatus())) {
            throw new RuntimeException("已失效");
        }

        // 获取标书售卖截止时间
        Date saleEndTime = inviteEntity.getSaleEndTime();
        if (DateUtil.compare(new Date(), saleEndTime) > 0) {
            if (inviteStatus.equals(TenderPackageStatus.STATUS_20.getStatus())) {
                throw new BaseException("已过标书售卖截止时间！此提示信息仅供查看，不能触发接受邀请动作");
            } else if (inviteStatus.equals(TenderPackageStatus.STATUS_30.getStatus())) {
                throw new BaseException("已过标书售卖截止时间！此提示信息仅供查看，不能触发拒绝邀请动作");
            }
        }
        //修改供应商应答状态
        Integer responseStatus = entity.getInviteStatus().equals(TenderPackageStatus.STATUS_20.getStatus()) ? PackagePurchaseResponseStatusEnum.RESPONSE_CODE_210.getCode() : PackagePurchaseResponseStatusEnum.RESPONSE_CODE_220.getCode();
        TenderPackageSupplierResponseEntity responseEntity = TenderRegisterAssembler.buildUpdateSupplierResponse(entity.getRelTenderPackageId(), responseStatus, true);
        tenderPackageSupplierResponseRepo.update(responseEntity);
        // 更新邀请函处理状态
        return tenderPackageInviteRepository.updateInvite(entity);
    }

    public PageInfo<TenderPackageInviteEntity> getSuppliersByTenderPackageId(PagingInfo<TenderPackageInviteCondition> conditionPage) {
        Page<Object> page = PageHelper.startPage(conditionPage.currentPage, conditionPage.pageLength, true);
        TenderPackageInviteCondition condition = conditionPage.getFilterModel();
        List<TenderPackageInviteEntity> list = tenderPackageInviteRepository.getSuppliersByTenderPackageId(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        PageInfo<TenderPackageInviteEntity> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }
}
