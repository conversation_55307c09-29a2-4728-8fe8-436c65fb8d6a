package com.wanlianyida.bidding.domain.project.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 标包-邀请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class TenderPackageInviteEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private Long id;


    /**
     * 项目id
     */
    private Long relProjectId;


    /**
     * 项目编号
     */
    private String projectNo;


    /**
     * 项目名称
     */
    private String projectName;


    /**
     * 标包id
     */
    private Long relTenderPackageId;


    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 供应商id
     */
    private String supplierId;


    /**
     * 供应商名称
     */
    private String supplierName;


    /**
     * 社会信用代码
     */
    private String socialCreditCode;


    /**
     * 采购方式 10公开竞谈 20邀请竞谈
     */
    private String purchaseMethod;


    /**
     * 状态 10预邀请 20已接受邀请 30已拒绝邀请 40已失效
     */
    private Integer inviteStatus;


    /**
     * 邀请人id
     */
    private String inviterId;


    /**
     * 邀请人姓名
     */
    private String inviterName;


    /**
     * 邀请人账号
     */
    private String inviterAccount;


    /**
     * 接受邀请时间
     */
    private Date acceptInvitationTime;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 标书售卖截止时间
     */
    private Date saleEndTime;

    /**
     * 投标截止时间
     */
    private Date bidEndTime;

    /**
     * 邀请时间
     */
    private Date inviteTime;

    /**
     * 是否已经购买标书 0否 1是
     */
    private Integer purchasedDocFlag;

    private List<AttachmentEntity> attachmentEntityList;

    /**
     * 供应商账号
     */
    private String supplierAccount;

    /**
     * 联系人姓名
     */
    private String linkmanName;

    /**
     * 联系人手机号
     */
    private String linkmanMobile;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;
}
