package com.wanlianyida.bidding.domain.bidding.model.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 招投标信息变更日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BidChangeLogBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long packageId;
    private String supplierName;
    private String supplierAccount;
    private BigDecimal techScore;
    private BigDecimal commerceScore;
    private BigDecimal priceScore;
    private BigDecimal extraScore;
    private BigDecimal totalScore;
    private BigDecimal taxRate;
    private BigDecimal taxAmount;
    private BigDecimal withoutTaxTotalAmount;
    private BigDecimal withTaxTotalAmount;
    private BigDecimal withoutTaxUnitPrice;
    private BigDecimal withTaxUnitPrice;
    private String creatorName;
    private String creatorAccount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String changeBeforeValue;
    private String changeAfterValue;
    private Integer bidRound;



}
