package com.wanlianyida.bidding.domain.bidding.model.entity;

import lombok.Data;

import java.util.Date;

@Data
public class BtBidChangeLogEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标包ID
     */
    private Long packageId;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 操作类型[10-招标时间变更,20-候选人排名修改]
     */
    private Integer optType;

    /**
     * 变更类型[10-标书售卖开始时间,20-标书售卖截止时间,30-投标截止时间,40-开标时间]
     */
    private Integer changeType;

    /**
     * 变更前的值
     */
    private String changeBeforeValue;

    /**
     * 变更后的值
     */
    private String changeAfterValue;

    /**
     * 轮次
     */
    private Integer bidRound;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建人账号
     */
    private String creatorAccount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人ID
     */
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 删除标志[0-正常,1-删除]
     */
    private Integer delFlag;

}