package com.wanlianyida.bidding.domain.win.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 结果通知书
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-12
 */
@Getter
@Setter
public class BtBiddingResultNoticeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 标包id
     */
    private Long packageId;

    /**
     * 通知书类型[10中标通知书 20结果通知书]
     */
    private String noticeType;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 中标金额(元)
     */
    private BigDecimal winBidAmount;

    /**
     * 是否含税 0否 1是
     */
    private Integer taxFlag;

    /**
     * 结果通知书发送状态[10-未发送，20-已发送]
     */
    private Integer noticeSendStatus;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 结果通知书发送时间
     */
    private Date noticeSendTime;

    /**
     * 通知书url
     */
    private String noticeUrl;

    /**
     * 供应商账号
     */
    private String supplierAccount;
}
