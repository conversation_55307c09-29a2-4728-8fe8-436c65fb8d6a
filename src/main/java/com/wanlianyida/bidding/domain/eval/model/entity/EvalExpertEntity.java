package com.wanlianyida.bidding.domain.eval.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 评标专家表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 20:02:57
 */
@Data
public class EvalExpertEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 标包id
     */
    private Long packageId;

    /**
     * 评标id
     */
    private Long evalId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职务
     */
    private String position;

    /**
     * 角色[10-专家,20-组长]
     */
    private String role;

    /**
     * 已生成账号标志[0-否,1-是]
     */
    private Integer accountCreateFlag;

    /**
     * 专家用户id
     */
    private String expertUserId;

    /**
     * 专家登录账号
     */
    private String expertLoginAccount;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 创建用户名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer delFlag;
}
