package com.wanlianyida.bidding.domain.eval.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EvalTaskFindOneCondition {
    /**
     * id
     */
    private Long id;

    /**
     * 评标id
     */
    private Long evalId;

    /**
     * 任务编号
     */
    private String taskNo;

    /**
     * 最新记录标志[0-否,1-是]
     */
    private Integer latestFlag;

}
