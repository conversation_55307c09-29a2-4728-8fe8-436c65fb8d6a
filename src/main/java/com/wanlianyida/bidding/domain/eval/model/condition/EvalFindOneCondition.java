package com.wanlianyida.bidding.domain.eval.model.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EvalFindOneCondition {

    /**
     * id
     */
    private Long id;

    /**
     * 标包id
     */
    private Long packageId;


    /**
     * 标轮次
     */
    private Integer bidRound;

    public EvalFindOneCondition(Long id) {
        this.id = id;
    }
}
