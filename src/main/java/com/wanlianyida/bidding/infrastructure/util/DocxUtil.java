package com.wanlianyida.bidding.infrastructure.util;

import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.xwpf.usermodel.UnderlinePatterns;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DocxUtil {

    /**
     * 用一个docx文档作为模板，然后替换其中的内容，再写入目标文档中。
     * @throws Exception
     */
    public static void templateWrite(InputStream fis, String outFilePath, Map<String, Object> params) throws Exception {
        // 模板文档
        // InputStream fis = new FileInputStream(filePath);
        // 加载模版文档
        XWPFDocument doc = new XWPFDocument(fis);
        // 替换段落里面的变量
        replaceInPara(doc, params);
        // 目标文档
        OutputStream fos = new FileOutputStream(outFilePath);
        // 写入目标文档
        doc.write(fos);
        // 关闭输出流
        close(fos);
        // 关闭输入流
        close(fis);
    }

    /**
     * 替换段落里面的变量
     * @param doc 要替换的文档
     * @param params 参数
     */
    private static void replaceInPara(XWPFDocument doc, Map<String, Object> params) {
        Iterator<XWPFParagraph> iterator = doc.getParagraphsIterator();
        XWPFParagraph para;
        while (iterator.hasNext()) {
            para = iterator.next();
            replaceInPara(para, params);
        }
    }

    /**
     * 替换段落里面的变量
     * @param para 要替换的段落
     * @param params 参数
     */
    private static void replaceInPara(XWPFParagraph para, Map<String, Object> params) {
        List<XWPFRun> runs;
        Matcher matcher;
        String runText = "";
        int fontSize = 0;
        UnderlinePatterns underlinePatterns = null;
        if (matcher(para.getParagraphText()).find()) {
            runs = para.getRuns();
            if (runs.size() > 0) {
                int j = runs.size();
                for (int i = 0; i < j; i++) {
                    XWPFRun run = runs.get(0);
                    if (fontSize == 0) {
                        fontSize = run.getFontSize();
                    }
                    if(underlinePatterns==null){
                        underlinePatterns=run.getUnderline();
                    }
                    String i1 = run.toString();
                    runText += i1;
                    para.removeRun(0);
                }
            }
            matcher = matcher(runText);
            if (matcher.find()) {
                while ((matcher = matcher(runText)).find()) {
                    runText = matcher.replaceFirst(String.valueOf(params.get(matcher.group(1))));
                }
                /**
                 * 直接调用XWPFRun的setText()方法设置文本时，在底层会重新创建一个XWPFRun，把文本附加在当前文本后面，
                 * 所以我们不能直接设值，需要先删除当前run，然后再自己手动插入一个新的run。
                 * 注意：addTab()和addCarriageReturn() 对setText()的使用先后顺序有关：比如先执行addTab，再写Text这是对当前这个Text的Table，反之是对下一个run的Text的Tab效果。
                 */
                XWPFRun run = para.createRun();
                run.setText(runText,0);
                run.setFontSize(fontSize);
                run.setFontFamily("simsun");
                run.setUnderline(underlinePatterns);
            }
        }
    }

    /**
     * 正则匹配字符串
     * @param str
     * @return
     */
    private static Matcher matcher(String str) {
        Pattern pattern = Pattern.compile("\\$\\{(.+?)\\}", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(str);
        return matcher;
    }

    /**
     * 关闭输入流
     * @param is
     */
    private static void close(InputStream is) {
        if (is != null) {
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 关闭输出流
     * @param os
     */
    private static void close(OutputStream os) {
        if (os != null) {
            try {
                os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void mkdirs(String path) {
        File file = new File(FilenameUtils.normalize(path));
        // 如果文件的目录不存在
        if (!file.exists()) {
            // 创建目录
            file.mkdirs();
        }
    }

    public static MultipartFile convertFileToMultipartFile(File file, String fileName) {
        CommonsMultipartFile multipartFile = null;
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
            byte[] bytes = Files.readAllBytes(Paths.get(file.getPath()));
            // 创建DiskFileItemFactory
            DiskFileItemFactory factory = new DiskFileItemFactory();
            FileItem fileItem = factory.createItem("file", MediaType.APPLICATION_PDF_VALUE, false, fileName);
            // 写入数据到FileItem
            OutputStream os = fileItem.getOutputStream();
            os.write(bytes);
            // 将生成的中标通知书包装为CommonsMultipartFile，用于后续上传操作
            multipartFile = new CommonsMultipartFile(fileItem);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return multipartFile;
    }

    public static void wordToPdf(String source, String target) {
        InputStream sourceStream = null;
        OutputStream targetStream = null;
        XWPFDocument doc = null;
        try {
            sourceStream = new FileInputStream(source);
            targetStream = new FileOutputStream(target);
            doc = new XWPFDocument(sourceStream);
            PdfConverter.getInstance().convert(doc, targetStream, null);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            if (sourceStream != null) {
                close(sourceStream);
            }
            if (targetStream != null) {
                close(targetStream);
            }
        }
    }
}