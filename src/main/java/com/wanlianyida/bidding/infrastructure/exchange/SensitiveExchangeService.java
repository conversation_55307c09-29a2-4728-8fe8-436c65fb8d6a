package com.wanlianyida.bidding.infrastructure.exchange;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.SensitiveWordsInter;
import com.wanlianyida.support.api.model.command.CheckSensitiveWordsCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SensitiveExchangeService {

    @Resource
    SensitiveWordsInter sensitiveWordsInter;

    public String checkWords(String content) {
        CheckSensitiveWordsCommand command = new CheckSensitiveWordsCommand();
        command.setContent(content);
        try {
            ResultMode<String> resultMode = sensitiveWordsInter.checkSensitiveWords(command);
            if (!resultMode.isSucceed()) {
                log.debug("检查到敏感词参数#{}返回值#{}", command, resultMode);
                if (resultMode.getModel() != null) {
                    return resultMode.getModel();
                }
            }
            return "";
        } catch (Exception e) {
            log.error("checkWordsError", e);
        }
        // 服务异常返回通过
        return "";
    }

}
