package com.wanlianyida.bidding.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 澄清表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10 15:35:00
 */
@Getter
@Setter
@TableName("bt_clarify")
public class ClarifyPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId("id")
    private Long id;

    /**
     * 标包id
     */
    @TableField("package_id")
    private Long packageId;

    /**
     * 澄清类型[10-发标澄清,20-评标澄清]
     */
    @TableField("clarify_type")
    private String clarifyType;

    /**
     * 澄清标题
     */
    @TableField("clarify_title")
    private String clarifyTitle;

    /**
     * 澄清内容
     */
    @TableField("clarify_content")
    private String clarifyContent;

    /**
     * 审核状态[10-待发布,20-审批中,30-已发布,40-审批驳回]
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 澄清发布时间
     */
    @TableField("clarify_release_time")
    private Date clarifyReleaseTime;

    /**
     * 澄清事项[10-发标澄清,20-技术澄清,30-商务澄清,40-价格澄清]
     */
    @TableField("clarify_matter")
    private String clarifyMatter;

    /**
     * 澄清截止时间
     */
    @TableField("clarify_end_time")
    private Date clarifyEndTime;

    /**
     * 发布人id
     */
    @TableField("publish_user_id")
    private String publishUserId;

    /**
     * 发布人姓名
     */
    @TableField("publish_user_name")
    private String publishUserName;

    /**
     * 供应商应答数量
     */
    @TableField("supplier_response_qty")
    private Integer supplierResponseQty;

    /**
     * 标轮次
     */
    @TableField("bid_round")
    private Integer bidRound;

    /**
     * 投标开标时间
     */
    @TableField("bid_open_time")
    private Date bidOpenTime;

    /**
     * 投标截止时间
     */
    @TableField("bid_end_time")
    private Date bidEndTime;

    /**
     * 开标地址
     */
    @TableField("bid_open_place")
    private String bidOpenPlace;

    /**
     * 删除标志[0-正常,1-删除]
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建用户id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;
}
