package com.wanlianyida.bidding.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.bidding.domain.project.model.entity.TenderPackageProductEntity;
import com.wanlianyida.bidding.infrastructure.repository.po.TenderPackageProductPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月12日 13:28
 */
public interface TenderPackageProductMapper extends BaseMapper<TenderPackageProductPO> {

    void batchInsert(@Param("list") List<TenderPackageProductPO> poList);

    void batchUpdateByProjectProductId(List<TenderPackageProductEntity> list);
}
