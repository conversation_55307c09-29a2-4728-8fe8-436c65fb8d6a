package com.wanlianyida.bidding.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BasePO implements Serializable {

    @TableField("creator_id")
    private String creatorId;

    @TableField("created_date")
    private Date createdDate;

    @TableField("updater_id")
    private String updaterId;

    @TableField("updated_date")
    private Date updatedDate;

    @TableField("version_code")
    private Integer versionCode;
}
