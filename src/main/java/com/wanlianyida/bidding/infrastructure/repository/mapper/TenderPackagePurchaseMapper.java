package com.wanlianyida.bidding.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.bidding.domain.issue.model.bo.TenderPackagePurchaseListBO;
import com.wanlianyida.bidding.domain.issue.model.condition.TenderPackagePurchaseCondition;
import com.wanlianyida.bidding.infrastructure.repository.po.TenderPackagePurchasePO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月17日 14:30
 */
public interface TenderPackagePurchaseMapper extends BaseMapper<TenderPackagePurchasePO> {
    List<TenderPackagePurchaseListBO> queryCondition(TenderPackagePurchaseCondition condition);

    List<TenderPackagePurchaseListBO> queryPurchaseList(TenderPackagePurchaseCondition condition);
}
