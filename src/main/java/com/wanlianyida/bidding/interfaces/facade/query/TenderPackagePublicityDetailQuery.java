package com.wanlianyida.bidding.interfaces.facade.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class TenderPackagePublicityDetailQuery {
    /**
     * 标包id
     */
    @NotNull(message = "标包id为空")
    private Long relTenderPackageId;
    /**
     * 公示类型 10招标公告,20中标候选人公示,30中标结果公示
     */
    @NotBlank(message = "公示类型为空")
    private String publicityType;

    /**
     * 业务ID
     */
    private String id;
}
