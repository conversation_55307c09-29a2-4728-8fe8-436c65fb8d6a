package com.wanlianyida.bidding.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/7 16:34
 */
@Data
public class ClarifyFyDTO {

    private Long id;
    private Long packageId;
    private Integer clarifyType;
    private String clarifyTitle;
    private String clarifyMatter;
    private Integer auditStatus;
    private String clarifyContent;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clarifyReleaseTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clarifyEndTime;
    private String supplierNames;
    private Integer bidRound;
}
