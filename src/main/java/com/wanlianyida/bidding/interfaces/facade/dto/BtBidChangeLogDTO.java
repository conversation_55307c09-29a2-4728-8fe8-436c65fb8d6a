package com.wanlianyida.bidding.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BtBidChangeLogDTO{

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("标包ID")
    private Long packageId;

    @ApiModelProperty("业务ID")
    private String bizId;

    @ApiModelProperty("操作类型[10-招标时间变更,20-候选人排名修改]")
    private Integer optType;

    @ApiModelProperty("变更类型[10-标书售卖开始时间,20-标书售卖截止时间,30-投标截止时间,40-开标时间]")
    private String changeType;

    @ApiModelProperty("变更前的值")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeBeforeValue;

    @ApiModelProperty("变更后的值")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeAfterValue;

    @ApiModelProperty("轮次")
    private Integer bidRound;

    @ApiModelProperty("创建人ID")
    private Long creatorId;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("创建人账号")
    private String creatorAccount;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("最后更新人ID")
    private Long lastUpdaterId;

    @ApiModelProperty("最后更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateTime;


}
