package com.wanlianyida.bidding.interfaces.facade.query;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
public class AttachmentQuery {
    /**
     * 招标公示id
     */
    private String publicityId;
    /**
     * 业务id
     */
    @NotBlank(message = "业务id不能为空", groups = {SingleGroup.class})
    private String businessId;
    /**
     * 业务id List
     */
    @NotEmpty(message = "业务id列表不能为空", groups = {MoreGroup.class})
    private List<String> businessIdList;
    /**
     * 附件类型(字典编码ATTACHMENT_TYPE_E)
     */
    @NotBlank(message = "附件类型不能为空", groups = {SingleGroup.class, MoreGroup.class})
    private String attachmentType;
    /**
     * 链接转换标识：1：转换成有效链接
     */
    private Integer transferFlag;
    /**
     * 文件名
     */
    private String fileName;

    public interface SingleGroup {
    }

    public interface MoreGroup {
    }
}
