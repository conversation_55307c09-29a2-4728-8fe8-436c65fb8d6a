package com.wanlianyida.bidding.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class TenderPublicityDTO {

    /**
     * 公告id
     */
    private Long id;
    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 业务id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessId;

    /**
     * 开标地址
     */
    private String bidOpeningPlace;

    /**
     * 采购公司主体id
     */
    private String purchaseCompanyId;

    /**
     * 采购公司主体名称
     */
    private String purchaseCompanyName;

    /**
     * 公告类型 10招标公告 20侯选人公示 30中标公示
     */
    private String publicityType;

    /**
     * 有效开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 有效结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * * 发布状态 10未发布 20已发布 30已结束
     */
    private Integer publicityStatus;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;
    /**
     * 公告标题
     */
    private String title;
}
