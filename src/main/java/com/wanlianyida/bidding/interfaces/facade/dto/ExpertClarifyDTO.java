package com.wanlianyida.bidding.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ExpertClarifyDTO {
    /**
     * id
     */
    private String id;

    /**
     * 标包id
     */
    private String packageId;

    /**
     * 澄清类型[10-发标澄清,20-评标澄清]
     */
    private String clarifyType;

    /**
     * 澄清标题
     */
    private String clarifyTitle;

    /**
     * 澄清内容
     */
    private String clarifyContent;

    /**
     * 审核状态[10-待发布,20-审批中,30-已发布,40-审批驳回]
     */
    private Integer auditStatus;

    /**
     * 澄清发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clarifyReleaseTime;

    /**
     * 澄清事项[10-发标澄清,20-技术澄清,30-商务澄清,40-价格澄清]
     */
    private String clarifyMatter;

    /**
     * 澄清截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clarifyEndTime;

    /**
     * 发布人id
     */
    private String publishUserId;

    /**
     * 发布人姓名
     */
    private String publishUserName;


    /**
     * 标轮次
     */
    private Integer bidRound;


    /**
     * 回复时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replyTime;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;


    /**
     * 供应商账号
     */
    private String supplierAccount;

    /**
     * 供应商应答数量
     */
    private Integer supplierResponseQty;
}
