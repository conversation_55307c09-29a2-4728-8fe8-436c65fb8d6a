package com.wanlianyida.bidding.interfaces.facade.query;

import lombok.Data;

import java.util.List;

@Data
public class TenderPackageInviteQuery {
    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 状态 10预邀请 20已接受邀请 30已拒绝邀请 40已失效
     */
    private List<Integer> inviteStatusList;

    /**
     * 标包id
     */
    private String relTenderPackageId;

    /**
     * id
     */
    private Long id;

    /**
     * 公共参数（供应商名称或账号）
     */
    private String commonParams;

    /**
     * 联系人名称
     */
    private String linkmanName;

    /**
     * 供应商名称
     */
    private String supplierName;
}
