package com.wanlianyida.bidding.interfaces.facade.query;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 结果通知书
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-12
 */
@Getter
@Setter
public class BiddingWinNoticeQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     * 标包id
     */
    @NotNull(message = "标包id不允许为空")
    private Long packageId;

    /**
     * 通知书类型[10中标通知书 20结果通知书]
     */
    private String noticeType;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不允许为空")
    private String supplierId;


}
