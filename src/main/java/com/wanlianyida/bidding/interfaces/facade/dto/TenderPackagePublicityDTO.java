package com.wanlianyida.bidding.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class TenderPackagePublicityDTO {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 标包id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relTenderPackageId;

    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 类型 10公示侯选人 20公示中标结果
     */
    private String publicityType;

    /**
     * 公示开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publicityStartDate;

    /**
     * 公示结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publicityEndDate;
    /**
     * 投标截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidEndTime;

    /**
     * 开标时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidOpeningTime;

    /**
     * 开标地址
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String bidOpeningPlace;
    /**
     * 内容详情
     */
    private String contentText;
    /**
     * 标题
     */
    private String title;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;

    /**
     * 标包编号
     */
    private String tenderNo;

    /**
     * 中标结果公示状态 10待发布 20审批中 30已发布 40审批驳回
     */
    private Integer winBidResult;

    /**
     * 标包子阶段
     */
    private Integer tenderSubStage;
}
