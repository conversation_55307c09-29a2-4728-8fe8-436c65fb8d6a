package com.wanlianyida.bidding.interfaces.facade;

import com.wanlianyida.bidding.application.dto.OpenSupplierListDTO;
import com.wanlianyida.bidding.application.service.OpenBidQueryAppService;
import com.wanlianyida.bidding.interfaces.facade.dto.AttachmentDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.CompressFileInfoDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.OpeningSingPriceDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.SupplierConfDTO;
import com.wanlianyida.bidding.interfaces.facade.query.OpeningSingPriceQuery;
import com.wanlianyida.bidding.interfaces.facade.query.ReviewAttachmentQuery;
import com.wanlianyida.bidding.interfaces.facade.query.SupplierConfQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/7 11:16
 */
@RestController
@RequestMapping("/open/bid")
public class OpenBidQueryController {

    @Resource
    private OpenBidQueryAppService openBidQueryAppService;


    /**
     * 不开标设置分页列表
     */
    @ApiOperation("不开标设置列表")
    @PostMapping("/pageQueryNobidSet")
    public ResultMode<List<SupplierConfDTO>> pageQueryNobidSet(@RequestBody @Valid PagingInfo<SupplierConfQuery> query) {
        return openBidQueryAppService.pageQueryNobidSet(query);
    }

    /**
     * 唱价分页列表
     */
    @ApiOperation("唱价列表")
    @PostMapping("/pageQuerySingPrice")
    public ResultMode<List<OpeningSingPriceDTO>> pageQuerySingPrice(@RequestBody PagingInfo<OpeningSingPriceQuery> query) {
        return openBidQueryAppService.pageQuerySingPrice(query);
    }

    /**
     * 开标记录分页列表
     */
    @ApiOperation("开标记录列表")
    @PostMapping("/pageQueryOpenBidRecord")
    public ResultMode<List<OpenSupplierListDTO>> pageQueryOpenBidRecord(@RequestBody @Valid PagingInfo<SupplierConfQuery> query) {
        return openBidQueryAppService.pageQueryOpenBidRecord(query);
    }

    /**
     * 供应商签到
     */
    @PostMapping("/querySupplierSign")
    public ResultMode<List<AttachmentDTO>> querySupplierSign(@RequestBody @Valid PagingInfo<ReviewAttachmentQuery> pageQuery) {
        return openBidQueryAppService.querySupplierSign(pageQuery);
    }

    /**
     * 开标结束
     */
    @PostMapping("/queryOpenbidEnd")
    public ResultMode<List<AttachmentDTO>> queryOpenbidEnd(@RequestBody @Valid PagingInfo<ReviewAttachmentQuery> pageQuery) {
        return openBidQueryAppService.queryOpenbidEnd(pageQuery);
    }

    /**
     * 供应商签到获取url
     * @param
     * @return
     */
    @ApiOperation("供应商签到信息获取url")
    @PostMapping("/querySupplierSignUrlList")
    public ResultMode<List<CompressFileInfoDTO>> querySupplierSignUrlList(@RequestBody @Validated ReviewAttachmentQuery query) {
        return openBidQueryAppService.querySupplierSignUrlList(query);
    }

    /**
     * 开标结束获取url
     * @param
     * @return
     */
    @ApiOperation("开标结束获取url")
    @PostMapping("/queryOpenbidEndUrlList")
    public ResultMode<List<CompressFileInfoDTO>> queryOpenbidEndUrlList(@RequestBody @Validated ReviewAttachmentQuery query) {
        return openBidQueryAppService.queryOpenbidEndUrlList(query);
    }


}
