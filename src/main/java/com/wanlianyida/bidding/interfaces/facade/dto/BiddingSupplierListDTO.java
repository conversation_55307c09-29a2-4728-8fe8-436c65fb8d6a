package com.wanlianyida.bidding.interfaces.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class BiddingSupplierListDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商账号
     */
    private String supplierAccount;
    /**
     * 投标状态[10-未投标，20-已投标]
     */
    private Integer bidStatus;
    /**
     * 投标时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidTime;
    /**
     * 供应商联系人姓名
     */
    private String linkmanName;
    /**
     *
     * 供应商联系人手机号
     */
    private String linkmanMobile;
    /**
     * 供应商联系人邮箱
     */
    private String linkmanMailbox;
}
