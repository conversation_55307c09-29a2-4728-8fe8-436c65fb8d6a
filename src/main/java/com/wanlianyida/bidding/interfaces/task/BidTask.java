package com.wanlianyida.bidding.interfaces.task;

import com.wanlianyida.bidding.application.service.BidJobAppService;
import com.wanlianyida.bidding.application.service.TenderPublicityAppService;
import com.wanlianyida.bidding.application.service.WinningResultManagerAppService;
import com.wanlianyida.bidding.domain.issue.service.TenderPackageDomainService;
import com.wanlianyida.bidding.domain.issue.service.TenderRegisterDomainService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务
 */
@Slf4j
@Component
public class BidTask {

    @Resource
    private TenderRegisterDomainService tenderRegisterDomainService;

    @Resource
    private WinningResultManagerAppService winningResultManagerAppService;

    @Resource
    private TenderPublicityAppService tenderPublicityAppService;

    @Resource
    private TenderPackageDomainService tenderPackageDomainService;

    @Resource
    private BidJobAppService bidJobAppService;

    /**
     * 变更报名状态
     */
    @XxlJob("tenderRegisterStatus")
    public void tenderRegisterStatus(){
        tenderRegisterDomainService.tenderRegisterStatus();
    }


    /**
     * 处理开标、评标表数据：初始化抵达投标截止时间的数据
     * 更新标包阶段、子阶段
     */
    @XxlJob("changeWinBidStageTask")
    public void changeBidWinningStageTask(){
//        winningResultManagerAppService.changeBidWinningStageTask();
        log.info("changeWinBidStageTask start running");
        bidJobAppService.bidEndTimeArrive();
    }

//    /**
//     * 变更标包子阶段为：供应商应答阶
//     */
//    @XxlJob("changeTenderPackageSubStage210")
//    public void changeTenderPackageSubStage210() {
//        tenderPackageDomainService.changeTenderPackageSubStage210();
//    }

    /**
     * 变更公示状态为: 已发布
     */
    @XxlJob("changePublicityStatusRelease")
    public void changePublicityStatusRelease() {
        tenderPublicityAppService.changePublicityStatusRelease();
    }

    /**
     * 变更公示状态为: 已结束
     */
    @XxlJob("changePublicityStatusEnd")
    public void changePublicityStatusEnd() {
        tenderPublicityAppService.changePublicityStatusEnd();
    }
}
