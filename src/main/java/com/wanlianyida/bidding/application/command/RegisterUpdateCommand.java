package com.wanlianyida.bidding.application.command;

import lombok.Data;

import java.util.Date;

/**
 * 标包-报名表
 */
@Data
public class RegisterUpdateCommand {

    /**
     * 标包id
     */
    private Long relTenderPackageId;

    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 采购方式
     */
    private String purchaseMethod;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 社会信用代码
     */
    private String socialCreditCode;

    /**
     * 状态 10未报名 20待确认 30确认通过 40确认不通过 40已失效
     */
    private Integer registerStatus;

    /**
     * 报名人id
     */
    private String registerUserId;

    /**
     * 报名人姓名
     */
    private String registerUserName;

    /**
     * 报名人账号
     */
    private String registerUserAccount;


    /**
     * 报名时间
     */
    private Date registerTime;

    /**
     * 是否完善报名信息
     */
    private boolean perfectRegisterInfo;

    /**
     * 通信地址
     */
    private String address;

    /**
     * 业务联系人姓名
     */
    private String contactUserName;

    /**
     * 业务联系人电话
     */
    private String contactUserPhone;

    /**
     * 业务联系人邮箱
     */
    private String contactUserMailbox;

    /**
     * 确认人id
     */
    private String confirmUserId;

    /**
     * 确认人姓名
     */
    private String confirmUserName;

    /**
     * 确认时间
     */
    private Date confirmDate;

    /**
     * 确认状态
     */
    private Integer confirmStatus;

    /**
     * 确认不通过原因
     */
    private String confirmRejectReason;


}
