package com.wanlianyida.bidding.application.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 详评-提交个人汇总
 */
@Data
public class EvalStageSubmitCommand {

    /**
     * 详评id
     */
    @NotNull(message = "详评id不允许为空")
    private Long evalDetailId;

    /**
     * 操作类型：StageSubmitBussTypeEnum
     */
    @NotBlank(message = "操作类型不允许为空")
    private String bussType;

    /**
     * 评标阶段 [枚举bid_eval_stage_*]
     */
    private Integer bidEvalStage;

}