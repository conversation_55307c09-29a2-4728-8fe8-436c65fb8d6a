package com.wanlianyida.bidding.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.bidding.application.query.BiddingListQuery;
import com.wanlianyida.bidding.application.query.TenderPackagePurchasePageQuery;
import com.wanlianyida.bidding.domain.bidding.model.condition.BiddingCondition;
import com.wanlianyida.bidding.domain.bidding.model.entity.BiddingEntity;
import com.wanlianyida.bidding.domain.bidding.repository.SupplierResponseQueryRepository;
import com.wanlianyida.bidding.domain.common.model.condition.AttachmentCondition;
import com.wanlianyida.bidding.domain.common.service.AttachmentDomainService;
import com.wanlianyida.bidding.domain.issue.model.bo.TenderPackagePurchaseListBO;
import com.wanlianyida.bidding.domain.issue.model.condition.TenderPackagePurchaseCondition;
import com.wanlianyida.bidding.domain.issue.service.TenderPackagePurchaseDomainService;
import com.wanlianyida.bidding.domain.project.model.entity.AttachmentEntity;
import com.wanlianyida.bidding.domain.project.repository.IAttachmentRepository;
import com.wanlianyida.bidding.infrastructure.constant.BiddingConstant;
import com.wanlianyida.bidding.infrastructure.enums.AttachmentTypeEnum;
import com.wanlianyida.bidding.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.bidding.interfaces.facade.dto.BiddingSupplierListDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.CompressFileInfoDTO;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/4 15:59
 */
@Service
public class SupplierResponseAppService {

    @Resource
    private SupplierResponseQueryRepository supplierResponseQueryRepository;

    @Resource
    private TenderPackagePurchaseDomainService purchaseDomainService;

    @Resource
    private IAttachmentRepository attachmentRepository;

    @Resource
    private UploadExchangeService uploadExchangeService;

    @Resource
    private AttachmentDomainService attachmentDomainService;

    /**
     * 投标供应商分页查询列表
     */
    public ResultMode<List<BiddingSupplierListDTO>> queryBidInfoPage(PagingInfo<BiddingListQuery> pagingInfo) {
        Page<BiddingListQuery> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        BiddingCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), BiddingCondition.class);
        List<BiddingEntity> list = supplierResponseQueryRepository.pageQueryInspection(condition);
        if (!CollectionUtil.isEmpty(list)) {
            //TODO 先重置为空字符串，后续和产品讨论后再完善
            list.forEach(entity -> {
                entity.setLinkmanName("");
                entity.setLinkmanMobile("");
                entity.setLinkmanMailbox("");
            });
        }
        return ResultMode.successPageList(BeanUtil.copyToList(list, BiddingSupplierListDTO.class), (int) page.getTotal());
    }

    public ResultMode<List<CompressFileInfoDTO>> queryBidpaymentUrlList(TenderPackagePurchasePageQuery query) {

        TenderPackagePurchaseCondition condition = BeanUtil.toBean(query, TenderPackagePurchaseCondition.class);
        if (condition.getSource().equals(1)) {
            condition.setSupplierId(JwtUtil.getTokenInfo().getCompanyId());
        } else if (condition.getRelTenderPackageId() == null) {
            return ResultMode.fail("标包id不能为空");
        }
        // List<TenderPackagePurchaseListBO> list = purchaseDomainService.queryCondition(condition);
        List<TenderPackagePurchaseListBO> list = purchaseDomainService.queryPurchaseList(condition);
        if (CollectionUtil.isEmpty(list)) {
            return ResultMode.successPageList(Collections.EMPTY_LIST, 0);
        }
        List<Long> idList = list.stream()
                .map(TenderPackagePurchaseListBO::getId)
                .collect(Collectors.toList());
        AttachmentCondition attachmentCondition = new AttachmentCondition(idList, AttachmentTypeEnum.TENDER_DOC_PAYMENT.getType());
        // List<AttachmentEntity> attachmentList = attachmentRepository.queryList(attachmentCondition);
        attachmentCondition.setTransferFlag(BiddingConstant.CONVERT_VALID_URL);
        List<AttachmentEntity> attachmentList = attachmentDomainService.queryList(attachmentCondition);
        if(CollectionUtil.isNotEmpty(attachmentList)) {
            List<CompressFileInfoDTO> fileList = attachmentList.parallelStream().map(item -> {
                CompressFileInfoDTO dto = new CompressFileInfoDTO();
                dto.setFileName(item.getFileName());
                dto.setUrl(item.getFileUrl());
                return dto;
            }).collect(Collectors.toList());
            return ResultMode.success(fileList);
        }
        return ResultMode.success(new ArrayList<>());
    }
}
