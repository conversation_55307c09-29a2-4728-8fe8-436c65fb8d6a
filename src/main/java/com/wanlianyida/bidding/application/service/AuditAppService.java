package com.wanlianyida.bidding.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.bidding.application.assembler.AuditAssembler;
import com.wanlianyida.bidding.application.command.AuditApplyCommand;
import com.wanlianyida.bidding.application.command.AuditCommand;
import com.wanlianyida.bidding.application.command.AuditOptCommand;
import com.wanlianyida.bidding.application.command.SubmitCommand;
import com.wanlianyida.bidding.application.dto.PackageAuditListDTO;
import com.wanlianyida.bidding.application.dto.ProjectAuditListDTO;
import com.wanlianyida.bidding.application.query.PackageAuditListQuery;
import com.wanlianyida.bidding.application.query.ProjectAuditListQuery;
import com.wanlianyida.bidding.application.service.strategy.AuditHandlerService;
import com.wanlianyida.bidding.application.service.strategy.AuditHandlerStrategy;
import com.wanlianyida.bidding.domain.audit.model.condition.PackageAuditListCondition;
import com.wanlianyida.bidding.domain.audit.model.condition.ProjectAuditListCondition;
import com.wanlianyida.bidding.domain.audit.service.AuditDomainService;
import com.wanlianyida.bidding.infrastructure.enums.AuditEnum;
import com.wanlianyida.bidding.infrastructure.exception.CtpCoreBiddingExceptionEnum;
import com.wanlianyida.bidding.infrastructure.factory.BiddingHandlerFactory;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 审批
 *
 * <AUTHOR>
 * @since 2024/12/12 15:00
 */
@Service
@Slf4j
public class AuditAppService {

    @Resource
    private AuditDomainService auditDomainService;

    @Resource
    private Map<String, AuditHandlerService> submitAudit;

    /**
     * 立项审批列表查询
     */
    public ResultMode<List<ProjectAuditListDTO>> queryProjectAuditList(PagingInfo<ProjectAuditListQuery> pagingInfo) {
        ProjectAuditListCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), ProjectAuditListCondition.class);
        PagingInfo<ProjectAuditListCondition> conditionPage = new PagingInfo<>(condition,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        PageInfo<ProjectAuditListDTO> page = AuditAssembler.assembleProjectAuditListDTO(auditDomainService.queryProjectAuditList(conditionPage));
        return ResultMode.successPageList(page.getList(), (int) page.getTotal());
    }

    /**
     * 标包审批列表查询（发标、中标候选人公示、中标结果公示）
     */
    public ResultMode<List<PackageAuditListDTO>> queryPackageAuditList(PagingInfo<PackageAuditListQuery> pagingInfo) {
        PackageAuditListCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), PackageAuditListCondition.class);
        PagingInfo<PackageAuditListCondition> conditionPage = new PagingInfo<>(condition,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        PageInfo<PackageAuditListDTO> page = AuditAssembler.assemblePackageAuditListDTO(auditDomainService.queryPackageAuditList(conditionPage));
        return ResultMode.successPageList(page.getList(), (int) page.getTotal());
    }

    /**
     * 通用审批申请
     */
    public ResultMode<?> auditApply(AuditApplyCommand auditApplyCommand) {
        AuditHandlerStrategy handler = BiddingHandlerFactory.getHandler(AuditHandlerStrategy.class, auditApplyCommand.getTaskType());
        if (handler == null) {
            return ResultMode.fail("taskType 参数错误");
        }
        return handler.auditApply(auditApplyCommand);
    }

    /**
     * 通用审批操作
     */
    public ResultMode<?> auditOpt(AuditOptCommand auditOptCommand) {
        AuditHandlerStrategy handler = BiddingHandlerFactory.getHandler(AuditHandlerStrategy.class, auditOptCommand.getTaskType());
        if (handler == null) {
            return ResultMode.fail("taskType 参数错误");
        }
        return handler.auditOpt(auditOptCommand);
    }

    public ResultMode<Void> submit(SubmitCommand submitCommand){
        // 获取提交审批策略处理器
        AuditHandlerService auditHandlerService = this.submitAudit.get(AuditEnum.getSubmitAuditHandler(submitCommand.getProcessNo()));
        if(ObjUtil.isNull(auditHandlerService)){
            return ResultMode.fail(CtpCoreBiddingExceptionEnum.AUDIT_PROCESS_ON_ERROR.getCode(),CtpCoreBiddingExceptionEnum.AUDIT_PROCESS_ON_ERROR.getMsg());
        }
        ResultMode<Void> resultMode = auditHandlerService.submitAudit(submitCommand);
        if(!resultMode.isSucceed()){
            log.info("提交审批-submitAudit-处理失败,流程号={}，业务id={}，失败原因={}", submitCommand.getProcessNo(),submitCommand.getBusinessKey(),resultMode.getMessage());
        }
        log.info("提交审批-submitAudit-处理成功,流程号={}，业务id={}", submitCommand.getProcessNo(),submitCommand.getBusinessKey());
        return resultMode;
    }

    public ResultMode<Void> audit(AuditCommand auditCommand){
        // 获取审批策略处理器
        AuditHandlerService auditHandlerService = this.submitAudit.get(AuditEnum.getSubmitAuditHandler(auditCommand.getProcessNo()));
        if(ObjUtil.isNull(auditHandlerService)){
            return ResultMode.fail(CtpCoreBiddingExceptionEnum.AUDIT_PROCESS_ON_ERROR.getCode(),CtpCoreBiddingExceptionEnum.AUDIT_PROCESS_ON_ERROR.getMsg());
        }
        // 审批操作
        return auditHandlerService.auditOpt(auditCommand);
    }

}
