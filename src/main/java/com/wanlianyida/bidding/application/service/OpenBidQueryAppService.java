package com.wanlianyida.bidding.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.bidding.application.assembler.OpenAssembler;
import com.wanlianyida.bidding.application.dto.OpenSupplierListDTO;
import com.wanlianyida.bidding.application.query.OpenSupplierListQuery;
import com.wanlianyida.bidding.domain.bidding.model.entity.BiddingEntity;
import com.wanlianyida.bidding.domain.bidding.repository.OpeningSingPriceRepository;
import com.wanlianyida.bidding.domain.bidding.service.BiddingDomainService;
import com.wanlianyida.bidding.domain.common.model.bo.OpeningSingPriceBO;
import com.wanlianyida.bidding.domain.common.model.bo.SupplierConfBO;
import com.wanlianyida.bidding.domain.common.model.condition.AttachmentCondition;
import com.wanlianyida.bidding.domain.common.service.AttachmentDomainService;
import com.wanlianyida.bidding.domain.open.model.condition.*;
import com.wanlianyida.bidding.domain.open.model.entity.BidOpeningEntity;
import com.wanlianyida.bidding.domain.open.service.OpenDomainService;
import com.wanlianyida.bidding.domain.project.model.entity.AttachmentEntity;
import com.wanlianyida.bidding.domain.project.repository.IAttachmentRepository;
import com.wanlianyida.bidding.infrastructure.constant.BiddingConstant;
import com.wanlianyida.bidding.infrastructure.enums.AttachmentTypeEnum;
import com.wanlianyida.bidding.infrastructure.enums.OpenStepEnum;
import com.wanlianyida.bidding.infrastructure.enums.SupplierOpenStatusEnum;
import com.wanlianyida.bidding.infrastructure.exchange.UploadExchangeService;
import com.wanlianyida.bidding.interfaces.facade.dto.AttachmentDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.CompressFileInfoDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.OpeningSingPriceDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.SupplierConfDTO;
import com.wanlianyida.bidding.interfaces.facade.query.OpeningSingPriceQuery;
import com.wanlianyida.bidding.interfaces.facade.query.ReviewAttachmentQuery;
import com.wanlianyida.bidding.interfaces.facade.query.SupplierConfQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/7 11:22
 */
@Service
public class OpenBidQueryAppService {


    @Resource
    private OpeningSingPriceRepository openingSingPriceRepository;

    @Resource
    private OpenDomainService openDomainService;

    @Resource
    private BiddingDomainService biddingDomainService;

    @Resource
    private OpenAssembler openAssembler;

    @Resource
    private IAttachmentRepository attachmentRepository;

    @Resource
    private UploadExchangeService uploadExchangeService;

    @Resource
    private AttachmentDomainService attachmentDomainService;

    /**
     * 唱价分页列表
     */
    public ResultMode<List<OpeningSingPriceDTO>> pageQuerySingPrice(PagingInfo<OpeningSingPriceQuery> pagingInfo) {
        Page<OpeningSingPriceQuery> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        OpeningSingPriceCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), OpeningSingPriceCondition.class);
        condition.setSupplierOpenStatus(SupplierOpenStatusEnum.OPEN_STATUS_10.getStatus());
        List<OpeningSingPriceBO> list = openingSingPriceRepository.pageQueryInspection(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, OpeningSingPriceDTO.class), (int) page.getTotal());
    }

    /**
     * 开标记录分页列表
     */
    public ResultMode<List<OpenSupplierListDTO>> pageQueryOpenBidRecord(PagingInfo<SupplierConfQuery> pagingInfo) {

        Page<OpenSupplierListQuery> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        SupplierConfCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), SupplierConfCondition.class);
        // 开标实体
        BidOpeningEntity openingEntity = new BidOpeningEntity();
        openingEntity.setPackageId(condition.getPackageId());
        openingEntity.setBidRound(condition.getBidRound());
        openingEntity.setSupplierName(condition.getSupplierName());
        openingEntity.setBidderAccount(condition.getBidderAccount());
        // 查询投标数据
        List<BiddingEntity> list = biddingDomainService.queryCondition(openAssembler.buildOpenBiddingCondition(openingEntity));
        return ResultMode.successPageList(BeanUtil.copyToList(list, OpenSupplierListDTO.class), (int) page.getTotal());
    }

    public ResultMode<List<SupplierConfDTO>> pageQueryNobidSet(PagingInfo<SupplierConfQuery> pagingInfo) {
        Page<SupplierConfQuery> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        SupplierConfCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), SupplierConfCondition.class);
        List<SupplierConfBO> list = openingSingPriceRepository.pageQueryNobidSet(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, SupplierConfDTO.class), (int) page.getTotal());
    }

    public ResultMode<List<AttachmentDTO>> querySupplierSign(PagingInfo<ReviewAttachmentQuery> pagingInfo) {
        Page<ReviewAttachmentQuery> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        ReviewAttachmentCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), ReviewAttachmentCondition.class);
        condition.setAttachmentTypeList(Arrays.asList(AttachmentTypeEnum.TYPE_260.getType()));
        //查询开标ID
        List<AttachmentEntity> attachmentList = openDomainService.queryOpenbidEnd(condition);
        this.convertAttachmentEntityUrl(attachmentList);
        return ResultMode.successPageList(BeanUtil.copyToList(attachmentList, AttachmentDTO.class), (int) page.getTotal());
    }

    public ResultMode<List<AttachmentDTO>> queryOpenbidEnd(PagingInfo<ReviewAttachmentQuery> pagingInfo) {
        Page<ReviewAttachmentQuery> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        ReviewAttachmentCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), ReviewAttachmentCondition.class);
        condition.setAttachmentTypeList(Arrays.asList(AttachmentTypeEnum.TYPE_260.getType(),AttachmentTypeEnum.TYPE_270.getType()));
        condition.setOpenStep(OpenStepEnum.OPEN_STEP_60.getStep());
        //查询开标ID
        List<AttachmentEntity> attachmentList = openDomainService.queryOpenbidEnd(condition);
        this.convertAttachmentEntityUrl(attachmentList);
        return ResultMode.successPageList(BeanUtil.copyToList(attachmentList, AttachmentDTO.class), (int) page.getTotal());
    }

    public ResultMode<List<CompressFileInfoDTO>> querySupplierSignUrlList(ReviewAttachmentQuery query) {
        //查询开标ID
        BidOpeningEntity openingEntity = openDomainService.querySupplierSign(BeanUtil.toBean(query, ReviewAttachmentCondition.class));
        if (!ObjectUtil.isEmpty(openingEntity)) {
            AttachmentCondition attCondition = new AttachmentCondition();
            attCondition.setBusinessId(String.valueOf(openingEntity.getId()));
            attCondition.setAttachmentType(AttachmentTypeEnum.TYPE_260.getType());
            if (StringUtils.isNotEmpty(query.getFileName())) {
                attCondition.setFileName(query.getFileName());
            }
            // List<AttachmentEntity> attachmentList = attachmentRepository.queryList(attCondition);
            attCondition.setTransferFlag(BiddingConstant.CONVERT_VALID_URL);
            List<AttachmentEntity> attachmentList = attachmentDomainService.queryList(attCondition);
            if(CollectionUtil.isNotEmpty(attachmentList)) {
                List<CompressFileInfoDTO> fileList = attachmentList.parallelStream().map(item -> {
                    CompressFileInfoDTO dto = new CompressFileInfoDTO();
                    dto.setFileName(item.getFileName());
                    dto.setUrl(item.getFileUrl());
                    return dto;
                }).collect(Collectors.toList());
                return ResultMode.success(fileList);
            }
        }
        return ResultMode.success(new ArrayList<>());
    }

    public ResultMode<List<CompressFileInfoDTO>> queryOpenbidEndUrlList(ReviewAttachmentQuery query) {
        //查询开标ID
        ReviewAttachmentCondition condition = BeanUtil.toBean(query, ReviewAttachmentCondition.class);
        BidOpeningEntity openingEntity = openDomainService.querySupplierSign(condition);
        if(!ObjectUtil.isEmpty(openingEntity)){
            List<String> list = new ArrayList<>();
            list.add(AttachmentTypeEnum.TYPE_260.getType());
            list.add(AttachmentTypeEnum.TYPE_270.getType());
            AttachmentCondition attCondition = new AttachmentCondition();
            attCondition.setBusinessId(String.valueOf(openingEntity.getId()));
            attCondition.setAttachmentTypeList(list);
            if(StringUtils.isNotEmpty(query.getFileName())){
                attCondition.setFileName(query.getFileName());
            }
            // List<AttachmentEntity> attachmentList = attachmentRepository.queryList(attCondition);
            attCondition.setTransferFlag(BiddingConstant.CONVERT_VALID_URL);
            List<AttachmentEntity> attachmentList = attachmentDomainService.queryList(attCondition);
            if(CollectionUtil.isNotEmpty(attachmentList)) {
                List<CompressFileInfoDTO> fileList = attachmentList.parallelStream().map(item -> {
                    CompressFileInfoDTO dto = new CompressFileInfoDTO();
                    dto.setFileName(item.getFileName());
                    dto.setUrl(item.getFileUrl());
                    return dto;
                }).collect(Collectors.toList());
                return ResultMode.success(fileList);
            }
        }
        return ResultMode.success(new ArrayList<>());
    }

    // 附件url转换
    private void convertAttachmentEntityUrl(List<AttachmentEntity> attachmentEntityList) {
        // 附件url转换
        if (!CollectionUtil.isEmpty(attachmentEntityList)) {
            List<String> urlList = attachmentEntityList.stream().map(AttachmentEntity::getFileUrl).collect(Collectors.toList());
            Map<String, String> convertUrlMap = uploadExchangeService.convertUrlList(urlList);
//            log.info("附件url转换结果：{}", convertUrlMap);
            if (CollectionUtil.isEmpty(convertUrlMap)) {
                return;
            }
            attachmentEntityList.forEach(attachmentEntity -> {
                if (convertUrlMap.containsKey(attachmentEntity.getFileUrl())) {
                    attachmentEntity.setFileUrl(convertUrlMap.get(attachmentEntity.getFileUrl()));
                }
            });
        }
    }
}
