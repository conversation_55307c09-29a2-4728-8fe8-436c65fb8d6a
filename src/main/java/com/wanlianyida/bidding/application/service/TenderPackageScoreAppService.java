package com.wanlianyida.bidding.application.service;

import com.wanlianyida.bidding.domain.win.model.condition.BtTenderPackageScoreCondition;
import com.wanlianyida.bidding.domain.win.model.entity.TenderPackageScoreEntity;
import com.wanlianyida.bidding.domain.win.repository.ITenderPackageScoreRepository;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/04/11/11:41
 */
@Service
public class TenderPackageScoreAppService {

    @Resource
    private ITenderPackageScoreRepository tenderPackageScoreRepository;

    /**
     * 查询列表
     *
     * @param condition 条件
     * @return {@link List }<{@link BtTenderPackageScoreCondition }>
     */
    public List<TenderPackageScoreEntity> queryList(BtTenderPackageScoreCondition condition) {
        return tenderPackageScoreRepository.queryList(condition);
    }

}
