package com.wanlianyida.bidding.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.bidding.domain.bidding.model.bo.BiddingDetailBO;
import com.wanlianyida.bidding.domain.bidding.model.bo.BiddingSubmitBO;
import com.wanlianyida.bidding.domain.bidding.model.bo.NewRoundCreateBO;
import com.wanlianyida.bidding.domain.bidding.model.condition.BiddingCondition;
import com.wanlianyida.bidding.domain.bidding.model.entity.BiddingEntity;
import com.wanlianyida.bidding.domain.bidding.model.entity.BiddingRoundConfigEntity;
import com.wanlianyida.bidding.domain.bidding.model.entity.BiddingRoundEntity;
import com.wanlianyida.bidding.domain.issue.model.condition.TenderPackageSupplierResponseCondition;
import com.wanlianyida.bidding.domain.issue.model.entity.TenderPackageSupplierResponseEntity;
import com.wanlianyida.bidding.domain.open.model.entity.BidOpeningEntity;
import com.wanlianyida.bidding.domain.project.model.entity.AttachmentEntity;
import com.wanlianyida.bidding.domain.project.model.entity.TenderPackageEntity;
import com.wanlianyida.bidding.infrastructure.enums.BidStatusEnum;
import com.wanlianyida.bidding.infrastructure.enums.PackagePurchaseResponseStatusEnum;
import com.wanlianyida.bidding.infrastructure.enums.SupplierOpenStatusEnum;
import com.wanlianyida.bidding.interfaces.facade.command.BiddingSubmitCommand;
import com.wanlianyida.bidding.interfaces.facade.command.NewRoundCreateCommand;
import com.wanlianyida.bidding.interfaces.facade.dto.BiddingDTO;
import com.wanlianyida.bidding.interfaces.facade.dto.BiddingDetailDTO;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/04/06/11:48
 */
public class BiddingAssembler {

    /**
     * 立项审批列表查询
     */
    public static BiddingSubmitBO assembleSubmitBidding(BiddingSubmitCommand updateCommand) {
        BiddingSubmitBO biddingSubmitBO = BeanUtil.copyProperties(updateCommand, BiddingSubmitBO.class);
        buildAttachmentList(updateCommand, biddingSubmitBO);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String userBaseId = StringUtils.isNotBlank(tokenInfo.getUserBaseId()) ? tokenInfo.getUserBaseId() : "test";
        BiddingEntity biddingEntity = biddingSubmitBO.getBiddingUpdateCommand();

        getAttachmentList(biddingSubmitBO, biddingEntity, userBaseId);
        biddingEntity.setBidStatus(BidStatusEnum.STATUS_20.getCode());
        biddingEntity.setBidderId(userBaseId);
        biddingEntity.setBidderName(tokenInfo.getUsername());
        biddingEntity.setBidderAccount(tokenInfo.getLoginName());
        biddingEntity.setSocialCreditCode(tokenInfo.getLicenseNo());
        biddingSubmitBO.setBiddingUpdateCommand(biddingEntity);
        return biddingSubmitBO;
    }

    private static void buildAttachmentList(BiddingSubmitCommand updateCommand, BiddingSubmitBO biddingSubmitBO) {
        List<AttachmentEntity> attachmentList = new ArrayList<>();
        attachmentList.addAll(BeanUtil.copyToList(updateCommand.getQuotationFileUrlList(), AttachmentEntity.class));
        attachmentList.addAll(BeanUtil.copyToList(updateCommand.getBusinessFileUrlList(), AttachmentEntity.class));
        attachmentList.addAll(BeanUtil.copyToList(updateCommand.getTechFileUrlList(), AttachmentEntity.class));
        biddingSubmitBO.setAttachmentList(attachmentList);
    }

    private static void getAttachmentList(BiddingSubmitBO biddingSubmitBO, BiddingEntity biddingEntity, String userBaseId) {
        List<AttachmentEntity> attachmentList = biddingSubmitBO.getAttachmentList();
        attachmentList.forEach(attachment -> {
            attachment.setCreatorId(userBaseId);
            attachment.setUpdaterId(userBaseId);
            attachment.setBusinessId(biddingEntity.getId().toString());
        });
        biddingEntity.setCreatorId(userBaseId);
        biddingEntity.setUpdaterId(userBaseId);
    }

    public static BiddingDetailDTO buildBiddingDetailDto(BiddingDetailBO biddingDetailBO, TenderPackageSupplierResponseEntity detail) {
        BiddingDetailDTO biddingDetailDTO = BeanUtil.copyProperties(biddingDetailBO, BiddingDetailDTO.class);
        BiddingDTO biddingDTO = biddingDetailDTO.getBiddingDTO();
        biddingDTO.setProjectNo(detail.getProjectNo());
        biddingDTO.setProjectName(detail.getProjectName());
        biddingDTO.setBidEndTime(detail.getBidEndTime());
        biddingDTO.setPackageName(detail.getTenderPackageName());
        biddingDTO.setQuotationContent(ObjUtil.isNotNull(biddingDetailBO.getBiddingRoundEntity()) ? biddingDetailBO.getBiddingRoundEntity().getQuotationContent() : "");
        biddingDTO.setQuotationRequirement(biddingDetailBO.getQuotationRequirement());
        biddingDetailDTO.setBiddingDTO(biddingDTO);
        return biddingDetailDTO;
    }

    public static BiddingCondition buildCondition(BidOpeningEntity bidOpeningEntity) {
        BiddingCondition condition = new BiddingCondition();
        condition.setPackageId(bidOpeningEntity.getPackageId());
        condition.setBidRound(bidOpeningEntity.getBidRound());
        condition.setBidStatus(BidStatusEnum.STATUS_20.getCode());
        condition.setSupplierOpenStatus(SupplierOpenStatusEnum.OPEN_STATUS_10.getStatus());
        return condition;
    }

    public static TenderPackageSupplierResponseCondition buildResponseCondition(NewRoundCreateCommand newRoundCreateCommand) {
        TenderPackageSupplierResponseCondition condition = new TenderPackageSupplierResponseCondition();
        condition.setRelTenderPackageId(newRoundCreateCommand.getPackageId());
        condition.setSupplierIdList(newRoundCreateCommand.getSupplierIdList());
        condition.setBidRound(newRoundCreateCommand.getBidRound());
        return condition;
    }
    public static NewRoundCreateBO buildResponseEntityNew(List<TenderPackageSupplierResponseEntity> responseEntityList,
                                                          NewRoundCreateCommand newRoundCreateCommand,
                                                          TenderPackageEntity packageEntity) {
        TenderPackageSupplierResponseEntity entity = IterUtil.getFirst(responseEntityList);
        NewRoundCreateBO newRoundCreateBO = new NewRoundCreateBO();
        newRoundCreateBO.setResponseEntityList(responseEntityList);
        Integer newRound = newRoundCreateCommand.getBidRound() + 1;
        String userBaseId = StringUtils.isBlank(JwtUtil.getTokenInfo().getUserBaseId()) ? "" : JwtUtil.getTokenInfo().getUserBaseId();
        //应答表 多
        responseEntityList.forEach(responseEntity -> {
            responseEntity.setId(null);
            responseEntity.setBidRound(newRound);
            responseEntity.setResponseStatus(PackagePurchaseResponseStatusEnum.RESPONSE_CODE_400.getCode());
            responseEntity.setCreatedDate(null);
            responseEntity.setUpdatedDate(null);
        });
        newRoundCreateBO.setTenderPackageSupplierResponseEntityList(responseEntityList);
        //轮次表 1
        BiddingRoundEntity biddingRoundEntity = new BiddingRoundEntity();
        biddingRoundEntity.setProjectId(entity.getRelProjectId());
        biddingRoundEntity.setPackageId(entity.getRelTenderPackageId());
        biddingRoundEntity.setPackageName(packageEntity.getTenderPackageName());
        biddingRoundEntity.setBidRound(newRound);
        // biddingRoundEntity.setQuotationContent(newRoundCreateCommand.getQuotationContent());
        biddingRoundEntity.setQuotationContent(sortQuotationContent(newRoundCreateCommand.getQuotationContent()));
        biddingRoundEntity.setBidEndTime(newRoundCreateCommand.getBidEndTime());
        biddingRoundEntity.setBidOpenTime(newRoundCreateCommand.getBidEndTime());
        biddingRoundEntity.setCreatorId(userBaseId);
        biddingRoundEntity.setUpdaterId(userBaseId);
        newRoundCreateBO.setBiddingRoundEntity(biddingRoundEntity);
        //轮次配置 1
        BiddingRoundConfigEntity biddingRoundConfigEntity = new BiddingRoundConfigEntity();
        biddingRoundConfigEntity.setQuotationRequirement(newRoundCreateCommand.getQuotationRequirement());
        biddingRoundConfigEntity.setUpdaterId(userBaseId);
        //投标表 多
        List<BiddingEntity> biddingEntityList = new ArrayList<>();
        responseEntityList.forEach(responseEntity -> {
            BiddingEntity biddingEntity = new BiddingEntity();
            biddingEntity.setProjectId(entity.getRelProjectId());
            biddingEntity.setPackageId(entity.getRelTenderPackageId());
            biddingEntity.setSupplierId(entity.getSupplierId());
            biddingEntity.setSupplierName(entity.getSupplierName());
            biddingEntity.setQuotationMethod(ObjUtil.isNull(packageEntity) ? null : packageEntity.getQuotationMethod());
            biddingEntity.setBidRound(newRound);
            biddingEntity.setUpdaterId(userBaseId);
            //        biddingEntity.setLinkmanName(entity.getLinkmanName());
//        biddingEntity.setLinkmanMobile(entity.getLinkmanMobile());
            biddingEntityList.add(biddingEntity);
        });
        newRoundCreateBO.setBiddingEntityList(biddingEntityList);
        newRoundCreateBO.setBiddingRoundConfigEntity(biddingRoundConfigEntity);
        newRoundCreateBO.setAttachmentCommandList(BeanUtil.copyToList(newRoundCreateCommand.getAttachmentCommandList(), AttachmentEntity.class));
        return newRoundCreateBO;
    }

    /**
     * 报价内容字典码升序处理
     * @param quotationContent
     * @return
     */
    private static String sortQuotationContent(String quotationContent) {
        if (StrUtil.isBlank(quotationContent)) {
            return "";
        }
        String[] arr = quotationContent.split(",");
        Arrays.sort(arr);
        return String.join(",", Arrays.stream(arr).collect(Collectors.toList()));
    }

}
