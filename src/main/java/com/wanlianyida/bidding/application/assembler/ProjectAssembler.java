package com.wanlianyida.bidding.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.bidding.application.command.*;
import com.wanlianyida.bidding.application.dto.ProjectAuditDetailDTO;
import com.wanlianyida.bidding.application.dto.ProjectDTO;
import com.wanlianyida.bidding.application.dto.ProjectDetailDTO;
import com.wanlianyida.bidding.application.query.ProjectPageQuery;
import com.wanlianyida.bidding.domain.audit.model.bo.AuditAddBO;
import com.wanlianyida.bidding.domain.audit.model.bo.AuditOptBO;
import com.wanlianyida.bidding.domain.audit.model.bo.UserTaskAddBO;
import com.wanlianyida.bidding.domain.project.model.condition.ProjectQueryCondition;
import com.wanlianyida.bidding.domain.project.model.entity.AttachmentEntity;
import com.wanlianyida.bidding.domain.project.model.entity.ProjectEntity;
import com.wanlianyida.bidding.domain.project.model.entity.ProjectProductEntity;
import com.wanlianyida.bidding.domain.project.model.entity.TenderPackageEntity;
import com.wanlianyida.bidding.infrastructure.enums.CompleteResultEnum;
import com.wanlianyida.bidding.infrastructure.enums.ProjectStatusEnum;
import com.wanlianyida.bidding.infrastructure.enums.TaskAuditStatusEnum;
import com.wanlianyida.bidding.infrastructure.enums.TaskTypeEnum;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 立项
 * <AUTHOR>
 */
public class ProjectAssembler {

    public static ProjectQueryCondition buildPageQueryCondition(ProjectPageQuery query) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        ProjectQueryCondition condition = new ProjectQueryCondition();
        // 项目名称
        condition.setProjectName(query.getProjectName());
        // 项目编号
        condition.setProjectNo(query.getProjectNo());
        // 项目状态
        condition.setProjectStatus(query.getProjectStatus());
        // 项目经理(模糊匹配)
        condition.setProjectManagerName(query.getProjectManagerName());
        // 数据权限,项目创建人可见自己创建的项目，项目经理可见归属自己的采购项目
        condition.setDataScopeUser(tokenInfo.getUserBaseId());
        return condition;
    }

    public static ProjectQueryCondition buildVerifyAddCondition(ProjectAddCommand command) {
        ProjectQueryCondition condition = new ProjectQueryCondition();
        // 项目名称精准匹配
        condition.setProjectNameCompleteMatching(command.getProject().getProjectName().trim());
        return condition;
    }

    public static ProjectQueryCondition buildVerifyProjectNameCondition(ProjectNameVerifyCommand command) {
        ProjectQueryCondition condition = new ProjectQueryCondition();
        // 项目名称精准匹配
        condition.setProjectNameCompleteMatching(command.getProjectName().trim());
        return condition;
    }

    public static ProjectQueryCondition buildVerifyEditCondition(ProjectEditCommand command) {
        ProjectQueryCondition condition = new ProjectQueryCondition();
        // 项目名称精准匹配
        condition.setProjectNameCompleteMatching(command.getProject().getProjectName().trim());
        return condition;
    }

    public static ProjectDetailDTO buildProjectDetailDTO(ProjectEntity projectEntity,List<ProjectProductEntity>  productEntityList,List<AttachmentEntity> attachmentEntityList){
        ProjectDetailDTO detailDTO = new ProjectDetailDTO();
        detailDTO.setProjectDto(getProjectDto(projectEntity));
        detailDTO.setProjectProductDto(BeanUtil.copyToList(productEntityList, ProjectDetailDTO.ProjectProductDto.class));
        detailDTO.setProjectAttachmentDto(BeanUtil.copyToList(attachmentEntityList, ProjectDetailDTO.ProjectAttachmentDto.class));
        return detailDTO;
    }

    private static ProjectDetailDTO.ProjectDto getProjectDto(ProjectEntity projectEntity){
        ProjectDetailDTO.ProjectDto bean = BeanUtil.toBean(projectEntity, ProjectDetailDTO.ProjectDto.class);
        if(bean.getCreatorDeptId() != null && bean.getCreatorDeptId() == 0){
            bean.setCreatorDeptId(null);
        }
        if(bean.getPurchaseDeptId() != null && bean.getPurchaseDeptId() == 0){
            bean.setPurchaseDeptId(null);
        }
        return bean;
    }

    public static ProjectAuditDetailDTO buildProjectAuditDetailDTO(ProjectEntity projectEntity, List<ProjectProductEntity>  productEntityList, List<AttachmentEntity> attachmentEntityList, List<TenderPackageEntity> packageEntityList){
        ProjectAuditDetailDTO auditDetailDTO = new ProjectAuditDetailDTO();
        auditDetailDTO.setProjectDto(BeanUtil.toBean(projectEntity, ProjectAuditDetailDTO.ProjectDto.class));
        auditDetailDTO.setProjectProductDto(BeanUtil.copyToList(productEntityList, ProjectAuditDetailDTO.ProjectProductDto.class));
        auditDetailDTO.setProjectAttachmentDto(BeanUtil.copyToList(attachmentEntityList, ProjectAuditDetailDTO.ProjectAttachmentDto.class));
        auditDetailDTO.setPackageDtoList(BeanUtil.copyToList(packageEntityList,ProjectAuditDetailDTO.ProjectTenderPackageDto.class));
        return auditDetailDTO;
    }

    public static AuditAddBO buildAuditAddBo(AuditApplyCommand command,ProjectEntity projectEntity){
        AuditAddBO auditAddBo = new AuditAddBO();

        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        String userId = "";
        String userName = "";
        if(!StringUtils.isEmpty(tokenInfo.getUserBaseId())){
            userId = tokenInfo.getUserBaseId();
        }
        if(!StringUtils.isEmpty(tokenInfo.getUsername())){
            userName = tokenInfo.getUsername();
        }

        // 任务类型 10立项审批 20发标审批 30公示侯选人审批 40公示中标结果审批
        auditAddBo.setTaskType(TaskTypeEnum.TYPE_10.getType());
        // 业务id
        auditAddBo.setBusinessId(command.getBusinessId()+"");
        // 业务编号
        auditAddBo.setBusinessNo(projectEntity.getProjectNo());
        // 业务名称
        auditAddBo.setBusinessName(projectEntity.getProjectName());

        // 申请人id
        auditAddBo.setApplicantId(userId);
        // 申请人姓名
        auditAddBo.setApplicantName(userName);
        // 申请时间
        auditAddBo.setApplyDate(new Date());

        List<UserTaskAddBO> userTaskAddBoList = BeanUtil.copyToList(command.getAuditUserList(), UserTaskAddBO.class);
        auditAddBo.setUserList(userTaskAddBoList);
        return auditAddBo;
    }

    public static ProjectEntity buildSubmitAuditUpdateEntity(AuditApplyCommand command){
        ProjectEntity projectEntity = new ProjectEntity();
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String userId = "";
        if(!StringUtils.isEmpty(tokenInfo.getUserBaseId())){
            userId = tokenInfo.getUserBaseId();
        }
        // id
        projectEntity.setId(command.getBusinessId());
        // 项目状态:20审批中
        projectEntity.setProjectStatus(ProjectStatusEnum.IN_AUDIT.getStatus());
        // 最后更新人id
        projectEntity.setUpdaterId(userId);
        return projectEntity;
    }

    public static ProjectEntity buildAuditUpdateEntity(AuditOptCommand command){
        ProjectEntity projectEntity = new ProjectEntity();
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        String userId = "";
        if(!StringUtils.isEmpty(tokenInfo.getUserBaseId())){
            userId = tokenInfo.getUserBaseId();
        }
        // id
        projectEntity.setId(command.getBusinessId());
        // 审核状态：审核通过
        if(TaskAuditStatusEnum.STATUS_20.getStatus().equals(command.getAuditStatus())){
            // 项目状态:立项完成
            projectEntity.setProjectStatus(ProjectStatusEnum.PROJECT_COMPLETED.getStatus());
        }
        // 审核状态：审核不通过
        if(TaskAuditStatusEnum.STATUS_30.getStatus().equals(command.getAuditStatus())){
            // 项目状态:审批驳回
            projectEntity.setProjectStatus(ProjectStatusEnum.AUDIT_REJECT.getStatus());
        }

        // 最后更新人id
        projectEntity.setUpdaterId(userId);
        return projectEntity;
    }

    public static AuditOptBO buildAuditBo(AuditOptCommand command){
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();

        AuditOptBO auditOptBO = BeanUtil.toBean(command,AuditOptBO.class);

        String userId = "";
        String userName = "";
        if(!StringUtils.isEmpty(tokenInfo.getUserBaseId())){
            userId = tokenInfo.getUserBaseId();
        }
        if(!StringUtils.isEmpty(tokenInfo.getUsername())){
            userName = tokenInfo.getUsername();
        }

        // 审核人id
        auditOptBO.setAuditorId(userId);
        // 审核人姓名
        auditOptBO.setAuditorName(userName);
        // 审核时间
        auditOptBO.setAuditDate(new Date());

        return auditOptBO;
    }

    public static ProjectEntity buildProjectSubmitAuditEntity(SubmitCommand command){
        ProjectEntity projectEntity = new ProjectEntity();
        // id
        projectEntity.setId(Long.valueOf(command.getBusinessKey()));
        // 项目状态:20审批中
        projectEntity.setProjectStatus(ProjectStatusEnum.IN_AUDIT.getStatus());
        // 最后更新人id
        if(StringUtils.isNotBlank(command.getSubmitUserId())){
            projectEntity.setUpdaterId(command.getSubmitUserId());
        }
        return projectEntity;
    }

    public static ProjectEntity buildProjectAuditOptEntity(AuditCommand command){
        ProjectEntity projectEntity = new ProjectEntity();
        String userId = command.getCompleteUserId();
        // id
        projectEntity.setId(Long.valueOf(command.getBusinessKey()));
        // 审核状态：审核通过
        if(CompleteResultEnum.RESULT_10.getResult().equals(command.getCompleteResult())){
            // 项目状态:立项完成
            projectEntity.setProjectStatus(ProjectStatusEnum.PROJECT_COMPLETED.getStatus());
            // 赋值立项时间
            projectEntity.setProjectApprovalTime(getProjectApprovalTime(command.getCompleteTimestamp()));
        }
        // 审核状态：审核不通过
        if(CompleteResultEnum.RESULT_20.getResult().equals(command.getCompleteResult())){
            // 项目状态:审批驳回
            projectEntity.setProjectStatus(ProjectStatusEnum.AUDIT_REJECT.getStatus());
        }

        // 最后更新人id
        if(StringUtils.isNotBlank(userId)){
            projectEntity.setUpdaterId(userId);
        }
        return projectEntity;
    }

    private static Date getProjectApprovalTime(Long completeTimestamp){
        if(completeTimestamp == null){
            return null;
        }
        return new Date(completeTimestamp);
    }

    public static ProjectDTO buildProjectDTO(ProjectEntity project){
        ProjectDTO returnDto = new ProjectDTO();
        if(project != null){
            // 项目id
            returnDto.setId(project.getId());
            // 项目编号
            returnDto.setProjectNo(project.getProjectNo());
            // 项目名称
            returnDto.setProjectName(project.getProjectName());
            // 项目经理用户id
            returnDto.setProjectManagerId(project.getProjectManagerId());
            // 项目经理用户名
            returnDto.setProjectManagerName(project.getProjectManagerName());
            // 采购公司主体id
            returnDto.setPurchaseCompanyId(project.getPurchaseCompanyId());
            // 采购公司主体名称
            returnDto.setPurchaseCompanyName(project.getPurchaseCompanyName());
            // 项目创建时间
            returnDto.setCreatedDate(project.getCreatedDate());
        }
        return returnDto;
    }

    public static ProjectQueryCondition buildProjectNoCondition(String projectNo){
        ProjectQueryCondition condition = new ProjectQueryCondition();
        condition.setProjectNo(projectNo);
        return condition;
    }
}
