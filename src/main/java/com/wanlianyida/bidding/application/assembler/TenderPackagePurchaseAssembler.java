package com.wanlianyida.bidding.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.bidding.application.command.PackagePurchaseCommand;
import com.wanlianyida.bidding.application.command.PackagePurchaseConfirmCommand;
import com.wanlianyida.bidding.application.dto.TenderPackagePurchaseDetailDTO;
import com.wanlianyida.bidding.domain.issue.model.entity.BtConfirmRecordEntity;
import com.wanlianyida.bidding.domain.issue.model.entity.TenderPackagePurchaseEntity;
import com.wanlianyida.bidding.domain.project.model.entity.AttachmentEntity;
import com.wanlianyida.bidding.domain.project.model.entity.TenderPackageEntity;
import com.wanlianyida.bidding.infrastructure.constant.BiddingConstant;
import com.wanlianyida.bidding.infrastructure.enums.AttachmentTypeEnum;
import com.wanlianyida.bidding.infrastructure.enums.ConfirmTypeEnum;
import com.wanlianyida.bidding.infrastructure.enums.PackagePurchaseConfirmStatusEnum;
import com.wanlianyida.bidding.infrastructure.enums.PurchaseStatusEnum;
import com.wanlianyida.bidding.interfaces.facade.command.TenderPackagePurchaseCommand;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcore.utils.IdUtil;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月17日 20:22
 */
@Component
public class TenderPackagePurchaseAssembler {

    private static IdUtil idUtil;

    @Resource
    public void setStaticIdUtil(IdUtil idUtil) {
        TenderPackagePurchaseAssembler.idUtil = idUtil;
    }

    public static TenderPackagePurchaseEntity purchase(PackagePurchaseCommand command, TenderPackagePurchaseEntity purchaseEntity) {
        TenderPackagePurchaseEntity entity = new TenderPackagePurchaseEntity();
        entity.setId(command.getId());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        Date now = new Date();
        if (purchaseEntity.getTenderDocAmount().compareTo(BigDecimal.ZERO) == 0) {
            entity.setPurchaseStatus(PurchaseStatusEnum.CONFIRM_PURCHASE.getCode());
            entity.setConfirmStatus(PackagePurchaseConfirmStatusEnum.PASS.getCode());
            entity.setConfirmDate(now);
        } else {
            entity.setPurchaseStatus(PurchaseStatusEnum.WAITING_CONFIRM.getCode());
        }
        entity.setPurchaseId(tokenInfo.getUserBaseId());
        entity.setPurchaseName(tokenInfo.getUsername());
        entity.setPurchaseAccount(tokenInfo.getLoginName());
        entity.setUpdaterId(tokenInfo.getUserBaseId());
        entity.setPurchaseTime(now);
        entity.setUpdatedDate(now);
        return entity;
    }


    public static TenderPackagePurchaseEntity buildTenderPackagePurchase(TenderPackagePurchaseCommand command, TenderPackageEntity tenderPackageEntity) {
        TenderPackagePurchaseEntity tenderPackagePurchaseEntity = BeanUtil.toBean(command, TenderPackagePurchaseEntity.class);
        tenderPackagePurchaseEntity.setTenderDocAmount(command.getTenderExpense());

        // 这里token获取不到，创建人id需要传参过来
        tenderPackagePurchaseEntity.setPurchaseMethod(tenderPackagePurchaseEntity.getPurchaseMethod());
        tenderPackagePurchaseEntity.setCreatorId(command.getCreatorId());
        tenderPackagePurchaseEntity.setUpdaterId(command.getCreatorId());
        Date date = new Date();
        tenderPackagePurchaseEntity.setCreatedDate(date);
        tenderPackagePurchaseEntity.setUpdatedDate(date);
        return tenderPackagePurchaseEntity;
    }

    public static TenderPackagePurchaseEntity confirm(PackagePurchaseConfirmCommand command) {
        TenderPackagePurchaseEntity entity = new TenderPackagePurchaseEntity();
        entity.setId(command.getId());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        entity.setConfirmUserId(tokenInfo.getUserBaseId());
        entity.setConfirmUserName(tokenInfo.getUsername());
        entity.setConfirmStatus(command.getConfirmStatus());
        if (PackagePurchaseConfirmStatusEnum.PASS.getCode().equals(command.getConfirmStatus())) {
            entity.setPurchaseStatus(PurchaseStatusEnum.CONFIRM_PURCHASE.getCode());
            entity.setConfirmStatus(PackagePurchaseConfirmStatusEnum.PASS.getCode());
        } else {
            entity.setPurchaseStatus(PurchaseStatusEnum.REJECT.getCode());
            entity.setConfirmStatus(PackagePurchaseConfirmStatusEnum.REJECT.getCode());
        }
        entity.setConfirmReason(command.getConfirmReason());
        entity.setUpdaterId(tokenInfo.getUserBaseId());
        Date now = new Date();
        entity.setConfirmDate(now);
        entity.setUpdatedDate(now);
        return entity;
    }

    public static TenderPackagePurchaseDetailDTO purchaseDetail(List<AttachmentEntity> attachmentEntityList, TenderPackagePurchaseEntity tenderPackageTimeEntity) {
        TenderPackagePurchaseDetailDTO tenderPackagePurchaseDetailDTO = new TenderPackagePurchaseDetailDTO();
        if (CollectionUtil.isNotEmpty(attachmentEntityList)) {
            tenderPackagePurchaseDetailDTO.setAttachmentDTOList(BeanUtil.copyToList(attachmentEntityList, TenderPackagePurchaseDetailDTO.AttachmentDTO.class));
        }
        BeanUtil.copyProperties(tenderPackageTimeEntity, tenderPackagePurchaseDetailDTO);
        return tenderPackagePurchaseDetailDTO;
    }

    public static BtConfirmRecordEntity buildConfirmRecordInsert(TenderPackagePurchaseEntity entity, TenderPackagePurchaseEntity buildEntity) {
        BtConfirmRecordEntity recordEntity = new BtConfirmRecordEntity();
        recordEntity.setId(idUtil.generateId(BiddingConstant.APP_NAME));
        recordEntity.setTenderPackageId(entity.getRelTenderPackageId());
        recordEntity.setSupplierId(entity.getSupplierId());
        recordEntity.setConfirmType(ConfirmTypeEnum.TYPE_20.getCode());
        recordEntity.setConfirmUserId(buildEntity.getConfirmUserId());
        recordEntity.setConfirmUserName(buildEntity.getConfirmUserName());
        recordEntity.setConfirmTime(buildEntity.getConfirmDate());
        recordEntity.setConfirmStatus(buildEntity.getConfirmStatus());
        recordEntity.setConfirmRejectReason(buildEntity.getConfirmReason());
        recordEntity.setSubmitTime(entity.getPurchaseTime());
        recordEntity.setCreatorId(buildEntity.getConfirmUserId());
        recordEntity.setCreateTime(new Date());
        recordEntity.setLastUpdaterId(buildEntity.getConfirmUserId());
        recordEntity.setLastUpdateTime(new Date());
        recordEntity.setDelFlag(0);
        recordEntity.setConfirmResult(buildEntity.getPurchaseStatus());
        return recordEntity;
    }

    public static AttachmentEntity buildAttachmentInsert(BtConfirmRecordEntity recordEntity, AttachmentEntity entity) {
        AttachmentEntity attachment = new AttachmentEntity();
        attachment.setBusinessId(String.valueOf(recordEntity.getId()));
        attachment.setAttachmentType(AttachmentTypeEnum.TENDER_DOC_PAYMENT.getType());
        attachment.setFileSize(entity.getFileSize());
        attachment.setFileName(entity.getFileName());
        attachment.setFileUrl(entity.getFileUrl());
        attachment.setCreatorId(recordEntity.getCreatorId());
        attachment.setUpdaterId(recordEntity.getLastUpdaterId());
        attachment.setCreatedDate(recordEntity.getCreateTime());
        attachment.setUpdatedDate(recordEntity.getLastUpdateTime());
        return attachment;
    }
}
