package com.wanlianyida.bidding.application.assembler;

import cn.hutool.core.date.DateTime;
import com.wanlianyida.bidding.domain.bidding.model.condition.BiddingRoundCondition;
import com.wanlianyida.bidding.domain.bidding.model.entity.BiddingRoundEntity;
import com.wanlianyida.bidding.infrastructure.enums.SyncFlagEnum;
import com.wanlianyida.bidding.infrastructure.event.TenderTimeUpdateEvent;

/**
 * 投标轮次
 */
public class BiddingRoundAssembler {

    /**
     * 处理开标、评标，查询轮次的condition
     */
    public static BiddingRoundCondition buildSyncJobCondition() {
        BiddingRoundCondition condition = new BiddingRoundCondition();
        // 未同步
        condition.setSyncFlag(SyncFlagEnum.FLAG_0.getFlag());
        // 当前时间
        condition.setLessBidEndTime(DateTime.now());
        return condition;
    }

    /**
     * 处理开标、评标，更新字段内容实体
     */
    public static BiddingRoundEntity buildSyncJobUpdateEntity() {
        BiddingRoundEntity entity = new BiddingRoundEntity();
        // 已经同步
        entity.setSyncFlag(SyncFlagEnum.FLAG_1.getFlag());
        return entity;
    }

    public static BiddingRoundEntity buildBiddingRoundEntity(TenderTimeUpdateEvent event) {
        BiddingRoundEntity biddingRoundEntity = new BiddingRoundEntity();
        biddingRoundEntity.setBidEndTime(event.getBidEndTime());
        biddingRoundEntity.setBidOpenTime(event.getBidOpeningTime());
        return biddingRoundEntity;
    }

    public static BiddingRoundCondition buildBiddingRoundCondition(TenderTimeUpdateEvent event) {
        BiddingRoundCondition condition = new BiddingRoundCondition();
        condition.setPackageId(event.getPackageId());
        condition.setBidRound(1);
        return condition;
    }

}
