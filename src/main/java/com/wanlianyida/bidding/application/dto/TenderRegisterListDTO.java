package com.wanlianyida.bidding.application.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报名列表
 */
@Data
public class TenderRegisterListDTO implements Serializable {
    private static final long serialVersionUID = -8096679205036433226L;

    /**
     * 标包id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String relTenderPackageId;

    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 采购方式 10公开竞谈 20邀请竞谈
     */
    private String purchaseMethod;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 状态 10未报名 20待确认 30确认通过 40确认不通过 40已失效
     */
    private Integer registerStatus;

    /**
     * 标书售卖开始时间
     */
    private Date saleStartTime;

    /**
     * 标书售卖截止时间
     */
    private Date saleEndTime;

    /**
     * 投标截止时间
     */
    private Date bidEndTime;

    /**
     * 报名时间
     */
    private Date registerTime;

    /**
     * 确认时间
     */
    private Date confirmDate;
    
}
