package com.wanlianyida.bidding.application.dto.tenderpackageissue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 标包-内容信息DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenderPackageContentDTO {

    /**
     * 标包ID
     */
    private String id;
    /**
     * 类型
     * "ch_information_invitation", "招标-邀请函"
     * "ch_information_tender_notice", "招标公告"
     * "ch_information_candidate_notice", "候选人公示"
     * "ch_information_bid_notice", "中标公示"
     */
    private String contentType;
    /**
     * 标题
     */
    private String tittle;
    /**
     * 内容摘要
     */
    private String contentSummary;
    /**
     * 内容详情
     */
    private String contentText;
}
