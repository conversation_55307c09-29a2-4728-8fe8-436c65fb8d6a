package com.wanlianyida.bidding.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月16日 16:43
 */
@Data
public class ProjectPackageListDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty("项目id")
    private Long relProjectId;

    @ApiModelProperty("标包名称")
    private String tenderPackageName;

    @ApiModelProperty("备注名称")
    private String remark;

    @ApiModelProperty("预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty("投标保证金")
    private BigDecimal marginAmount;

    @ApiModelProperty("采购方式 10公开竞谈 20邀请竞谈")
    private String purchaseMethod;

    @ApiModelProperty("是否线下递交投标文件 0否 1是")
    private Integer offlineSubmit;

    @ApiModelProperty("招标负责人id")
    private String tenderLeaderId;

    @ApiModelProperty("招标负责人姓名")
    private String tenderLeaderName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    @ApiModelProperty("标包阶段 10立项、20发标、30开标、40评标、50中标")
    private Integer tenderStage;

    @ApiModelProperty("标包编号")
    private String tenderNo;

    /**
     * 标包子阶段 100待立项、200发标、210供应商应答、500中标结果确定、510结束
     */
    private Integer tenderSubStage;

    /**
     * 是否含税 0否 1是
     */
    private Integer taxFlag;
}
