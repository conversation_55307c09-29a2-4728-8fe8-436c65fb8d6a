package com.wanlianyida.bidding.application.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClarifySupplierDTO {
    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商账号
     */
    private String supplierAccount;

    /**
     * 供应商应答id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierResponseId;

    /**
     * 供应商应答状态
     */
    private Integer supplierResponseStatus;
}
