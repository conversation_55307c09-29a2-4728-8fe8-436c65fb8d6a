package com.wanlianyida.bi.application.service;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.bi.application.assembler.CtpsPlatCommodityPriceInfoAssembler;
import com.wanlianyida.bi.domain.model.condition.CtpsPlatCommodityPriceInfoCondition;
import com.wanlianyida.bi.domain.model.entity.AdsCtpsPlatCommodityPriceInfoHfEntity;
import com.wanlianyida.bi.domain.repository.AdsCtpsPlatCommodityPriceInfoHfRepository;
import com.wanlianyida.bi.interfaces.facade.dto.AdsCtpsPlatCommodityPriceInfoHfDTO;
import com.wanlianyida.bi.interfaces.facade.query.CtpsPlatCommodityPriceInfoQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class AdsCtpsPlatCommodityPriceInfoHfAppService {

    @Autowired
    private AdsCtpsPlatCommodityPriceInfoHfRepository commodityPriceInfoHfRepository;


    public ResultMode<List<AdsCtpsPlatCommodityPriceInfoHfDTO>> queryPageList(PagingInfo<CtpsPlatCommodityPriceInfoQuery> pagingInfo) {
        CtpsPlatCommodityPriceInfoCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), CtpsPlatCommodityPriceInfoCondition.class);

        Page<Object> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, true);
        PageHelper.orderBy("prd_code desc");
        CtpsPlatCommodityPriceInfoAssembler.assembleCondition( condition);
        List<AdsCtpsPlatCommodityPriceInfoHfEntity> entityList = commodityPriceInfoHfRepository.queryPageList(condition);
        List<AdsCtpsPlatCommodityPriceInfoHfDTO> dtoList = BeanUtil.copyToList(entityList, AdsCtpsPlatCommodityPriceInfoHfDTO.class);
        return ResultMode.successPageList(dtoList, (int) page.getTotal());
    }

    public ResultMode<AdsCtpsPlatCommodityPriceInfoHfDTO> querySynTime() {
        AdsCtpsPlatCommodityPriceInfoHfDTO dto = commodityPriceInfoHfRepository.querySynTime();
        return ResultMode.success(dto);
    }
}
