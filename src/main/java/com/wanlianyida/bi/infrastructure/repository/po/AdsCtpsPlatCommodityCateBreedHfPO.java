package com.wanlianyida.bi.infrastructure.repository.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 11种品类和商品品种的对应表,商品贸易平台 | 货品 | 钢联货品分类表
 *
 * @TableName ads_ctps_plat_commodity_cate_breed_hf
 */
@Data
@TableName("ads_ctps_plat_commodity_cate_breed_hf")
public class AdsCtpsPlatCommodityCateBreedHfPO implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String cateName;

    /**
     * 品种编码
     */
    @ApiModelProperty("品种编码")
    private String breedCode;

    /**
     * 品种名称
     */
    @ApiModelProperty("品种名称")
    private String breedName;

    /**
     * 品种简称
     */
    @ApiModelProperty("品种简称")
    private String breedShortName;

    /**
     * 材质编码
     */
    @ApiModelProperty("材质编码")
    private String mqCode;

    /**
     * 材质名称
     */
    @ApiModelProperty("材质名称")
    private String mqName;

    /**
     * 材质简称
     */
    @ApiModelProperty("材质简称")
    private String mqShortName;

    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String scCode;

    /**
     * 规格名称
     */
    @ApiModelProperty("规格名称")
    private String scName;

    /**
     * 规格简称
     */
    @ApiModelProperty("规格简称")
    private String scShortName;

    /**
     * 数据同步时间（datetime）
     */
    @ApiModelProperty("数据同步时间（datetime）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date synTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;

    /**
     * 最后更新人
     */
    @ApiModelProperty("最后更新人")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @ApiModelProperty("最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedDate;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer versionCode;

    /**
     * 删除标志[0-正常,1-删除]
     */
    @ApiModelProperty("删除标志[0-正常,1-删除]")
    private Integer delFlag;

}
