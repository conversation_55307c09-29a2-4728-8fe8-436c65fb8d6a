package com.wanlianyida.bi.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wanlianyida.bi.domain.model.condition.AdsCtpsPlatNewsMysteelRptDiCondition;
import com.wanlianyida.bi.domain.model.entity.AdsCtpsPlatCommodityCateBreedHfEntity;
import com.wanlianyida.bi.domain.model.entity.AdsCtpsPlatNewsMysteelRptDiEntity;
import com.wanlianyida.bi.domain.repository.AdsCtpsPlatNewsMysteelRptDiRepository;
import com.wanlianyida.bi.infrastructure.repository.mapper.AdsCtpsPlatNewsMysteelRptDiMapper;
import com.wanlianyida.bi.infrastructure.repository.po.AdsCtpsPlatNewsMysteelRptDiPO;
import com.wanlianyida.bi.interfaces.facade.query.AdsCtpsPlatNewsMysteelRptDiDetailQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class AdsCtpsPlatNewsMysteelRptDiServiceImpl implements AdsCtpsPlatNewsMysteelRptDiRepository {

    @Autowired
    private AdsCtpsPlatNewsMysteelRptDiMapper adsCtpsPlatNewsMysteelRptDiMapper;
    @Override
    public List<AdsCtpsPlatNewsMysteelRptDiEntity> queryPageList(AdsCtpsPlatNewsMysteelRptDiCondition condition) {
        LambdaQueryWrapper<AdsCtpsPlatNewsMysteelRptDiPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StrUtil.isNotBlank(condition.getCateName()), AdsCtpsPlatNewsMysteelRptDiPO::getCateName, condition.getCateName());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getRptType()), AdsCtpsPlatNewsMysteelRptDiPO::getRptType, condition.getRptType());
        queryWrapper.like(StrUtil.isNotBlank(condition.getTitle()), AdsCtpsPlatNewsMysteelRptDiPO::getTitle,condition.getTitle());
        queryWrapper.between(ObjUtil.isNotEmpty(condition.getStartPublishTime()) && ObjUtil.isNotEmpty(condition.getEndPublishTime()), AdsCtpsPlatNewsMysteelRptDiPO::getPublishTime, condition.getStartPublishTime()+" 00:00:00", condition.getEndPublishTime()+" 23:59:59");
        queryWrapper.eq(AdsCtpsPlatNewsMysteelRptDiPO::getDelFlag, 0);
        List<AdsCtpsPlatNewsMysteelRptDiPO> list = adsCtpsPlatNewsMysteelRptDiMapper.selectList(queryWrapper);
        List<AdsCtpsPlatNewsMysteelRptDiEntity> entityList = BeanUtil.copyToList(list, AdsCtpsPlatNewsMysteelRptDiEntity.class);
        return entityList;
    }

    @Override
    public AdsCtpsPlatNewsMysteelRptDiEntity queryDetail(AdsCtpsPlatNewsMysteelRptDiDetailQuery detailQuery) {

        LambdaQueryWrapper<AdsCtpsPlatNewsMysteelRptDiPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StrUtil.isNotBlank(detailQuery.getTitle()), AdsCtpsPlatNewsMysteelRptDiPO::getTitle,detailQuery.getTitle());
        queryWrapper.eq(AdsCtpsPlatNewsMysteelRptDiPO::getPublishTime,detailQuery.getPublishTime());

        AdsCtpsPlatNewsMysteelRptDiPO platNewsMysteelRptDiPO = adsCtpsPlatNewsMysteelRptDiMapper.selectOne(queryWrapper);
        if (platNewsMysteelRptDiPO != null) {
            AdsCtpsPlatNewsMysteelRptDiEntity entity = BeanUtil.copyProperties(platNewsMysteelRptDiPO, AdsCtpsPlatNewsMysteelRptDiEntity.class);
            return entity;
        }
        return null;
    }

    @Override
    public List<AdsCtpsPlatCommodityCateBreedHfEntity> queryClassify() {
        LambdaQueryWrapper<AdsCtpsPlatNewsMysteelRptDiPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(AdsCtpsPlatNewsMysteelRptDiPO::getCateName);
        queryWrapper.groupBy(AdsCtpsPlatNewsMysteelRptDiPO::getCateName);
        List<AdsCtpsPlatNewsMysteelRptDiPO> adsCtpsPlatNewsMysteelRptDiPOS = adsCtpsPlatNewsMysteelRptDiMapper.selectList(queryWrapper);
        List<AdsCtpsPlatCommodityCateBreedHfEntity> entityList = BeanUtil.copyToList(adsCtpsPlatNewsMysteelRptDiPOS, AdsCtpsPlatCommodityCateBreedHfEntity.class);
        return entityList;
    }
}
