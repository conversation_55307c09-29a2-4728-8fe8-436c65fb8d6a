package com.wanlianyida.support.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wanlianyida.support.domain.model.bo.AutoAuditBiddingConfigBO;
import com.wanlianyida.support.domain.model.condition.BdAutoAuditConfigCondition;
import com.wanlianyida.support.domain.model.entity.BdAutoAuditConfigEntity;
import com.wanlianyida.support.domain.repository.IBdAutoAuditConfigRepository;
import com.wanlianyida.support.infrastructure.repository.mapper.BdAutoAuditConfigMapper;
import com.wanlianyida.support.infrastructure.repository.po.BdAutoAuditConfigPO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <p>
 * 自动审核-配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Service
public class BdAutoAuditConfigServiceImpl implements IBdAutoAuditConfigRepository {

    @Resource
    private BdAutoAuditConfigMapper bdAutoAuditConfigMapper;

    @Override
    public Boolean add(BdAutoAuditConfigEntity bdAutoAuditConfigEntity) {
        return bdAutoAuditConfigMapper.insert(BeanUtil.toBean(bdAutoAuditConfigEntity, BdAutoAuditConfigPO.class)) > 0;
    }

    @Override
    public Boolean update(BdAutoAuditConfigEntity bdAutoAuditConfigEntity) {
        //更新库存
        LambdaUpdateWrapper<BdAutoAuditConfigPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(BdAutoAuditConfigPO::getId, bdAutoAuditConfigEntity.getId());
        updateWrapper.set(BdAutoAuditConfigPO::getAutoAudit, bdAutoAuditConfigEntity.getAutoAudit());
        return bdAutoAuditConfigMapper.update(null, updateWrapper) >0;
    }

    @Override
    public List<BdAutoAuditConfigEntity> queryCondition(BdAutoAuditConfigCondition condition) {
        LambdaQueryWrapper<BdAutoAuditConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(condition.getAuditType()), BdAutoAuditConfigPO::getAuditType, condition.getAuditType());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getSceneType()), BdAutoAuditConfigPO::getSceneType, condition.getSceneType());
        queryWrapper.eq(ObjUtil.isNotNull(condition.getAutoAudit()), BdAutoAuditConfigPO::getAutoAudit, condition.getAutoAudit());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getBizType()), BdAutoAuditConfigPO::getBizType, condition.getBizType());
        queryWrapper.in(ObjUtil.isNotEmpty(condition.getSceneTypeList()), BdAutoAuditConfigPO::getSceneType, condition.getSceneTypeList());
        queryWrapper.orderByAsc(BdAutoAuditConfigPO::getAuditType).orderByAsc(BdAutoAuditConfigPO::getSceneType);
        return BeanUtil.copyToList(bdAutoAuditConfigMapper.selectList(queryWrapper), BdAutoAuditConfigEntity.class);
    }

    @Override
    public List<String> queryAutoAuditConfig(List<BdAutoAuditConfigCondition> poList) {
        List<AutoAuditBiddingConfigBO> autoAuditBiddingConfigBOS = bdAutoAuditConfigMapper.queryAutoAuditConfig(poList);
        return autoAuditBiddingConfigBOS.stream().map(AutoAuditBiddingConfigBO::getSceneType).collect(Collectors.toList());
    }
}
