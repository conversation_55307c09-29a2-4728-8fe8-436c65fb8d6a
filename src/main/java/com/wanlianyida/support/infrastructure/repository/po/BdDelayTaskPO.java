package com.wanlianyida.support.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 延迟消息表
 * @TableName bd_delay_task
 */
@TableName(value ="bd_delay_task")
@Data
public class BdDelayTaskPO{
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务类型
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 业务id
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 执行时间
     */
    @TableField("exec_time")
    private Date execTime;

    /**
     * 执行状态:[0-待执行,1-执行中,2-已执行]
     */
    @TableField("exec_status")
    private Integer execStatus;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField("created_date")
    private Date createdDate;

    /**
     * 最后更新人
     */
    @TableField("updater_id")
    private String updaterId;

    /**
     * 最后更新时间
     */
    @TableField("updated_date")
    private Date updatedDate;

    /**
     * 版本号
     */
    @TableField("version_code")
    private Integer versionCode;

    /**
     * 删除标识
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 请求类型
     */
    @TableField("call_type")
    private Integer callType;

    /**
     * 请求地址
     */
    @TableField("call_url")
    private String callUrl;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;
}
