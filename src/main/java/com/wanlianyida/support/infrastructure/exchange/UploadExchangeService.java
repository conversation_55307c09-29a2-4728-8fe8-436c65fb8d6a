package com.wanlianyida.support.infrastructure.exchange;

import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.file.query.FileUrlQuery;
import com.wanlianyida.file.query.FileUrlsQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月29日 16:28
 */
@Service
public class UploadExchangeService {

    @Resource
    private IUploadService uploadService;

    /**
     * 批量url转换
     */
    public Map<String, String> convertUrlList(List<String> urlList) {
        FileUrlsQuery query = new FileUrlsQuery();
        query.setUrls(urlList);
        ResponseMessage<Map<String, String>> result = uploadService.getUrls(query);
        return result.getModel();
    }

    /**
     * 批量url转换(缩略)
     */
    public Map<String, String> convertThumbnailUrlList(List<String> urlList) {
        FileUrlsQuery query = new FileUrlsQuery();
        query.setUrls(urlList);
        ResponseMessage<Map<String, String>> result = uploadService.getThumbnailUrls(query);
        return result.getModel();
    }

    /**
     * url转换(原图)
     */
    public String getUrl(String url) {
        FileUrlQuery query = new FileUrlQuery();
        query.setUrl(url);
        ResponseMessage<String> result = uploadService.getUrl(query);
        return result.getModel();
    }
}
