package com.wanlianyida.support.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.ctp.starters.rocketmq.MqEventPublisher;
import com.wanlianyida.framework.ctpcommon.constant.CommonTopicConstants;
import com.wanlianyida.framework.ctpcommon.entity.MqEventMessage;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.api.model.command.MsgCommand;
import com.wanlianyida.support.api.model.enums.MsgTemplateEnum;
import com.wanlianyida.support.application.assembler.BdUserFeedbackAppAssembler;
import com.wanlianyida.support.domain.model.condition.BdAttachmentCondition;
import com.wanlianyida.support.domain.model.condition.BdUserFeedbackCondition;
import com.wanlianyida.support.domain.model.condition.BdUserFeedbackQueryCondition;
import com.wanlianyida.support.domain.model.entity.BdAttachmentEntity;
import com.wanlianyida.support.domain.model.entity.BdUserFeedbackEntity;
import com.wanlianyida.support.domain.service.BdAttachmentDomainService;
import com.wanlianyida.support.domain.service.BdUserFeedbackDomainService;
import com.wanlianyida.support.interfaces.model.command.BdAttachmentAddCommand;
import com.wanlianyida.support.interfaces.model.command.BdUserFeedbackAddCommand;
import com.wanlianyida.support.interfaces.model.command.BdUserFeedbackAndAttachAddCommand;
import com.wanlianyida.support.interfaces.model.command.BdUserFeedbackAndAttachReplyUpdateCommand;
import com.wanlianyida.support.interfaces.model.dto.BdAttachmentDTO;
import com.wanlianyida.support.interfaces.model.dto.BdUserFeedbackAndAttachDetailDTO;
import com.wanlianyida.support.interfaces.model.dto.BdUserFeedbackDTO;
import com.wanlianyida.support.interfaces.model.query.BdUserFeedbackQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @createDate 2025-05-22 17:17:59
 */
@Service
@Slf4j
public class BdUserFeedbackAppService {

    @Resource
    private BdUserFeedbackDomainService bdUserFeedbackDomainService;

    @Resource
    private BdAttachmentDomainService bdAttachmentDomainService;

    @Resource
    private MqEventPublisher mqEventPublisher;

    @Transactional(rollbackFor = Exception.class)
    public void add(BdUserFeedbackAndAttachAddCommand command) {
        BdUserFeedbackAddCommand bdUserFeedbackAddCommand = command.getBdUserFeedbackAddCommand();
        BdUserFeedbackEntity feedbackEntity = BdUserFeedbackAppAssembler.buildBdUserFeedbackEntity(bdUserFeedbackAddCommand);

        feedbackEntity = bdUserFeedbackDomainService.add(feedbackEntity);
        Long feedbackId = feedbackEntity.getId();
        List<BdAttachmentAddCommand> attachmentAddCommandList = command.getAttachmentAddCommandList();
        if(CollUtil.isNotEmpty(attachmentAddCommandList)){
            List<BdAttachmentEntity> bdAttachmentEntities = BdUserFeedbackAppAssembler.buildBdAttachmentEntity(attachmentAddCommandList, String.valueOf(feedbackId));
            bdAttachmentDomainService.addBatch(bdAttachmentEntities);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateReplyFeedback(BdUserFeedbackAndAttachReplyUpdateCommand command) {

        BdUserFeedbackCondition feedbackCondition = BdUserFeedbackAppAssembler.buildBdUserFeedbackCondition(command.getFeedbackReplyUpdateCommand());
        bdUserFeedbackDomainService.updateReplyFeedback(feedbackCondition);
        if(CollUtil.isNotEmpty(command.getAttachmentAddCommandList())){
            List<BdAttachmentEntity> bdAttachmentEntities = BdUserFeedbackAppAssembler.buildBdAttachmentEntity(command.getAttachmentAddCommandList(), String.valueOf(feedbackCondition.getId()));
            bdAttachmentDomainService.addBatch(bdAttachmentEntities);
        }
        //发送站内信
        BdUserFeedbackEntity feedbackEntity = bdUserFeedbackDomainService.getDetail(command.getFeedbackReplyUpdateCommand().getId());
        MsgCommand msgCommand = MsgCommand.builder()
                .templateId(MsgTemplateEnum.COMPLAINT_SUGGESTION_REPLIED.getId())
               // .companyId(feedbackEntity.getFeedbackCompanyId())
                .sceneType("10")
                .targetUserBaseIdList(Arrays.asList(feedbackEntity.getFeedbackUserId()))
                .addParam("msgType", "personal")
                .build();
        mqEventPublisher.syncSendNormalMessage(MqEventMessage.buildEventMessage(CommonTopicConstants.BIZ_MSG_TOPIC, msgCommand));
    }

    public BdUserFeedbackAndAttachDetailDTO getDetail(@Valid BdUserFeedbackQuery query) {
        Long feedbackId = query.getId();
        BdUserFeedbackEntity feedbackEntity = bdUserFeedbackDomainService.getDetail(feedbackId);
        BdAttachmentCondition attachmentCondition = new BdAttachmentCondition();
        attachmentCondition.setBizNo(feedbackId.toString());
        List<BdAttachmentEntity> bdAttachmentEntities = bdAttachmentDomainService.getByBizNo(attachmentCondition);

        BdUserFeedbackAndAttachDetailDTO detailDTO = new BdUserFeedbackAndAttachDetailDTO();
        detailDTO.setBdUserFeedbackDTO(BeanUtil.toBean(feedbackEntity, BdUserFeedbackDTO.class));
        detailDTO.setAttachmentList(BeanUtil.copyToList(bdAttachmentEntities, BdAttachmentDTO.class));
        return detailDTO;
    }

    public ResultMode<List<BdUserFeedbackDTO>> pageList(PagingInfo<BdUserFeedbackQuery> pagingInfo) {
        BdUserFeedbackQueryCondition condition = BeanUtil.toBean(pagingInfo.getFilterModel(), BdUserFeedbackQueryCondition.class);
        condition = BdUserFeedbackAppAssembler.buildPageListCondition(condition);
        PagingInfo<BdUserFeedbackQueryCondition> conditionPage = new PagingInfo<>(condition,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.getCountTotal());
        PageInfo<BdUserFeedbackEntity> page = bdUserFeedbackDomainService.pageList(conditionPage);
        List<BdUserFeedbackDTO> dtoList = BeanUtil.copyToList(page.getList(), BdUserFeedbackDTO.class);
        return ResultMode.successPageList(dtoList, (int) page.getTotal());
    }
}




