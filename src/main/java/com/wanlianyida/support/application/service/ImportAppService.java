package com.wanlianyida.support.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.application.assembler.ImportAssembler;
import com.wanlianyida.support.domain.model.bo.ParseFileBO;
import com.wanlianyida.support.domain.service.strategy.imports.GenerateTemplateHandler;
import com.wanlianyida.support.domain.service.strategy.imports.ParseFileHandler;
import com.wanlianyida.support.infrastructure.enums.ImportEnum;
import com.wanlianyida.support.infrastructure.exception.CtpCoreSupportExceptionEnum;
import com.wanlianyida.support.interfaces.model.command.GenerateTemplateCommand;
import com.wanlianyida.support.interfaces.model.command.ParseFileCommand;
import com.wanlianyida.support.interfaces.model.dto.GenerateTemplateDTO;
import com.wanlianyida.support.interfaces.model.dto.ParseFileDTO;
import org.springframework.stereotype.Service;

import java.util.Map;

import javax.annotation.Resource;

@Service
public class ImportAppService {

    @Resource
    private Map<String, GenerateTemplateHandler> generateTemplateHandlerMap;

    @Resource
    private Map<String, ParseFileHandler> parseFileHandlerMap;


    /**
     * 条件查询
     */
    public ResultMode<GenerateTemplateDTO> generateTemplate(GenerateTemplateCommand command) {
        GenerateTemplateHandler generateTemplateHandler = this.generateTemplateHandlerMap.get(ImportEnum.getHandlerByType(command.getBizType()));
        if(ObjUtil.isNull(generateTemplateHandler)){
            return ResultMode.fail(CtpCoreSupportExceptionEnum.PURCHASE_IMPORT_MSG_325.getCode(), CtpCoreSupportExceptionEnum.PURCHASE_IMPORT_MSG_325.getMsg());
        }
        return ResultMode.success(generateTemplateHandler.generateTemplate(ImportAssembler.buildGenerateTemplateBO(command)));
    }


    /**
     * 解析文件
     */
    public ResultMode<ParseFileDTO> parseFile(ParseFileCommand command) {
        ParseFileHandler parseFileHandler = this.parseFileHandlerMap.get(ImportEnum.getHandlerByType(command.getBizType()));
        if(ObjUtil.isNull(parseFileHandler)){
            return ResultMode.fail(CtpCoreSupportExceptionEnum.PURCHASE_IMPORT_MSG_325.getCode(), CtpCoreSupportExceptionEnum.PURCHASE_IMPORT_MSG_325.getMsg());
        }
        return parseFileHandler.parseFile(BeanUtil.copyProperties(command, ParseFileBO.class));
    }
}
