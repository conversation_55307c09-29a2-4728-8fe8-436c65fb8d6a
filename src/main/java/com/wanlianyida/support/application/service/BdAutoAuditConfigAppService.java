package com.wanlianyida.support.application.service;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.application.assembler.AutoAuditRuleAppAssembler;
import com.wanlianyida.support.domain.model.condition.BdAutoAuditConfigCondition;
import com.wanlianyida.support.domain.model.entity.BdAutoAuditConfigEntity;
import com.wanlianyida.support.domain.service.BdAutoAuditConfigDomainService;
import com.wanlianyida.support.domain.service.BdAutoAuditRuleDetailDomainService;
import com.wanlianyida.support.domain.service.BdAutoAuditRuleDomainService;
import com.wanlianyida.support.infrastructure.enums.AutoConfigBizTypeEnum;
import com.wanlianyida.support.interfaces.model.command.AutoAuditBiddingConfigAddCommand;
import com.wanlianyida.support.interfaces.model.command.AutoAuditConfigAddCommand;
import com.wanlianyida.support.interfaces.model.command.AutoAuditConfigUpdateCommand;
import com.wanlianyida.support.interfaces.model.dto.AutoAuditBiddingConfigListDTO;
import com.wanlianyida.support.interfaces.model.dto.AutoAuditConfigListDTO;
import com.wanlianyida.support.interfaces.model.query.AutoAuditBiddingConfigListQuery;
import com.wanlianyida.support.interfaces.model.query.AutoAuditConfigListQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <p>
 * 自动审核-配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Service
@Slf4j
public class BdAutoAuditConfigAppService {

    @Resource
    private BdAutoAuditConfigDomainService bdAutoAuditConfigDomainService;

    @Resource
    private BdAutoAuditRuleDomainService bdAutoAuditRuleDomainService;;

    @Resource
    private BdAutoAuditRuleDetailDomainService bdAutoAuditRuleDetailDomainService;

    public Boolean update(AutoAuditConfigUpdateCommand updateCommand) {
        BdAutoAuditConfigEntity bdAutoAuditConfigEntity = BeanUtil.copyProperties(updateCommand, BdAutoAuditConfigEntity.class);
        return bdAutoAuditConfigDomainService.update(bdAutoAuditConfigEntity);
    }

    public Boolean add(AutoAuditConfigAddCommand addCommand) {
        BdAutoAuditConfigEntity bdAutoAuditConfigEntity = BeanUtil.copyProperties(addCommand, BdAutoAuditConfigEntity.class);
        return bdAutoAuditConfigDomainService.add(bdAutoAuditConfigEntity);
    }

    public ResultMode<List<AutoAuditConfigListDTO>> queryCondition(PagingInfo<AutoAuditConfigListQuery> query) {
        Page<List<AutoAuditConfigListDTO>> page = PageHelper.startPage(query.currentPage, query.pageLength, query.getCountTotal());
        BdAutoAuditConfigCondition condition = BeanUtil.copyProperties(query.getFilterModel(), BdAutoAuditConfigCondition.class);
        condition.setBizType(AutoConfigBizTypeEnum.HOME_PAGE.getCode());
        List<BdAutoAuditConfigEntity> bdAutoAuditConfigEntities = bdAutoAuditConfigDomainService.queryCondition(condition);
        List<AutoAuditConfigListDTO> autoAuditConfigListDTOS = BeanUtil.copyToList(bdAutoAuditConfigEntities, AutoAuditConfigListDTO.class);
        return ResultMode.successPageList(autoAuditConfigListDTOS, (int) page.getTotal());
    }

    public ResultMode<List<AutoAuditConfigListDTO>> listConfig(AutoAuditConfigListQuery query) {
        BdAutoAuditConfigCondition condition = BeanUtil.copyProperties(query, BdAutoAuditConfigCondition.class);
        List<BdAutoAuditConfigEntity> bdAutoAuditConfigEntities = bdAutoAuditConfigDomainService.queryCondition(condition);
        List<AutoAuditConfigListDTO> autoAuditConfigListDTOS = BeanUtil.copyToList(bdAutoAuditConfigEntities, AutoAuditConfigListDTO.class);
        return ResultMode.success(autoAuditConfigListDTOS);
    }

    public ResultMode<List<AutoAuditBiddingConfigListDTO>> queryBiddingConfigList(AutoAuditBiddingConfigListQuery query) {
        List<BdAutoAuditConfigCondition> condition = AutoAuditRuleAppAssembler.buildQueryBiddingConfigCondition(query);
        List<String> sTList = bdAutoAuditConfigDomainService.queryAutoAuditConfig(condition);
        return ResultMode.success(AutoAuditRuleAppAssembler.buildQueryBiddingConfigRes(query, sTList));
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addBiddingConfig(List<AutoAuditBiddingConfigAddCommand> addCommand) {
        List<BdAutoAuditConfigEntity> bdAutoAuditConfigEntities = bdAutoAuditConfigDomainService.queryCondition(AutoAuditRuleAppAssembler.buildBdAutoAuditConfigCondition(addCommand));
        Map<String, List<BdAutoAuditConfigEntity>> autoConfigBizTypeEnumMap = bdAutoAuditConfigEntities.stream().collect(Collectors.groupingBy(BdAutoAuditConfigEntity::getSceneType));
        addCommand.forEach(item -> {
            Long ruleId = bdAutoAuditRuleDomainService.addBiddingRule(AutoAuditRuleAppAssembler.buildBdAutoAuditConfig(autoConfigBizTypeEnumMap, item.getSceneType()));
            if (item.getAutoAudit() == 1) {
                bdAutoAuditRuleDomainService.closeBiddingRule(AutoAuditRuleAppAssembler.buildAuditRuleDetailMap(ruleId, item.getSceneType()));
            } else {
                bdAutoAuditRuleDomainService.openBiddingRule(AutoAuditRuleAppAssembler.buildAuditRuleDetailMap(ruleId, item.getSceneType()));
            }
        });
        return true;
    }

}
