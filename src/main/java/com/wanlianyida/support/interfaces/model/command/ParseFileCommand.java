package com.wanlianyida.support.interfaces.model.command;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 导入command
 * <AUTHOR>
 */
@Data
public class ParseFileCommand {
    /**
     * 文件地址
     */
    @NotBlank(message = "文件地址不能为空")
    private String url;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 模板基础配置信息
     */
    @NotNull(message = "模板基础配置信息不能为空")
    @Valid
    private TemplateConfig config;

    /**
     * 固定表头信息
     */
    private List<CellInfo> staticCellInfoBOList;

    /**
     * 非固定表头信息
     */
    private List<CellInfo> dynamicCellInfoBOList;

    /**
     * 特殊处理情况: 计量单位
     */
    private List<MeasurementUnit> measurementUnitList;

    @Data
    public static class CellInfo {
        /**
         * 单元格Map：key 为单元格的行数索引，value为单元格的名字
         */
        private Map<Integer, String> cellMap;

        /**
         * 下拉框信息
         */
        private SelectBoxInfo selectBoxInfo;
    }

    /**
     * 下拉框信息
     */
    @Data
    public static class SelectBoxInfo {
        /**
         * 下拉框生效的起始行
         */
        private Integer firstRow;

        /**
         * 下拉框生效的结束行
         */
        private Integer lastRow;

        /**
         * 是否显示错误信息
         */
        private Boolean showErrorBox = true;

        /**
         * 下拉框中的值（无需下拉框此值为空）
         */
        private List<String> selectBoxList;
    }

    /**
     * 模板基础配置信息
     */
    @Data
    public static class TemplateConfig{
        /**
         * 模板标题
         */
        private String title;

        /**
         * 模板sheet的名字
         */
        private String sheetName;

        /**
         * 生成的模板文件名字
         */
        private String templateName;
    }

    /**
     * 计量单位
     */
    @Data
    public static class MeasurementUnit{
        /**
         * 计量单位ID
         */
        private Long id;

        /**
         * 计价单位编码
         */
        private Integer pricingType;

        /**
         * 计量单位名称
         */
        private String pricingTypeName;

        /**
         * 小数点前位数
         */
        private Integer beforePoint;

        /**
         * '小数点后位数
         */
        private Integer afterPoint;

        /**
         * 最小值
         */
        private BigDecimal minValue;

        /**
         * 最大值
         */
        private BigDecimal maxValue;
    }

}

