package com.wanlianyida.support.interfaces.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* 用户反馈表
*/
@Data
public class BdUserFeedbackDTO implements Serializable {

    /**
    * 主键id
    */
    private Long id;

    /**
    * 反馈类型[字典suggestType]
    */
    private Integer feedbackType;

    /**
    * 反馈内容
    */
    private String feedbackContent;

    /**
    * 反馈用户id
    */
    private String feedbackUserId;

    /**
    * 反馈用户名称
    */
    private String feedbackUserName;

    /**
    * 反馈用户手机号
    */
    private String feedbackUserMobile;

    /**
    * 反馈企业id
    */
    private String feedbackCompanyId;

    /**
    * 反馈企业名称
    */
    private String feedbackCompanyName;

    /**
    * 回复状态[10-未回复,20-已回复]
    */
    private Integer replyStatus;

    /**
    * 回复内容
    */
    private String replyContent;

    /**
    * 回复用户id
    */
    private String replyUserId;

    /**
    * 回复用户名称
    */
    private String replyUserName;

    /**
    * 回复时间
    */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replyTime;

    /**
    * 创建人id
    */
    private String creatorId;

    /**
    * 创建时间
    */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
    * 最后更新人id
    */
    private String updaterId;

    /**
    * 最后更新时间
    */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
    * 删除标志[0-正常,1-删除]
    */
    private Integer delFlag;


}
