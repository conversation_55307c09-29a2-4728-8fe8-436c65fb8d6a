package com.wanlianyida.support.interfaces.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/02/25/09:01
 */
@Data
public class BdAutoAuditRuleAddCommand {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 配置id
     */
    @NotNull
    private String configId;

    /**
     * 规则类型 字典值=auto_rule_type
     */
    @NotNull
    private String ruleType;

    /**
     * 规则名称
     */
    @NotNull
    private String ruleName;

    /**
     * 规则值 字典值=
     * auto_rule_value
     * auto_rule_value_product
     * auto_rule_value_content
     */
    @NotNull
    private Integer ruleValue;

    /**
     * 规则名称
     */
    @NotNull
    private String ruleValueName;

    /**
     * 是否包含内部商家 1 包含 0 不包含
     */
    private Integer addInnerCompany;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 业务id集合
     */
    private List<String> busiIdList;
}
