package com.wanlianyida.support.interfaces.facade;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.support.application.service.BdAutoAuditConfigAppService;
import com.wanlianyida.support.interfaces.model.command.AutoAuditBiddingConfigAddCommand;
import com.wanlianyida.support.interfaces.model.command.AutoAuditConfigAddCommand;
import com.wanlianyida.support.interfaces.model.command.AutoAuditConfigUpdateCommand;
import com.wanlianyida.support.interfaces.model.dto.AutoAuditBiddingConfigListDTO;
import com.wanlianyida.support.interfaces.model.dto.AutoAuditConfigListDTO;
import com.wanlianyida.support.interfaces.model.query.AutoAuditBiddingConfigListQuery;
import com.wanlianyida.support.interfaces.model.query.AutoAuditConfigListQuery;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 自动审核-配置表
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@RestController
@RequestMapping("/autoAuditConfig")
public class BdAutoAuditConfigController {


    @Resource
    private BdAutoAuditConfigAppService autoAuditConfigAppService;


    /**
     * 新增
     * @param addCommand
     * @return
     */
    @PostMapping("/addConfig")
    public ResultMode add(@Validated @RequestBody AutoAuditConfigAddCommand addCommand) {
        if (autoAuditConfigAppService.add(addCommand)) {
            return ResultMode.success();
        }
        return ResultMode.fail();
    }

    /**
     * 更新
     *
     * @param updateCommand
     * @return
     */
    @PostMapping("/updateConfig")
    public ResultMode update(@RequestBody @Validated AutoAuditConfigUpdateCommand updateCommand) {
        if (autoAuditConfigAppService.update(updateCommand)) {
            return ResultMode.success();
        }
        return ResultMode.fail();
    }


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @PostMapping("/listPageConfig")
    public ResultMode<List<AutoAuditConfigListDTO>> list(@Validated @RequestBody PagingInfo<AutoAuditConfigListQuery> query) {
        return autoAuditConfigAppService.queryCondition(query);
    }

    /**
     *
     *
     * @param query
     * @return
     */
    @PostMapping("/listConfig")
    public ResultMode<List<AutoAuditConfigListDTO>> listConfig(@Validated @RequestBody AutoAuditConfigListQuery query) {
        return autoAuditConfigAppService.listConfig(query);
    }

    /**
     * 招投标配置查询
     * @param query
     * @return
     */
    @PostMapping("/queryBiddingConfigList")
    public ResultMode<List<AutoAuditBiddingConfigListDTO>> queryBiddingConfigList(@Validated @RequestBody AutoAuditBiddingConfigListQuery query) {
        return autoAuditConfigAppService.queryBiddingConfigList(query);
    }

    /**
     * 招投标配置新增
     * @param addCommand
     * @return
     */
    @PostMapping("/addBiddingConfig")
    public ResultMode<Void> addBiddingConfig(@Validated @RequestBody List<AutoAuditBiddingConfigAddCommand> addCommand) {
        if (autoAuditConfigAppService.addBiddingConfig(addCommand)) {
            return ResultMode.success();
        }
        return ResultMode.fail();
    }

}
