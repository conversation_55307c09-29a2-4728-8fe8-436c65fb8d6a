package com.wanlianyida.support.domain.model.condition;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 用户反馈表
*/
@Data
public class BdUserFeedbackCondition implements Serializable {

    /**
    * 主键id
    */
    private Long id;

    /**
    * 回复状态[10-未回复,20-已回复]
    */
    private Integer replyStatus;

    /**
    * 回复内容
    */
    private String replyContent;

    /**
    * 回复用户id
    */
    private String replyUserId;

    /**
    * 回复用户名称
    */
    private String replyUserName;

    /**
    * 回复时间
    */
    private Date replyTime;


    /**
    * 最后更新人id
    */
    private String updaterId;

    /**
    * 最后更新时间
    */
    private Date updatedDate;

}
