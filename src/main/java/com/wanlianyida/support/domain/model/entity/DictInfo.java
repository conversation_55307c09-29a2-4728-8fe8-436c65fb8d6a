package com.wanlianyida.support.domain.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 字典表(DictInfo)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:17
 */
@Data
@ApiModel("字典表")
public class DictInfo {
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("平台编号")
    private String platformCode;

    @ApiModelProperty("字典编码")
    private String dictCode;

    @ApiModelProperty("字典名")
    private String dictName;

    @ApiModelProperty("描述")
    private String dictDesc;

    @ApiModelProperty("是否删除，1是0否")
    private Integer deleted;

    @ApiModelProperty("创建人")
    private String creatorId;

    @ApiModelProperty("创建时间")
    private Date createdDate;

    @ApiModelProperty("最后更新人")
    private String updaterId;

    @ApiModelProperty("最后更新时间")
    private Date updatedDate;

    @ApiModelProperty("版本号")
    private Integer versionCode;

}

