package com.wanlianyida.support.domain.model.condition;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年09月06 17:14
 */
@Data
public class DictValueCondition {

    @ApiModelProperty("字典编码")
    private String dictCode;

    @ApiModelProperty("字典编码")
    private List<String> dictCodeList;

    @ApiModelProperty("字典值")
    private String dictValue;

    @ApiModelProperty("是否启用，1启用，0禁用")
    private Integer dictStatus;
}
