package com.wanlianyida.support.domain.model.condition;

import lombok.Data;

/**
 * 计价类型配置
 */
@Data
public class BdPricingTypeConfigCondition {

    /**
     * 主键
     */
    private Long id;

    private Long idNotEqual;

    /**
     * 平台编号
     */
    private String platformCode;

    /**
     * 计价类型名称
     */
    private String pricingTypeName;

    private String pricingTypeNameEq;

    /**
     * 启用状态：10启用 20停用
     */
    private String enableStatus;

    /**
     * 删除标记0正常1删除
     */
    private Integer deleted;

    /**
     * 计量单位 0否 1是
     */
    private Integer measureUnit;

    /**
     * 计价单位 0否1是
     */
    private Integer pricingUnit;

}
