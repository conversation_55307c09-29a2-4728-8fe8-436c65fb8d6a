package com.wanlianyida.support.domain.model.condition;

import lombok.Data;

import java.util.Date;

/**
 * 延迟消息表
 * @TableName bd_delay_task
 */
@Data
public class BdDelayTaskCondition {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 执行时间
     */
    private Date execTime;

    /**
     * 执行状态:[0-待执行,1-执行中,2-已执行]
     */
    private Integer execStatus;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 下次执行时间
     */
    private Date nextExecTime;

    /**
     * 备注
     */
    private String remark;


    /**
     * 请求类型
     */
    private Integer callType;

    /**
     * 请求地址
     */
    private String callUrl;

    /**
     * 分片索引
     */
    private Integer shardIndex;

    /**
     * 分片总数
     */
    private Integer shardTotal;

}