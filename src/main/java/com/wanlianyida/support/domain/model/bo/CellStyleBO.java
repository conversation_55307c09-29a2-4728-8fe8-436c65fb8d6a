package com.wanlianyida.support.domain.model.bo;

import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 单元格样式
 */
@Data
public class CellStyleBO {
    /**
     * 索引行数
     */
    private int rowIndex;

    /**
     * 索引列数
     */
    private int columnIndex;

    /**
     * 字体名称
     */
    private String fontName = "宋体";

    /**
     * 字体大小
     */
    private short fontSize = 10;

    /**
     * 是否加粗
     */
    private Boolean isBold = false;

    /**
     * 是否自动换行
     */
    private Boolean wrapText = true;

    /**
     * 行高度
     */
    private float rowHeight = 20;

    /**
     * 是否*号标红
     */
    private Boolean highlightAsterisk = false;

    /**
     * 水平对齐方式：0-左对齐，1-居中，2-右对齐
     */
    private int horizontalAlignment = 1;

    /**
     * 字体颜色索引 默认黑色
     */
    private short fontColorIndex = IndexedColors.BLACK.getIndex();
}