package com.wanlianyida.support.domain.model.bo;

import com.wanlianyida.support.interfaces.model.dto.ParseFileDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 解析文件模板bo
 */
@Data
public class ParseFileTemplateBO {
    /**
     * 失败信息
     */
    private ParseFileDTO.FailInfo failInfo;

    /**
     * 导入结果信息
     */
    private ParseFileDTO.ImportResultInfo importResultInfo;

    /**
     * 校验通过的数据集合
     */
    private List<Map<String, Object>> successDataList;

    /**
     * 校验失败的数据集合
     */
    private List<Map<String, Object>> failDataList;
}
