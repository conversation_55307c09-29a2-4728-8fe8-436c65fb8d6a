package com.wanlianyida.support.domain.model.bo;

import com.wanlianyida.support.interfaces.model.command.ParseFileCommand;
import lombok.Data;

import java.util.List;

/**
 * 导入BO
 */
@Data
public class ParseFileBO {

    /**
     * 文件地址
     */
    private String url;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 模板基础配置信息
     */
    private ParseFileCommand.TemplateConfig config;

    /**
     * 固定表头信息
     */
    private List<ParseFileCommand.CellInfo> staticCellInfoBOList;

    /**
     * 非固定表头信息
     */
    private List<ParseFileCommand.CellInfo> dynamicCellInfoBOList;

    /**
     * 特殊处理情况: 计量单位
     */
    private List<ParseFileCommand.MeasurementUnit> measurementUnitList;

}

