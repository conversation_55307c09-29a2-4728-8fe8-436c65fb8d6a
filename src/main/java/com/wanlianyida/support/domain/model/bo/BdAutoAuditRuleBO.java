package com.wanlianyida.support.domain.model.bo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 自动审核-规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Getter
@Setter
public class BdAutoAuditRuleBO implements Serializable {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 配置id
     */
    private String configId;

    /**
     * 规则类型 字典值=auto_rule_type
     */
    private String ruleType;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则值 字典值=
     * auto_rule_value
     * auto_rule_value_product
     * auto_rule_value_content
     */
    private Integer ruleValue;

    /**
     * 规则名称
     */
    private String ruleValueName;

    /**
     * 是否包含内部商家 1 包含 0 不包含
     */
    private Integer addInnerCompany;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 业务id集合
     */
    private List<String> busiIdList;


}
