package com.wanlianyida.support.domain.model.entity;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * pv uv 统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
@Data
public class BdStatisticsCountEntity extends BaseEntity {


    /**
     * 主键
     */
    private Long id;

    /**
     * 业务类型 10搜索关键字 20联想词 30资讯
     */
    private String businessType;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * PV 数量
     */
    private Long pv;

    /**
     * UV 数量
     */
    private Long uv;


    /**
     * 创建时间
     */
    private Date createdDate;


    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否删除(0正常 1已经删除)
     */
    private Integer deleted;
}
