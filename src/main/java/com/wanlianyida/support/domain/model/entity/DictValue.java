package com.wanlianyida.support.domain.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 字典值(DictValue)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:17
 */
@Data
@ApiModel("字典值")
public class DictValue {
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("字典编码")
    private String dictCode;

    @ApiModelProperty("字典值")
    private String dictValue;

    @ApiModelProperty("名称")
    private String valueName;

    @ApiModelProperty("排序")
    private Integer sortNum;

    @ApiModelProperty("字典描述")
    private String dictDesc;

    @ApiModelProperty("是否启用，1启用，0禁用")
    private Integer dictStatus;

    @ApiModelProperty("创建人")
    private String creatorId;

    @ApiModelProperty("创建时间")
    private Date createdDate;

    @ApiModelProperty("最后更新人")
    private String updaterId;

    @ApiModelProperty("最后更新时间")
    private Date updatedDate;

    @ApiModelProperty("版本号")
    private Integer versionCode;

}

