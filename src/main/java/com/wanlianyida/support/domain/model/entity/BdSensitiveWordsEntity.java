package com.wanlianyida.support.domain.model.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BdSensitiveWordsEntity extends BaseEntity{
    /**
     * 主键
     */
    private Long id;

    /**
     * id集合
     */
    private List<Long> ids;

    /**
     * 敏感词
     */
    private String senWord;

    /**
     * 敏感词集合，按逗号分隔
     */
    private String senWords;


    /**
     * 平台编号 20商贸门户
     */
    private String platformCode;

    /**
     * 状态(0停用 1启用)
     */
    private Short enableStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createdDate;
}
