package com.wanlianyida.support.domain.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 自动审核-规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Getter
@Setter
public class BdAutoAuditRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 配置id
     */
    private Long configId;

    /**
     * 规则类型 字典值=auto_rule_type
     */
    private String ruleType;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则值 字典值=
     */
    private Integer ruleValue;

    /**
     * 规则名称
     */
    private String ruleValueName;

    /**
     * 是否包含内部商家 1 包含 0 不包含
     */
    private Integer addInnerCompany;

    /**
     * 创建人
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String updaterId;

    /**
     * 最后更新人名字
     */
    private String updaterName;

    /**
     * 最后更新时间
     */
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;


}
