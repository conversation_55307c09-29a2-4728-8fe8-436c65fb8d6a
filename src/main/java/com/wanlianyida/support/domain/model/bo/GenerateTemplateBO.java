package com.wanlianyida.support.domain.model.bo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 生成模板BO
 */
@Data
public class GenerateTemplateBO {

    /**
     * 模板基础配置信息
     */
    private TemplateConfig config;

    /**
     * 固定表头信息
     */
    private List<CellInfo> staticCellInfoBOList;

    /**
     * 非固定表头信息
     */
    private List<CellInfo> dynamicCellInfoBOList;

    /**
     * 失败数据
     */
    private List<Map<String, Object>> dataList;

    @Data
    public static class CellInfo {
        /**
         * 单元格Map：key 为单元格的行数索引，value为单元格的名字
         */
        private Map<Integer, String> cellMap;

        /**
         * 下拉框信息
         */
        private SelectBoxInfo selectBoxInfo;
    }

    /**
     * 下拉框信息
     */
    @Data
    public static class SelectBoxInfo {
        /**
         * 下拉框生效的起始行
         */
        private Integer firstRow;

        /**
         * 下拉框生效的结束行
         */
        private Integer lastRow;

        /**
         * 是否显示错误信息框
         */
        private Boolean showErrorBox = true;

        /**
         * 下拉框中的值（无需下拉框此值为空）
         */
        private List<String> selectBoxList;
    }

    /**
     * 模板基础配置信息
     */
    @Data
    public static class TemplateConfig{
        /**
         * 模板标题
         */
        private String title;

        /**
         * 模板sheet的名字
         */
        private String sheetName;

        /**
         * 生成的模板文件名字
         */
        private String templateName;
    }



}

