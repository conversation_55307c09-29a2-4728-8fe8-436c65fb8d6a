package com.wanlianyida.support.domain.service;

import com.wanlianyida.support.domain.model.condition.BdAutoAuditRuleDetailCondition;
import com.wanlianyida.support.domain.model.entity.BdAutoAuditRuleDetailEntity;
import com.wanlianyida.support.domain.repository.IBdAutoAuditRuleDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * <p>
 * 自动审核-规则明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Service
@Slf4j
public class BdAutoAuditRuleDetailDomainService{


    @Resource
    private IBdAutoAuditRuleDetailRepository autoAuditRuleDetailRepository;

    /**
     * 新增
     * @param bdAutoAuditRuleDetailEntity
     * @return
     */
    public Boolean add(BdAutoAuditRuleDetailEntity bdAutoAuditRuleDetailEntity){
        return autoAuditRuleDetailRepository.batchInsert(Collections.singletonList(bdAutoAuditRuleDetailEntity));
    }

    /**
     * 查询列表
     * @param condition
     * @return
     */
    public List<BdAutoAuditRuleDetailEntity> queryCondition(BdAutoAuditRuleDetailCondition condition){
        return autoAuditRuleDetailRepository.queryCondition(condition);
    }

    /**
     * 更新
     *
     * @param bdAutoAuditRuleDetailEntity
     * @return
     */
    public Boolean update(BdAutoAuditRuleDetailEntity bdAutoAuditRuleDetailEntity){
        return autoAuditRuleDetailRepository.update(bdAutoAuditRuleDetailEntity);
    }

}
