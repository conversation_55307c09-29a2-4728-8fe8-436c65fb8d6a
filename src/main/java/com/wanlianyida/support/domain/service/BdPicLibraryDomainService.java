package com.wanlianyida.support.domain.service;

import com.wanlianyida.support.domain.model.condition.BdPicLibraryCondition;
import com.wanlianyida.support.domain.model.entity.BdPicLibraryEntity;
import com.wanlianyida.support.domain.repository.IBdPicLibraryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/03/12/14:10
 */
@Service
@Slf4j
public class BdPicLibraryDomainService {

    @Resource
    private IBdPicLibraryRepository picLibraryRepository;

    /**
     * 新增
     * @param picLibraryEntityList
     * @return
     */
    public Boolean batchInsert(List<BdPicLibraryEntity> picLibraryEntityList) {
        try {
            return picLibraryRepository.batchInsert(picLibraryEntityList);
        } catch (Exception e) {
            log.error("新增图片失败", e);
            return false;
        }
    }

    /**
     * 批量删除
     * @param picLibraryEntity
     * @return
     */
    public Boolean batchDelete(BdPicLibraryEntity picLibraryEntity) {
        return picLibraryRepository.batchDelete(picLibraryEntity);
    }

    /**
     * 查询列表
     * @param condition
     * @return
     */
    public List<BdPicLibraryEntity> queryCondition(BdPicLibraryCondition condition) {
        return picLibraryRepository.queryCondition(condition);
    }

    /**
     * 更新
     *
     * @param picLibraryEntity
     * @return
     */
    public Boolean update(BdPicLibraryEntity picLibraryEntity) {
        return picLibraryRepository.update(picLibraryEntity);
    }

}
