package com.wanlianyida.support.domain.service.strategy.imports;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.support.application.assembler.ImportAssembler;
import com.wanlianyida.support.domain.model.bo.GenerateTemplateBO;
import com.wanlianyida.support.infrastructure.enums.BusinessTypeEnum;
import com.wanlianyida.support.infrastructure.util.EasypoiUtil;
import com.wanlianyida.support.interfaces.model.dto.GenerateTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 构建生成模板抽象类
 * <AUTHOR>
 */
@Slf4j
public abstract class GenerateTemplateHandler {


    @Resource
    private IUploadService iUploadService;


    /**
     * 生成模板
     */
    private Workbook doGenerateTemplate(GenerateTemplateBO generateTemplateBO){
        // 创建导入模板需要的表头参数实体集合
        List<ExcelExportEntity> excelExportEntityList = ImportAssembler.buildExcelExportEntityList(generateTemplateBO);

        // 创建ExportParams实体
        ExportParams exportParams = ImportAssembler.buildExportParams(generateTemplateBO);

        // 生成excel模板
        return ExcelExportUtil.exportExcel(exportParams,excelExportEntityList,new LinkedList<>());
    }

    /**
     * 设置模板样式
     */
    protected abstract void setTemplateStyle(Workbook workbook, GenerateTemplateBO generateTemplateBO);

    /**
     * 设置模板下拉框
     */
    protected abstract void setTemplateDropdownOptions(Workbook workbook, GenerateTemplateBO generateTemplateBO);

    /**
     * 设置数字验证
     */
    protected abstract void setTemplateNumericValidation(Workbook workbook, GenerateTemplateBO generateTemplateBO);

    /**
     * 上传模板文件
     */
    private String uploadTemplateFile(Workbook workbook, GenerateTemplateBO generateTemplateBO) {
        //文件名称
        String templateName = generateTemplateBO.getConfig().getTemplateName() + ".xlsx";
        //文件业务类型
        String businessType =  BusinessTypeEnum.IMPORT_TEMP.getCode();

        //失败数据是否有数据
        if (generateTemplateBO.getDataList()!= null && !generateTemplateBO.getDataList().isEmpty()){
            businessType =  BusinessTypeEnum.IMPORT_ERROR_MSG.getCode();
        }
        // 将 Workbook 转换为字节数组
        CommonsMultipartFile multipartFile = EasypoiUtil.workbookConversionMultipartFile(workbook,templateName);
        ResponseMessage<String> importOriginal = iUploadService.uploadFile(multipartFile, businessType, null ,null);
        log.info("batchImportFileSucceed：{}", importOriginal.getModel());
        if (importOriginal.isSucceed()){
            return importOriginal.getModel();
        }
        return "";
    }

    /**
     * 扩展逻辑(自由扩展其他需要的逻辑)
     */
    protected void expandLogic(Workbook workbook, GenerateTemplateBO generateTemplateBO){

    }


    /**
     * 填充数据
     */
    protected abstract void setTemplateData(Workbook workbook,GenerateTemplateBO generateTemplateBO);


    /**
     * 生成模板流程
     * @return 返回品类树结构
     */
    public GenerateTemplateDTO generateTemplate(GenerateTemplateBO generateTemplateBO){
        Workbook workbook = null;
        try{
            // 1. 生成模板
            workbook = this.doGenerateTemplate(generateTemplateBO);

            // 2. 扩展逻辑(自由扩展其他需要的逻辑)
            this.expandLogic(workbook,generateTemplateBO);

            // 3. 设置模板样式
            this.setTemplateStyle(workbook,generateTemplateBO);

            // 4. 设置模板下拉框
            this.setTemplateDropdownOptions(workbook,generateTemplateBO);

            // 5. 设置数字验证
            this.setTemplateNumericValidation(workbook,generateTemplateBO);

            // 6. 填充数据
            this.setTemplateData(workbook,generateTemplateBO);

            // 7. 上传模板文件
            String url = this.uploadTemplateFile(workbook, generateTemplateBO);

            // 返回处理后的树结构
            return ImportAssembler.buildGenerateTemplateDTO(generateTemplateBO,url);
        }catch (Exception e){
            log.info("generateTemplateFail：{}", e.getMessage());
        } finally {
            try {
                if(workbook != null){
                    workbook.close();
                }
            } catch (Exception ignored) {}
        }
        return new GenerateTemplateDTO();
    }
}
