package com.wanlianyida.support.domain.service.strategy;

import com.wanlianyida.support.domain.model.bo.AutoAuditRuleValueBO;
import org.springframework.stereotype.Service;

/**
 * 全部
 * <AUTHOR>
 * @since 2025/03/05/09:03
 */
@Service("ruleValueAllService")
public class RuleValueAllServiceImpl extends AutoAuditRuleValueStrategy {
    @Override
    protected Boolean handleStatus(AutoAuditRuleValueBO ruleValueBO) {
        return true;
    }
}
