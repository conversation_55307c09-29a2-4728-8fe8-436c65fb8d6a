package com.wanlianyida.support.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import com.wanlianyida.support.domain.assembler.BdAutoAuditRuleAssembler;
import com.wanlianyida.support.domain.model.bo.BdAutoAuditRuleBO;
import com.wanlianyida.support.domain.model.condition.BdAutoAuditRuleCondition;
import com.wanlianyida.support.domain.model.entity.BdAutoAuditRuleDetailEntity;
import com.wanlianyida.support.domain.model.entity.BdAutoAuditRuleEntity;
import com.wanlianyida.support.domain.repository.IBdAutoAuditRuleDetailRepository;
import com.wanlianyida.support.domain.repository.IBdAutoAuditRuleRepository;
import com.wanlianyida.support.infrastructure.repository.mapper.BdAutoAuditRuleDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <p>
 * 自动审核-规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Service
@Slf4j
public class BdAutoAuditRuleDomainService {

    @Resource
    private IBdAutoAuditRuleRepository autoAuditRuleRepository;

    @Resource
    private IBdAutoAuditRuleDetailRepository autoAuditRuleDetailRepository;

    private BdAutoAuditRuleDetailMapper bdAutoAuditRuleBO;

    /**
     * 新增
     * @param bdAutoAuditRuleBO
     * @return
     */
    public Boolean addOrUpdateRule(BdAutoAuditRuleBO bdAutoAuditRuleBO){
        Long ruleId = bdAutoAuditRuleBO.getId();
        BdAutoAuditRuleEntity bdAutoAuditRuleEntity = BeanUtil.copyProperties(bdAutoAuditRuleBO, BdAutoAuditRuleEntity.class);
        if (ObjectUtil.isEmpty(ruleId)) {
            ruleId = autoAuditRuleRepository.add(bdAutoAuditRuleEntity);
        } else {
            autoAuditRuleRepository.update(BeanUtil.copyProperties(bdAutoAuditRuleBO, BdAutoAuditRuleEntity.class));
        }
        BdAutoAuditRuleDetailEntity bdAutoAuditRuleDetailEntity = new BdAutoAuditRuleDetailEntity();
        bdAutoAuditRuleDetailEntity.setRuleId(ruleId);
        autoAuditRuleDetailRepository.batchDelete(bdAutoAuditRuleDetailEntity);
        List<BdAutoAuditRuleDetailEntity> bdAutoAuditRuleDetailEntityList = BdAutoAuditRuleAssembler.buildProjectDetailCondition(bdAutoAuditRuleBO, ruleId);
        if (ObjectUtil.isNotEmpty(bdAutoAuditRuleDetailEntityList)) {
            autoAuditRuleDetailRepository.batchInsert(bdAutoAuditRuleDetailEntityList);
        }
        return true;
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    public BdAutoAuditRuleEntity ruleDetail(Long id){
        return autoAuditRuleRepository.detail(id);
    }

    public Boolean openBiddingRule(BdAutoAuditRuleDetailEntity bdAutoAuditRuleDetailEntity){
        autoAuditRuleDetailRepository.batchDelete(bdAutoAuditRuleDetailEntity);
        autoAuditRuleDetailRepository.save(BeanUtil.copyProperties(bdAutoAuditRuleDetailEntity, BdAutoAuditRuleDetailEntity.class));
        return true;
    }

    public Long addBiddingRule(Long configId){
        String bizId = JwtUtil.getTokenInfo().getCompanyId();
        List<BdAutoAuditRuleEntity> list = autoAuditRuleRepository.queryCondition(BdAutoAuditRuleCondition.builder().configId(configId).build());
        if (ObjectUtil.isNotEmpty(list)) {
           return IterUtil.getFirst(list).getId();
        }
        return autoAuditRuleRepository.add(BdAutoAuditRuleAssembler.buildAuditRuleDetail(configId, bizId));
    }

    public Boolean closeBiddingRule(BdAutoAuditRuleDetailEntity bdAutoAuditRuleDetailEntity){
        return autoAuditRuleDetailRepository.batchDelete(bdAutoAuditRuleDetailEntity);
    }

    /**
     * 查询列表
     * @param condition
     * @return
     */
    public List<BdAutoAuditRuleEntity> queryCondition(BdAutoAuditRuleCondition condition){
        return autoAuditRuleRepository.queryCondition(condition);
    }

    /**
     * 更新
     *
     * @param bdAutoAuditRuleEntity
     * @return
     */
    public Boolean update(BdAutoAuditRuleEntity bdAutoAuditRuleEntity){
        return autoAuditRuleRepository.update(bdAutoAuditRuleEntity);
    }
}
