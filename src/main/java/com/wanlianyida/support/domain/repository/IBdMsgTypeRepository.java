package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.condition.BdMsgTypeCondition;
import com.wanlianyida.support.domain.model.entity.BdMsgTypeEntity;

import java.util.List;

public interface IBdMsgTypeRepository {
    /**
     * 详情查询
     * @param id
     * @return <p>
     */
    BdMsgTypeEntity queryDetail(String id);

    /**
     * 条件查询
     * @param condition
     * @return
     */
    List<BdMsgTypeEntity> queryByCondition(BdMsgTypeCondition condition);

    /**
     * 新增消息类别
     * @param bdMsgTypeEntity
     * @return
     */
    String addMsgType(BdMsgTypeEntity bdMsgTypeEntity);

    /**
     * 更新消息类别
     * @param bdMsgTypeEntity
     */
    void updateMsgType(BdMsgTypeEntity bdMsgTypeEntity);

    /**
     * 删除消息类别
     * @param id
     */
    void delMsgType(String id);
}
