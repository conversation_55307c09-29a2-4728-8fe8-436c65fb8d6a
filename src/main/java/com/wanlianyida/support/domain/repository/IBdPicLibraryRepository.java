package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.condition.BdPicLibraryCondition;
import com.wanlianyida.support.domain.model.entity.BdPicLibraryEntity;

import java.util.List;

/**
 * <p>
 * 图片库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface IBdPicLibraryRepository {

    /**
     * 新增
     * @param picLibraryEntityList
     * @return
     */
    Boolean batchInsert(List<BdPicLibraryEntity> picLibraryEntityList);

    /**
     * 批量删除
     * @param picLibraryEntity
     * @return
     */
    Boolean batchDelete(BdPicLibraryEntity picLibraryEntity);

    /**
     * 查询列表
     * @param condition
     * @return
     */
    List<BdPicLibraryEntity> queryCondition(BdPicLibraryCondition condition);

    /**
     * 更新
     *
     * @param picLibraryEntity
     * @return
     */
    Boolean update(BdPicLibraryEntity picLibraryEntity);
}
