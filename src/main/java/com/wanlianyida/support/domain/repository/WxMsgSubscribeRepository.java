package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.condition.MsgSubscribeCondition;
import com.wanlianyida.support.domain.model.entity.WxMsgSubscribeEntity;

import java.util.List;

/**
 * 微信消息订阅仓储层
 */
public interface WxMsgSubscribeRepository {
    /**
     * 批量新增
     */
    void batchInsert(List<WxMsgSubscribeEntity> subscribeEntityList);

    /**
     * 消息订阅条件查询
     * @param condition 条件
     * @return 结果集合
     */
    List<WxMsgSubscribeEntity> queryCondition(MsgSubscribeCondition condition);

    /**
     * 根据模板id和用户id集合删除
     * @param userIdList 用户id集合
     * @param templateId 模板id
     */
    void deleteByTemplateAndUserList(List<String> userIdList, String templateId, String messageReceiver);
}
