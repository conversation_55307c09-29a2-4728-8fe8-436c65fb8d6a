package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.condition.BdSearchHotWordCondition;
import com.wanlianyida.support.domain.model.condition.SearchHotWordPageCondition;
import com.wanlianyida.support.domain.model.entity.BdSearchHotWordEntity;

import java.util.List;

public interface ISearchHotWordRepository {
    /**
     * 保存或更新
     * @param hotWordEntity
     */
    void saveOrUpdate(BdSearchHotWordEntity hotWordEntity);

    /**
     * 根据热词名称查询
     * @param hotWordName
     * @return
     */
    BdSearchHotWordEntity queryByHotName(String hotWordName);

    /**
     * 查询热词详情
     * @param condition
     * @return
     */
    BdSearchHotWordEntity queryDetail(BdSearchHotWordCondition condition);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 分页查询
     * @param condition
     * @return
     */
    List<BdSearchHotWordEntity> queryPage(SearchHotWordPageCondition condition);

    /**
     * 查询指定条数的数据
     * @param dataSize
     * @return
     */
    List<BdSearchHotWordEntity> queryLimitedData(Integer dataSize);
}
