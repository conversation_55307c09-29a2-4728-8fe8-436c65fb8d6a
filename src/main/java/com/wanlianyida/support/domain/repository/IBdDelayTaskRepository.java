package com.wanlianyida.support.domain.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wanlianyida.support.domain.model.condition.BdDelayTaskCondition;
import com.wanlianyida.support.domain.model.entity.BdDelayTaskEntity;
import com.wanlianyida.support.infrastructure.repository.po.BdDelayTaskPO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【bd_delay_task(延迟消息表)】的数据库操作Service
* @createDate 2025-05-28 13:52:18
*/
public interface IBdDelayTaskRepository extends IService<BdDelayTaskPO> {

    Boolean addDelayTask(BdDelayTaskEntity bean);

    Boolean updateDelayTask(BdDelayTaskEntity bean);

    List<BdDelayTaskEntity> listPageDelayTask(BdDelayTaskCondition bean);

    Boolean deleteDelayTask(BdDelayTaskEntity bean);

    Boolean addDelayTaskList(List<BdDelayTaskEntity> bdDelayTaskEntities);
}
