package com.wanlianyida.support.domain.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.support.domain.model.entity.BdMsgTypeEntity;
import com.wanlianyida.support.interfaces.model.dto.BdMsgTypeDTO;

public class BdMsgTypeAssemble {
    private BdMsgTypeAssemble(){}

    /**
     * 分页查询
     *
     * @param pageInfo 页面信息
     * @return {@code PageInfo<BdMsgTypeDTO> }
     */
    public static PageInfo<BdMsgTypeDTO> queryPage(PageInfo<BdMsgTypeEntity> pageInfo){
        if (ObjUtil.isNull(pageInfo) || IterUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        PageInfo<BdMsgTypeDTO> page = new PageInfo();
        page.setTotal(pageInfo.getTotal());
        page.setList(BeanUtil.copyToList(pageInfo.getList(), BdMsgTypeDTO.class));
        return page;
    }
}
