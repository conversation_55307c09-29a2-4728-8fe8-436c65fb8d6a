package com.wanlianyida;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication
@EnableDiscoveryClient
@Slf4j
public class GatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
        log.info(">>>>>>>>>>>>>>>>>>>>>网关系统访问成功！！！>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
    }

}
