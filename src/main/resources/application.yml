server:
  port: 9003
spring:
  application:
    name: gateway
  profiles:
    active: settlement
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
  gson:
    date-format: yyyy-MM-dd HH:mm:ss
  jackson:
    time-zone: GMT+8
logging:
  config: classpath:log4j2.xml
management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"
  health:
    redis:
      enabled: false

