server:
  port: 9803
  servlet:
    context-path: /ctp-platform
spring:
  profiles:
    active: dev
  application:
    name: ctp-orch-platform
  autoconfigure:
    exclude: org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration,org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration
feign:
  httpclient:
    connection-timeout: 1000
    enabled: true
ribbon:
  ConnectTimeout: 1000
  ReadTimeout: 80000
