database-base-url: ************************************************************************
spring:
  cache:
    caffeine:
      spec: initialCapacity=50,maximumSize=10000,expireAfterWrite=600s
    type: caffeine
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    username: sit_user
    password: thoh1ayieZ
    url: ${database-base-url}/inner_wlyd_ctp?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    druid:
      filters: stat,log4j2
      initialSize: 5
      maxActive: 20
      maxPoolPreparedStatementPerConnectionSize: 20
      maxWait: 60000
      minEvictableIdleTimeMillis: 300000
      minIdle: 5
      poolPreparedStatements: true
      testOnBorrow: false
      testOnReturn: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 60000
      validationQuery: SELECT 1 FROM DUAL
      stat:
        mergeSql: true
        slowSqlMillis: 5000
  kafka:
    bootstrap-servers: *************:9092
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: true
      group-id: ${spring.profiles.active}_ctpcorebidding_group
      max-poll-records: 20
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      linger: 10
      retries: 3
  redis:
    host: *************
    maxActive: 200
    maxIdle: 20
    maxWait: 3000
    minIdle: 5
    password: 123456
    port: 6379
    testOnBorrow: true
    testOnReturn: true
    timeout: 3000
    database: 5
xxl:
  job:
    admin:
      addresses:  http://*************:8090/xxl-job-admin
    accessToken: default_token
    executor:
      appname: xxl-job-ctpcorebidding
      address:
      ip:
      port: 0
      logpath: /tmp/applogs/xxl-job/ctpcorebidding/jobhandler
      logretentiondays: 30
common:
  jwt:
    loginPath: /login
    expiration: 10080
    header: Authorization
    secret: aaHR0cHM6Ly9teS5vc2NoaW5hLm5ldC91LzM2ODE4Njg