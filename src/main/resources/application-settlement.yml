spring:
  redis:
    host: *************
    maxActive: 200
    maxIdle: 20
    maxWait: 3000
    minIdle: 5
    password: 123456
    port: 6379
    testOnBorrow: true
    testOnReturn: true
    timeout: 3000
    database: 5
  kafka:
    bootstrap-servers: *************:9092
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: true
      max-poll-records: 20
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      linger: 10
      retries: 3
datasource:
  url: http://ld-8vb432g58h0em96sw-proxy-tsdb-pub.lindorm.aliyuncs.com:8242
  username: root
  password: ieEwstUIAKec
  database: inner_wlyd_log