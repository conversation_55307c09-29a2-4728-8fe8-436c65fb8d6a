spring:
  cloud:
    nacos:
      username: nacos
      password: wlyd2018
      server-addr: *************:8848
      discovery:
        namespace: ctp_settlement
        enabled: true
        register-enabled: true
      config:
        name: fss_base_log
        enabled: true
        file-extension: yaml
        namespace: ctp_settlement
        refresh-enabled: true
        # 多服务间共享的配置列表
        shared-configs:
          # 要共享的配置文件id
          - data-id: common.yaml
            # 是否动态刷新，默认为false
            refresh: true
register-center-ip-port: *************:21000
eureka:
  client:
    register-with-eureka: true
    service-url:
      defaultZone: http://${register-center-ip-port}/eureka/
  instance:
    lease-expiration-duration-in-seconds: 20
    lease-renewal-interval-in-seconds: 20
    prefer-ip-address: true
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  nacos:
    enabled: false
logging:
  config: classpath:log4j2.xml