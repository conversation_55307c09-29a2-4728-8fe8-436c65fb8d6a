spring:
  cloud:
    nacos:
      username: nacos
      password: wlyd2018
      server-addr: *************:8848
      #server-addr: nacos.dev.10000da.vip:8848
      discovery:
        namespace: ctp
        enabled: true
        register-enabled: false
      config:
        enabled: true
        file-extension: yaml
        namespace: ctp
        refresh-enabled: true
        name: fss-base-user-auth-dev.yaml
        # 多服务间共享的配置列表
        shared-configs:
          # 要共享的配置文件id
          - data-id: common.yaml
            # 是否动态刷新，默认为false
            refresh: true
logging:
  config: classpath:log4j2.xml