database-base-url: *********************************************************************
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    username: inner_gtsp_log_svc
    password: eA63JyyJPTwa2Vy8
    url: ${database-base-url}/inner_gtsp_log?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    druid:
      filters: stat,log4j2
      initialSize: 5
      maxActive: 20
      maxPoolPreparedStatementPerConnectionSize: 20
      maxWait: 60000
      minEvictableIdleTimeMillis: 300000
      minIdle: 5
      poolPreparedStatements: true
      testOnBorrow: false
      testOnReturn: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 60000
      validationQuery: SELECT 1 FROM DUAL
      stat:
        mergeSql: true
        slowSqlMillis: 5000
  redis:
    host: redis.ctp.10000da.vip
    maxActive: 200
    maxIdle: 20
    maxWait: 3000
    minIdle: 5
    password: wlyd2019
    port: 36379
    testOnBorrow: true
    testOnReturn: true
    timeout: 3000
    database: 5
  kafka:
    bootstrap-servers: *************:9092
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: true
      max-poll-records: 20
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      linger: 10
      retries: 3
datasource:
  url: http://ld-8vb432g58h0em96sw-proxy-tsdb-pub.lindorm.aliyuncs.com:8242
  username: root
  password: ieEwstUIAKec
  database: inner_wlyd_log
