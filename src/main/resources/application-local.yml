spring:
  redis:
    host: redis.ctp.10000da.vip
    maxActive: 200
    maxIdle: 20
    maxWait: 3000
    minIdle: 5
    password: wlyd2019
    port: 36379
    testOnBorrow: true
    testOnReturn: true
    timeout: 3000
    database: 5
common:
  jwt:
    loginPath: /login
    expiration: 10080
    header: Authorization
    secret: aaHR0cHM6Ly9teS5vc2NoaW5hLm5ldC91LzM2ODE4Njg
pay:
  api:
    url: http://wlyd-hw-base-api.10000da.vip
wlyd:
  ctp:
    platform:
      socialCreditCode: 91110105MAE5H15H3L
      companyId: 4
      companyName: 北京万联易达商贸有限公司
      companyShortName: 北京万联商贸

rocketmq:
  producer:
    endpoints: rmq-cn-8bm466q4l08.cn-zhangjiakou.rmq.aliyuncs.com:8080
    access-key: An1KX20HN8G0Z647
    secret-key: rhJ1q12cEG8TQ0B4
    name-space: rmq-cn-8bm466q4l08
  push-consumer:
    endpoints: rmq-cn-8bm466q4l08.cn-zhangjiakou.rmq.aliyuncs.com:8080
    access-key: An1KX20HN8G0Z647
    secret-key: rhJ1q12cEG8TQ0B4
    name-space: rmq-cn-8bm466q4l08