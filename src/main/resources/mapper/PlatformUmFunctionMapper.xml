<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.fssbase.userauth.infrastructure.repository.mapper.PlatformUmFunctionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.fssbase.userauth.infrastructure.po.PlatformUmFunctionPO">
        <id column="func_id" property="funcId" />
        <result column="parent_id" property="parentId" />
        <result column="func_name" property="funcName" />
        <result column="action_name" property="actionName" />
        <result column="enfunc_name" property="enfuncName" />
        <result column="url" property="url" />
        <result column="style_name" property="styleName" />
        <result column="func_type" property="funcType" />
        <result column="hierarchy" property="hierarchy" />
        <result column="sortno" property="sortno" />
        <result column="app_id" property="appId" />
        <result column="sys_type" property="sysType" />
        <result column="btn_oper_type" property="btnOperType" />
        <result column="news" property="news" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="modify_by" property="modifyBy" />
        <result column="modify_date" property="modifyDate" />
        <result column="version_code" property="versionCode" />
        <result column="item1" property="item1" />
        <result column="item2" property="item2" />
        <result column="item3" property="item3" />
        <result column="item4" property="item4" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        func_id, parent_id, func_name, action_name, enfunc_name, url, style_name, func_type, hierarchy, sortno, app_id, sys_type, btn_oper_type, news, create_by, create_date, modify_by, modify_date, version_code, item1, item2, item3, item4
    </sql>

</mapper>
