<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanlianyida.sett.infrastructure.repository.mapper.SettSettlementApplyMapper">
    <resultMap id="BaseResultMap" type="com.wanlianyida.sett.infrastructure.repository.po.SettSettlementApplyPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sett_apply_no" property="settApplyNo" jdbcType="VARCHAR"/>
        <result column="seller_company_id" property="sellerCompanyId" jdbcType="VARCHAR"/>
        <result column="seller_company_name" property="sellerCompanyName" jdbcType="VARCHAR"/>
        <result column="buyer_company_id" property="buyerCompanyId" jdbcType="VARCHAR"/>
        <result column="buyer_company_name" property="buyerCompanyName" jdbcType="VARCHAR"/>
        <result column="sett_total_amount" property="settTotalAmount" jdbcType="DECIMAL"/>
        <result column="sett_status" property="settStatus" jdbcType="SMALLINT"/>
        <result column="pay_total_amount" property="payTotalAmount" jdbcType="DECIMAL"/>
        <result column="pay_status" property="payStatus" jdbcType="SMALLINT"/>
        <result column="invoice_reg_status" property="invoiceRegStatus" jdbcType="TINYINT"/>
        <result column="prepaid_total_amount" property="prepaidTotalAmount" jdbcType="DECIMAL"/>
        <result column="creator_id" property="creatorId" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="buyer_confirmor_id" property="buyerConfirmorId" jdbcType="VARCHAR"/>
        <result column="buyer_confirm_time" property="buyerConfirmTime" jdbcType="TIMESTAMP"/>
        <result column="seller_confirmor_id" property="sellerConfirmorId" jdbcType="VARCHAR"/>
        <result column="seller_confirm_time" property="sellerConfirmTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="return_reason" property="returnReason" jdbcType="VARCHAR"/>
        <result column="updater_id" property="updaterId" jdbcType="VARCHAR"/>
        <result column="updated_date" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="version_code" property="versionCode" jdbcType="TINYINT"/>
        <result column="sett_time" property="settTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, sett_apply_no, seller_company_id, seller_company_name, buyer_company_id, buyer_company_name,
        sett_total_amount, sett_status, pay_total_amount, pay_status, invoice_reg_status, prepaid_total_amount,
        creator_id, created_date, buyer_confirmor_id, buyer_confirm_time, seller_confirmor_id, seller_confirm_time,
        remark, return_reason, updater_id, updated_date, version_code
    </sql>

    <sql id="Export_Column_List">
        sett.sett_apply_no, prod.order_no, sett.buyer_company_id, sett.buyer_company_name, sett.seller_company_id,
        sett.seller_company_name, prod.pickup_no, prod.sku_code, prod.sku_name, prod.unit_con, prod.contract_unit_price,
        prod.shipment_qty, prod.receive_qty, prod.sment_quantity, prod.sment_price, prod.sement_subtotal,
        prod.adjust_amount, prod.payable_sett_amount, sett.sett_status, sett.pay_status, sett.invoice_reg_status,
        sett.created_date, sett.sett_total_amount, sett.pay_total_amount, sett.prepaid_total_amount
    </sql>

    <sql id="Export_Basic_Column_List">
        sett.sett_apply_no, prod.order_no, sett.buyer_company_id, sett.buyer_company_name, sett.seller_company_id,
        sett.seller_company_name, sett.sett_status, sett.pay_status, sett.invoice_reg_status,
        sett.created_date, sett.sett_total_amount, sett.pay_total_amount, sett.prepaid_total_amount
    </sql>

    <update id="updateByPrimaryKey" parameterType="com.wanlianyida.sett.infrastructure.repository.po.SettSettlementApplyPO">
        UPDATE sett_sett_apply
        <set>
            <if test="settApplyNo != null">sett_apply_no = #{settApplyNo},</if>
            <if test="sellerCompanyId != null">seller_company_id = #{sellerCompanyId},</if>
            <if test="sellerCompanyName != null">seller_company_name = #{sellerCompanyName},</if>
            <if test="buyerCompanyId != null">buyer_company_id = #{buyerCompanyId},</if>
            <if test="buyerCompanyName != null">buyer_company_name = #{buyerCompanyName},</if>
            <if test="settTotalAmount != null">sett_total_amount = #{settTotalAmount},</if>
            <if test="settStatus != null">sett_status = #{settStatus},</if>
            <if test="payTotalAmount != null">pay_total_amount = #{payTotalAmount},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="invoiceRegStatus != null">invoice_reg_status = #{invoiceRegStatus},</if>
            <if test="prepaidTotalAmount != null">prepaid_total_amount = #{prepaidTotalAmount},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="createdDate != null">created_date = #{createdDate},</if>
            <if test="buyerConfirmorId != null">buyer_confirmor_id = #{buyerConfirmorId},</if>
            <if test="buyerConfirmTime != null">buyer_confirm_time = #{buyerConfirmTime},</if>
            <if test="sellerConfirmorId != null">seller_confirmor_id = #{sellerConfirmorId},</if>
            <if test="sellerConfirmTime != null">seller_confirm_time = #{sellerConfirmTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="returnReason != null">return_reason = #{returnReason},</if>
            <if test="updaterId != null">updater_id = #{updaterId},</if>
            <if test="updatedDate != null">updated_date = #{updatedDate},</if>
            <if test="versionCode != null">version_code = #{versionCode},</if>
        </set>
        WHERE id = #{id}
    </update>


    <update id="batchUpdateSettlementList">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE sett_sett_apply
            <set>
                <if test="item.settStatus != null">sett_status = #{item.settStatus},</if>
                <if test="item.payStatus != null">pay_status = #{item.payStatus},</if>
                <if test="item.invoiceRegStatus != null">invoice_reg_status = #{item.invoiceRegStatus},</if>
                <if test="item.updaterId != null">updater_id = #{item.updaterId},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.returnReason != null">return_reason = #{item.returnReason},</if>
                updated_date = now()
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sett_sett_apply
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sett_sett_apply
    </select>

    <select id="querySettExportByCondition" resultType="com.wanlianyida.sett.domain.model.bo.ExportSettlementListBO">
        select
        distinct
        <if test="con.detailField != null and con.detailField == 0">
            <include refid="Export_Basic_Column_List"/>
        </if>
        <if test="con.detailField != null and con.detailField == 1">
            <include refid="Export_Column_List"/>
        </if>
        from
        sett_sett_apply_product prod
        inner join sett_sett_apply sett on
        sett.sett_apply_no = prod.sett_apply_no
        <where>
            <if test="con.companyType != null and con.companyType == 10">
                and sett.seller_company_id = #{con.companyId}
            </if>
            <if test="con.companyType != null and con.companyType == 20">
                and sett.buyer_company_id = #{con.companyId}
            </if>
            <if test="con.settApplyNo != null and con.settApplyNo != ''">
                and sett.sett_apply_no like concat('%', #{con.settApplyNo}, '%')
            </if>
            <if test="con.buyerCompanyName != null and con.buyerCompanyName != ''">
                and sett.buyer_company_name like concat('%', #{con.buyerCompanyName}, '%')
            </if>
            <if test="con.sellerCompanyName != null and con.sellerCompanyName != ''">
                and sett.seller_company_name like concat('%', #{con.sellerCompanyName}, '%')
            </if>
            <if test="con.settStatus != null and con.settStatus != ''">
                and sett.sett_status = #{con.settStatus}
            </if>
            <if test="con.invoiceRegStatus != null and con.invoiceRegStatus != ''">
                and sett.invoice_reg_status = #{con.invoiceRegStatus}
            </if>
            <if test="con.createdDateStart != null">
                and sett.created_date <![CDATA[>=]]> #{con.createdDateStart}
            </if>
            <if test="con.createdDateEnd != null">
                and sett.created_date <![CDATA[<=]]> #{con.createdDateEnd}
            </if>
        </where>
        order by sett.created_date desc, order_no desc, pickup_no desc
    </select>

    <select id="querySettExportByIds" resultType="com.wanlianyida.sett.domain.model.bo.ExportSettlementListBO">
        select
        distinct
        <if test="con.detailField != null and con.detailField == 0">
            <include refid="Export_Basic_Column_List"/>
        </if>
        <if test="con.detailField != null and con.detailField == 1">
            <include refid="Export_Column_List"/>
        </if>
        from
        sett_sett_apply_product prod
        inner join sett_sett_apply sett on
        sett.sett_apply_no = prod.sett_apply_no
        <where>
            <if test="con.ids != null">
                sett.id in
                <foreach collection="con.ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by sett.created_date desc, order_no desc, pickup_no desc
    </select>

    <select id="querySettlementInfoById" resultType="com.wanlianyida.sett.domain.model.bo.SettSettlementApplyBO">
        select
            sett.id,
            sett.sett_apply_no,
            ord.order_no,
            ord.sment_amount as smentAmount,
            ord.prepaid_amount as prepaidAmount,
            ord.payable_total_amount as payableAmount,
            sett.prepaid_total_amount,
            ord.prepaid_amount as prepaidAmount,
            sett.buyer_company_id as payerCompanyId,
            sett.buyer_company_name as payerCompanyName,
            sett.seller_company_id as receiverCompanyId,
            sett.seller_company_name as receiverCompanyName,
            sett.creator_id as creatorId
        from
            sett_sett_apply sett
            inner join sett_order_settlement_detail ord on
            ord.sett_apply_no = sett.sett_apply_no
        where
            sett.id = #{id}
    </select>

    <select id="queryCompanyNameList" resultType="com.wanlianyida.sett.domain.model.bo.CompanyNameListBO">
        select
            distinct
            <if test="con.companyType != null and con.companyType == 10">
                sett.buyer_company_id as companyId,
                sett.buyer_company_name as companyName
            </if>
            <if test="con.companyType != null and con.companyType == 20">
                sett.seller_company_id as companyId,
                sett.seller_company_name as companyName
            </if>
        from
        sett_sett_apply sett
        <where>
            <if test="con.companyType != null and con.companyType == 10">
                sett.seller_company_id = #{con.companyId}
                <if test="con.companyName != null and con.companyName != ''">
                    and sett.buyer_company_name like concat('%', #{con.companyName}, '%')
                </if>
            </if>
            <if test="con.companyType != null and con.companyType == 20">
                sett.buyer_company_id = #{con.companyId}
                <if test="con.companyName != null and con.companyName != ''">
                    and sett.seller_company_name like concat('%', #{con.companyName}, '%')
                </if>
            </if>
        </where>
    </select>

    <select id="queryStatusStatistics" resultType="java.util.Map">
        select
            sett.sett_status as status,
            count(1) as count
        from
        sett_sett_apply sett
        <where>
            sett.sett_status in (10, 20, 30)
            <if test="companyType != null and companyType == 10">
                and sett.seller_company_id = #{sellerCompanyId}
            </if>
            <if test="companyType != null and companyType == 20">
                and sett.buyer_company_id = #{buyerCompanyId}
            </if>
            <if test="settApplyNo != null and settApplyNo != ''">
                and sett.sett_apply_no like concat('%', #{settApplyNo}, '%')
            </if>
            <if test="buyerCompanyName != null and buyerCompanyName != ''">
                and sett.buyer_company_name like concat('%', #{buyerCompanyName}, '%')
            </if>
            <if test="sellerCompanyName != null and sellerCompanyName != ''">
                and sett.seller_company_name like concat('%', #{sellerCompanyName}, '%')
            </if>
            <if test="invoiceRegStatus != null and invoiceRegStatus != ''">
                and sett.invoice_reg_status = #{invoiceRegStatus}
            </if>
            <if test="createdDateStart != null">
                and date_format(sett.created_date,'%Y-%m-%d') <![CDATA[>=]]> #{createdDateStart}
            </if>
            <if test="createdDateEnd != null">
                and date_format(sett.created_date,'%Y-%m-%d') <![CDATA[<=]]> #{createdDateEnd}
            </if>
        </where>
        group by
            sett_status
    </select>

    <select id="querySettlementApplyByOrderNo" resultType="com.wanlianyida.sett.domain.model.entity.SettSettlementApplyEntity">
        select
        distinct ssa.sett_apply_no,
        ssa.prepaid_total_amount,
        ssa.pay_total_amount
        from
        sett_sett_apply ssa
        inner join sett_sett_apply_detail ssad on
        ssad.sett_apply_no = ssa.sett_apply_no
        where
        ssad.order_no = #{orderNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM sett_sett_apply
        WHERE id = #{id}
    </delete>
</mapper>