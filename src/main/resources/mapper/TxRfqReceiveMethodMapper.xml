<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.transaction.infrastructure.repository.mapper.TxRfqReceiveMethodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.transaction.infrastructure.repository.po.TxRfqReceiveMethodPO">
        <id column="id" property="id" />
        <result column="rfq_no" property="rfqNo" />
        <result column="receive_method" property="receiveMethod" />
        <result column="receiver_account_name" property="receiverAccountName" />
        <result column="receiver_bank_union_no" property="receiverBankUnionNo" />
        <result column="receiver_bank_name" property="receiverBankName" />
        <result column="receiver_bank_account_no" property="receiverBankAccountNo" />
        <result column="updater_id" property="updaterId" />
        <result column="updated_date" property="updatedDate" />
        <result column="version_code" property="versionCode" />
        <result column="creator_id" property="creatorId" />
        <result column="created_date" property="createdDate" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rfq_no, receive_method, receiver_account_name, receiver_bank_union_no, receiver_bank_name, receiver_bank_account_no, updater_id, updated_date, version_code, creator_id, created_date, del_flag
    </sql>
    <insert id="batchInsert" parameterType="com.wanlianyida.transaction.infrastructure.repository.po.TxRfqReceiveMethodPO">
        insert into tx_rfq_receive_method
        (rfq_no,receive_method,receiver_account_name,receiver_bank_union_no,receiver_bank_name,receiver_bank_account_no,creator_id,created_date,updater_id,updated_date)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.rfqNo},#{item.receiveMethod},#{item.receiverAccountName},#{item.receiverBankUnionNo},#{item.receiverBankName},#{item.receiverBankAccountNo},#{item.creatorId},#{item.createdDate},#{item.updaterId},#{item.updatedDate})
        </foreach>
    </insert>

</mapper>
