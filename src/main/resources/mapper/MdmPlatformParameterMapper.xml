<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.basemdm.infrastructure.repository.mapper.MdmPlatformParameterMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.basemdm.domain.model.entity.MdmPlatformParameterEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="plf_code" property="plfCode" jdbcType="VARCHAR"/>
        <result column="para_code" property="paraCode" jdbcType="VARCHAR"/>
        <result column="para_value" property="paraValue" jdbcType="VARCHAR"/>
        <result column="para_name" property="paraName" jdbcType="VARCHAR"/>
        <result column="para_desc" property="paraDesc" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="creator_id" property="creatorId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="last_updater_id" property="lastUpdaterId" jdbcType="VARCHAR"/>
        <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, plf_code, para_code, para_value, para_name, para_desc, group_code, group_name, del_flag,
        creator_id, create_time, last_updater_id, last_update_time
    </sql>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.wanlianyida.basemdm.domain.model.entity.MdmPlatformParameterEntity">
        INSERT INTO mdm_platform_parameter (
        id, plf_code, para_code, para_value, para_name, para_desc, group_code, group_name, del_flag,
        creator_id, create_time, last_updater_id, last_update_time
        ) VALUES (
        #{id}, #{plfCode}, #{paraCode}, #{paraValue}, #{paraName}, #{paraDesc}, #{groupCode}, #{groupName}, #{delFlag},
        #{creatorId}, #{createTime}, #{lastUpdaterId}, #{lastUpdateTime}
        )
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.wanlianyida.basemdm.domain.model.entity.MdmPlatformParameterEntity">
        UPDATE mdm_platform_parameter
        <set>
            <if test="plfCode != null">plf_code = #{plfCode},</if>
            <if test="paraCode != null">para_code = #{paraCode},</if>
            <if test="paraValue != null">para_value = #{paraValue},</if>
            <if test="paraName != null">para_name = #{paraName},</if>
            <if test="paraDesc != null">para_desc = #{paraDesc},</if>
            <if test="groupCode != null">group_code = #{groupCode},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="lastUpdaterId != null">last_updater_id = #{lastUpdaterId},</if>
            <if test="lastUpdateTime != null">last_update_time = #{lastUpdateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据参数编码查询 -->
    <select id="selectByParaCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        WHERE para_code = #{paraCode} AND del_flag = 0
    </select>

    <!-- 根据分组编码查询 -->
    <select id="selectByGroupCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        WHERE group_code = #{groupCode} AND del_flag = 0
        ORDER BY create_time DESC, id DESC
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        WHERE del_flag = 0
        ORDER BY create_time DESC, id DESC
    </select>

    <!-- 条件查询 -->
    <select id="queryByCondition"
            parameterType="com.wanlianyida.basemdm.interfaces.model.query.MdmPlatformParameterQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="plfCode != null and plfCode != ''">
                AND plf_code = #{plfCode}
            </if>
            <if test="paraCode != null and paraCode != ''">
                AND para_code = #{paraCode}
            </if>
            <if test="paraValue != null and paraValue != ''">
                AND para_value LIKE CONCAT('%', #{paraValue}, '%')
            </if>
            <if test="paraName != null and paraName != ''">
                AND para_name LIKE CONCAT('%', #{paraName}, '%')
            </if>
            <if test="paraDesc != null and paraDesc != ''">
                AND para_desc LIKE CONCAT('%', #{paraDesc}, '%')
            </if>
            <if test="groupCode != null and groupCode != ''">
                AND group_code = #{groupCode}
            </if>
            <if test="groupName != null and groupName != ''">
                AND group_name LIKE CONCAT('%', #{groupName}, '%')
            </if>
            <if test="paraCodes != null and paraCodes.size() > 0">
                AND para_code IN
                <foreach collection="paraCodes" item="paraCode" open="(" separator="," close=")">
                    #{paraCode}
                </foreach>
            </if>
            AND del_flag = 0
        </where>
    </select>

    <!-- 根据多个参数编码批量查询 -->
    <select id="selectByParaCodes" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        WHERE
        <choose>
            <when test="list != null and list.size() > 0">
                para_code IN
                <foreach collection="list" item="paraCode" open="(" separator="," close=")">
                    #{paraCode}
                </foreach>
            </when>
            <otherwise>
                1 = 0
            </otherwise>
        </choose>
        AND del_flag = 0
    </select>

    <!-- 根据参数编码和平台编码查询未删除的记录 -->
    <select id="selectByParaCodeAndPlfCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mdm_platform_parameter
        WHERE para_code = #{paraCode} AND plf_code = #{plfCode} AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 软删除 -->
    <update id="deleteById">
        UPDATE mdm_platform_parameter
        SET del_flag = 1,
        last_updater_id = #{deletedBy},
        last_update_time = #{deletedAt}
        WHERE id = #{id}
    </update>

</mapper> 