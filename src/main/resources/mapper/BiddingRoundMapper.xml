<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.bidding.infrastructure.repository.mapper.BiddingRoundMapper">

    <select id="queryByCondition" resultType="com.wanlianyida.bidding.domain.bidding.model.bo.BiddingRoundBO">
        select
            bbr.id,
            bbr.package_id,
            bbr.package_name,
            bbr.project_id,
            bbr.quotation_content,
            bbrc.quotation_requirement,
            bbr.bid_open_time,
            bbr.bid_end_time,
            bbr.bid_round,
            bbr.creator_name,
            bbr.created_date
        from
            bt_bidding_round bbr
        inner join bt_bidding_round_config bbrc on
            bbrc.bid_round_id = bbr.id
        <where>
            bbr.package_id = #{packageId}
            and bbr.bid_round &gt; 1
            <if test="bidRoundList != null and bidRoundList.size() > 0">
                and bbr.bid_round in
                <foreach collection="bidRoundList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="quotationContent != null and quotationContent != ''">
                and bbr.quotation_content like concat('%',#{quotationContent},'%')
            </if>
            <if test="createdDateStart != null">
                and bbr.created_date <![CDATA[>=]]> #{createdDateStart}
            </if>
            <if test="createdDateEnd != null">
                and bbr.created_date <![CDATA[<=]]> #{createdDateEnd}
            </if>
            <if test="bidEndTimeStart != null">
                and bbr.bid_end_time <![CDATA[>=]]> #{bidEndTimeStart}
            </if>
            <if test="bidEndTimeEnd != null">
                and bbr.bid_end_time <![CDATA[<=]]> #{bidEndTimeEnd}
            </if>
        </where>
        order by
            bbr.created_date,bbr.id desc
    </select>
</mapper>
