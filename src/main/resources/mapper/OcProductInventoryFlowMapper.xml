<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.ctpcore.order.infrastructure.repository.mapper.OcProductInventoryFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.ctpcore.order.infrastructure.repository.po.OcProductInventoryFlowPO">
        <id column="id" property="id" />
        <result column="sku_code" property="skuCode" />
        <result column="company_id" property="companyId" />
        <result column="bus_num" property="busNum" />
        <result column="change_quantity" property="changeQuantity" />
        <result column="bus_action" property="busAction" />
        <result column="operation_action" property="operationAction" />
        <result column="remark" property="remark" />
        <result column="creator_id" property="creatorId" />
        <result column="created_date" property="createdDate" />
        <result column="updater_id" property="updaterId" />
        <result column="updated_date" property="updatedDate" />
        <result column="version_code" property="versionCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku_code, company_id, bus_num, change_quantity, bus_action, operation_action, remark, deleted, creator_id, created_date, updater_id, updated_date, version_code
    </sql>

    <insert id="addBatch" parameterType="com.wanlianyida.ctpcore.order.infrastructure.repository.po.OcProductInventoryFlowPO">
        insert into oc_product_inventory_flow
        (id, sku_code, company_id, bus_num, change_quantity, bus_action, operation_action, remark, deleted, creator_id, created_date, updater_id, updated_date, version_code)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id},
            #{item.skuCode},
            #{item.companyId},
            #{item.busNum},
            #{item.changeQuantity},
            #{item.busAction},
            #{item.operationAction},
            #{item.operationDate},
            #{item.remark},
            #{item.creatorId},
            #{item.createdDate},
            #{item.updaterId},
            #{item.updatedDate},
            #{item.versionCode}
            )
        </foreach>

    </insert>

</mapper>
