<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.support.infrastructure.repository.mapper.BdPicLibraryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.support.infrastructure.repository.po.BdPicLibraryPO">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="pic_type" property="picType" />
        <result column="pic_name" property="picName" />
        <result column="pic_url" property="picUrl" />
        <result column="pixels_length" property="pixelsLength" />
        <result column="pixels_width" property="pixelsWidth" />
        <result column="pic_size" property="picSize" />
        <result column="upload_date" property="uploadDate" />
        <result column="creator_id" property="creatorId" />
        <result column="created_date" property="createdDate" />
        <result column="updater_id" property="updaterId" />
        <result column="updated_date" property="updatedDate" />
        <result column="version_code" property="versionCode" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, pic_type,pixels_length,pixels_width, pic_name, pic_url, pic_size, upload_date, creator_id, created_date, updater_id, updated_date, version_code, deleted
    </sql>

    <insert id="batchInsert" parameterType="com.wanlianyida.support.infrastructure.repository.po.BdPicLibraryPO">
        insert into bd_pic_library
        (company_id,pic_type,pic_name,pic_url,
        pixels_length,pixels_width,pic_size,creator_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.companyId},#{item.picType}, #{item.picName},#{item.picUrl},
            #{item.pixelsLength} ,#{item.pixelsWidth},#{item.picSize} ,#{item.creatorId})
        </foreach>

    </insert>

</mapper>
