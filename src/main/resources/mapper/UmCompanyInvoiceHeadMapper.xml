<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.ctpcore.partner.infrastructure.repository.mapper.UmCompanyInvoiceHeadMapper">

    <sql id="sqlColumns">
        id, company_id,social_credit_code,is_default,address_detail, phone, email, head_type, head_name, bank_name, bank_card, invoice_remark, creator_id, created_date, updater_id, updated_date, version_code, del_flag
    </sql>

    <resultMap id="BaseResultMap" type="com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyInvoiceHeadEntity">
        <result column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="is_default" property="isDefault"/>
        <result column="head_type" property="headType"/>
        <result column="head_name" property="headName"/>
        <result column="social_credit_code" property="socialCreditCode"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_card" property="bankCard"/>
        <result column="address_detail" property="addressDetail"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="invoice_remark" property="invoiceRemark"/>
        <result column="creator_id" property="creatorId"/>
        <result column="created_date" property="createdDate"/>
        <result column="updater_id" property="updaterId"/>
        <result column="updated_date" property="updatedDate"/>
        <result column="version_code" property="versionCode"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="queryByCondition" parameterType="com.wanlianyida.ctpcore.partner.domain.model.condition.UmCompanyInvoiceHeadCondition" resultMap="BaseResultMap">
        select
        <include refid="sqlColumns" />
        from um_company_invoice_head
        <where>
            del_flag = '0'
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="isDefault != null and isDefault != ''">
                and is_default = #{isDefault}
            </if>
            <if test="headName != null and headName != ''">
                and head_name like concat('%',#{headName},'%')
            </if>
            <if test="fullMatchHeadName != null and fullMatchHeadName != ''">
                and head_name = #{fullMatchHeadName}
            </if>
            <if test="headType != null and headType != ''">
                and head_type = #{headType}
            </if>
            <if test="socialCreditCode != null and socialCreditCode != ''">
                and social_credit_code = #{socialCreditCode}
            </if>
            <if test="bankName != null and bankName != ''">
                and bank_name = #{bankName}
            </if>
            <if test="bankCard != null and bankCard != ''">
                and bank_card = #{bankCard}
            </if>
            <if test="addressDetail != null and addressDetail != ''">
                and address_detail = #{addressDetail}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="invoiceRemark != null and invoiceRemark != ''">
                and invoice_remark = #{invoiceRemark}
            </if>
            <if test="creatorId != null and creatorId != ''">
                and creator_id = #{creatorId}
            </if>
            <if test="createdDate != null">
                and created_date = #{createdDate}
            </if>
            <if test="updaterId != null and updaterId != ''">
                and updater_id = #{updaterId}
            </if>
            <if test="updatedDate != null">
                and updated_date = #{updatedDate}
            </if>
            <if test="versionCode != null">
                and version_code = #{versionCode}
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="sqlColumns" />
        from um_company_invoice_head
        where id = #{id}
        </select>

    <insert id="insertSelective" parameterType="com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyInvoiceHeadEntity">
        insert into um_company_invoice_head
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="isDefault != null and isDefault != ''">
                is_default,
            </if>
            <if test="headName != null and headName != ''">
                head_name,
            </if>
            <if test="headType != null and headType != ''">
                head_type,
            </if>
            <if test="socialCreditCode != null and socialCreditCode != ''">
                social_credit_code,
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name,
            </if>
            <if test="bankCard != null and bankCard != ''">
                bank_card,
            </if>
            <if test="addressDetail != null and addressDetail != ''">
                address_detail,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="invoiceRemark != null and invoiceRemark != ''">
                invoice_remark,
            </if>
            <if test="creatorId != null and creatorId != ''">
                creator_id,
            </if>
            <if test="createdDate != null">
                created_date,
            </if>
            <if test="updaterId != null and updaterId != ''">
                updater_id,
            </if>
            <if test="updatedDate != null">
                updated_date,
            </if>
            <if test="versionCode != null">
                version_code,
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="companyId != null">
                #{companyId},
            </if>
            <if test="isDefault != null and isDefault != ''">
                #{isDefault},
            </if>
            <if test="headName != null and headName != ''">
                #{headName},
            </if>
            <if test="headType != null and headType != ''">
                #{headType},
            </if>
            <if test="socialCreditCode != null and socialCreditCode != ''">
                #{socialCreditCode},
            </if>
            <if test="bankName != null and bankName != ''">
                #{bankName},
            </if>
            <if test="bankCard != null and bankCard != ''">
                #{bankCard},
            </if>
            <if test="addressDetail != null and addressDetail != ''">
                #{addressDetail},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="invoiceRemark != null and invoiceRemark != ''">
                #{invoiceRemark},
            </if>
            <if test="creatorId != null and creatorId != ''">
                #{creatorId},
            </if>
            <if test="createdDate != null">
                #{createdDate},
            </if>
            <if test="updaterId != null and updaterId != ''">
                #{updaterId},
            </if>
            <if test="updatedDate != null">
                #{updatedDate},
            </if>
            <if test="versionCode != null">
                #{versionCode},
            </if>
            <if test="delFlag != null and delFlag != ''">
                #{delFlag},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.wanlianyida.ctpcore.partner.domain.model.entity.UmCompanyInvoiceHeadEntity">
        update um_company_invoice_head
        <set>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="isDefault != null and isDefault != ''">
                is_default = #{isDefault},
            </if>
            <if test="headName != null and headName != ''">
                head_name = #{headName},
            </if>
            <if test="headType != null and headType != ''">
                head_type = #{headType},
            </if>
            <if test="socialCreditCode != null and socialCreditCode != ''">
                social_credit_code = #{socialCreditCode},
            </if>
            <if test="bankName != null and bankName != ''">
                bank_name = #{bankName},
            </if>
            <if test="bankCard != null and bankCard != ''">
                bank_card = #{bankCard},
            </if>
            <if test="addressDetail != null and addressDetail != ''">
                address_detail = #{addressDetail},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
                invoice_remark = #{invoiceRemark},
            <if test="updaterId != null and updaterId != ''">
                updater_id = #{updaterId},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="versionCode != null">
                version_code = #{versionCode},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>


