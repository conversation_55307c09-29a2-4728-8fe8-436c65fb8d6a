<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.basemdm.infrastructure.repository.mapper.MdmCompanyContactMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.basemdm.infrastructure.repository.po.MdmCompanyContactPO">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="company_code" property="companyCode" />
        <result column="company_name" property="companyName" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phone" property="contactPhone" />
        <result column="default_contact_flag" property="defaultContactFlag" />
        <result column="status" property="status" />
        <result column="platform_code" property="platformCode" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="created_date" property="createdDate" />
        <result column="updater_id" property="updaterId" />
        <result column="updater_name" property="updaterName" />
        <result column="updated_date" property="updatedDate" />
        <result column="version_code" property="versionCode" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, company_code, company_name, contact_name, contact_phone, default_contact_flag, status, platform_code, creator_id, creator_name, created_date, updater_id,updater_name, updated_date, version_code,del_flag
    </sql>

</mapper>
