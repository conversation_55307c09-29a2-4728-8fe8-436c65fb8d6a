<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.ctpcore.partner.infrastructure.repository.mapper.UmShareCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wanlianyida.ctpcore.partner.infrastructure.repository.po.UmInviteCodePO">
        <result column="id" property="id"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="enable_flag" property="enableFlag"/>
        <result column="expire_time" property="expireTime"/>
        <result column="short_link_url" property="shortLinkUrl"/>
        <result column="used_count" property="usedCount"/>
        <result column="company_id" property="companyId"/>
        <result column="company_name" property="companyName"/>
        <result column="creator_id" property="creatorId"/>
        <result column="created_time" property="createdTime"/>
        <result column="last_updater_id" property="lastUpdaterId"/>
        <result column="last_updated_time" property="lastUpdatedTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        share_code, status, expire_time, short_link_url, used_count, company_id, company_name, creator_id, created_time, last_updater_id, last_updated_time, del_flag
    </sql>
</mapper>