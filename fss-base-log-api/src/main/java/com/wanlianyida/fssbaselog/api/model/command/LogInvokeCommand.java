package com.wanlianyida.fssbaselog.api.model.command;

import lombok.Data;

import java.io.Serializable;

@Data
public class LogInvokeCommand implements Serializable {
    private static final long serialVersionUID = 8558073837326091742L;

    private String bizId;

    private String bizType;

    private String faceName;

    private String invokeUrl;

    private String invokeParam;

    private String invokeResult;

    private String status;

    private String bizStatus;

    private String invokeCostTime;

    private String startTime;

    private String endTime;

    private String operator;

    // 业务key 用于查询
    private String bizKey;
}
