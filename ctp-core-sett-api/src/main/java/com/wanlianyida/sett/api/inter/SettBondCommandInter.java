package com.wanlianyida.sett.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.api.model.command.SettBondApiCommand;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;

@Api("保证金操作相关")
@FeignClient(name = "ctp-core-sett", contextId = "settBondCommandInter", path = "/ctp-core-sett")
public interface SettBondCommandInter {

    /**
     * 保证金分页查询
     * @param
     * @return
     */
    @RequestMapping("/bond/command-bond-submit")
    ResultMode<Void> commandBondSubmit(SettBondApiCommand command);
}
