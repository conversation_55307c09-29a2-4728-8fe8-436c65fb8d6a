package com.wanlianyida.sett.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.sett.api.model.dto.SettBondApiDTO;
import com.wanlianyida.sett.api.model.dto.SettBondDetailApiDTO;
import com.wanlianyida.sett.api.model.query.SettBondDetailApiQuery;
import com.wanlianyida.sett.api.model.query.SettBondListApiQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Api("保障金查询相关")
@FeignClient(name = "ctp-core-sett", contextId = "settBondQueryInter", path = "/ctp-core-sett")
public interface SettBondQueryInter {

    /**
     * 保证金分页查询
     * @param
     * @return
     */
    @RequestMapping("/bond/query-bond-list")
    ResultMode<List<SettBondApiDTO>> queryBondList(PagingInfo<SettBondListApiQuery> query);

    /**
     * 保证金单据详情查询
     * @param
     * @return
     */
    @RequestMapping("/bond/query-bond-detail")
    ResultMode<SettBondDetailApiDTO> queryBondDetail(SettBondDetailApiQuery bean);
}
