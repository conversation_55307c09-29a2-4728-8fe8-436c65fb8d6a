package com.wanlianyida.sett.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * BankOpenCallBackCommand
 *
 * <AUTHOR>
 * @since 2025/4/24
 */

@Data
public class BankCardBindCallBackCommand {


    /**
     * 业务流水号
     * 必填项
     */
    @NotBlank(message = "业务流水号不能为空")
    private String outRequestNo;

    /**
     * 绑卡状态 [10-未绑定,20-绑定中,30-绑卡成功,40-绑卡失败]
     * 必填项
     */
    @NotBlank(message = "绑卡状态不能为空")
    private String bindStatus;

    /**
     *  描述信息
     */
    private String message;


}