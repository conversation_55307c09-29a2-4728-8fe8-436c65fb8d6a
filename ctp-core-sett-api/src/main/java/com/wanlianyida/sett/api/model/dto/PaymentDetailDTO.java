package com.wanlianyida.sett.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 付款单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
public class PaymentDetailDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 付款单号
     */
    private String paymentNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 结算单号
     */
    private String settNo;

    private String platformFlowNo;


    /**
     * 交易类型[10-预付款20-尾款]
     */
    private Integer transactionType;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;

    /**
     * 付款状态[10-待付款20-待收款30-付款成功40-付款失败]
     */
    private Integer paymentStatus;

    /**
     * 付款方式[20-银行承兑30-商业承兑40-电汇(电子钱包)50-电汇(银行账户)]
     */
    private String paymentMethod;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 到款确认时间
     */
    private Date receiptConfirmTime;

    /**
     * 付款公司id
     */
    private String payerCompanyId;

    /**
     * 付款公司名称
     */
    private String payerCompanyName;

    /**
     * 收款公司id
     */
    private String receiverCompanyId;

    //收款公司名称
    private String receiverCompanyName;



}
