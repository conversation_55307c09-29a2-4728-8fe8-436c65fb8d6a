package com.wanlianyida.sett.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@Data
public class SettBondListApiQuery {

    /**
     * 保证金单据号
     */
    private String bondNo;

    /**
     * 付款方公司名称
     */
    private String payerCompanyName;

    /**
     * 收款方公司名称
     */
    private String receiverCompanyName;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 创建时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDateStart;
    /**
     * 创建时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDateEnd;

    /**
     * 业务单据号
     */
    private String orderNo;

    /**
     * 保证金状态[110-待付款；120-待确认收款；130-已付款；140-确认收款驳回]
     */
    private String bondStatus;

    /**
     * 公司类型[10卖家20买家]
     */
    private String companyType;



}
