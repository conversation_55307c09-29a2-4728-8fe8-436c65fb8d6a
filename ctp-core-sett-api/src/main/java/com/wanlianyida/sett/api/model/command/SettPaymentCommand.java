package com.wanlianyida.sett.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SettPaymentCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 付款单id
     */
    private Long id;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 已付金额/实付金额
     */
//    @DecimalMin(value = "0.01", message = "实付金额不可为0")
    private BigDecimal paidAmount;

    /**
     * 付款方式[20-银行承兑30-商业承兑40-电汇(电子钱包)50-电汇(银行账户)]
     */
//    @NotBlank(message = "付款方式不能为空")
    private String paymentMethod;

    /**
     * 付款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @NotNull(message = "付款时间不能为空")
    private Date paymentTime;

    /**
     * 收款方开户名称
     */
    private String receiverAccountName;
    /**
     * 收款方银行账号
     */
    private String receiverBankAccountNumber;
    /**
     * 收款方开户行联行号
     */
    private String receiverBankUnionNumber;
    /**
     * 收款方开户行名称
     */
    private String receiverBankName;
    /**
     * 交易类型[10-预付款20-尾款]
     */
    private String transactionType;

    private String bankChannelCode; // 银行渠道编

    /**
     * 付款凭证
     */
//    @NotNull(message = "付款凭证不能为空")
//    @Size(min = 1,message = "付款凭证不能为空")
    private List<settPaymentAttachment> settPaymentAttachmentList;

    /**
     * 付款单对应多个附件
     */
    @Data
    public static class settPaymentAttachment {
        /**
         * 附件url
         */
        private String fileUrl;
        /**
         * 附件名
         */
        private String fileName;
        /**
         * 文件大小
         */
        private String fileSize;
    }
}


