package com.wanlianyida.sett.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PaymentDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 付款单号
     */
    private String paymentNo;

    /**
     * 交易类型[10-预付款20-尾款]
     */
    private Integer transactionType;

    /**
     * 付款状态[10-待付款20-待收款30-付款成功40-付款失败]
     */
    private String paymentStatus;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;

    /**
     * 付款方式[20-银行承兑30-商业承兑40-电汇(电子钱包)50-电汇(银行账户)]
     */
    private String paymentMethod;

    /**
     * 付款时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date paymentTime;

    /**
     * 到款确认时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiptConfirmTime;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 付款单凭证集合
     */
    private List<PaymentAttachment> attachmentList;

    /**
     * 付款单凭证
     */
    @Data
    public static class PaymentAttachment {
        /**
         * 附件url
         */
        private String fileUrl;

        /**
         * 附件名
         */
        private String fileName;
    }

}
