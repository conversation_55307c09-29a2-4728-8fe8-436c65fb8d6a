package com.wanlianyida.fssbaseprocess.api.model.dto;

import lombok.Data;

@Data
public class HistoryAuditNodeResDTO {

    /**
     * 节点id
     */
    private String nodeId;
    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点状态
     */
    private String nodeStatus;

    /**
     * 审批用时
     */
    private String usedTime;

    /**
     * 处理时间
     */
    private String handleTime;

    /**
     * 逐签序号
     */
    private Integer orderNo;

    /**
     * 审批人信息
     */
    private ApproveUserDTO approveUser;

    /**
     * activityId
     */
    private String activityId;


    /**
     * 审批备注
     */
    private String completeComment;
}
