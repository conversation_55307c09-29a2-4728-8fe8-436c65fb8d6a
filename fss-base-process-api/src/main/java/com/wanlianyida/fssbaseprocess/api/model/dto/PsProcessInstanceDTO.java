package com.wanlianyida.fssbaseprocess.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PsProcessInstanceDTO {
    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 流程定义key
     */
    private String definitionKey;

    /**
     * 流程定义id
     */
    private String definitionId;

    /**
     * 流程部署id
     */
    private String deploymentId;

    /**
     * 流程部署版本号
     */
    private String deploymentVersion;

    /**
     * 当前活动节点id
     */
    private String activityId;

    /**
     * 当前活动节点名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 流程状态 10运行中 20挂起 30 已结束
     */
    private String state;

    /**
     * 是否结束 1是 0否
     */
    private Integer ended;
}
