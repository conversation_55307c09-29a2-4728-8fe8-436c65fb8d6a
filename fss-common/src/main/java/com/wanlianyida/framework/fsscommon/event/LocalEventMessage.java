package com.wanlianyida.framework.fsscommon.event;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 本地消息类，用于封装本地消息的数据结构。
 */
@Data
public class LocalEventMessage<T> implements Serializable {

    public static <T> LocalEventMessage.EventMessage<T> buildEventMessage(String eventType, T data) {
        if (StrUtil.isBlank(eventType)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        return EventMessage.<T>builder()
                .messageId(StrUtil.uuid().replace("-", ""))
                .eventType(eventType)
                .timestamp(new Date())
                .data(data)
                .build();
    }

    /**
     * 事件消息类，用于封装具体的事件数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EventMessage<T> {
        /**
         * 事件类型
         */
        private String eventType;

        /**
         * 唯一标识ID
         */
        private String messageId;

        /**
         * 业务id
         */
        private String busId;

        /**
         * 消息key
         */
        String messageKey;

        /**
         * 时间戳
         */
        private Date timestamp;

        /**
         * 事件数据
         */
        private T data;
    }
}
