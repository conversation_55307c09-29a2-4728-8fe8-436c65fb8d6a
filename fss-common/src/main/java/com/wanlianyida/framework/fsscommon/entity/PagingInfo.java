package com.wanlianyida.framework.fsscommon.entity;

import java.io.Serializable;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月22 10:27
 */
public class PagingInfo<T> implements Serializable {

    public PagingInfo() {
    }

    /**
     * 查询当前页及页长，默认不返回总记录（提交查询速度）
     *
     * @param currentPage 当前页
     * @param pageLength  每页多少条
     */
    public PagingInfo(int currentPage, int pageLength) {
        this.currentPage = currentPage;
        this.pageLength = pageLength;
        this.countTotal = false;
    }

    /**
     * 查询当前页及页长
     *
     * @param currentPage 当前页
     * @param pageLength  每页多少条
     * @param countTotal     是否返回总页数 默认false
     */
    public PagingInfo(int currentPage, int pageLength, boolean countTotal) {
        this.currentPage = currentPage;
        this.pageLength = pageLength;
        this.countTotal = countTotal;
    }

    /**
     * 查询当前页及页长
     *
     * @param filterModel 查询条件实体内容
     * @param currentPage 当前页
     * @param pageLength  每页多少条
     * @param countTotal     是否返回总页数 默认false
     */
    public PagingInfo(T filterModel, int currentPage, int pageLength, boolean countTotal) {
        this.filterModel = filterModel;
        this.currentPage = currentPage;
        this.pageLength = pageLength;
        this.countTotal = countTotal;
    }

    /**
     * 查询条件实体内容
     */
    @Valid
    public T filterModel;
    /**
     * 是否返回总页数，（设置否false）这样可以提高查询速度
     */
    private boolean countTotal = false;

    /**
     * 当前页 以1开始为第一页
     */
    public int currentPage = 1;

    /**
     * 每页多少条
     */
    public int pageLength = 20;

    /**
     * 排序方向 0--asc ,1 --desc
     */
    public String sort = "";

    public T getFilterModel() {
        return filterModel;
    }

    public void setFilterModel(T filterModel) {
        this.filterModel = filterModel;
    }

    public boolean getCountTotal() {
        return countTotal;
    }

    public void setCountTotal(boolean countTotal) {
        this.countTotal = countTotal;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getPageLength() {
        return pageLength;
    }

    public void setPageLength(int pageLength) {
        this.pageLength = pageLength;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
}
