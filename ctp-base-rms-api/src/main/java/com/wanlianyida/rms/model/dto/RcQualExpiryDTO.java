package com.wanlianyida.rms.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 资质到期表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
public class RcQualExpiryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 法人名称
     */
    private String legalPersonName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 资质类型[10-企业资质,20-法人资质]
     */
    private String qualType;

    /**
     * 资质名称
     */
    private String qualName;

    /**
     * 资质到期日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date qualExpireDate;

    /**
     * 距到期天数
     */
    private Integer daysUntilExpire;

    /**
     * 到期状态[10-已到期,20-未到期]
     */
    private String expireStatus;

    /**
     * 风控结果[10-阻断,20-放行]
     */
    private String rcResult;

    /**
     * 有效标记[0-否,1-是]
     */
    private Integer validFlag;


    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;


}
