package com.wanlianyida.rms.model.command;

import lombok.Data;

import java.io.Serializable;

/**
 * 企业信息
 */
@Data
public class CompanyCommand implements Serializable {
    private static final long serialVersionUID = -394155335008910404L;


    /**
     * 联系人姓名
     */
    private String linkName;

    /**
     * 联系人电话
     */
    private String linkTelephone;

    /**
     * 账号信息
     */
    private String account;


    /**
     * 企业di
     */
    private String companyId;

    /**
     * 企业name
     */
    private String companyName;

    /**
     * 卖家店铺id(仅卖方有)
     */
    private Long shopId;

    /**
     * 卖家店铺名称(仅卖方有)
     */
    private String shopName;

    /**
     * 卖家店铺联系人名称(仅卖方有)
     */
    private String shopLinkName;

    /**
     * 卖家店铺联系人电话(仅卖方有)
     */
    private String shopLinkTelephone;

    /**
     * 营业执照编号
     */
    private String licenseNo;

    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 法人身份证号码
     */
    private String legalPersonIdCard;

    /**
     * 联系人手机
     */
    private String userMobile;

}
