package com.wanlianyida.rms.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 巡检结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RcInspResultDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 营业执照经营有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate bizLicenceExpireDate;

    /**
     * 法人名称
     */
    private String legalPersonName;

    /**
     * 法人证件类型
     */
    private String legalPersonCertType;

    /**
     * 法人证件有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate legalPersonCertExpireDate;

    /**
     * 巡检批次
     */
    private String inspBatch;

    /**
     * 巡检时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime inspTime;

    /**
     * 巡检结果[10-通过,20-不通过]
     */
    private String inspResult;

    /**
     * 巡检不通过原因
     */
    private String inspRejectReason;

    /**
     * 经营状态[10-正常,20-存续,30-待注销,40-开业,50-在业,60-在营,70-异常]
     */
    private String businessStatus;

    /**
     * 企业注册日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate regDate;


}
