商贸项目接入rocketMq步骤：

## 1. 引入包
```
        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>ctp-starters-rocketmq</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
```
## 2. 配置
```
rocketmq:
  producer:
    endpoints: ep-8vbic6830cb93590a073.epsrv-8vbrgx0ktiq9tznfndcc.cn-zhangjiakou.privatelink.aliyuncs.com:8080
    access-key: 859hrYdy218opcHX
    secret-key: 4Oxl8CCOWiH9C1Xv
    topic: topic_normal

```
## 3.使用示例

### 3.1生产者
```
    @Resource
    private RocketMQClientTemplate rocketMQClientTemplate;

    /**
     * 同步发送消息：调用该方法后，程序会阻塞直到消息发送过程完全结束。
     * 适用场景：适用于对消息发送可靠性要求较高的场景，如重要通知、短信相关业务。
     * 重试机制：方法内部会根据 DefaultMQProducer 中设置的重试次数进行重试，这可能导致消息重复发送到 Broker。
     * 重复消息处理：注释提醒开发者需要自行处理可能出现的消息重复问题。
     *
     * @param topic
     * @param eventMessage
     */
    public SendReceipt publishNormalMessage(String topic, MqEventMessage.EventMessage<?> eventMessage) {
        SendReceipt sendReceipt;
        try {
            sendReceipt = rocketMQClientTemplate.syncSendNormalMessage(topic, eventMessage.getData());
            log.info("normalSend to topic:{} sendReceipt:{}", topic, sendReceipt);
        } catch (Exception e) {
            log.error("发送MQ消息失败 topic:{} message:{}", topic, JSONUtil.toJsonStr(eventMessage), e);
            throw e;
        }
        return sendReceipt;
    }
```

### 3.2消费者
```

```

## 4.注意事项
```

```