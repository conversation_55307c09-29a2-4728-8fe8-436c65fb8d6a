package com.wanlianyida.fssuserauth.api.inter.auth;

import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.TenantDiverseInfoCommand;
import com.wanlianyida.fssuserauth.api.model.command.TenantUpdateStatusCommand;
import com.wanlianyida.fssuserauth.api.model.dto.TenantDiverseInfoDTO;
import com.wanlianyida.fssuserauth.api.model.query.TenantDiverseInfoQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 租户多元化信息表 Inter
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Api(value = "租户多元化信息表feign api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth:}", name = "fss-base-user-auth", contextId = "TenantDiverseInfoInter", path = "/fss-base-user-auth")
public interface TenantDiverseInfoInter {

    /**
     * 分页查询
     * @param pagingInfo 分页查询参数
     * @return {@link ResponseMessage}<{@link TenantDiverseInfoDTO}>
     */
    @PostMapping("/tenantDiverseInfo/queryPage")
    ResponseMessage<List<TenantDiverseInfoDTO>> queryPage(@RequestBody PagingInfo<TenantDiverseInfoQuery> pagingInfo);

    /**
     * 列表查询
     * @param query 查询参数
     * @return {@link ResponseMessage}<{@link TenantDiverseInfoDTO}>
     */
    @PostMapping("/tenantDiverseInfo/queryList")
    ResponseMessage<List<TenantDiverseInfoDTO>> queryList(@RequestBody TenantDiverseInfoQuery query);

    /**
	 * 新增
	 * @param command
	 * @return String
	 */
	@PostMapping("/tenantDiverseInfo/insert")
	ResponseMessage<String> insert(@RequestBody @Validated TenantDiverseInfoCommand command);

	/**
	 * 修改
	 * @param command
	 * @return String
	 */
	@PostMapping("/tenantDiverseInfo/update")
	ResponseMessage<String> update(@RequestBody TenantDiverseInfoCommand command);

	/**
	 * 逻辑删除
	 * @param id
	 * @return String
	 */
	@PostMapping("/tenantDiverseInfo/delete/{id}")
	ResponseMessage<String> deleteById(@PathVariable("id") Long id);

	/**
	 * 根据operatorId删除租户
	 * @param operatorId 操作员Id
	 * @return 无
	 */
	@PostMapping("/tenantDiverseInfo/delete/by-operatorId/{operatorId}")
	ResponseMessage<Void> deleteByOperatorId(@PathVariable("operatorId") String operatorId);

	/**
	 * 修改租户状态
	 * @param command 修改租户状态command
	 * @return 无
	 */
	@PostMapping("/tenantDiverseInfo/modify-status")
	ResponseMessage<Void> modifyStatus(@RequestBody TenantUpdateStatusCommand command);
}
