package com.wanlianyida.fssuserauth.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "主体银行账户信息表")
@Data
public class PlatformMianBodyBankAccountDTO {

    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "平台运营主体管理表主键id")
    private String operationMainBodyId;
    @ApiModelProperty(value = "主体账户名称")
    private String bodyAccountName;
    @ApiModelProperty(value = "银行账户")
    private String bankAccount;
    @ApiModelProperty(value = "开户行")
    private String openBank;
    @ApiModelProperty(value = "主体账户类型:1-收款(交易签约)、2-收款(网络货运)、3-充值(网络货运)4付款")
    private String type;

    //#region 新增字段 QLMR-48_网络货运主体更新
    @ApiModelProperty(value = "开户行支行名称")
    private String subbranch;

    @ApiModelProperty(value = "是否是默认渠道 0-否 1-是")
    private String defaultChannel;

    @ApiModelProperty(value = "支付方式 32-银行转账，9-虚拟账户")
    private String paymentType;

    @ApiModelProperty(value = "委托转账【0.不支持, 1.支持】")
    private String splitSwitch;

    @ApiModelProperty(value = "企业id")
    private String companyId;

}
