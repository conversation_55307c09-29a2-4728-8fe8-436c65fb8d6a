package com.wanlianyida.fssuserauth.api.model.platform.command;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 平台信息更新Command
 *
 * <AUTHOR>
 * @date 2025-07-11 14:50:21
 */
@Data
@Accessors(chain = true)
@NotNull
public class PlatformInfoUpdateCommand {

    /**
     * id
     */
    @NotNull(message = "id不可为空")
    private Long id;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 平台地址
     */
    private String platformUrl;

    /**
     * 超级管理员Id
     */
    private String superAdminId;

    /**
     * 是否启用鉴权
     */
    private String authFlag;

    /**
     * 描述
     */
    private String remark;

    /**
     * 最后更新人
     */
    private String lastUpdaterId;

}
