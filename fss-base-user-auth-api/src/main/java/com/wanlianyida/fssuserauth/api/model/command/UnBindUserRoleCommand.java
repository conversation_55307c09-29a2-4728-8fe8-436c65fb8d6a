package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 用户角色解绑command
 *
 * <AUTHOR>
 * @date 2025-07-30 09:40:33
 */
@Data
@Accessors(chain = true)
public class UnBindUserRoleCommand {

    /**
     * 系统类型
     */
    @NotBlank(message = "系统类型不可为空")
    private String sysType;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不可为空")
    private String userId;
}
