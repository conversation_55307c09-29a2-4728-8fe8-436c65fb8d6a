package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.UserUmPermissionPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.dto.UserPermissionFunctionsPlatformDTO;
import com.wanlianyida.fssuserauth.api.model.query.UserPermissionPlatformQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-账户管理")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", value = "platform", path = "/platform/PlatformUmUserFunctions",contextId = "PlatformUmUserFunctionsInter")
public interface UserUmPermissionInter {

    /**
     * 根据账户id查询菜单
     * @param query
     * @return
     */
    @PostMapping("/queryListBySysType")
    ResponseMessage<List<UserPermissionFunctionsPlatformDTO>> queryListBySysType(@RequestBody UserPermissionPlatformQuery query);
    /**
     * 保存用户权限表
     * @param command
     * @return
     */
    @PostMapping("/saveUserFunctions")
    ResponseMessage<?> saveUserFunctions(@RequestBody UserUmPermissionPlatformCommand command);
    /**
     * 删除用户权限表
     * @param command
     * @return
     */
    @PostMapping("/deleteUmUserFunctions")
    ResponseMessage<?> deleteUmUserFunctions(@RequestBody UserUmPermissionPlatformCommand command);

}
