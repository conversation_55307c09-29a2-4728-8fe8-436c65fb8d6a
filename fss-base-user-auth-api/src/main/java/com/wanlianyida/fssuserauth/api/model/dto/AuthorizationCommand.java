package com.wanlianyida.fssuserauth.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 鉴权指令
 * <p>
 * 用于内部服务发起：某系统、某公司(可选)、某用户是否有某资源访问权限的校验。
 * </p>
 * <AUTHOR>
 * @date 2025-06-27 10:21
 */
@Data
@Accessors(chain = true)
public class AuthorizationCommand {

    /**
     * 系统类型
     */
    @NotBlank(message = "系统类型不能为空")
    private String sysType;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 资源url
     * <pre>注：不包含 servlet-context-path</pre>
     */
    @NotBlank(message = "资源url不能为空")
    private String resourceUrl;

    /**
     * 公司id（可选）
     */
    private String companyId;

}
