package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.dto.UserPermissionFunctionsPlatformDTO;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-用户菜单管理")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", value = "platform", path = "/platform/subSystemFunction", contextId = "PlatformSubSystemFunctionInter")
public interface UserSubSystemFunctionInter {

    /**
     * 查询当前用户所拥有的角色的菜单 -- 管理端
     * @param sysType 系统类型
     * @return
     */
    @PostMapping(value = "/getUserFunction")
    ResponseMessage<List<UserPermissionFunctionsPlatformDTO>> getUserFunction(@RequestBody String sysType);

    /**
     * 查询当前用户所拥有的角色的菜单 -- 用户端
     * @param sysType 系统类型
     * @return
     */
    @PostMapping(value = "/queryAllUserFunction")
    ResponseMessage<List<UserPermissionFunctionsPlatformDTO>> queryAllUserFunction(@RequestBody String sysType);
}
