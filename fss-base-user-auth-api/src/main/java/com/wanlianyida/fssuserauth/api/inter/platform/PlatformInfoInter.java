package com.wanlianyida.fssuserauth.api.inter.platform;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.platform.command.PlatformInfoAddCommand;
import com.wanlianyida.fssuserauth.api.model.platform.command.PlatformInfoUpdateCommand;
import com.wanlianyida.fssuserauth.api.model.platform.dto.PlatformInfoDTO;
import com.wanlianyida.fssuserauth.api.model.platform.query.PlatformInfoQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 平台信息Inter
 *
 * <AUTHOR>
 * @date 2025-07-14 10:00:44
 */
@Api(value = "平台信息feign api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth:}", name = "fss-base-user-auth", contextId = "PlatformInfoInter", path = "/fss-base-user-auth")
public interface PlatformInfoInter {

    /**
     * 平台信息分页查询
     *
     * @param query 查询入参
     * @return 平台信息
     */
    @PostMapping("/platform-info/page/query")
    ResponseMessage<List<PlatformInfoDTO>> queryPage(@RequestBody PlatformInfoQuery query);

    /**
     * 平台信息列表查询
     *
     * @param query 查询入参
     * @return 平台信息
     */
    @PostMapping("/platform-info/list/query")
    ResponseMessage<List<PlatformInfoDTO>> queryList(@RequestBody PlatformInfoQuery query);

    /**
     * 新增平台信息
     *
     * @param addCommand 新增入参
     * @return 无
     */
    @PostMapping("/platform-info/add")
    ResponseMessage<Void> add(@RequestBody PlatformInfoAddCommand addCommand);

    /**
     * 更新平台信息
     *
     * @param updateCommand 更新入参
     * @return 无
     */
    @PostMapping("/platform-info/update")
    ResponseMessage<Void> update(@RequestBody PlatformInfoUpdateCommand updateCommand);

    /**
     * 根据id查询平台信息
     *
     * @param id 平台id
     * @return 平台信息
     */
    @PostMapping("/platform-info/one-by-id/{id}")
    ResponseMessage<PlatformInfoDTO> getById(@PathVariable("id") Long id);

    /**
     * 删除平台
     *
     * @param id 平台id
     * @return 无
     */
    @PostMapping("/platform-info/delete/{id}")
    ResponseMessage<Void> delete(@PathVariable("id") Long id);

    /**
     * 根据平台code查询平台信息
     *
     * @param platformCodes 平台codes
     * @return 平台信息
     */
    @PostMapping("/platform-info/list/by/platform-codes")
    ResponseMessage<List<PlatformInfoDTO>> listByPlatformCodes(List<String> platformCodes);
}
