package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * 角色解绑定资源
 *
 * <AUTHOR>
 * @date 2025-05-14 15:15
 */
@Data
@Accessors(chain = true)
public class ResourceUnBindCommand {

    /**
     * 资源id
     */
    @NotNull(message = "资源id不能为空")
    private List<Long> resourceIds;
}
