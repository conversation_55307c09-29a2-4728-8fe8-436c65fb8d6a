package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.dto.PlatformUmUserbaseinfoDTO;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 企业用户信息 Inter
 *
 * <AUTHOR>
 * @date 2024-12-2
 */
@Api(value = "企业用户信息 api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", name = "platform", path = "/platform/", contextId = "Platformumuserbaseinfo")
public interface PlatformUserBaseInfoInter {

	/**
	 * 用户端登录后获取企业账号信息
	 *
	 * @return {@link PlatformUmUserbaseinfoDTO}
	 */
	@PostMapping("/PlatformUmUserbaseinfo/getUserInfo")
	ResponseMessage<List<PlatformUmUserbaseinfoDTO>> getUserInfo();

	/**
	 * 退出登录
	 * @return
	 */
	@PostMapping("/PlatformUmUserbaseinfo/webLogout")
	ResponseMessage<List<String>> webLogout();
}
