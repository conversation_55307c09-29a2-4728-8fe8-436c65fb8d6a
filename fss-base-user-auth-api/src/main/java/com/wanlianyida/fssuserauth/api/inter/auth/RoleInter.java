package com.wanlianyida.fssuserauth.api.inter.auth;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.PlatformRoleDeleteCommand;
import com.wanlianyida.fssuserauth.api.model.command.ResourceBindCommand;
import com.wanlianyida.fssuserauth.api.model.command.ResourceUnBindCommand;
import com.wanlianyida.fssuserauth.api.model.dto.ResourceDTO;
import com.wanlianyida.fssuserauth.api.model.dto.RoleAddCommand;
import com.wanlianyida.fssuserauth.api.model.dto.RoleDTO;
import com.wanlianyida.fssuserauth.api.model.dto.RoleUpdateCommand;
import com.wanlianyida.fssuserauth.api.model.query.PlatformRolePageQuery;
import com.wanlianyida.fssuserauth.api.model.query.PlatformRoleResourceQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * platform 子系统角色管理
 */
@Api(value = "子系统角色管理", tags = {"子系统角色管理-操作接口"})
@FeignClient(url = "${gtsp.api.url.auth:}", name = "fss-base-user-auth", contextId = "roleInter", path = "/fss-base-user-auth")
public interface RoleInter {

    @RequestMapping(value = "/role/create", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<?> create(@RequestBody @Validated RoleAddCommand command);

    /**
     * 根据角色id删除角色
     *
     * @param command 角色删除命令
     * @return 删除结果
     */
    @RequestMapping(value = "/role/delete", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<?> delete(@RequestBody @Validated PlatformRoleDeleteCommand command);

    /**
     * 根据角色id更新角色
     *
     * @param command 信息
     * @return 结果
     */
    @RequestMapping(value = "/role/update", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<?> update(@RequestBody @Validated RoleUpdateCommand command);

    /**
     * 根据角色id查询资源列表
     *
     * @param query 查询条件
     * @return 资源列表
     */
    @RequestMapping(value = "/role/resource", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<ResourceDTO>> listResourcesByRole(@RequestBody @Validated PlatformRoleResourceQuery query);

    /**
     * 绑定资源到角色
     *
     * @param command 绑定资源命令
     */
    @RequestMapping(value = "/role/bind/resources", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<?> bindResources(@RequestBody @Validated ResourceBindCommand command);

    /**
     * 解绑资源
     *
     * @param roleId  角色id
     * @param command 解绑资源命令
     */
    @RequestMapping(value = "/role/{roleId}/unbind/resources", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<?> unbindResources(@PathVariable Long roleId, @RequestBody @Validated ResourceUnBindCommand command);

    /**
     * 根据平台查询角色列表
     *
     * @param query 查询条件
     * @return 角色列表
     */
    @RequestMapping(value = "/role/platform", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<RoleDTO>> listByPlatform(@RequestBody @Validated PlatformRolePageQuery query);

    /**
     * 查询角色所属平台id
     *
     * @param roleIds 角色ids
     * @return 系统类型
     */
    @RequestMapping(value = "/role/find/sys-type/by-role-ids", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<String>> findSysTypeByRoleIds(@RequestBody List<Integer> roleIds);
}
