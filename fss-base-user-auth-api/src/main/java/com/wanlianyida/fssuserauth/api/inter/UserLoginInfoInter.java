package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.UserLoginInfoPlatformCommand;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-用户关联系统")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", name = "platform", path = "/platform/PlatformUmLogininfo", contextId = "PlatformUmLoginInfoInter")
public interface UserLoginInfoInter {

    /**
     * 修改密码
     *
     * @param command 操作
     * @return
     */
    @PostMapping(value = "/updatePW")
    ResponseMessage<List<String>> updatePw(@RequestBody UserLoginInfoPlatformCommand command);
}
