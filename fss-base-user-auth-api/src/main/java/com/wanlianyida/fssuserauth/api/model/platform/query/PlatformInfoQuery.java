package com.wanlianyida.fssuserauth.api.model.platform.query;

import com.wanlianyida.fssmodel.PageRequest;
import com.wanlianyida.fssmodel.Sort;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.LinkedList;

/**
 * 平台查询query
 *
 * <AUTHOR>
 * @date 2025-07-11 14:50:21
 */
@Data
@Accessors(chain = true)
public class PlatformInfoQuery {

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 当前页码
     */
    @Min(value = 1, message = "当前页码必须大于0")
    private int pageNum = 1;

    /**
     * 每页数量, 默认20
     */
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 100, message = "每页数量不能大于100")
    private int pageSize = 20;

    /**
     * 排序信息
     */
    private LinkedList<Sort> sort;

    /**
     * 是否返回总数
     */
    private boolean returnCount = false;

}
