package com.wanlianyida.fssuserauth.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 租户多元化信息表 Query
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class TenantDiverseInfoQuery {

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 租户id
	 */
	private String tenantId;

	/**
	 * 租户编码
	 */
	private String tenantCode;

	/**
	 * 用户类别:10个人,20企业,30员工
	 */
	private String category;

	/**
	 * 是否启用:1是,0否
	 */
	private String enableFlag;

	/**
	 * 平台编码:10大宗,20物流
	 */
	private String systemCode;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

	/**
	 * 租户企业id
	 */
	private String companyId;

}
