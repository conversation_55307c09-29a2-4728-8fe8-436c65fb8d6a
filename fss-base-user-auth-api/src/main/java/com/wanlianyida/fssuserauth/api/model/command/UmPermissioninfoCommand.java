package com.wanlianyida.fssuserauth.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotBlank;

/**
 * 角色组管理表,角色编码 Command
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UmPermissioninfoCommand implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * 角色组管理表,角色编码
	 */
	private Long permissionId;

	/**
	 * 角色编码
	 */
	@NotBlank(message = "角色编码不能为空")
	private String permissionCode;

	/**
	 * 公司id,标识权限是属于那个公司的
	 */
	private String companyId;

	/**
	 * 创建人id,查询当前权限是谁创建的
	 */
	private String userBaseId;

	/**
	 * 角色名称
	 */
	@NotBlank(message = "角色名称不能为空")
	private String permissionName;

	/**
	 * 角色类别【select:0-超级管理员,1-管理员,2-用户】
	 */
	private String permissionType;

	/**
	 * 用于区分二级类型，如3PL平台及承运商都有自己的超级管理员及管理员，用户等的角色之分，这些可以自行定义，主要用于查询区分。
	 */
	private Integer appId;

	/**
	 * 系统类型 1:3PL 2:4PL
	 */
	@NotBlank(message = "角色类型不能为空")
	private String sysType;

	/**
	 * 角色状态【select:11-有效,21-无效】
	 */
	private String status;

	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 修改人
	 */
	private String modifyBy;

	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date modifyDate;

	/**
	 * 版本号
	 */
	private String versionCode;

	/**
	 * 预留字段
	 */
	private String item1;

	/**
	 * 预留字段
	 */
	private String item2;

	/**
	 * 预留字段
	 */
	private String item3;

	/**
	 * 预留字段
	 */
	private String item4;


}
