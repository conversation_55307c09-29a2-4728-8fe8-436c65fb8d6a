package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.dto.UserAutoAssignPermissionsDTO;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-用户关联系统")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", value = "platform", path = "/platform/AuthorityNoLogin", contextId = "AuthorityNoLoginInter")
public interface UserAuthorityLoginInter {

    /**
     * 用户端用户自动分配接口
     * @param userAutoAssignPermissionsDTO
     * @return
     */
    @PostMapping("/autoAssignUserPermissions")
    ResponseMessage<?> autoAssignUserPermissions(@RequestBody UserAutoAssignPermissionsDTO userAutoAssignPermissionsDTO);

}
