package com.wanlianyida.fssuserauth.api.model.platform.command;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 平台信息新增Command
 *
 * <AUTHOR>
 * @date 2025-07-11 14:50:21
 */
@Data
@Accessors(chain = true)
public class PlatformInfoAddCommand {

    /**
     * 平台类型
     */
    @NotBlank(message = "平台类型不可为空")
    private String platformType;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不可为空")
    private String platformCode;

    /**
     * 平台名称
     */
    @NotBlank(message = "平台名称不可为空")
    private String platformName;

    /**
     * 平台地址
     */
    private String platformUrl;

    /**
     * 超级管理员Id
     */
    private String superAdminId;

    /**
     * 是否启用鉴权
     */
    private String authFlag;

    /**
     * 描述
     */
    private String remark;

    /**
     * 创建用户id
     */
    private String creatorId;


    /**
     * 最后更新人
     */
    private String lastUpdaterId;

}
