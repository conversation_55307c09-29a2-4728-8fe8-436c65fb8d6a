package com.wanlianyida.fssuserauth.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PlatformUmUserbaseinfoDTO {

    /**
     * (增加)时间
     */
    @ApiModelProperty(value = "(增加)时间", name = "addTime",example = "2021-1-4 14:23:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date addTime;
    /**
     * (增加)类型
     */
    @ApiModelProperty(value = "(增加)类型", name = "addType")
    private int addType;
    /**
     * 现居住地址
     */
    @ApiModelProperty(value = "现居住地址", name = "address")
    private String address;
    /**
     * 地址编码
     */
    @ApiModelProperty(value = "地址编码", name = "addressCode")
    private String addressCode;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", name = "age")
    private int age;
    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", name = "dateOfBirth")
    private String dateOfBirth;
    /**
     * 0-不删除，1-删除
     */
    @ApiModelProperty(value = "21-不删除，11-删除", name = "deleted")
    private String deleted;
    /**
     * 教育程度（参照GB/T 4658。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类）
     */
    @ApiModelProperty(value = "教育程度（参照GB/T 4658。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类）", name = "educationalStatus")
    private String educationalStatus;
    /**
     * 邮箱地址
     */
    @ApiModelProperty(value = "邮箱地址", name = "email")
    private String email;
    /**
     * 紧急联系人姓名
     */
    @ApiModelProperty(value = "紧急联系人姓名", name = "emergencyContact")
    private String emergencyContact;
    /**
     * 紧急联系人关系（即与户主关系)采用GB/T 4761二位数字代码表。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类。
     */
    @ApiModelProperty(value = "紧急联系人关系（即与户主关系)采用GB/T 4761二位数字代码表。编码方法：采用层次码，用2位数字表示，第1位数字表示大类，第2位数字表示小类。", name = "emergencyRelation")
    private String emergencyRelation;
    /**
     * 紧急联系人手机号
     */
    @ApiModelProperty(value = "紧急联系人手机号", name = "emergencyTelephone")
    private String emergencyTelephone;
    /**
     * 身份证有效期
     */
    @ApiModelProperty(value = "身份证有效期", name = "idCardDeadline",example = "2021-1-4 14:23:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date idCardDeadline;

    /**
     * 身份证ID唯一键，如果小孩或外国人没有身份证号就生成一个GUID来标识先
     */
    @ApiModelProperty(value = "身份证ID唯一键，如果小孩或外国人没有身份证号就生成一个GUID来标识先", name = "idNumber")
    private String idNumber;
    /**
     * 用户登录ID（关联UM_LoginInfo)
     */
    @ApiModelProperty(value = "用户登录ID（关联UM_LoginInfo)", name = "loginId")
    private String loginId;
    /**
     * 身份证是否长期有效(1:是, 2:否)
     */
    @ApiModelProperty(value = "身份证是否长期有效(11:是, 21:否)", name = "longTermEffective")
    private String longTermEffective;
    /**
     * 婚姻状态（GB/T 2261.2   --10未婚，20已婚 , 21初婚 , 22再婚 , 23复婚 , 30丧偶 , 40离婚 , 90未说明的婚姻状况......)
     */
    @ApiModelProperty(value = "婚姻状态（GB/T 2261.2   --10未婚，20已婚 , 21初婚 , 22再婚 , 23复婚 , 30丧偶 , 40离婚 , 90未说明的婚姻状况......)", name = "maritalStatus")
    private String maritalStatus;
    /**
     * 民族（GB-3304-91）
     */
    @ApiModelProperty(value = "民族（GB-3304-91）", name = "nation")
    private String nation;
    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码", name = "postalCode")
    private String postalCode;
    /**
     * 相片
     */
    @ApiModelProperty(value = "相片", name = "profilePhoto")
    private String profilePhoto;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;
    /**
     * 户口所在地(通过数据字典获得如431026000000  汝城县 湖南省郴州市汝城县)
     */
    @ApiModelProperty(value = "户口所在地(通过数据字典获得如431026000000  汝城县 湖南省郴州市汝城县) ", name = "residentCity")
    private String residentCity;
    /**
     * 户口类型(GB/T 17538   1-非农业户口(城镇居民）2--农业户口（农村居民）9-其他)
     */
    @ApiModelProperty(value = "户口类型(GB/T 17538   1-非农业户口(城镇居民）2--农业户口（农村居民）9-其他)", name = "residentType")
    private String residentType;
    /**
     * 性别（0未知性别，1男，2女，9未说明性别, GB/T 2261.1。编码方法：采用顺序码，用1位数字表示）
     */
    @ApiModelProperty(value = "性别（0未知性别，1男，2女，9未说明性别, GB/T 2261.1。编码方法：采用顺序码，用1位数字表示）", name = "sex")
    private String sex;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", name = "telephone")
    private String telephone;
    /**
     * 用户基本信息表
     */
    @ApiModelProperty(value = "用户基本信息表 ", name = "userBaseId")
    private String userBaseId;
    /**
     * 用户信息状态(1:有效, 2:无效)
     */
    @ApiModelProperty(value = "用户信息状态(11:有效, 21:无效)", name = "userStatus")
    private String userStatus;
    /**
     * 学生姓名
     * 姓名
     */
    @ApiModelProperty(value = "用户姓名", name = "username")
    private String username;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate",example = "2021-1-4 14:23:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate",example = "2021-1-4 14:23:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号", name = "wechat")
    private String wechat;
    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段", name = "ex")
    private String ex;

    /**
     * 省Code
     */
    @ApiModelProperty(value = "省Code", name = "province")
    private String province;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称", name = "provinceName")
    private String provinceName;

    /**
     * 市Code
     */
    @ApiModelProperty(value = "市Code", name = "city")
    private String city;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称", name = "cityName")
    private String cityName;

    /**
     * 区/县Code
     */
    @ApiModelProperty(value = "区/县Code", name = "area")
    private String area;

    /**
     * 区/县名称
     */
    @ApiModelProperty(value = "区/县名称", name = "areaName")
    private String areaName;

    /**
     * 镇/街道Code
     */
    @ApiModelProperty(value = "镇/街道Code", name = "street")
    private String street;

    /**
     * 镇/街道名称
     */
    @ApiModelProperty(value = "镇/街道名称", name = "streetName")
    private String streetName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", name = "addressDetail")
    private String addressDetail;

    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item1")
    private String item1;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item2")
    private String item2;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item3")
    private String item3;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item4")
    private String item4;

    /**
     * 新手机号码
     */
    @ApiModelProperty(value = "新手机号码", name = "exNowTelephone")
    private String exNowTelephone;
    /**
     * E签宝个人账号id
     */
    @ApiModelProperty(value = "E签宝个人账号id", name = "accountId")
    private String accountId;


    @ApiModelProperty(value = "公司是否冻结【11-是,,21-否】,默认是否", name = "freez")
    private String companyFreez;

    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码", name = "exValidatecode")
    private String exValidatecode;

    @ApiModelProperty(value = "统一社会信用代码（企业表）", name = "socialCreditCode")
    private String socialCreditCode;

    private String idCardStartDate;
    private String versionCode;
    //记录每个登录用户的唯一id，3pl同一用户登录两次  另个clentId
    private String clentId;

    /**
     * 系统类型，角色属于那个系统
     */
    @ApiModelProperty(value = "系统类型", name = "sysType")
    private String sysType;

    /**
     *  企业全称
     */
    @ApiModelProperty(value = "企业全称", name = "companyName")
    private String companyName;

    @ApiModelProperty(value = "统一社会信用代码（主体）", name = "unifiedSocialCreditCode")
    private String unifiedSocialCreditCode;
    @ApiModelProperty(value = "主体银行账户信息")
    private List<PlatformMianBodyBankAccountDTO> bankAccountList ;
    @ApiModelProperty(value = "平台3PL标识,0不是3PL 1是3PL", name = "platformFlag")
    private String platformFlag;
    @ApiModelProperty(value = "运营主体主键ID  平台运营主体管理表", name = "operationMainBodyId")
    private String operationMainBodyId;
    /**
     * 登录账号
     */
    @ApiModelProperty(value = "登录账号", name = "loginName")
    private String loginName;
    /**
     * 初始密码
     */
    @ApiModelProperty(value = "初始密码", name = "password")
    private String password;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id", name = "companyId")
    private String companyId;

    /**
     * 是否是企业账号
     */
    @ApiModelProperty(value = "是否是企业账号", name = "companyAccountFlag")
    private boolean companyAccountFlag;

    /**
     * 公司员工等级
     */
    @ApiModelProperty(value = "公司员工等级", name = "exLevelType")
    private int exLevelType;

    /**
     * 公司层级【0-母公司,1-一级子公司,2-二级子公司,...】
     */
    @ApiModelProperty(value = "公司员工等级【0-母公司,1-一级子公司,2-二级子公司,...】", name = "exCompanyLevel")
    private int exCompanyLevel;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称", name = "exCompanyName")
    private String exCompanyName;

    /**
     * 公司简称
     */
    @ApiModelProperty(value = "公司简称", name = "exCompanyShortName")
    private String exCompanyShortName;

    /**
     * 公司账号等级【1-管理员账号,2-子账号】
     */
    @ApiModelProperty(value = "公司账号等级【1-管理员账号,2-子账号】", name = "exCompanyLevel")
    private String exAccountLevel;

    /**
     * 登录版本号
     */
    @ApiModelProperty(value = "登录版本号", name = "exLoginVersionCode")
    private long exLoginVersionCode;

    /**
     * 会员类型【select:1-一般会员,2-项目会员】
     */
    @ApiModelProperty(value = "会员类型【select:1-一般会员,2-项目会员】", name = "exMemberType")
    private int exMemberType;

    /**
     * 紧急联系人关系说明
     */
    @ApiModelProperty(value = "紧急联系人关系说明", name = "exEmergencyRelationExplain")
    private String exEmergencyRelationExplain;

    /**
     * 岗位
     */
    @ApiModelProperty(value = "岗位", name = "exPost")
    private String exPost;

    /**
     * 司机id
     */
    @ApiModelProperty(value = "司机id", name = "exDriverId")
    private String exDriverId;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", name = "permissionName")
    private String exPermissionName;

    /**
     * 移动端设备id
     */
    @ApiModelProperty(value = "移动端设备id", name = "exDeviceId")
    private String exDeviceId;


    /**
     * 身份证认证状态10-通过  20-不通过
     */
    @ApiModelProperty(value = "移动端设备id", name = "verifyStatus")
    private String verifyStatus;

    @ApiModelProperty(value = "用户邀请码 ",name = "inviteCodeNumber")
    private String inviteCodeNumber;

    /**
     * 身份证认证状态详情状态
     * 10-未做两要素验证 21-两要素验证不一致 20- 两要素验证一致  22-两要素验证无记录
     * 30-三要素验证通过  31-三要素不通过
     * 40-活体验证通过 41-人工待复核
     * 50-人工复核通过
     */
    @ApiModelProperty(value = "移动端设备id", name = "verifyStatusDetail")
    private String verifyStatusDetail;

    @ApiModelProperty(value = "原始企业ID，即用户登录时的企业ID", name = "originalCompanyId")
    private String originalCompanyId;

    @ApiModelProperty(value = "扩展字段[姓名(手机号)]", name = "exUserNameAndTelephone")
    private String exUserNameAndTelephone;

    /**
     * 用于切换企业时记录操作菜单类型
     */
    @ApiModelProperty(value = "操作菜单类型", name = "OperMenuType")
    private String openMenuType;

}
