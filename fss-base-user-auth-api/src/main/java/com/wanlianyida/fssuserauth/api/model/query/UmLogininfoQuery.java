package com.wanlianyida.fssuserauth.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 登录用户管理表 Query
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UmLogininfoQuery {

	/**
	 * 登录表ID 登录用户管理表
	 */
	private String loginId;

	/**
	 * 登录名称以手机号码或者邮箱在库中唯一
	 */
	private String loginName;

	/**
	 * 绑定的手机号
	 */
	private String telephone;

	/**
	 * 登录密码
	 */
	private String password;

	/**
	 * 账号状态是否可用 【radio:11-是,21-否】
	 */
	private String loginState;

	/**
	 * 账号加密Key
	 */
	private String singleKey;

	/**
	 * 上次密码修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date passwordModifyTime;

	/**
	 * 创建来源【radio:1-PC端web页面,2-移动端App】
	 */
	private String createSourse;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 修改人
	 */
	private String modifyBy;

	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date modifyDate;

	/**
	 * 注册协议更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date registrationProtocolUpdateTime;

	/**
	 * 注册协议阅读时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date registrationProtocolReadTime;

	/**
	 * 版本号
	 */
	private String versionCode;

	/**
	 * 预留字段
	 */
	private String item1;

	/**
	 * 预留字段
	 */
	private String item2;

	/**
	 * 预留字段
	 */
	private String item3;

	/**
	 * 预留字段
	 */
	private String item4;

	/**
	 * 企业地址（省Code）
	 */
	private String province;

	/**
	 * 企业地址（省）（中文）
	 */
	private String provinceName;

	/**
	 * 企业地址（市Code）
	 */
	private String city;

	/**
	 * 企业地址（市）（中文）
	 */
	private String cityName;

	/**
	 * 企业地址（区/县Code）
	 */
	private String area;

	/**
	 * 企业地址（区/县）（中文）
	 */
	private String areaName;

	/**
	 * 企业地址（镇/街道Code）
	 */
	private String street;

	/**
	 * 企业地址（镇/街道）（中文）
	 */
	private String streetName;

	/**
	 * 企业地址（详细地址）
	 */
	private String addressDetail;

	/**
	 * 主管税务机关
	 */
	private String taxAuthority;

	/**
	 * 车队长标识【10-车队长 20-司机】
	 */
	private String category;

	/**
	 * 租户id
	 */
	private String tenantId;

	/**
	 * 密码强度:10强,20中,30弱
	 */
	private String pwdStrength;

	/**
	 * 系统类型
	 */
	private String sysType;

}
