package com.wanlianyida.fssuserauth.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 用户所属角色组表 Query
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UmUserpermissionQuery {

	/**
	 * 用户所属角色组表
	 */
	private Long usePermissionId;

	/**
	 * 所属账号，与各系统进行匹配或者是直接存储用户基础ID
	 */
	private String accountId;

	/**
	 * 角色组ID
	 */
	private Long permissionId;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 修改人
	 */
	private String modifyBy;

	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date modifyDate;

	/**
	 * 版本号
	 */
	private String versionCode;

	/**
	 * 预留字段
	 */
	private String item1;

	/**
	 * 预留字段
	 */
	private String item2;

	/**
	 * 预留字段
	 */
	private String item3;

	/**
	 * 预留字段
	 */
	private String item4;

	/**
	 * 所属账号ids
	 */
	private List<String> accountIdList;


}
