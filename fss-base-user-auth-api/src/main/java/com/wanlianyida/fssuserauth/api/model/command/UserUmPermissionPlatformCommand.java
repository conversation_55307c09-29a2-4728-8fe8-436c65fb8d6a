package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 09:52
 * @Description:
 **/
@Data
public class UserUmPermissionPlatformCommand {
    /**
     * 所属账号，与各系统进行匹配或者是直接存储用户基础ID
     */

    private String accountId;
    /**
     * 角色组ID
     */
    private Long permissionId;
    /**
     * 用户所属角色组表
     */
    private Long usePermissionId;
    /**
     * 扩展字段
     */
    private String ex;

    private List<Integer> permissionIdList;

    private List<Long> funcIdList;
}
