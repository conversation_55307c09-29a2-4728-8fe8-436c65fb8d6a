package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 租户授权关系 Command
 * 一个方法逻辑关联多张表
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class TenantAuthRelationCommand implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * 租户信息
	 */
	private TenantDiverseInfoCommand tenantDiverseInfoCommand;

	/**
	 * 账号信息
	 */
	private UmLogininfoCommand umLogininfoCommand;

	/**
	 * 租户角色关系
	 */
	private List<UmUserpermissionCommand> umUserpermissionCommandList;

	/**
	 * 租户平台关系
	 */
	private UserSystemRelCommand userSystemRelCommand;

}
