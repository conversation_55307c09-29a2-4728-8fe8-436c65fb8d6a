package com.wanlianyida.fssuserauth.api.inter.auth;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.TokenCancellationCommand;
import com.wanlianyida.fssuserauth.api.model.command.TokenContextCommand;
import com.wanlianyida.fssuserauth.api.model.command.TokenGenerateCommand;
import com.wanlianyida.fssuserauth.api.model.command.TokenVerifyCommand;
import com.wanlianyida.fssuserauth.api.model.dto.Token;
import com.wanlianyida.fssuserauth.api.model.dto.TokenDTO;
import com.wanlianyida.fssuserauth.api.model.query.TokenQuery;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 认证 Inter
 *
 * <AUTHOR>
 * @date 2025-07-03 17:40:55
 */
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth:}", name = "fss-base-user-auth", contextId = "AuthenticationInter", path = "/fss-base-user-auth")
public interface AuthenticationInter {

    /**
     * 生成令牌
     *
     * @param command 生成令牌命令
     * @return token 令牌
     */
    @PostMapping("/authentication/token/generate")
    ResponseMessage<TokenDTO> generateToken(@RequestBody @Validated TokenGenerateCommand command);

    /**
     * 验证token[succeed = true]
     * <pre>
     *  【一次会话，请验证一次，建议在过滤器/拦截器中实现】
     * </pre>
     *
     * @param command token验证命令
     * @return void
     */
    @PostMapping("/authentication/token/verify")
    ResponseMessage<Void> verify(@RequestBody @Validated TokenVerifyCommand command);

    /**
     * 获取token 信息
     * <pre>
     *  【一次会话，请在本地缓存令牌，建议在过滤器/拦截器中实现】
     * </pre>
     *
     * @param query token查询参数
     * @return token信息
     */
    @PostMapping("/authentication/token/info")
    ResponseMessage<Token> getTokenInfo(@RequestBody @Validated TokenQuery query);

    /**
     * 注销token
     *
     * @param command token注销命令
     * @return void
     */
    @PostMapping("/authentication/token/cancellation")
    ResponseMessage<Void> cancellation(@RequestBody @Validated TokenCancellationCommand command);

    /**
     * 分布式会话上下文
     *
     * @param command 会话操作
     * @return void
     */
    @PostMapping("/token/context")
    ResponseMessage<Void> contextPut(@RequestBody @Validated TokenContextCommand command);
}
