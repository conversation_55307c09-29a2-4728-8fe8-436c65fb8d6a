package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.UserPermissionPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.dto.PlatformCompanyInfoDTO;
import com.wanlianyida.fssuserauth.api.model.dto.UserPermissionPlatformDTO;
import com.wanlianyida.fssuserauth.api.model.query.UserPermissionPlatformQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-角色管理")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", name = "platform", path = "/platform/PlatformUmPermissioninfo", contextId = "PlatformUmPermissionInfoInter")
public interface UserPermissionInter {

    /**
     * 新增角色
     */
    @PostMapping("/platformUmPermissioninfoAdd")
    ResponseMessage<List<String>> addUserPermission(@RequestBody UserPermissionPlatformCommand command);


    /**
     * 修改角色
     */
    @PostMapping("/platformUmPermissioninfoUpdate")
    ResponseMessage<List<String>> updateUserPermission(@RequestBody UserPermissionPlatformCommand command);


    /**
     * 删除角色
     */
    @PostMapping("/platformUmPermissioninfoDel")
    ResponseMessage<List<String>> delUserPermission(@RequestParam(value = "permissionId") int permissionId);


    /**
     * 角色-列表查询
     */
    @PostMapping("/platformUmPermissioninfoPaging")
    ResponseMessage<List<UserPermissionPlatformDTO>> queryList(@RequestBody PagingInfo<UserPermissionPlatformQuery> pagingInfo);

    /**
     * 用户角色-列表查询
     */
    @PostMapping("/queryUserRoleList")
    ResponseMessage<List<PlatformCompanyInfoDTO>> queryUserRoleList(@RequestBody List<String> userBaseIdList);
    /**
     * 分页获取账号拥有的角色信息
     * @param pageInfo
     * @return
     */
    @PostMapping("/queryListByUserBaseIdPaging")
    ResponseMessage<List<UserPermissionPlatformDTO>> queryListByUserBaseId(@RequestBody PagingInfo<UserPermissionPlatformQuery> pageInfo);
    /**
     * 获取账号拥有的角色信息---不分页
     * @param query
     * @return
     */
    @PostMapping("/queryListByUserBaseId")
    ResponseMessage<List<UserPermissionPlatformDTO>> queryByUserBaseId(@RequestBody UserPermissionPlatformQuery query);


}
