package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 角色绑定资源
 *
 * <AUTHOR>
 * @date 2025-05-14 15:15
 */
@Data
@Accessors(chain = true)
public class ResourceBindCommand {

    /**
     * 系统类型
     */
    @NotBlank(message = "系统类型不能为空")
    private String sysType;

    /**
     * 资源id
     */
    @NotNull(message = "资源id不能为空")
    private List<Long> resourceIds;

    /**
     * 角色id
     */
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    /**
     * 是否覆盖 true 覆盖 false 追加
     */
    private boolean overwrite = true;

    /**
     * 当前登录用户id
     */
    private String userBaseId;
}
