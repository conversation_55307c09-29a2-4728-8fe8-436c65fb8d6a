package com.wanlianyida.fssuserauth.api.model.platform.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 平台信息 DTO
 *
 * <AUTHOR>
 * @date 2025-07-11 14:50:21
 */
@Data
@Accessors(chain = true)
public class PlatformInfoDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 平台地址
     */
    private String platformUrl;

    /**
     * 超级管理员
     */
    private String superAdminId;

    /**
     * 超级管理员姓名
     */
    private String superAdminName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户名
     */
    private String loginName;

    /**
     * 是否启用鉴权
     */
    private String authFlag;

    /**
     * 描述
     */
    private String remark;

    /**
     * 有效标志  0-无效   1-有效
     */
    private String validFlag;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新人
     */
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     *  删除标志 0-否,1-是
     */
    private String delFlag;
}
