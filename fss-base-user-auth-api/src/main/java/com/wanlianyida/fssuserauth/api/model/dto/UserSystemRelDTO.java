package com.wanlianyida.fssuserauth.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户平台信息 DTO
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UserSystemRelDTO {

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 用户id 
	 */
	private String userBaseId;

	/**
	 * 平台编码
	 */
	private String systemCode;

	/**
	 * 平台名称
	 */
	private String systemName;

	/**
	 * 是否默认平台 10是 20不是
	 */
	private String defaultFlag;

	/**
	 * 创建人
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;


	/**
	 * url
	 */
	private String url;

	/**
	 * 平台描述
	 */
	private String description;

	/**
	 * 平台获取初始化信息路径
	 */
	private String dataInit;

	/**
	 * 租户id
	 */
	private String tenantId;

	/**
	 * 租户类别:10个人,20企业,30员工
	 */
	private String tenantCategory;


}
