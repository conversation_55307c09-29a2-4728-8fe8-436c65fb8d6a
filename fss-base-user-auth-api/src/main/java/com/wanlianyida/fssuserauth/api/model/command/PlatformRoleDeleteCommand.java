package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 平台角色删除命令
 *
 * <AUTHOR>
 * @date 2025-05-23 13:25
 */
@Data
@Accessors(chain = true)
public class PlatformRoleDeleteCommand {

    /**
     * 平台角色ID
     */
    @NotBlank(message = "平台角色ID不能为空")
    private String roleId;

    /**
     * 系统类型
     */
    @NotBlank(message = "系统类型不能为空")
    private String sysType;

}
