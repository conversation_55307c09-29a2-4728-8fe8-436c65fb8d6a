package com.wanlianyida.fssuserauth.api.model.dto;

import lombok.Data;

/**
 * 用户登录传输对象
 * 用于封装用户登录所需的信息
 * <AUTHOR>
 * @date 2025-05-20 10:37:22
 */
@Data
public class UserLoginDTO {

    /**
     * token
     * 用于身份验证的令牌
     */
    private String token;

    /**
     * userBaseId
     * 用户基础信息ID
     */
    private String userBaseId;

    /**
     * 密码校验是否过期
     * 10 未过期   20  临近过期   30  已经过期
     * 用于表示用户密码的过期状态
     */
    private String passwordCheckStatus;

    /**
     * 密码校验结果
     * 用于提供密码验证的详细信息
     */
    private String passwordMsg;

}
