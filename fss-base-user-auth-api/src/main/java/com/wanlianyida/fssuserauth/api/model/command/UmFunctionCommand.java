package com.wanlianyida.fssuserauth.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用所拥有的功能接口函数方法管理表 Command
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UmFunctionCommand implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * 功能函数方法ID  应用所拥有的功能接口函数方法管理表
	 */
	private Long funcId;

	/**
	 * 父类ID所拥功能模块的ID
	 */
	private Long parentId;

	/**
	 * 功能函数方法描述中文名
	 */
	private String funcName;

	/**
	 * 功能函数方法即代码中的接口方法名称如controller中的Funciton方法名
	 */
	private String actionName;

	/**
	 * 中文描述的简写(拼音)
	 */
	private String enfuncName;

	/**
	 * 公开接口的path调用路径如Requertmap注解的路径
	 */
	private String url;

	/**
	 * 如果为菜单类型的前端所需要配置的样式表(css)
	 */
	private String styleName;

	/**
	 * 功能函数类型【select:0-功能函数,1-菜单,2-按钮,3-数据】
	 */
	private String funcType;

	/**
	 * 菜单的层级关系，这里也可以与parentID实现，这里表示代码更直观编码更方便
	 */
	private Integer hierarchy;

	/**
	 * 功能函数排序，这里主要方便菜单及前端对些表进行编辑方便。
	 */
	private Integer sortno;

	/**
	 * 所属的功能程序ID表cm_appmanagement中的AppID
	 */
	private Integer appId;

	/**
	 * 系统类型，1：3PL，2：4PL
	 */
	private String sysType;

	/**
	 * 按钮操作类型 10:查看 20:操作
	 */
	private String btnOperType;

	/**
	 * 没有用到
	 */
	private Integer news;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 修改人
	 */
	private String modifyBy;

	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date modifyDate;

	/**
	 * 版本号
	 */
	private String versionCode;

	/**
	 * 功能说明
	 */
	private String item1;

	/**
	 * 预留字段
	 */
	private String item2;

	/**
	 * 预留字段
	 */
	private String item3;

	/**
	 * url接口访问限制
	 */
	private String item4;


}
