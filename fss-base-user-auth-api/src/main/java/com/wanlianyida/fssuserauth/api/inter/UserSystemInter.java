package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.UserSystemPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.dto.UserSystemDTO;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-用户关联系统")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", name = "platform", path = "/platform/PlatformSystem", contextId = "PlatformSystemInter")
public interface UserSystemInter {

    /**
     * 根据userBaseId 查询用户关联系统信息，没有新增
     *
     * @param command 操作
     * @return
     */
    @PostMapping(value = "/updatePlatformUserSystemRel")
    ResponseMessage<?> updatePlatformUserSystemRel(@RequestBody UserSystemPlatformCommand command);

    /**
     *
     *
     * @param command 操作
     * @return
     */
    @PostMapping(value = "/checkByUserIdAndSysCode")
    ResponseMessage<?> checkByUserIdAndSysCode(@RequestBody UserSystemPlatformCommand command);

    /**
     * 系统切换列表查询(基于登录用户的userBaseId)
     */
    @PostMapping(value = "/queryUserSystemByUserBaseId/{userId}")
    ResponseMessage<List<UserSystemDTO>> queryUserSystemByUserBaseId(@PathVariable("userId")String userId);

    /**
     * 根据userBaseId删除
     * @return {@link ResponseMessage}<{@link UserSystemPlatformCommand}>
     */
    @PostMapping(value = "/deleteByUserBaseId")
    ResponseMessage<?> deleteByUserBaseId(@RequestBody UserSystemPlatformCommand rel);

    /**
     * 根据userBaseId查询
     * @param userBaseId
     * @return
     */
    @PostMapping(value = "/queryListByUserBaseId")
    ResponseMessage<List<UserSystemDTO>> queryListByUserBaseId(String userBaseId);

    /**
     * 新增
     * @param rel
     * @return
     */
    @PostMapping(value = "/insertPlatformUserSystemRel")
    ResponseMessage<Void> insertPlatformUserSystemRel(UserSystemPlatformCommand rel);

}
