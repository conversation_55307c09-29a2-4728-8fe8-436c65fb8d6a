package com.wanlianyida.fssuserauth.api.inter.auth;

import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.UmPermissionfunctionsCommand;
import com.wanlianyida.fssuserauth.api.model.dto.UmPermissionfunctionsDTO;
import com.wanlianyida.fssuserauth.api.model.query.UmPermissionfunctionsQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 角色组所拥有的功能函数表  Inter
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Api(value = "角色组所拥有的功能函数表feign api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth:}", name = "fss-base-user-auth", contextId = "umPermissionfunctionsInter", path = "/fss-base-user-auth")
public interface UmPermissionfunctionsInter {

    /**
     * 列表查询
     * @param query 查询参数
     * @return {@link ResponseMessage}<{@link UmPermissionfunctionsDTO}>
     */
    @PostMapping("/umPermissionfunctions/queryList")
    ResponseMessage<List<UmPermissionfunctionsDTO>> queryList(@RequestBody UmPermissionfunctionsQuery query);

    /**
     * 批量新增
     *
     * @param commandList
     * @return {@link ResponseMessage }<{@link ? }>
     */
    @PostMapping("/umPermissionfunctions/batchInsert")
    ResponseMessage<?> batchInsert(@RequestBody @Validated List<UmPermissionfunctionsCommand> commandList);

    /**
     * 批量物理删除
     *
     * @param idList
     * @return {@link ResponseMessage }<{@link ? }>
     */
    @PostMapping("/umPermissionfunctions/batchDeleteByIds")
    ResponseMessage<?> batchDelete(@RequestBody List<Long> idList);

}
