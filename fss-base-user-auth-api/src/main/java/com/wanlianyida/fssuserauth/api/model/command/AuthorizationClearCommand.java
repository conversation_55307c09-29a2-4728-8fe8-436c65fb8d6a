package com.wanlianyida.fssuserauth.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 鉴权DTO
 *
 * <AUTHOR>
 * @date 2025-06-27 10:21
 */
@Data
public class AuthorizationClearCommand {

    /**
     * 系统类型
     */
    @NotBlank(message = "系统类型不能为空")
    private String sysType;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 公司id
     */
    private String companyId;

}
