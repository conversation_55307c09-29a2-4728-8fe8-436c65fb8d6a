package com.wanlianyida.fssuserauth.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 资源DTO
 *
 * <AUTHOR>
 * @date 2025-05-14 09:47
 */
@Data
@Accessors(chain = true)
public class ResourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 父类ID所拥功能模块的ID
     */
    private Long parentId;

    /**
     * 功能函数方法描述中文名
     */
    private String funcName;

    /**
     * 功能函数方法即代码中的接口方法名称如controller中的Funciton方法名
     */
    private String actionName;

    /**
     * 中文描述的简写(拼音)
     */
    private String enfuncName;

    /**
     * 鉴权url, 多个url用,分隔
     */
    private String authUrls;

    /**
     * 公开接口的path调用路径如Requertmap注解的路径
     */
    private String url;

    /**
     * 如果为菜单类型的前端所需要配置的样式表(css)
     */
    private String styleName;

    /**
     * 功能函数类型【select:0-功能函数,1-菜单,2-按钮,3-数据】
     */
    private String funcType;

    /**
     * 菜单的层级关系，这里也可以与parentID实现，这里表示代码更直观编码更方便
     */
    private Integer hierarchy;

    /**
     * 功能函数排序，这里主要方便菜单及前端对些表进行编辑方便。
     */
    private Integer sortno;

    /**
     * 所属的功能程序ID表cm_appmanagement中的AppID
     */
    private Integer appId;

    /**
     * 系统类型，1：3PL，2：4PL
     */
    private String sysType;

    /**
     * 按钮操作类型 10:查看 20:操作
     */
    private String btnOperType;

    /**
     * 没有用到
     */
    private Byte news;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private LocalDateTime modifyDate;

    /**
     * 版本号
     */
    private String versionCode;

    /**
     * 功能说明
     */
    private String item1;

    /**
     * 预留字段
     */
    private String item2;

    /**
     * 预留字段
     */
    private String item3;

    /**
     * url接口访问限制
     */
    private String item4;

    /**
     * 前端自定义
     */
    private String funcOption;

}
