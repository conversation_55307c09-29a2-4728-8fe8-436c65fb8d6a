package com.wanlianyida.fssuserauth.api.inter;

import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.model.command.UserInfoPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.command.UserLoginInfoPlatformCommand;
import com.wanlianyida.fssuserauth.api.model.dto.PlatformUmUserbaseinfoDTO;
import com.wanlianyida.fssuserauth.api.model.query.UserInfoPlatformQuery;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: Qin
 * @Date: 2024/11/22 10:08
 * @Description:认证中心inter
 **/
@Api("认证中心-用户管理")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.auth-platform:}", name = "platform", path = "/platform/PlatformUmUserbaseinfo", contextId = "PlatformUmUserBaseInfoInter")
public interface UserInfoInter {

    /**
     * 新增用户
     */
    @PostMapping("/platformUmUserbaseinfoAdd")
    ResponseMessage<List<String>> addUserInfo(@RequestBody UserInfoPlatformCommand command);


    /**
     * 修改用户
     */
    @PostMapping("/platformUmUserbaseinfoUpdate")
    ResponseMessage<List<String>> updateUserInfo(@RequestBody UserInfoPlatformCommand command);


    /**
     * 删除用户
     */
    @PostMapping("/platformUmUserbaseinfoDel")
    ResponseMessage<List<String>> delUserInfo(@RequestParam("userBaseId") String userBaseId);


    /**
     * 用户-列表查询
     */
    @PostMapping("/queryUserInfoListBySystemCode")
    ResponseMessage<?> queryList(@RequestBody PagingInfo<UserInfoPlatformQuery> pagingInfo);


    /**
     * 根据用户id集合获取用户信息
     */
    @PostMapping("/getUserInfoByUserIdList")
    ResponseMessage<List<PlatformUmUserbaseinfoDTO>> queryListByUserIds(@RequestBody List<String> userIdList);

    /**
     * 用户-明细查询
     */
    @PostMapping("/getPlatformuserinfoByKey")
    ResponseMessage<?> queryByUserBaseId(@RequestBody UserInfoPlatformQuery command);

    /**
     * 重置密码
     */
    @PostMapping(value = "/reset4plUserPassword")
    ResponseMessage<?> resetPW(@RequestBody UserLoginInfoPlatformCommand command);

    /**
     * 获取用户信息
     */
    @PostMapping(value = "/getUserInfo")
    ResponseMessage<List<PlatformUmUserbaseinfoDTO>> getUserInfo();

    /**
     * 校验登录账号和密码
     */
    @PostMapping("/checkLoginPassword")
    ResponseMessage<List<Boolean>> checkLoginPassword(@RequestBody UserInfoPlatformCommand command);
}
