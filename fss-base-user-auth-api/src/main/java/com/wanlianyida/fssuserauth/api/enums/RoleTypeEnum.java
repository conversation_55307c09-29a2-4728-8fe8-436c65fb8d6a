package com.wanlianyida.fssuserauth.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色类型
 *
 * <AUTHOR>
 * @date 2025-06-30 10:52
 */
@Getter
@AllArgsConstructor
public enum RoleTypeEnum {

    SUPER_ADMIN("0", "超级管理员"),
    ADMIN("1", "管理员"),
    USER("2", "普通用户");

    /**
     * 角色类型编码
     */
    private final String code;

    /**
     * 角色类型描述
     */
    private final String desc;
}
