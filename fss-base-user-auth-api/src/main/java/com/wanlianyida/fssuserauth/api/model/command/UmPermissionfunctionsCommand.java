package com.wanlianyida.fssuserauth.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 角色组所拥有的功能函数表  Command
 *
 * <AUTHOR>
 * @date 2025-03-27
 */
@Data
public class UmPermissionfunctionsCommand implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * 角色组所拥有的功能函数表 
	 */
	private Long permissionFunctionsId;

	/**
	 * 角色权限ID
	 */
	@Valid
	@NotNull(message = "角色id不能为空")
	private Long permissionId;

	/**
	 * 功能ID
	 */
	@Valid
	@NotNull(message = "功能id不能为空")
	private Long funcId;

	/**
	 * 状态【select:11-有效,21-无效】
	 */
	private String status;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createDate;

	/**
	 * 修改人
	 */
	private String modifyBy;

	/**
	 * 修改时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date modifyDate;

	/**
	 * 版本号
	 */
	private String versionCode;

	/**
	 * 预留字段
	 */
	private String item1;

	/**
	 * 预留字段
	 */
	private String item2;

	/**
	 * 预留字段
	 */
	private String item3;

	/**
	 * 预留字段
	 */
	private String item4;


}
