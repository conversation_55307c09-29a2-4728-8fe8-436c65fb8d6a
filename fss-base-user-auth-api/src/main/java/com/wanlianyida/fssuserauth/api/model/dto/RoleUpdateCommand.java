package com.wanlianyida.fssuserauth.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 角色更新
 *
 * <AUTHOR>
 * @date 2025-05-15 13:38
 */
@Data
@Accessors(chain = true)
public class RoleUpdateCommand {

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Length(max = 10, message = "角色名称不能超过10个字符")
    private String name;

    /**
     * 角色编码
     */
    @Length(max = 10, message = "角色编码不能超过10个字符")
    private String code;

    /**
     * 角色描述
     */
    @Length(max = 50, message = "角色描述不能超过50个字符")
    private String desc;

    /**
     * 系统类型
     */
    private String sysType;

    /**
     * 创建人id
     */
    private String createUserId;

}
