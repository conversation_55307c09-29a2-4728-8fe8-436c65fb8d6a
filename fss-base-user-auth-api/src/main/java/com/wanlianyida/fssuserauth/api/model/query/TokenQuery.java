package com.wanlianyida.fssuserauth.api.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * token信息查询
 *
 * <AUTHOR>
 * @date 2025-07-11 14:13
 */
@Data
@Accessors(chain = true)
public class TokenQuery {

    /**
     * 令牌
     */
    @NotBlank(message = "token不可为空")
    private String token;

    /**
     * 系统类型
     */
    private String sysType;

}
