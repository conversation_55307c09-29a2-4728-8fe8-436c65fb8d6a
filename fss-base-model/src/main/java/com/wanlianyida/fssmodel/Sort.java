package com.wanlianyida.fssmodel;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 排序（查询使用）
 * <pre>
 *     使用组合，请勿别使用继承
 *     多字段排序，请使用LinkedList
 *     例如：
 *     List<Sort> sortList = new LinkedList<>();
 *     sortList.add(new Sort().setFieldName("name").setAsc(true));
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-05-23 11:08
 */
@Data
@Accessors(chain = true)
public class Sort {

    /**
     * 排序字段
     */
    private String fieldName;

    /**
     * 是否升序
     */
    private boolean asc;

}
