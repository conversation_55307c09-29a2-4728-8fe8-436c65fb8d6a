package com.wanlianyida.fssmodel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedList;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 分页查询
 * <pre>
 *     使用请组合使用，别继承
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-05-23 11:05
 */
@Data
@Accessors(chain = true)
public class PageRequest {

    /**
     * 当前页码
     */
    @Min(value = 1, message = "当前页码必须大于0")
    private int pageNum = 1;

    /**
     * 每页数量, 默认20
     */
    @Min(value = 1, message = "每页数量必须大于0")
    @Max(value = 100, message = "每页数量不能大于100")
    private int pageSize = 20;

    /**
     * 排序信息
     */
    private LinkedList<Sort> sort;

    /**
     * 是否返回总数
     */
    private boolean returnCount = false;

}
