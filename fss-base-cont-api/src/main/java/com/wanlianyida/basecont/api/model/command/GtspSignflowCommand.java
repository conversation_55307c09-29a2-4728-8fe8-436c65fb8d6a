package com.wanlianyida.basecont.api.model.command;

import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotBlank;

/**
 * 合同签署流程表
 */
@Data
public class GtspSignflowCommand {
    /**
     * id主键
     */
    private String id;

    /**
     * 接入渠道：110：易签宝；120：法大大；150：爱签
     */
    @NotBlank(message = "渠道不能为空")
    private String channel;

    /**
     * 类型，10：订单，20：运单, 30:车主声明
     */
    @NotBlank(message = "业务类型不能为空")
    private String type;

    /**
     * 用于区分运单下挂载俩个合同，前后顺序 ：02:210--第一份、220--第二份,01后续扩展
     */
    private String typeModel;

    /**
     * 业务ID
     */
    @NotBlank(message = "业务ID不能为空")
    private String bizId;

    /**
     * 实际合同编号
     */
    private String contractCode;
    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 甲方用户ID
     */
    private String firstPartyUserBaseId;

    /**
     * 乙方用户ID
     */
    private String secondPartyUserBaseId;

    /**
     * 丙方用户ID
     */
    private String thirdPartyUserBaseId;

    /**
     * 流程状态，1：签署中，2：已签署，3：签署失败
     */
    private String flowStatus;

    /**
     * 签署方状态，字节标识签署方状态，如：010——甲、丙失败
     */
    private String signStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyDate;

    /**
     * 数据状态，1：有效，2：无效
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * E签宝合同模板编号
     */
    private String templateId;

    /**
     * 生效的框架合同flowId
     */
    private String parentFlowId;

    /**
     * 签署区域坐标Json串，多方合同会有多组数据，每组数据参数： page:签章所在页，x:横坐标 y:纵坐标；
     */
    private String esignArea;

    /**
     * 车牌号
     */
    private String carplateno;

    /**
     * 配置id:sign_config表id或网络货运主体id
     */
    private String configId;
    /**
     * 合同创建日期
     */
    private Date contractCreationDate;
    /**
     * 合同截至日期
     */
    private Date contractLife;

    /**
     * 合同删除标记，如：0—未删除 1--已删除
     */
    private String delFlag;

    /**
     * 是否框架合同
     */
    private Boolean frameContract;

    private Integer retryCnt;

    private Boolean updateFlag = false;
}
