package com.wanlianyida.basecont.api.inter;

import com.wanlianyida.basecont.api.model.dto.GtspContractSignTemplateChannelDTO;
import com.wanlianyida.basecont.api.model.query.GtspContractSignTemplateChannelQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * 合同签署模板默认渠道配置 feign api
 * <AUTHOR>
 * @date 2025/06/17
 */
@Api("合同签署模板默认渠道配置api")
@FeignClient(url = "${gtsp.api.url.cont:}", name = "gtsp-base-cont", contextId = "GtspContractSignTemplateChannelInter", path = "/gtsp-base-cont")
//@FeignClient(url = "localhost:9829", name = "gtsp-base-cont", contextId = "GtspContractSignTemplateChannelInter", path = "/gtsp-base-cont")
public interface GtspContractSignTemplateChannelInter {

    /**
     * 合同模板分页列表
     *
     * @param query
     * @return {@link ResponseMessage }<{@link List }<{@link GtspContractSignTemplateChannelDTO }>>
     */
    @ApiOperation("合同模板分页列表")
    @PostMapping(value = "/signTemplateChannel/queryList", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<GtspContractSignTemplateChannelDTO>> queryList(@RequestBody @NotNull GtspContractSignTemplateChannelQuery query);

}
