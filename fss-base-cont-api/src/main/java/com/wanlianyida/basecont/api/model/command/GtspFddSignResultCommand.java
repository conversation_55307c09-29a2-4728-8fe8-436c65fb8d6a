package com.wanlianyida.basecont.api.model.command;

import lombok.Data;

/**
 * 法大大签署结果异步通知（过滤）
 */
@Data
public class GtspFddSignResultCommand {

    /**
     * 交易号
     */
    private String transactionId;

    /**
     * 合同编号
     */
    private String contractId;

    /**
     * 签章结果代码
     * 3000（签章成功）
     * 3001（签章失败）
     * 3003（拒签）
     */
    private String resultCode;

    /**
     * 签章结果描述
     */
    private String resultDesc;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 查看地址
     */
    private String viewpdfUrl;

    /**
     * 请求时间（yyyyMMddHHmmss）
     */
    private String timestamp;

    /**
     * 摘要
     Base64(
     SHA1(
     app_id +MD5(timestamp)
     +SHA1(app_secret+transaction_id)
     )
     )
     */
    private String msgDigest;


}
