package com.wanlianyida.basecont.api.model.command;

import com.wanlianyida.basecont.api.model.dto.GtspTableRowDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 渠道合同创建
 */
@Data
public class GtspChannelCreateCommand {

    /**
     * 平台模板id
     */
    private Integer id;
    /**
     * 业务单号
     */
    @NotBlank(message = "业务ID不能为空")
    private String bizId;

    /**
     * 渠道模板id
     */
    @NotBlank(message = "模板ID不能为空")
    private String templateId;
    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 是否需要签署
     */
    private Boolean needSign;

    /**
     * 模板名称/合同名称
     */
    private String templateName;
    /**
     * 车牌号
     */
    private String carplateno;
    /**
     * 模板填充参数
     */
    private Map<String, Object> parameterMap;

    /**
     * 参与方列表
     */
    @Valid
    @NotEmpty(message = "签署方不能为空")
    private List<GtspSignatoryCommand> signatories;

    private ParentSignFlow parentSignFlow;

    /**
    * 数据表格
    */
    private List<TableData> tableRows ;

    @Valid
    @NotNull(message = "签署流程不能为空")
    private GtspSignflowCommand contractSignflowCommand;

    @NotBlank(message = "渠道不能为空")
    private String channel;

    @Data
    public static class ParentSignFlow {

        /**
         * 父级合同id
         */
        private String id;

        /**
         * 业务单号
         */
        private String bizId;

        /**
         * 类型，01：订单，02：运单
         */
        private String type;

        /**
         * 用于区分运单下挂载俩个合同，前后顺序 ：02:210--第一份、220--第二份,01后续扩展
         */
        private String typeModel;

        /**
         * 是否重签
         */
        private Boolean reSign;

    }
    @Data
    public static class TableData {

        /**
         * 表格名称
         */
        private String keyword;

        /**
         * 动态表格参数
         */
        private List<GtspTableRowDTO> tableRows;

    }

}
