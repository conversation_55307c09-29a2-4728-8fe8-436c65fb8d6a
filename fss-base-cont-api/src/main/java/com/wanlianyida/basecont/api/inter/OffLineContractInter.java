package com.wanlianyida.basecont.api.inter;

import com.wanlianyida.basecont.api.model.command.OffLineContractCommand;
import com.wanlianyida.basecont.api.model.command.OffLineContractNoOrderCommand;
import com.wanlianyida.basecont.api.model.dto.*;
import com.wanlianyida.basecont.api.model.query.*;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 大宗线下合同 Inter
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Api(value = "大宗线下合同api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.cont:}", name = "cont", contextId = "offLineContractBaseCont", path = "/cont")
public interface OffLineContractInter {

    /**
     * 大宗交易新增
     * @param command
     * @return {@link }
     */
    @ApiOperation(value = "大宗交易新增")
    @PostMapping(value = "/offLineContract/saveFromBulkTrade", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<?> saveFromBulkTrade(@RequestBody OffLineContractCommand command);

    /**
     * 大宗长期分页查询
     * @param pagingInfo 分页查询参数
     * @return {@link }<{@link ContractBaseDTO}>
     */
    @PostMapping(value = "/offLineContract/pageFromBulkTradeForLong", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractBaseDTO>> pageFromBulkTradeForLong(@RequestBody PagingInfo<ContractBaseQuery> pagingInfo);

    /**
     * 大宗归档分页查询
     * @param pagingInfo 分页查询参数
     * @return {@link }<{@link ContractSignArchiveDTO}>
     */
    @PostMapping(value = "/offLineContract/pageFromBulkTrade", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractSignArchiveDTO>> pageFromBulkTrade(@RequestBody PagingInfo<ContractSignArchiveQuery> pagingInfo);

    /**
     * 大宗订单取消作废关联合同归档
     *
     * @param bizId 订单号
     * @return {@link  }<{@link ? }>
     */
    @PostMapping(value = "/offLineContract/nullifyByBizId/{bizId}", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<?> nullifyByBizId(@PathVariable("bizId") String bizId);


    /**
     * 大宗交易保存合同（无订单id)
     * @param command
     * @return {@link }
     */
    @PostMapping(value = "/offLineContract/saveFromBulkTradeNoOrder", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractBaseDTO>> saveFromBulkTradeNoOrder(@RequestBody OffLineContractNoOrderCommand command);



    /**
     * 根据订单查询大宗合同包括附件以及附件名称
     * @param contractOrderQuery 查询参数
     * @return {@link }<{@link ContractOrderGroupDTO}>
     */
    @PostMapping(value = "/offLineContract/queryBulkTradeByBizId", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractOrderGroupDTO>> queryBulkTradeByBizId(@RequestBody ContractOrderQuery contractOrderQuery);



    /**
     * 根据订单号+合同编号+签订日期+(合作方+卖家+买家)+合同状态+合同性质查询大宗合同
     * @param pagingInfo 分页查询参数
     * @return {@link }<{@link ContractBaseDTO}>
     */
    @PostMapping(value = "/offLineContract/pageQueryAllContract", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractBaseDTO>> pageQueryAllContract(@RequestBody PagingInfo<ContractAndOrderQuery> pagingInfo);




    /**
     * 根据主合同id查询订单列表
     * @param pagingInfo 分页查询参数
     * @return {@link }<{@link ContractOrderDTO}>
     */
    @PostMapping(value = "/offLineContract/pageQueryOrderByContractId", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractOrderDTO>> pageQueryOrderByContractId(@RequestBody PagingInfo<ContractQuery> pagingInfo);


    /**
     * 根据批量主合同id查询订单列表
     * @param contractBatchMainIdQuery 查询参数
     * @return {@link }<{@link ContractGroupOrderDTO}>
     */
    @PostMapping(value = "/offLineContract/queryOrderByContractIds", produces = {"application/json;charset=UTF-8"})
    ResponseMessage<List<ContractGroupOrderDTO>> queryOrderByContractIds(@RequestBody ContractBatchMainIdQuery contractBatchMainIdQuery);

}
