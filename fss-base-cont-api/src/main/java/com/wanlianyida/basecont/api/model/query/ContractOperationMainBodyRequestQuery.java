package com.wanlianyida.basecont.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询合同请求参数
 */
@Data
public class ContractOperationMainBodyRequestQuery {

    @ApiModelProperty(value = "网络货运主键id")
    private String operationMainBodyId;

    /**
     * 10订单 20运单 30车主声明
     */
    @ApiModelProperty(value = "获取类型")
    private String type;

    @ApiModelProperty(value = "参与方")
    private String signatory;

    @ApiModelProperty(value = "车牌号")
    private String carPlateNo;


    @ApiModelProperty(value = "车颜色")
    private Integer carColor;

    @ApiModelProperty(value = "司机userBaseId")
    private String driverUserBaseId;

    @ApiModelProperty(value = "收款人userBaseId")
    private String payeeUserBaseId;

}
