package com.wanlianyida.basecont.api.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 获取预览签章
 */
@Data
public class GtspContractSignSealQuery {

    /**
     * 签署渠道
     */
    @NotBlank(message = "签署渠道不能为空")
    private String channel;

    /**
     * 客户编号
     */
    @NotBlank(message = "签署账号ID不能为空")
    private String signAccountId;

    /**
     * 文字签章ID
     */
    @NotBlank(message = "签章ID不能为空")
    private String sealIdText;

    /**
     * 手写签章ID
     */
    //@NotBlank(message = "手签章ID不能为空")
    private String sealIdHandwritten;
}
