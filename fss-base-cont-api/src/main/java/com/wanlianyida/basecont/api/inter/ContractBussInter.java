package com.wanlianyida.basecont.api.inter;

import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(url = "${gtsp.api.url.cont:}", name = "cont", contextId = "appContractBussInter", path = "/cont")
public interface ContractBussInter {

    /**
     * 根据模板id查看协议模板
     * @param templateId
     * @param operationMainBodyId
     * @return
     */
    @RequestMapping(value = "/app/queryDetailUrlByTemplateId", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.GET)
    ResponseMessage<List<String>> queryDetailUrlByTemplateId(@RequestParam("templateId")String templateId, @RequestParam(value = "operationMainBodyId",required = false) String operationMainBodyId);
}
