package com.wanlianyida.basecont.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 合同模板默认渠道配置表 query
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class GtspContractSignTemplateChannelQuery {

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 平台渠道id
	 */
	private Long platChannelId;

	/**
	 * 接入渠道[110-易签宝,120-法大大,150-爱签]
	 */
	private String channel;

	/**
	 * 模板名称
	 */
	private String tmplName;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 删除标志[0-未删除,1-删除]
	 */
	private Integer delFlag;


}
