package com.wanlianyida.basecont.api.model.command;

import lombok.Data;

/**
 * 爱签签署结果异步通知（过滤）
 */
@Data
public class GtspAsignSignResultCommand {

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 签章结果代码
     * 0：等待签约
     * 1：签约中
     * 2：已签约
     * 3：过期
     * 4：拒签
     * 6：作废
     * 7：撤销
     * -2：状态异常
     */
    private String status;

    /**
     * 签名
     */
    private String sign;

    /**
     * 合同过期时间 2025-05-28 23:59:59
     */
    private String validityTime;

    /**
     * 签署完成时间 2025-05-28 23:59:59
     */
    private String signTime;

    /**
     * 签署完成时间戳 2025-05-28 23:59:59
     */
    private String timestamp;

    /**
     * signCompleted(固定)
     */
    private String action;


}
