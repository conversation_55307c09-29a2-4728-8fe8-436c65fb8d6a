package com.wanlianyida.basecont.api.model.dto;

import lombok.Data;

/**
 * 合同模板 DTO
 * <AUTHOR>
 * @date 2025/05/14
 */
@Data
public class ContractSignTemplateDTO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 模板id
     */
    private String templateId;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 1—面向托运人合同模板;2—面向承运人合同模板
     */
    private String type;

    /**
     * 合同子类型
     */
    private String typeClass;


    /**
     * 签署区域坐标Json串，多方合同会有多组数据，每组数据参数： page:签章所在页，x:横坐标 y:纵坐标；
     */

    private String signArea;

    /**
     * oss存储的模板地址
     */
    private String templateOssUrl;

    /**
     * 显示名称
     */
    private String shortName;

    /**
     * 是否框架合同
     */
    private Boolean frameContract;

    /**
     * 模板状态：10-启用；20-停用
     */
    private String status;

    /**
     * 模板策略：
     */
    private String strategyCode;

}
