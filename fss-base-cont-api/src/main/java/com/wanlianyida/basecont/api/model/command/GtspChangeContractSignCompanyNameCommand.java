package com.wanlianyida.basecont.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 合同签署用户信息表
 * @TableName contract_sign_user
 */
@Data
public class GtspChangeContractSignCompanyNameCommand {

    /**
     * 企业用户ID
     */
    @NotBlank(message = "企业用户ID不能为空")
    private String userBaseId;

    /**
     * 新企业名称
     */
    @NotBlank(message = "新企业名称不能为空")
    private String newCompanyName;

    /**
     * 企业真实统一社会信用代码
     */
    private String socialCreditCodeReal;

    /**
     * 企业统一社会信用代码
     */
    @NotBlank(message = "企业统一社会信用代码不能为空")
    private String socialCreditCode;

    /**
     * 企业法人
     */
    @NotBlank(message = "企业法人不能为空")
    private String legalPerson;

    /**
     * 企业资质(营业执照)正面照片地址
     */
    @NotBlank(message = "企业营业执照正面照url不能为空")
    private String forntFileUrl;

    /**
     * 企业联系人
     */
    @NotBlank(message = "企业联系人不能为空")
    private String contacts;

}
