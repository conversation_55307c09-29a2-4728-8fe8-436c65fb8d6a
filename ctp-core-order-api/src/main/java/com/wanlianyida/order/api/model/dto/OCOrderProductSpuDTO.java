package com.wanlianyida.order.api.model.dto;

import lombok.Data;

@Data
public class OCOrderProductSpuDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 企业ID（店铺ID）
     */
    private Long companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * spu编号
     */
    private String spuCode;


    private String skuCode;

    /**
     * 品牌ID
     */
    private Long relBrandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 1级品类ID
     */
    private Long categoryId1;

    /**
     * 1级品类名称
     */
    private String categoryName1;

    /**
     * 2级品类ID
     */
    private Long categoryId2;

    /**
     * 2级品类名称
     */
    private String categoryName2;

    /**
     * 3级品类ID
     */
    private Long categoryId3;

    /**
     * 3级品类名称
     */
    private String categoryName3;

    /**
     * 品类全名
     */
    private String categoryFullName;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 是否审核后立即上架 1是 0否
     */
    private Boolean onSaleAfterAudit;

    /**
     * 关联仓库ID
     */
    private Long relStockId;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 交货地省编号
     */
    private String deliveryAddrProvinceCode;

    /**
     * 交货地省名称
     */
    private String deliveryAddrCityCode;

    /**
     * 交货地城市名称
     */
    private String deliveryAddrProvinceName;

    /**
     * 交货地城市编号
     */
    private String deliveryAddrCityName;

    /**
     * 计价单位ID
     */
    private Integer relPricingUnitId;

    /**
     * 计量单位ID
     */
    private Integer relMeasurementUnitId;
}
