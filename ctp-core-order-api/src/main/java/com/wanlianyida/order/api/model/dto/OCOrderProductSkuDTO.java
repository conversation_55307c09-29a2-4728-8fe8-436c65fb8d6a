package com.wanlianyida.order.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OCOrderProductSkuDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 企业ID（店铺ID）
     */
    private Long companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * SKU编号
     */
    private String skuCode;

    private String spuCode;

    /**
     * SKU规格名称(规格属性值value拼接)
     */
    private String skuSpecificationName;

    /**
     * SKU名称（品牌+三级品类+sku规格名称）
     */
    private String skuName;

    /**
     * 简单说明
     */
    private String skuDesc;

    /**
     * 上架状态（10未上架 20已上架 30违规下架）
     */
    private Integer onSaleStatus;

    /**
     * 上架时间
     */
    private Date onSaleDate;

    /**
     * 下架时间
     */
    private Date outSaleDate;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 单价
     */
    private BigDecimal priceFee;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单位转换 1吨=x吨度(计价单位转换为计量单位)
     */
    private BigDecimal unitTransfer;

    /**
     * 是否现货:1是 0否;
     */
    private Integer isSpotGoods;

    /**
     * 交货期限（天）
     */
    private String deliveryPeriod;

    /**
     * 计重方式（10理重 20过磅）
     */
    private String weightMeasurementType;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 1级品类名称
     */
    private String categoryName1;

    /**
     * 2级品类名称
     */
    private String categoryName2;

    /**
     * 3级品类名称
     */
    private String categoryName3;

    /**
     * 品类全名
     */
    private String categoryFullName;
}
