package com.wanlianyida.order.api.model.command;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.NotBlank;

/**
 * 订单状态变更操作
 */
@Data
public class OrderStatusOptCommand {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不允许为空")
    private String orderNo;

    /**
     * 订单操作
     */
    @NotBlank(message = "订单操作不允许为空")
    private String orderOpt;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 操作时间
     */
    private Date operatorDate;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 长期合同编号
     */
    private String longtermContractNo; 

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     *  10 买家取消 20 卖家取消
     */
    private Integer cancelSource;

    /**
     * 支付时间
     */
    private Date payDate;

    /**
     * 付款状态
     */
    private Integer paymentStatus;

    /**
     * 结算金额
     */
    private BigDecimal smentAmount;

}
