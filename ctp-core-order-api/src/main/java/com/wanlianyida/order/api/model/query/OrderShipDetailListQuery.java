package com.wanlianyida.order.api.model.query;

import lombok.Data;

import java.util.List;

/**
 * 发/提货单详情列表查询
 */
@Data
public class OrderShipDetailListQuery {
    /**
     * 订单号（必传）
     */
    private String orderNo;

    /**
     * 发/提货单号（可选）
     */
    private List<String> shipNoList;

    /**
     * 确认收货状态（可选）
     */
    private Integer receiveStatus;

    /**
     * 场景 10-订单详情 20-确认收货弹窗（可选，默认10）
     */
    private String scene;
}
