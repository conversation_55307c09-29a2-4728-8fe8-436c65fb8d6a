package com.wanlianyida.order.api.model.command;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 采购商品信息
 */
@Data
public class PurchaseGoodsCommand implements Serializable {
    private static final long serialVersionUID = -9056113081244405288L;

    //@NotBlank(message = "企业id")
    private String companyId;
    /**
     * skuCode
     */
    @NotEmpty(message = "skuCode不为空")
    private String skuCode;

    /**
     * 商品价格
     */
    @NotNull(message = "单价不为空")
    private BigDecimal price;

    /**
     * 购买数量
     */
    @NotNull(message = "购买数量不为空")
    private BigDecimal purchaseQuantity;

    /**
     * 单位转化
     */
    @NotNull(message = "单位转化不为空")
    private BigDecimal unitCon;

    /**
     * 小计
     */
    @NotNull(message = "小计不为空")
    private BigDecimal subtotal;

    private BigDecimal subTotal;

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        if(subTotal !=null ){
            if(this.getSubtotal() == null){
                this.setSubtotal(subTotal);
            }
        }
        this.subTotal = subTotal;
    }
}
