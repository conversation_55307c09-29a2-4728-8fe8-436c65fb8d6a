package com.wanlianyida.order.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.order.api.model.command.LogisticsPlanListAddressUpdateCmd;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * @ClassName: LogisticsPlanListUpdateCmd
 * @description:
 * @author: zhang<PERSON>hen
 * @date: 2025年04月24日
 * @version: 1.0
 */
@Getter
@Setter
public class LogisticsInquiryListUpdateCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("询价单号")
    @NotBlank(message = "询价单号不能为空")
    private String inquiryNo;

    // 基础信息
    @ApiModelProperty("发/提货编号")
    private String shipNo;

    @NotNull(message = "询价单状态不能为空")
    @ApiModelProperty("询价单状态[10-待报价,20-已报价,30-已成交,40-已拒绝,,50-已撤销 60-已过期]]")
    private Integer status;

    @ApiModelProperty("买家卖家类型[10-买家,20-卖家]")
    @NotNull(message = "买家卖家类型不能为空")
    private Integer sellerBuyerType;

    @NotNull(message = "询价类型不能为空")
    @ApiModelProperty("询价单类型 [10-无发货单询价，20-有发货单询价]")
    private Integer inquiryType;

    @ApiModelProperty("商品照片")
    private String picture;

    // 地址信息
    @NotBlank(message = "发货地址不能为空")
    @Size(max = 100, message = "发货地址长度不能超过100")
    @ApiModelProperty("发货地详细地址")
    private String startAddressName;

    @NotBlank(message = "收货地址不能为空")
    @Size(max = 100, message = "收货地址长度不能超过100")
    @ApiModelProperty("目的地详细地址")
    private String endAddressName;

    // 费用信息
    @Digits(integer = 18, fraction = 4, message = "运费总价格式错误")
    @PositiveOrZero(message = "运费总价不能为负数")
    @ApiModelProperty("运费总价")
    private BigDecimal freightFee;

    // 时间信息
    @Future(message = "报价截止时间必须是未来时间")
    @ApiModelProperty("报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offerEndTime;

    @Future(message = "询价结束时间必须是未来时间")
    @ApiModelProperty("询价结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enquiryEndTime;

    @NotNull(message = "最早发运时间不能为空")
    @ApiModelProperty("最早发运时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date earliestShippingTime;

    @NotNull(message = "最晚发运时间不能为空")
    @ApiModelProperty("最晚发运时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestShippingTime;

    @ApiModelProperty("最早到货时间")
    @NotNull(message = "最早到货时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date earliestArriveTime;

    @ApiModelProperty("最晚到货时间")
    @NotNull(message = "最晚到货时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestArriveTime;

    @ApiModelProperty("询价联系人")
    @NotBlank(message = "询价联系人不能为空")
    private String inquiryLinkName;

    @ApiModelProperty("询价联系人电话")
    @NotBlank(message = "询价联系人电话不能为空")
    private String inquiryLinkTel;

    // 商品信息
    @ApiModelProperty("商品编号")
    private String goodsCode;

    @NotBlank(message = "商品名称不能为空")
    @Size(max = 100, message = "商品名称长度不能超过100")
    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品品类")
    @NotNull(message = "商品品类名不能为空")
    private Integer categoryId3;

    @ApiModelProperty("商品品类名值")
    @NotBlank(message = "商品品类名值不能为空")
    private String categoryName3;

    @Digits(integer = 15, fraction = 4, message = "货品数量格式错误")
    @DecimalMin(value = "0.0001", message = "货品数量必须大于0")
    @ApiModelProperty("货品数量")
    private BigDecimal goodsQty;

    @Digits(integer = 15, fraction = 2, message = "货值格式错误")
    @PositiveOrZero(message = "货值不能为负数")
    @ApiModelProperty("货值(元)")
    private BigDecimal goodsPrice;

    @ApiModelProperty("货品数量单位")
    @NotBlank(message = "货品数量单位不能为空")
    private String goodsQtyUnits;

    @ApiModelProperty("货品体积（立方）")
    @Digits(integer = 10, fraction = 3, message = "货品体积格式错误")
    @DecimalMin(value = "0.0001", message = "货品体积必须大于0")
    private BigDecimal goodsVolume;

    @ApiModelProperty("商品总重量")
    @Digits(integer = 10, fraction = 3, message = "商品总重量格式错误")
    @DecimalMin(value = "0.0001", message = "商品总重量必须大于0")
    private BigDecimal goodsWeight;

    @ApiModelProperty("货品重量单位")
    private String goodsWeightUnits;

    @ApiModelProperty("运价含保险费标志[0-不包含,1-包含]")
    @NotNull(message = "运价含保险费标志不能为空")
    @Pattern(regexp = "^[0-1]$", message = "保险标识只能是0或1")
    private String includeInsuranceFlag;

    @NotBlank(message = "签单类型不能为空")
    @Pattern(regexp = "10|20|30", message = "无效的签单类型")
    @ApiModelProperty("签单类型[10-无需回单 20-原件返回 30-签单返回]")
    private String receiptType;

    @NotBlank(message = "结算方式不能为空")
    @Pattern(regexp = "10|20|99", message = "无效的结算方式")
    @ApiModelProperty("结算方式[10-现结,20-月结,99-其他]")
    private String settlementType;

    @ApiModelProperty("结算周期[10-月结]")
    private String settlementCycle;

    @ApiModelProperty("指定结算重量类型")
    @NotBlank(message = "指定结算重量类型不能为空")
    private String settlementWeightFlag;

    // 费用明细（嵌套校验）
    @Digits(integer = 15, fraction = 2, message = "装车费格式错误")
    @PositiveOrZero(message = "装车费不能为负数")
    @ApiModelProperty("装车进场费(元)")
    private BigDecimal loadEntryFee;

    @Digits(integer = 15, fraction = 2, message = "卸车费格式错误")
    @PositiveOrZero(message = "卸车费不能为负数")
    @ApiModelProperty("卸车进场费(元)")
    private BigDecimal unloadEntryFee;

    @ApiModelProperty("是否标载[0-否,1-是]")
    @NotNull(message = "是否标载不能为空")
    private String standardLoadFlag;

    @ApiModelProperty("收货工作时间")
    @Size(max = 50, message = "收货工作时间长度不能超过50")
    private String recvTimeRange;

    @ApiModelProperty("发货工作时间")
    @Size(max = 50, message = "发货工作时间长度不能超过50")
    private String sendTimeRange;

    // 其他字段
    @Size(max = 200, message = "备注长度不能超过200")
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("其他费用")
    private String otherExpenses;

    @ApiModelProperty("装车时长")
    private BigDecimal loadDuration;

    @ApiModelProperty("卸车时长")
    private BigDecimal unloadDuration;

//    @ApiModelProperty("日均发运量")
//    private String dailyAvgShipmentQty;
//
//    @ApiModelProperty("辅助耗材")
//    private String auxConsum;

    @ApiModelProperty("车型")
    private String carModel;

    @ApiModelProperty("车长")
    private String carLength;

    @ApiModelProperty("期望运费标识[0-暂无要求,1-单价，2-总价]0-否,1-是]")
    @NotNull(message = "期望运费标识不能为空")
    private Integer expectedPriceFlag;

    @ApiModelProperty("期望运费单价")
    private BigDecimal expectedUnitPrice;

    @ApiModelProperty("期望运费总价")
    private BigDecimal expectedTotalPrice;


    @ApiModelProperty("'装车进场费标识'[0-不包含,1-包含]")
    private String loadEntryFlag;

    @ApiModelProperty("'卸车进场费标识'[0-不包含,1-包含]")
    private String unloadEntryFlag;

    @ApiModelProperty("'其他费用标识'[0-不包含,1-包含]")
    private String otherExpensesFlag;

    @ApiModelProperty("'装车时长标识'[0-不包含,1-包含]")
    private String loadDurationFlag;

    @ApiModelProperty("'卸车时长标识'[0-不包含,1-包含]")
    private String unloadDurationFlag;

    @ApiModelProperty("买方卖方地址集合")
    @Valid
    private List<LogisticsPlanListAddressUpdateCmd> ocLogisticsPlanAddressCommands;
}
