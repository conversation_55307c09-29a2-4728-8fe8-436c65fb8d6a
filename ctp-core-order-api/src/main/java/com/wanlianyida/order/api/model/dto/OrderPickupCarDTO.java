package com.wanlianyida.order.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 提货单车辆信息
 */
@Data
public class OrderPickupCarDTO {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 提货单号
     */
    private String pickUpNo;

    /**
     * 提货车牌号
     */
    private String pickUpCarNo;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 司机身份证
     */
    private String driverIdcard;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 业务类型[10-提货,20-发货]
     */
    private Integer bizType;

    /**
     * 签收状态[10-未签收,20-已签收]
     */
    private Integer signStatus;

    /**
     * 签收时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTime;

    /**
     * 签收单
     */
    private List<OrderAttachmentDTO> signOrderList;


}
