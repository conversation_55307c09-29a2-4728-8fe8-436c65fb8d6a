package com.wanlianyida.order.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单提货、发货单商品
 */
@Data
public class SettlementOrderShipProductDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 发货/提货单号
     */
    private String shipNo;

    /**
     * 商品编号
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 提货、发货数量
     */
    private BigDecimal shipmentQty;

    /**
     * 实际提货、收货数量
     */
    private BigDecimal receiveQty;

    /**
     * 单价
     */
    private BigDecimal contractUnitPrice;

    /**
     * 计价单位
     */
    private BigDecimal priceUnit;


    /**
     * 数量单位
     */
    private BigDecimal purchaseQuantityUnit;

    /**
     * 订单商品id
     */
    private Long orderProductId;

    /**
     * 单位转化
     */
    private BigDecimal unitCon;

}
