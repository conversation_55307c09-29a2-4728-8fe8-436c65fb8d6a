package com.wanlianyida.framework.ctpauth.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.cache.impl.RedisService;
import com.wanlianyida.framework.ctpcommon.constant.CommonRedisConstants;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.framework.ctpcommon.entity.TokenInfo;
import com.wanlianyida.framework.ctpcommon.enums.CommonResultCode;
import com.wanlianyida.framework.ctpcore.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class UserSessionInterceptor implements HandlerInterceptor {

    // 管理端登录标志
    private static final String ADMIN_LOGIN_FLAG = "10";
    private static final String HEADER_NAME = "Authorization";
    private static final RedisService redisService = SpringUtil.getBean(RedisService.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String tokenStr = request.getHeader(HEADER_NAME);

        // 获取token
        ResultMode<TokenInfo> res = getToken(tokenStr);
        if (!res.isSucceed()) {
            log.error("获取token异常： {}", JSONUtil.toJsonStr(res));
            return failedMode(response, res.getCode(), res.getMessage());
        }

        // 校验token
        ResultMode<Void> validRes = validToken(res.getModel(), tokenStr);
        if (!validRes.isSucceed()) {
            log.error("token校验失败：{}", JSONUtil.toJsonStr(validRes));
            return failedMode(response, validRes.getCode(), validRes.getMessage());
        }
        return true;
    }

    /**
     * 获取token
     */
    private ResultMode<TokenInfo> getToken(String tokenStr) {
        if (StrUtil.isBlank(tokenStr)) {
            return ResultMode.fail(CommonResultCode.AU_UNAUTHORIZED);
        }

        Claims claims = JwtUtil.getClaimByToken(tokenStr);
        if (claims == null || JwtUtil.isTokenExpired(claims.getExpiration())) {
            return ResultMode.fail(CommonResultCode.AU_TOKEN_EXPIRED);
        }

        if (!JSONUtil.isTypeJSON(claims.getSubject())) {
            return ResultMode.fail(CommonResultCode.AU_TOKEN_INVALID);
        }

        return ResultMode.success(JSONUtil.toBean(claims.getSubject(), TokenInfo.class));
    }

    /**
     * 校验token
     */
    private ResultMode<Void> validToken(TokenInfo tokenInfo, String tokenStr) {
        // 运营端： 请求不校验
        if (StrUtil.equals(tokenInfo.getIsAdminLogin(), ADMIN_LOGIN_FLAG)) {
            return ResultMode.success();
        }

        // 专家端登录： userId不存在, userBaseId存在
        if (StrUtil.isEmpty(tokenInfo.getUserId())) {
            if (StrUtil.isNotEmpty(tokenInfo.getUserBaseId())) {
                return ResultMode.success();
            }
            // 登录异常
            return ResultMode.fail(CommonResultCode.AU_TOKEN_INVALID);
        }

        if (StrUtil.isEmpty(tokenInfo.getUserBaseId())) {
            log.debug("未绑定企业: {}", JSONUtil.toJsonStr(tokenInfo));
        }

        // 普通用户：校验redisToken
        String redisToken = getRedisToken(tokenInfo.getUserId());
        if (StrUtil.isEmpty(redisToken)) {
            log.warn("FATAL_ERR: 未获取到redisToken1: {}", JSONUtil.toJsonStr(tokenInfo));
            return ResultMode.fail(CommonResultCode.AU_TOKEN_INVALID);
        }
        if (!StrUtil.equals(redisToken, tokenStr)) {
            return ResultMode.fail(CommonResultCode.AU_OTHER_LOGIN);
        }
        return ResultMode.success();
    }

    private String getRedisToken(String userId) {
        String redisKey = CommonRedisConstants.REDIS_TOKENINFO_INFO_KEY + userId;
        return redisService.get(redisKey);
    }

    private boolean failedMode(HttpServletResponse response, String code, String msg) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        try {
            response.getWriter().write(JSONUtil.toJsonStr(ResultMode.fail(code, msg)));
        } catch (IOException e) {
            log.error("FATAL_ERR: 消息响应写入失败", e);
        }
        return false;
    }
}
