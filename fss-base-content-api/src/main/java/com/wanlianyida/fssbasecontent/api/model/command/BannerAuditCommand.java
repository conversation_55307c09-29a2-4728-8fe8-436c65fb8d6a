package com.wanlianyida.fssbasecontent.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * 轮播图审核
 *
 * <AUTHOR>
 * @date 2024/12/01
 */
@Data
public class BannerAuditCommand extends BaseInfoCommand{
    /**
     * ids
     */
    @NotNull(message = "主键列表不能为空")
    private List<Long> ids;

    /**
     * 审核状态(20 审核通过 30审核驳回)
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    /**
     * 审核意见
     */
    private String auditRemark;
}
