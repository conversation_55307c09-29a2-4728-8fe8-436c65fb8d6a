package com.wanlianyida.fssbasecontent.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 广告位更新
 * <AUTHOR>
 * @date 2024/11/22
 */
@Data
public class AdSpaceUpdateCommand extends BaseInfoCommand{
    /**
     * 主键
     */
    @NotNull(message ="主键不能为空")
    private Long id;

    /**
     * 调用标识
     */
    @NotNull(message ="调用标识不能为空")
    private String callIndex;


    /**
     * 名称
     */
    @NotNull(message ="名称不能为空")
    private String name;


    /**
     * 上传数量
     */
    @NotNull(message ="上传数量不能为空")
    private Integer num;

    /**
     * 尺寸大小
     */
    @NotNull(message ="尺寸大小不能为空")
    private String size;

    /**
     * 排序序号
     */
    private Integer sortNumber;
}
