package com.wanlianyida.framework.cache.lock;

import org.redisson.api.RLock;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月21日 17:21
 */
public interface DistributedLocker {

    /**
     * 加锁
     * @param lockKey
     * @return
     */
    void lock(String lockKey);

    /**
     * 带超时时间的锁
     * @param lockKey key
     * @param leaseTime 持有锁的最长时限，超过时间且未调用unlock则自动释放，传-1则持有锁直到手动释放
     * @param unit    超时时间单位
     */
    void lock(String lockKey, int leaseTime, TimeUnit unit);

    /**
     * 带过期时间的锁，超时单位默认秒
     *
     * @param lockKey key
     * @param leaseTime 持有锁的最长时限，超过时间且未调用unlock则自动释放，传-1则持有锁直到手动释放
     * @return
     */
    void lock(String lockKey, int leaseTime);

    /**
     * 尝试获取锁，成功获取后返回true，超时则放弃
     * @param lockKey key
     * @param waitTime 最多等待时间
     * @param leaseTime 上锁后自动释放锁时间
     * @param unit 超时时间单位
     */
    boolean tryLock(String lockKey, int waitTime, int leaseTime, TimeUnit unit);

    /**
     * 尝试获取锁
     * @param lockKey
     * @param waitTime 最多等待时间
     * @param unit 超时时间单位
     * @return
     */
    boolean tryLock(String lockKey, int waitTime, TimeUnit unit);

    /**
     * 释放锁
     * @param lockKey
     */
    void unlock(String lockKey);


    /**
     *获取锁
     * @param lockKeys
     * @return
     */
    boolean tryMultiLock(List<String> lockKeys);

    /**
     *获取锁
     * @param lockKeys
     * @return
     */
    boolean tryMultiLock(List<String> lockKeys, int waitTime, TimeUnit unit);

    /**
     *获取锁
     * @param lockKeys
     * @return
     */
    RLock getMultiLock(List<String> lockKeys);

    /**
     * 释放锁
     * @param lockKeys
     */
    void unlockMulti(List<String> lockKeys);

}
