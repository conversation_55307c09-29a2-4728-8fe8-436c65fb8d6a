/**
 * =========================================================
 * ===       此类是由代码工具生成，框架开发者
 * ===       框架开发者Create By: 李健华
 * ===       创建时间: 2019/11/15 15:29:32
 * =========================================================
 */

package com.wanlianyida.basemsg.api.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 消息模版查询参数
 * <AUTHOR>
 * @date 2025/05/24
 */
@ApiModel(description = "消息模版Query对象")
@Data
public class MsgTemplateQuery implements Serializable {

    /**
     * 建议序列化的class都给一个序列化的ID，这样可以保证序列化的成功，版本的兼容性。
     */
    private static final long serialVersionUID = 100000L;

    /**
     * 消息模版id
     */
    @ApiModelProperty(value = "消息模版id", name = "templateId")
    private String templateId;
    /**
     * 消息模板标题
     */
    @ApiModelProperty(value = "消息模板标题", name = "templateTitle")
    private String templateTitle;
    /**
     * 消息模板内容
     */
    @ApiModelProperty(value = "消息模板内容", name = "templateContent")
    private String templateContent;
    /**
     * 消息模板状态【checkbox:1-启用,2-禁用】
     */
    @ApiModelProperty(value = "消息模板状态【checkbox:1-启用,2-禁用】", name = "status",example = "1")
    private int status;

    /**
     * 消息接收人
     */
    @ApiModelProperty(value = "消息接收人", name = "receiver")
    private String receiver;

    /**
     * 消息模版类型【checkbox:1-短信,2-邮件,3-app,4-微信,5-站内信】
     */
    @ApiModelProperty(value = "消息模版类型【checkbox:1-短信,2-邮件,3-app,4-微信,5-站内信】", name = "templateType",example = "1")
    private String templateType;
    /**
     * 消息模版正式id
     */
    @ApiModelProperty(value = "消息模版正式id", name = "templateFormalId")
    private String templateFormalId;
    /**
     * 消息模板审核状态【select:1-审核中,2-审核成功,3-审核失败)
     */
    @ApiModelProperty(value = "消息模板审核状态【select:1-审核中,2-审核成功,3-审核失败)", name = "templateExamine",example = "1")
    private int templateExamine;
    /**
     * 应用程序id
     */
    @ApiModelProperty(value = "应用程序id", name = "appId")
    private int appId;

    /**
     * 消息模版id集合
     */
    private List<String> templateIdList;
}
