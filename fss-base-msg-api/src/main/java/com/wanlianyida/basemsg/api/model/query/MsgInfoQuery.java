/**
 * =========================================================
 * ===       此类是由代码工具生成，框架开发者
 * ===       框架开发者Create By: 李健华
 * ===       创建时间: 2019/11/15 15:29:32
 * =========================================================
 */

package com.wanlianyida.basemsg.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 模板消息Query
 * <AUTHOR>
 * @date 2024/12/09
 */
@Data
public class MsgInfoQuery {

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id", name = "messageId")
    private String messageId;
    /**
     * 消息发送人
     */
    @ApiModelProperty(value = "消息发送人", name = "sender")
    private String sender;
    /**
     * 消息发送时间
     */
    @ApiModelProperty(value = "消息发送时间", name = "sendTime",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendTime;
    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容", name = "content")
    private String content;
    /**
     * 消息接收人
     */
    @ApiModelProperty(value = "消息接收人", name = "receiver")
    private String receiver;

    /**
     * 消息接收人列表
     */
    @ApiModelProperty(value = "消息接收人列表", name = "receiverList")
    private List<String> receiverList;
    /**
     * 消息接收时间
     */
    @ApiModelProperty(value = "消息接收时间", name = "receiveTime",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date receiveTime;
    /**
     * 状态【checkbox:1-已读,2-未读】
     */
    @ApiModelProperty(value = "状态【checkbox:1-已读,2-未读】", name = "status",example = "1")
    private int status;
    /**
     * 应用程序id
     */
    @ApiModelProperty(value = "应用程序id", name = "appId")
    private int appId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remarks")
    private String remarks;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate",example = "2020-12-31 10:10:10")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item1")
    private String item1;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item2")
    private String item2;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item3")
    private String item3;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item4")
    private String item4;

    /**
     * 根据关键字匹配消息内容
     */
    @ApiModelProperty(value = "根据关键字匹配消息内容", name = "ex")
    private String contentQuery;

    /**
     * 模板id列表
     */
    @ApiModelProperty(value = "模板id列表", name = "templateIdList")
    private List<String> templateIdList;
}
