package com.wanlianyida.framework.ctpcommon.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月22日 10:44
 */
@Data
public class TokenInfo implements Serializable {
    private static final long serialVersionUID = -1319364318630957077L;
    /**
     * 改造于2025年4月26日，用于兼容历史业务
     * 此字段来源于主数据operator表的operator_code字段。operator_code存储的是platform的user_base_id字段，
     * 此字段用于兼容历史业务，新数据基于uuid生成。历史数据由platform迁移
     */
    @ApiModelProperty("此字段来源于主数据operator表的operator_code字段。operator_code存储的是platform的user_base_id字段，此字段用于兼容历史业务，新数据基于uuid生成。历史数据由platform迁移")
    private String userBaseId;

    @ApiModelProperty("主数据userInfo表主键")
    private String userId;

    @ApiModelProperty("主数据userInfo表用户名")
    private String username;

    @ApiModelProperty("ctp 企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    private String deviceId;

    @ApiModelProperty("企业信用代码")
    private String licenseNo;

    @ApiModelProperty("主数据loginId")
    private String loginId;

    @ApiModelProperty("主数据用户登录账号")
    private String loginName;

    @ApiModelProperty("ctp 员工id")
    private String memberId;

    @ApiModelProperty("主数据操作员id")
    private String operatorId;

    @ApiModelProperty("10-管理员,20-用户")
    private String operatorType;

    @ApiModelProperty("主数据公司id")
    private String companyIdForCmd;

    @ApiModelProperty("登录端唯一id：登出用")
    private String clentId;

    @ApiModelProperty("是否运营端登录 10-运营端登录，20-用户端登录")
    private String isAdminLogin = "20";

}
