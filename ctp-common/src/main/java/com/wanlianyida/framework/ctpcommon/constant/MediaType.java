package com.wanlianyida.framework.ctpcommon.constant;

public class MediaType {
    public static final String APPLICATION_ATOM_XML = "application/atom+xml";
    public static final String APPLICATION_FORM_URLENCODED = "application/x-www-form-urlencoded;charset=UTF-8";
    public static final String APPLICATION_OCTET_STREAM = "application/octet-stream";
    public static final String APPLICATION_SVG_XML = "application/svg+xml";
    public static final String APPLICATION_XHTML_XML = "application/xhtml+xml";
    public static final String APPLICATION_XML = "application/xml;charset=UTF-8";
    public static final String APPLICATION_JSON = "application/json;charset=UTF-8";
    public static final String MULTIPART_FORM_DATA = "multipart/form-data;charset=UTF-8";
    public static final String TEXT_HTML = "text/html;charset=UTF-8";
    public static final String TEXT_PLAIN = "text/plain;charset=UTF-8";
    private final String type;
    private final String charset;

    private MediaType(String type, String charset) {
        this.type = type;
        this.charset = charset;
    }
}
