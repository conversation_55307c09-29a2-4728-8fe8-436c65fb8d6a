package com.wanlianyida.framework.ctpcommon.model.base;

import lombok.Data;

/**
 * 基础类：外网请求通用参数（如签名、设备ID等）
 */
@Data
public class ExternalBaseRequest {

    /**
     * 用户凭证
     */
    private String passport;

    /**
     * 签名
     */
    private String sign;

    /**
     * 用户IP
     */
    private String sub_ip;

    /**
     * 客户端：client_web(web端)、client_android(安卓端)、 client_ios(安卓端)、client_wechat_mp(微信小程序端);内部服务:service_ctsp
     */
    private String app_platform;

    /**
     * 客户端版本：默认初始版本号1.0.0
     */
    private String client_version;

    /**
     * 接口版本
     */
    private String api_version;

    /**
     * 设备id
     */
    private String device_id;


    /**
     * mac地址
     */
    private String mac_address;


    /**
     * 时间戳毫秒数
     */
    private String client_timestamp;


    /**
     * 业务产品类型：
     * 物流： lgi_app_driver(司机端)、lgi_app_shipper(货主端)、lgi_3pl(web端)、lgi_4pl(web端)
     * 商贸：ctp_app_buyer(买家端)、ctp_app_seller(卖家端)、ctp_portal(门户端)、ctp_user(用户端)、ctp_platform(平台管理端)
     */
    private String client_biz_type;

    /**
     * 随机数（1000-9999整数）
     */
    private String c_random;
}
