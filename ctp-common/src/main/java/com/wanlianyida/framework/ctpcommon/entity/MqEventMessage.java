package com.wanlianyida.framework.ctpcommon.entity;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * mq消息类，用于封装mq消息的数据结构, kafka和rocketMq都应该兼容此格式
 */
@Data
public class MqEventMessage<T> implements Serializable {

    public static <T> EventMessage<T> buildEventMessage(String topic, T data) {
        if (StrUtil.isBlank(topic)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        return EventMessage.<T>builder()
                .messageId(StrUtil.uuid().replace("-", ""))
                .topic(topic)
                .timestamp(new Date())
                .data(data)
                .build();
    }

    public static <T> EventMessage<T> buildEventMessage(String topic, String messageKey, String busId, T data) {
        if (StrUtil.isBlank(topic) || StrUtil.isBlank(messageKey) || StrUtil.isBlank(busId) ) {
            throw new IllegalArgumentException("参数不能为空");
        }
        return EventMessage.<T>builder()
                .messageId(StrUtil.uuid().replace("-", ""))
                .topic(topic)
                .messageKey(messageKey)
                .busId(busId)
                .timestamp(new Date())
                .data(data)
                .build();
    }


    /**
     * 事件消息类，用于封装具体的事件数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EventMessage<T> {
        /**
         * 消息主题
         */
        private String topic;
        /**
         * 唯一标识ID
         */
        private String messageId;

        /**
         * 业务id
         */
        private String busId;

        /**
         * 消息key
         */
        String messageKey;

        /**
         * 时间戳
         */
        private Date timestamp;

        /**
         * 事件数据
         */
        private T data;
    }
}
