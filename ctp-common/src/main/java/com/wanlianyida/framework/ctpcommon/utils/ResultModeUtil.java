package com.wanlianyida.framework.ctpcommon.utils;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;

import java.util.List;

public class ResultModeUtil {

    /**
     * 将原始的 ResultMode<T> 转换为目标类型的 ResultMode<R>
     *
     * @param originalResult 原始结果
     * @param targetClass 目标元素类型
     * @param <T> 原始数据类型
     * @param <R> 目标数据类型
     * @return 转换后的 ResultMode
     */
    public static <T, R> ResultMode<List<R>> convertResultModeList(ResultMode<List<T>> originalResult, Class<R> targetClass) {
        ResultMode<List<R>> result = new ResultMode<>();
        result.setSucceed(originalResult.isSucceed());
        result.setCode(originalResult.getCode());
        result.setMessage(originalResult.getMessage());
        result.setTotal(originalResult.getTotal());

        if (originalResult.getModel() != null) {
            List<R> convertedList = BeanUtil.copyToList(originalResult.getModel(), targetClass);
            result.setModel(convertedList);
        }

        return result;
    }

    /**
     * 将原始的 ResultMode<T> 转换为目标类型的 ResultMode<R>
     *
     * @param originalResult 原始结果
     * @param targetClass 目标类型
     * @param <T> 原始数据类型
     * @param <R> 目标数据类型
     * @return 转换后的 ResultMode
     */
    public static <T, R> ResultMode<R> convertResultMode(ResultMode<T> originalResult, Class<R> targetClass) {
        ResultMode<R> result = new ResultMode<>();
        result.setSucceed(originalResult.isSucceed());
        result.setCode(originalResult.getCode());
        result.setMessage(originalResult.getMessage());
        result.setTotal(originalResult.getTotal());

        if (originalResult.getModel() != null) {
            R converted = BeanUtil.copyProperties(originalResult.getModel(), targetClass);
            result.setModel(converted);
        }

        return result;
    }

}
