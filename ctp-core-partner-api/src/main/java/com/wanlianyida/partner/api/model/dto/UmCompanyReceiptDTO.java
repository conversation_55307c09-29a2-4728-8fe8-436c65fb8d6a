package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:04
 */
@Data
@ApiModel("企业收款信息")
public class UmCompanyReceiptDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("企业编码")
    private String companyCode;

    @ApiModelProperty("账户类型[10-对公账户]")
    private Integer accountType;

    @ApiModelProperty("开户行联行号")
    private String unionBankNo;

    @ApiModelProperty("开户行名称")
    private String bankName;

    @ApiModelProperty("开户行编码")
    private String bankCode;

    @ApiModelProperty("支行名称")
    private String branchBankName;

    @ApiModelProperty("银行账号")
    private String bankAccount;

    @ApiModelProperty("开户名称")
    private String accountName;

    @ApiModelProperty("默认标识[1-默认,0-非默认]")
    private Integer defaultFlag;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
