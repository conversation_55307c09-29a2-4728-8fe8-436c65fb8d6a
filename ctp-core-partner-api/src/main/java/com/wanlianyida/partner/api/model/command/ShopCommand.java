package com.wanlianyida.partner.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 店铺管理
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
public class ShopCommand {
    /**
     * 主键
     */
    private Long id;

    /**
     * 企业id
     */
    @NotNull(message = "企业id不能为空")
    private String companyId;

    /**
     * 企业名称
     */
    @NotNull(message = "企业名称不能为空")
    @Length(max = 50, message = "企业名称最多支持50个字符")
    private String companyName;

    /**
     * 企业社会信用代码
     */
    @NotNull(message = "企业社会信用代码不能为空")
    private String socialCreditCode;

    /**
     * 店铺名称
     */
    @NotNull(message = "店铺名称不能为空")
    @Length(max = 50, message = "店铺名称最多支持50个字符")
    private String shopName;

    /**
     * 店铺开店日期
     */
    @NotNull(message = "店铺开店日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openDate;

    /**
     * 店铺logo地址
     */
    private String shopLogoUrl;

    /**
     * 店铺联系人
     */
    @Length(max = 10, message = "店铺联系人最多支持10个字符")
    @NotNull(message = "店铺联系人不能为空")
    private String contacts;

    /**
     * 店铺联系电话
     */
    @Length(max = 11, message = "店铺联系电话最多支持11个字符")
    @NotNull(message = "店铺联系电话不能为空")
    private String phone;

    /**
     * 店铺电子邮件地址
     */
    @Length(max = 30, message = "店铺电子邮件地址最多支持30个字符")
    private String email;

    /**
     * 省编号
     */

    private String provinceCode;

    /**
     * 城市编号
     */
    private String cityCode;

    /**
     * 区编号
     */
    private String areaCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区名称
     */
    private String areaName;

    /**
     * 店铺详细地址
     */
    @NotNull(message = "店铺详细地址不能为空")
    @Length(max = 200, message = "店铺详细地址最多支持200个字符")
    private String addressDetail;

    /**
     * 店铺分类导航的来源, 10平台标准分类 20自定义分类
     */
    @Length(max = 2, message = "店铺分类导航的来源最多支持2个字符")
    private String navType;

    /**
     * 店铺启用状态 1启用 0禁用
     */
    private Boolean enabledStatus;

    /**
     * 店铺品类
     */
    @Size(max = 5,message = "最多支持选择5个主营品类")
    private List<ShopCategoryCommand> shopCategoryCommandList;

    /**
     * 店铺主营区域
     */
    @Size(max = 5,message = "最多支持选择5个主营地区")
    private List<ShopAreaCommand> shopAreaCommandList;
}
