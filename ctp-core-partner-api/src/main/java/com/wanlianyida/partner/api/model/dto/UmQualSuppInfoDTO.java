package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 企业风控补充资料信息 DTO
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
public class UmQualSuppInfoDTO implements java.io.Serializable{
    @ApiModelProperty("ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("companyId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long companyId;

    @ApiModelProperty("统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("补充资料url")
    private String suppInfoUrl;

    @ApiModelProperty("补充资料说明")
    private String suppInfoRemark;

    @ApiModelProperty("资质类型[10-统一社会信用代码,20-法人身份证]")
    private String qualType;

    @ApiModelProperty("申请人姓名")
    private String applyName;

    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @ApiModelProperty("审核状态[10-待审核,20-不通过,30-通过]")
    private Byte auditStatus;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    @ApiModelProperty("拒绝原因")
    private String refuseReason;

    @ApiModelProperty("有效截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date validEndDate;

    @ApiModelProperty("创建人id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty("用户展示提交按钮标记")
    private Boolean showFlag;
}
