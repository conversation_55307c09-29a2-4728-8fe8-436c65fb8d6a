package com.wanlianyida.partner.api.model.command;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * 物流公司更新
 *
 * <AUTHOR>
 * @date 2024/12/05
 */
@Data
public class ExpressUpdateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 物流公司名称
     */
    @NotNull(message = "物流公司名称不能为空")
    private String companyName;

    /**
     * 联系人姓名
     */
    @NotNull(message = "联系人姓名不能为空")
    private String contactName;

    /**
     * 联系人电话
     */
    @NotNull(message = "联系人电话不能为空")
    private String contactPhone;

    /**
     * 简介
     */
    private String companyProfile;

    /**
     * 状态(0停用 1启用)
     */
    @NotNull(message = "状态不能为空")
    private Integer enableStatus;

}
