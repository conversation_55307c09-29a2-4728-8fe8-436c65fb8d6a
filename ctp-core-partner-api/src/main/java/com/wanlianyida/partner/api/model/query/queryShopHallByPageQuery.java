package com.wanlianyida.partner.api.model.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class queryShopHallByPageQuery {
    /**
     * 店铺名称
     */
    @NotNull(message = "店铺名称不能为空")
    private String shopName;

    /**
     * 排序规则 10 正序 20 倒序
     */
    private String sortingRule;

    /**
     * 排序字段 10默认值 20商品上新时间  30店铺名称
     */
    private String sortField;
}
