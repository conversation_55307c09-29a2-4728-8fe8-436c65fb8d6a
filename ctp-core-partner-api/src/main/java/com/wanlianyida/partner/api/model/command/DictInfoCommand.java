package com.wanlianyida.partner.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 字典表(DictInfo)表实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:17
 */
@Data
@ApiModel("字典表")
public class DictInfoCommand {
    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("字典编码")
    @NotBlank(message = "字典编码不能为空")
    private String dictCode;

    @ApiModelProperty("字典名")
    @NotBlank(message = "字典名不能为空")
    private String dictName;

    @ApiModelProperty("描述")
    private String dictDesc;

    @ApiModelProperty("平台编码")
    private String platformCode;
}

