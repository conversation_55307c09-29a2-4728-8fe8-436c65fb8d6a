package com.wanlianyida.partner.api.model.query;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * 员工
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@Data
public class MemberListQuery {

    private String id;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 用户ID
     */
    private List<String> userBaseIds;

    /**
     * 用户id
     */
    private String userBaseId;

    /**
     * 登录名称
     */
    private String loginName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @NotNull(message = "平台类型不能为空")
    private String platformType;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 操作员id集合
     */
    private List<String> operatorIds;

}
