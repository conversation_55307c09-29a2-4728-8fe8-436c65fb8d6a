package com.wanlianyida.partner.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 授权委托书信息
 * <AUTHOR>
 * @date 2025/03/29
 */
@Data
public class EnterpriseAuthorizationInfoDTO {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("被授权人姓名")
    private String userName;

    @ApiModelProperty("被授权人身份证号")
    private String idCard;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("授权委托书文件地址")
    private String fileUrl;

    @ApiModelProperty("审核状态[1-待审核，2-审核驳回，3-审核通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("过户用户手机号")
    private String transferMobile;

    /**
     * 主数据授权委托书id
     */
    private String mdmPoAId;

    /**
     * 商贸授权委托书审核id
     */
    private String mallPoAId;

}
