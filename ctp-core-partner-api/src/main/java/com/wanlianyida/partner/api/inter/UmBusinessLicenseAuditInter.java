package com.wanlianyida.partner.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.partner.api.model.command.UmBusinessLicenseAuditUpdateCommand;
import com.wanlianyida.partner.api.model.dto.AuditStatisticsDTO;
import com.wanlianyida.partner.api.model.dto.UmBusinessLicenseAuditDetailDTO;
import com.wanlianyida.partner.api.model.dto.UmBusinessLicenseAuditListDTO;
import com.wanlianyida.partner.api.model.query.UmBusinessLicenseAuditQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月31日 10:07
 */
@Api("营业执照审核")
@FeignClient(name = "ctp-core-partner", contextId = "businessLicenseAuditService", path = "/ctp-core-partner")
public interface UmBusinessLicenseAuditInter {

    @ApiOperation("分页列表")
    @PostMapping("/businessLicense/pageCondition")
    ResultMode<List<UmBusinessLicenseAuditListDTO>> pageCondition(@RequestBody @Validated PagingInfo<UmBusinessLicenseAuditQuery> pageQuery);

    @ApiOperation("查询详情")
    @PostMapping("/businessLicense/queryDetail")
    ResultMode<UmBusinessLicenseAuditDetailDTO> queryDetail(@RequestBody @Validated IdQuery query);

    @ApiOperation("审核")
    @PostMapping("/businessLicense/audit")
    ResultMode<?> audit(@RequestBody @Validated UmBusinessLicenseAuditUpdateCommand command);

    @ApiOperation("数据统计")
    @PostMapping("/businessLicense/statistics")
    ResultMode<AuditStatisticsDTO> statistics();
}
