package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * `@Description
 * @Date 2025年03月26日 15:18
 */
@Data
@ApiModel("实名认证结果")
public class AuthenticateRealNameDTO {
    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty("性别，1男2女")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("详细地址")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issuingAgency;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date untilDate;

    @ApiModelProperty("审核状态[1-待审核，2-通过，3-不通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 是否过期 10-未过期 20-已过期 30-无数据
     */
    private Integer isExpired;

    /**
     * 长期有效标志 0-否 1-是
     */
    private Integer validFlag;
}
