package com.wanlianyida.partner.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 账户证书审核
 */
@Data
public class UmAcctCertAuditCommand {
    /**
     * 证书审核记录主键
     */
    @NotNull(message = "证书审核记录id不能为空")
    private Long id;

    /**
     * 审核状态[1-待审核,2-审核未通过,3-审核通过]
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    private String rejectReason;
}