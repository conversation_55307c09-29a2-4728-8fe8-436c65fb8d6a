package com.wanlianyida.partner.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 部门批量删除
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class DeptDeleteCommand {
    /**
     * 主键集合
     */
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @NotNull(message = "平台类型不能为空")
    private String platformType;

    /**
     * 企业id
     */
    private String companyId;

}
