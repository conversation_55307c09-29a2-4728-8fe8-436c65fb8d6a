package com.wanlianyida.partner.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * `@Description
 * @Date 2025年03月26日 15:18
 */
@Data
@ApiModel("实名认证结果")
public class AuthenticateRealNameResultDTO {

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("识别结果")
    private Boolean recognitionResult;
}
