package com.wanlianyida.partner.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 企业信息表 UpdateCommand
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
public class UmCompanyUpdateCommand {
    /**
     * 公司证照(资质)营业执照ID
     */
    @ApiModelProperty(value = "公司证照(资质)营业执照ID", name = "companyBusinessLicenseId")
    private String companyBusinessLicenseId;
    /**
     * 公司信息ID 关联um_company表
     */
    @ApiModelProperty(value = "公司信息ID 关联um_company表", name = "companyId")
    private String companyId;
    /**
     * 证照(资质)类型【select:1-企业营业执照，2-个体工商户执照，3-道路运输资质。4-其他执照】即为证书附件的一级分类 按目前业务只有一种营业执照 即此表为营业执照
     */
    @ApiModelProperty(value = "证照(资质)类型【select:1-企业营业执照，2-个体工商户执照，3-道路运输资质。4-其他执照】即为证书附件的一级分类 按目前业务只有一种营业执照 即此表为营业执照", name = "certificateType")
    private String certificateType;
    /**
     * 预留字段 证件类型，属于二级分类，
     */
    @ApiModelProperty(value = "预留字段 证件类型，属于二级分类，", name = "licenseType")
    private int licenseType;
    /**
     * 资质名称即：营业执照公司企业名称
     */
    @ApiModelProperty(value = "资质名称即：营业执照公司企业名称", name = "licenseName")
    private String licenseName;
    /**
     * 预留（冗余）字段 证照(资质)号码企业营业执照  即为统一信用代码
     */
    @ApiModelProperty(value = "预留（冗余）字段 证照(资质)号码企业营业执照  即为统一信用代码 ", name = "licenseNo")
    private String licenseNo;
    /**
     * 企业法人
     */
    @ApiModelProperty(value = "企业法人", name = "legalPerson")
    private String legalPerson;

    /**
    * 法人身份证号
    */
    @ApiModelProperty(value = "企业法人身份证号", name = "legalPersonIdCard")
    private String legalPersonIdCard;
    /**
     * 发证机关单位(广东，北京，或者某区工商所)
     */
    @ApiModelProperty(value = "发证机关单位(广东，北京，或者某区工商所)", name = "licenseDepartmentGov")
    private String licenseDepartmentGov;
    /**
     * 初次发证日期(签发证日期)
     */
    @ApiModelProperty(value = "初次发证日期(签发证日期)", name = "licenseFirstTime",example = "2021-1-4")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date licenseFirstTime;
    /**
     * 资质(营业执照)有效期起日期
     */
    @ApiModelProperty(value = "资质(营业执照)有效期起日期", name = "licenseStartDate",example = "2021-1-4")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date licenseStartDate;
    /**
     * 资质(营业执照)有效期始日期
     */
    @ApiModelProperty(value = "资质(营业执照)有效期始日期", name = "licenseEndDate",example = "2021-1-4")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date licenseEndDate;
    /**
     * 有效期总数，此字段设置只为前端使用，后台可以先计算存储直接拿出来显示，通过start-end得到的结果
     */
    @ApiModelProperty(value = "有效期总数，此字段设置只为前端使用，后台可以先计算存储直接拿出来显示，通过start-end得到的结果", name = "licenseValidDate")
    private int licenseValidDate;
    /**
     * 资质(营业执照)是否为长期有效【select:1-是,2-否】当是时清空license_end_date
     */
    @ApiModelProperty(value = "资质(营业执照)是否为长期有效【select:11-是,21-否】当是时清空license_end_date", name = "licenseValidIsLong")
    private String licenseValidIsLong;
    /**
     * 资质(营业执照)正面照片地址
     */
    @ApiModelProperty(value = "资质(营业执照)正面照片地址", name = "forntFileUrl")
    private String forntFileUrl;
    /**
     * 资质(营业执照)反面照片地址
     */
    @ApiModelProperty(value = "资质(营业执照)反面照片地址", name = "backFileUrl")
    private String backFileUrl;
    /**
     * 资质(营业执照)照片地址（预留字段可以使用比如上传三维图片)
     */
    @ApiModelProperty(value = "资质(营业执照)照片地址（预留字段可以使用比如上传三维图片)", name = "otherFileUrl")
    private String otherFileUrl;
    /**
     * 把以上三个附件地址字段以json的格式存储下来，没有就空，以逗号分隔，以后可以把附件表的ID存储下来，如果有需要的。
     */
    @ApiModelProperty(value = "把以上三个附件地址字段以json的格式存储下来，没有就空，以逗号分隔，以后可以把附件表的ID存储下来，如果有需要的。", name = "fileAttId")
    private String fileAttId;
    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围", name = "manageScope")
    private String manageScope;
    /**
     * 经营性质
     */
    @ApiModelProperty(value = "经营性质", name = "businessNature")
    private String businessNature;
    /**
     * 公司资质(营业执照)状态【select:1-待审核,2-审核不通过,3-审核通过】
     */
    @ApiModelProperty(value = "公司资质(营业执照)状态【select:1-待审核,2-审核不通过,3-审核通过】", name = "aptitudeStatus")
    private String aptitudeStatus;
    /**
     * 审核不通过原因
     */
    @ApiModelProperty(value = "审核不通过原因", name = "reviewReason")
    private String reviewReason;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", name = "versionCode")
    private String versionCode;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate",example = "2021-1-4 14:23:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate",example = "2021-1-4 14:23:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item1")
    private String item1;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item2")
    private String item2;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item3")
    private String item3;
    /**
     * 预留字段
     */
    @ApiModelProperty(value = "预留字段", name = "item4")
    private String item4;
    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段", name = "ex")
    private String ex;
    /**
     * 企业地址（省Code）
     */
    @ApiModelProperty(value = "企业地址（省Code）", name = "province")
    private String province;
    /**
     * 企业地址（省中文）
     */
    @ApiModelProperty(value = "企业地址（省中文）", name = "provinceName")
    private String provinceName;
    /**
     * 企业地址（市Code）
     */
    @ApiModelProperty(value = "企业地址（市Code）", name = "city")
    private String city;
    /**
     * 企业地址（市中文）
     */
    @ApiModelProperty(value = "企业地址（市中文）", name = "cityName")
    private String cityName;
    /**
     * 企业地址（区/县Code）
     */
    @ApiModelProperty(value = "企业地址（区/县Code）", name = "area")
    private String area;
    /**
     * 企业地址（区/县中文）
     */
    @ApiModelProperty(value = "企业地址（区/县中文）", name = "areaName")
    private String areaName;
    /**
     * 企业地址（镇/街道Code）
     */
    @ApiModelProperty(value = "企业地址（镇/街道Code）", name = "street")
    private String street;
    /**
     * 企业地址（镇/街道中文）
     */
    @ApiModelProperty(value = "企业地址（镇/街道中文）", name = "streetName")
    private String streetName;
    /**
     * 企业地址（详细地址）
     */
    @ApiModelProperty(value = "企业地址（详细地址）", name = "addressDetail")
    private String addressDetail;
    /**
     * 企业类型(这个是营业执照里面的文本)
     */
    @ApiModelProperty(value = "企业类型(这个是营业执照里面的文本)", name = "companyType")
    private String companyType;
    /**
     * 最新记录【select:1-是,2否】
     */
    @ApiModelProperty(value = "最新记录【select:1-是,2否】", name = "lastReader")
    private int lastReader;

    /**
     * 司打标标识【0-未打标 1-宽限期(有效) 2-已过期(失效)】
     */
    @ApiModelProperty(value = "打标标识【0-未打标 1-宽限期(有效) 2-已过期(失效)】", name = "tagStatus")
    private String tagStatus;


    /**
     * 宽限期至
     */
    @ApiModelProperty(value = "宽限期至", name = "graceTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date graceTime;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码", name = "socialCreditCode")
    private String socialCreditCode;

    /**
     * 统一社会信用代码真实
     */
    @ApiModelProperty(value = "统一社会信用代码真实", name = "socialCreditCodeReal")
    private String socialCreditCodeReal;


    @ApiModelProperty(value = "企业业务配置状态", name = "bizSetStatus")
    private String bizSetStatus;
    /**
     * 法人代表证明及授权书url
     */
    @ApiModelProperty(value = "法人代表证明及授权书url", name = "legalPersonAuthUrl")
    private String legalPersonAuthUrl;

    private String exMemberType;
    private String exProvince;
    private String exCity;
    private String exArea;
    private String exStreet;
    private String exAddressDetail;
    private String exLicenseShortName;

    /**
     * 企业类型【select:1-企业工商,2-个体工商,3-其他】
     */
    private Integer exLicenseType;
    /**
     * 经营主体id
     */
    private String exMainBodyId;
    /**
     * 企业名称
     */
    private String exCompanyName;
    /**
     * 审核结果【1-审核通过,2-审核不通过】
     */
    private String exFindingsAudit;

    private String exLoginName;
    private String exTelephone;

    /**
     * 区分4PL审核还是补录【1.补录 2.审核】
     *
     * */
    private String exKeepOrAudit;

    private String exLegalPerson;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人", name = "contacts")
    private String contacts;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话", name = "phone")
    private String phone;

    /**
     * 主管税务机关
     */
    @ApiModelProperty(value = "主管税务机关", name = "taxAuthority")
    private String taxAuthority;


    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本", name = "registeredCapital")
    private BigDecimal registeredCapital;


    /**
     * 成立日期
     */
    @ApiModelProperty(value = "成立日期", name = "foundDate")
    private String foundDate;

    /**
     * 贸易业务归属企业id
     */
    private String bizCompanyId;

    /**
     * 商家类型:10自营,20直营,30贸易商
     */
    private String bizType;

    /**
     * 主营品类
     */
    private List<Long> bizCategoryList;

    /**
     * 主营品牌
     */
    private List<Long> bizBrandList;

}
