package com.wanlianyida.partner.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 组织
 *
 * <AUTHOR>
 * @date 2024/12/08
 */
@Data
public class OrgCommand {
    /**
     * 主键
     */
    private Long id;

    /**
     * 上级组织id
     */
    private String parentOrgId;

    /**
     * 上级组织名称
     */
    private String parentOrgName;

    /**
     * 上级组织信用代码
     */
    private String parentSocialCreditCode;

    /**
     * 组织名称
     */
    @NotNull(message = "组织名称不能为空")
    private String orgName;

    /**
     * 统一社会信用代码
     */
    @NotNull(message = "统一社会信用代码不能为空")
    private String socialCreditCode;

    /**
     * 管理员账号
     */
    @NotNull(message = "管理员账号不能为空")
    private String adminAccount;

    /**
     * 管理员密码
     */
    private String adminPassword;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 操作类型 10-组织新增 20-组织更新 30-组织删除
     */
    private String operationType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 平台类型 21-商贸管理端 22-商贸用户端
     */
    @NotNull(message = "平台类型不能为空")
    private String platformType;
}
