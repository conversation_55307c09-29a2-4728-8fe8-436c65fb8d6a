package com.wanlianyida.partner.api.model.command;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * 企业业务配置信息表 entity
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
public class UmCompanyConfigCommand implements Serializable {

    private static final long serialVersionUID = 1L;


	/**
	 * 企业信息ID
	 */
	@NotNull(message = "企业信息ID不能为空")
	private String companyId;

	/**
	 * 企业配置key
	 */
	@NotNull(message = "企业配置key不能为空")
	private String configKey;

	/**
	 * 企业配置值
	 */
	@NotNull(message = "企业配置值不能为空")
	private String configValue;



}
