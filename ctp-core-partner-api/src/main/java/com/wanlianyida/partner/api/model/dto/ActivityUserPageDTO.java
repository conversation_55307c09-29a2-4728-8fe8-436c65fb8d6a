package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 活动用户列表DTO
 *
 */
@Data
public class ActivityUserPageDTO {
    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * 录入公司名称
     */
    private String recordCompanyName;

    /**
     * 业务方向dict=biz_direction
     */
    private String bizDirection;

    /**
     * 参会目的[10-采购,20-销售]
     */
    private String attendancePurpose;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
