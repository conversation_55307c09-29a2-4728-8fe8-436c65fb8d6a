package com.wanlianyida.partner.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:19
 */
@Data
@ApiModel("授权委托书审核")
public class UmAccreditAuditUpdateCommand {

    @NotNull(message = "审核id不能为空")
    private Long id;

    @NotNull(message = "审核状态不能为空")
    @ApiModelProperty("审核状态[1-待审核，2-不通过，3-通过]")
    private Integer auditStatus;

    @ApiModelProperty("拒绝原因")
    private String rejectReason;

    @ApiModelProperty("备注")
    private String remark;
}
