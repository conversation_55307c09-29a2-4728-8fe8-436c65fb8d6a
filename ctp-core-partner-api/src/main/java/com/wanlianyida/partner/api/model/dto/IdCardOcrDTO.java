package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
public class IdCardOcrDTO {
    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @ApiModelProperty("性别[1-男,2-女]")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("详细地址")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issuingAgency;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty("截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate untilDate;
}
