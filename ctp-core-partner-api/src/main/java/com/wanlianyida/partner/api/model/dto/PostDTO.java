package com.wanlianyida.partner.api.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 岗位管理DTO
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
@Data
public class PostDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 企业ID
     */
    private String relCompanyId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位级别
     */
    private String postGrade;

    /**
     * 状态(0停用 1启用)
     */
    private Short enableStatus;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdDate;

}
