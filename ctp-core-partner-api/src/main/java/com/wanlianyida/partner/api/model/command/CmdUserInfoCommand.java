package com.wanlianyida.partner.api.model.command;

import lombok.Data;

@Data
public class CmdUserInfoCommand {
    /**
     * 手机号
     */
    private String phone;

    /**
     * 验证码
     */
    private String code;

    /**
     * 10-注册 20-登录 30-修改密码 40-修改手机号 50-申请管理员 60-管理员过户 70-用户解绑
     */
    private String type;

    /**
     *是否需要主动查询手机号 true-前端脱敏展示的 false-明文传输的
     */
    private Boolean isNeedQueryPhone;

    /**
     * 密码
     */
    private String password;

    /**
     * 密码强度
     */
    private String passwordStrength;

    /**
     * 是否同意平台协议
     */
    private String isPlatformAgreementAgreed;

    /**
     * 邮箱地址
     */
    private String emailAddress;

    /**
     * 新手机号
     */
    private String newPhone;

    /**
     * 新手机号验证码
     */
    private String newPhoneCode;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请码渠道[10-链接,20-二维码,30-手动输入]
     */
    private Integer channelType;
}
