package com.wanlianyida.partner.api.model.command;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Data
public class AddressManageUpdateCommand {
    /**
     *  主键
     */
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 企业id
     */
    @JsonIgnore
    private String companyId;

    /**
     * 类型 10买家收货地址 20卖家发货地址 30买家仓库地址 40卖家仓库地址
     */
    @Length(max = 2, message = "类型长度超过限制")
    @NotNull(message = "类型不能为空")
    private String type;

    /**
     * 联系人
     */
    @NotNull(message = "联系人不能为空")
    private String contactName;

    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String phone;

    /**
     * 编码（省/直辖市）
     */
    @NotNull(message = "编码不能为空")
    private String provinceCode;

    /**
     * 编码（市）
     */
    @NotNull(message = "编码不能为空")
    private String cityCode;

    /**
     * 编码（县）
     */
    @NotNull(message = "编码不能为空")
    private String areaCode;

    /**
     * 中文（省/直辖市）
     */
    @NotNull(message = "省/直辖市不能为空")
    private String province;

    /**
     * 中文（市）
     */
    @NotNull(message = "市不能为空")
    private String city;

    /**
     * 中文（区）
     */
    @NotNull(message = "区不能为空")
    private String area;

    /**
     * 详细地址
     */
    @NotNull(message = "详细地址不能为空")
    private String detailAddress;

    /**
     * 仓库名称
     */
    private String shortName;;

    /**
     * 是否默认收货地址(0否 1是)
     */
    private Integer defaultStatus;
}
