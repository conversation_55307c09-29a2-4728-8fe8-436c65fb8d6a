package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 大宗商品sku DTO
 */
@Data
public class CtpSkuDTO {
    /**
     * SKU ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 企业ID
     */
    private String companyId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 商品SPU ID
     */
    private Long relSpuId;
    /**
     * SKU编号
     */
    private String skuCode;
    /**
     * SKU规格属性值ID拼接
     */
    private String skuSpecificationId;
    /**
     * SKU规格属性值ID拼接-hash值
     */
    private String skuSpecificationHash;
    /**
     * SKU规格名称(规格属性值value拼接)
     */
    private String skuSpecificationName;
    /**
     * SKU名称（品牌+三级品类+sku规格名称）
     */
    private String skuName;
    /**
     * sku简要说明
     */
    private String skuDesc;
    /**
     * 上架状态（10未上架 20已上架 30违规下架）
     */
    private Integer onSaleStatus;
    /**
     * 批号
     */
    private String batchNo;
    /**
     * 单价
     */
    private BigDecimal priceFee;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 单位转换 1吨=x吨度(计价单位转换为计量单位)
     */
    private BigDecimal unitTransfer;
    /**
     * 是否现货:1是 0否;
     */
    private Integer isSpotGoods;
    /**
     * 交货期限（天）
     */
    private String deliveryPeriod;
    /**
     * 计重方式（10理重 20过磅）
     */
    private String weightMeasurementType;
    /**
     * 品牌ID
     */
    private Long relBrandId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 1级品类ID
     */
    private Long categoryId1;
    /**
     * 1级品类名称
     */
    private String categoryName1;
    /**
     * 2级品类ID
     */
    private Long categoryId2;
    /**
     * 2级品类名称
     */
    private String categoryName2;
    /**
     * 3级品类ID
     */
    private Long categoryId3;
    /**
     * 3级品类名称
     */
    private String categoryName3;
    /**
     * 品类名多级拼接
     */
    private String categoryFullName;
    /**
     * SPU名称
     */
    private String spuName;
    /**
     * 关联仓库ID
     */
    private Long relStockId;
    /**
     * 仓库名称
     */
    private String stockName;
    /**
     * 计量单位编号
     */
    private Integer relMeasurementUnitId;
    /**
     * 计价单位编号
     */
    private Integer relPricingUnitId;
    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
    /**
     * 最后更新人id
     */
    private String updaterId;
    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;
    /**
     * 版本号
     */
    private Integer versionCode;
    /**
     * 是否删除 1删除 0未删除
     */
    private Integer deleted;
    /**
     * 创建人名字
     */
    private String creatorName;
    /**
     * 更新人名字
     */
    private String updaterName;
    /**
     * 当前快照版本
     */
    private String version;
    /**
     * 交货地省编号
     */
    private String deliveryAddrProvinceCode;
    /**
     * 交货地城市编号
     */
    private String deliveryAddrCityCode;
    /**
     * 交货地省名称
     */
    private String deliveryAddrProvinceName;
    /**
     * 交货地城市名称
     */
    private String deliveryAddrCityName;
    /**
     * 上架时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onSaleDate;
    /**
     * 下架时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outSaleDate;
    /**
     * sku图片url
     */
    private String skuUrl;
    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 店铺商品一级分类ids
     */
    private String customCategoryIds1;
    /**
     * 店铺商品一级分类names
     */
    private String customCategoryNames1;
    /**
     * 店铺商品二级分类ids
     */
    private String customCategoryIds2;
    /**
     * 店铺商品二级分类names
     */
    private String customCategoryNames2;
}
