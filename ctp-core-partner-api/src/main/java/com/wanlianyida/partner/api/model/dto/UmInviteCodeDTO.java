package com.wanlianyida.partner.api.model.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 分享码表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "UmInviteCodeDTO对象", description = "分享码表")
public class UmInviteCodeDTO {

    @ApiModelProperty("邀请码id")
    private String id;

    @ApiModelProperty("邀请码")
    private String inviteCode;

    @ApiModelProperty("状态[0-禁用,1-启用]")
    private Integer enableFlag;

    @ApiModelProperty("过期时间")
    private Date expireTime;

    @ApiModelProperty("短链地址")
    private String shortLinkUrl;

    @ApiModelProperty("已使用次数")
    private Integer usedCount;

    @ApiModelProperty("企业ID")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("创建用户id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    private Date createdTime;

    @ApiModelProperty("最后更新人id")
    private String lastUpdaterId;

    @ApiModelProperty("最后更新时间")
    private Date lastUpdatedTime;

    @ApiModelProperty("是否删除标志[0-正常,1-删除]")
    private Integer delFlag;
}

