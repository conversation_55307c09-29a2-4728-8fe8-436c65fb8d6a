package com.wanlianyida.partner.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 授权委托书
 * <AUTHOR>
 * @date 2025/04/05
 */
@Data
public class PowerOfAttorneyCommand {
    /**
     * 授权委托书url
     */
    @NotNull(message = "授权委托书文件不能为空")
    private String fileUrl;


    /**
     * 被授权人姓名
     */
    @NotNull(message = "被授权人姓名不能为空")
    private String userName;

    /**
     * 被授权人身份证号
     */
    @NotNull(message = "被授权人身份证号不能为空")
    private String idCardNo;

    /**
     * 企业名称
     */
    @NotNull(message = "企业名称不能为空")
    private String companyId;

    /**
     * 企业id
     */
    @NotNull(message = "企业id不能为空")
    private String companyName;

    /**
     * 场景 10-创建 20-变更
     */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    /**
     * 商贸授权委托书审核id
     */
    private String mallPoAId;
}
