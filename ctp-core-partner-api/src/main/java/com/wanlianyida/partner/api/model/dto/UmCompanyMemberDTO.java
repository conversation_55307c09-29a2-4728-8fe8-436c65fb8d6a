package com.wanlianyida.partner.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 企业员工信息表 DTO
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
public class UmCompanyMemberDTO {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 企业信息ID
	 */
	private String companyId;

	/**
	 * 用户等级:1管理员,2普通员工
	 */
	private String levelType;

	/**
	 * 用户id
	 */
	private String userBaseId;

	/**
	 * 用户名
	 */
	private String userName;

	/**
	 * 用户名
	 */
	private String loginName;

	/**
	 * 部门
	 */
	private String department;

	/**
	 * 岗位ID
	 */
	private String post;

	/**
	 * 状态:11有效,21无效
	 */
	private String status;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;

	/**
	 * 用户手机号
	 */
	private String userMobile;


}
