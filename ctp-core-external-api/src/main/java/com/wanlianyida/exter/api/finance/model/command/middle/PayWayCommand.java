package com.wanlianyida.exter.api.finance.model.command.middle;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PayWayCommand extends MiddleBaseCommand {

    // 业务方订单号
    private String partner_order_code;

    // 支付方式
    private String pay_type;

    // 支付场景(大额转账、大额付款、扫码支付、代扣)
    private String pay_scenarios;

    // 用户ip
    private String ip;

    // 用户mac地址
    private String mac_address;

    // 用户设备号
    private String device_id;

    // 通知地址
    private String notify_url;

    // 返回地址
    private String return_url;

    // 交易对手主体id
    private String trans_member_id;

    // 交易对手名称
    private String trans_member_name;

    // 交易对手类型(1、企业，2、用户)
    private String trans_member_type;

    // 支付金额(单位分)
    private Long amount;

    // 币种
    private String currency;

    // 扩展参数
    private String ext_param;

    // 客户端类型
    private String client_code;

    // 商品id
    private String order_id;

    // 商品名称
    private String order_title;

    // 商品详情
    private String order_detail;

    // 订单显示商品url
    private String order_show_url;

    // 订单模式(1、正常订单 2、测试订单)
    private String pay_mode;

    // 请求来源
    private String source;

    // 过期时间 
    private String expire_time;

}