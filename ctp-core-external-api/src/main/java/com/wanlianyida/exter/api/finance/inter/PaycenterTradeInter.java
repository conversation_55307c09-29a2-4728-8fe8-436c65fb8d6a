package com.wanlianyida.exter.api.finance.inter;

import com.wanlianyida.exter.api.finance.model.command.CashOutCommand;
import com.wanlianyida.exter.api.finance.model.dto.CashOutDTO;
import com.wanlianyida.exter.api.finance.model.dto.FinanceResultMode;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 交易类 service api
 *
 * <AUTHOR>
 */
@RefreshScope
@FeignClient(url = "${pay.api.url:}", name = "paycenter", contextId = "paycenterTradeService", path = "/paycenter")
public interface PaycenterTradeInter {


    @ApiOperation("提现")
    @PostMapping(value = "/v1/pcs/trade/settle")
    FinanceResultMode<CashOutDTO> cashOut(CashOutCommand command);
}
