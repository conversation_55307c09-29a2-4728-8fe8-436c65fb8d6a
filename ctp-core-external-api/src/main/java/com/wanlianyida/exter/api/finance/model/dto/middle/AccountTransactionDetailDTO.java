package com.wanlianyida.exter.api.finance.model.dto.middle;

import lombok.Data;

@Data
public class AccountTransactionDetailDTO {

    // 会员id
    private String member_id;

    // 会员名称
    private String member_name;

    // 会员类型 1:企业 2:个人
    private Integer member_type;

    // 开户渠道：1银联
    private String channel;

    // 三方账户名称
    private String third_account_name;

    // 三方账户
    private String thrid_account_no;

    // 法人名称
    private String legal_name;

    // 企业名称
    private String company_name;

    // 法人证件号
    private String legal_id_card_no;

    // 企业证件
    private String company_credit_image;

    // 卡号
    private String bank_card_no;

    // 企业执照
    private String company_credit_code;

    // 开户行
    private String opening_bank;

    // 支行名称
    private String bank_branch_name;

    // 银行编码
    private String bank_code;

    // 状态
    private Integer status;

    // 流水id
    private String transcation_id;

    // 申请时间
    private String apply_time;

    // 错误信息
    private String error_message;
}