package com.wanlianyida.exter.api.lgi.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: DictionaryItem
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2025年04月29日
 * @version: 1.0
 */
@Data
public class DictionaryItem implements Serializable {
    @ApiModelProperty(value = "字典ID", example = "20200222214319100001")
    private String dictionaryId;

    @ApiModelProperty(value = "枚举编码", example = "0110")
    private String enumCode;

    @ApiModelProperty(value = "英文名称")
    private String enName;

    @ApiModelProperty(value = "启用状态(0-禁用 1-启用)", example = "1")
    private String enable;

    @ApiModelProperty(value = "中文名称", example = "煤炭及制品-焦炭")
    private String name;

    @ApiModelProperty(value = "父级字典ID", example = "2")
    private String parentdicId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "简称")
    private String shortName;

    @ApiModelProperty(value = "排序号", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private String createDate;

    @ApiModelProperty(value = "修改人")
    private String modifyBy;

    @ApiModelProperty(value = "修改时间")
    private String modifyDate;

    @ApiModelProperty(value = "预留字段1")
    private String item1;

    @ApiModelProperty(value = "预留字段2")
    private String item2;

    @ApiModelProperty(value = "预留字段3")
    private String item3;

    @ApiModelProperty(value = "预留字段4")
    private String item4;

    @ApiModelProperty(value = "子级字典项列表")
    private List<DictionaryItem> children;

    @ApiModelProperty(value = "层级(0-根节点)", example = "0")
    private Integer level;
}
