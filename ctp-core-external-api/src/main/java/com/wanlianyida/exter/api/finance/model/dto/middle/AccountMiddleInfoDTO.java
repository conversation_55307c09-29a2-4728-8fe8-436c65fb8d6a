package com.wanlianyida.exter.api.finance.model.dto.middle;

import lombok.Data;

@Data
public class AccountMiddleInfoDTO {

    // 总额
    private Long total;
    
    // 余额
    private Long balance;
    
    // 冻结
    private Long frozen;
    
    // 收入
    private Long income;
    
    // 支出
    private Long outcome;
    
    // 状态
    private Integer status;
    
    // 会员id
    private String member_id;
    
    // 会员类型 1:企业 2:个人
    private String member_type;
    
    // 会员名称
    private String member_name;

}