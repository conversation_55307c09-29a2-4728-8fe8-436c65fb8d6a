package com.wanlianyida.exter.api.finance.model.command;

import lombok.Data;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025/05/10/09:58
 */
@Data
public class FreezeRequest {

    /**
     * 渠道
     */
    @NotNull(message = "渠道不能为空")
    private String channel;
    /**
     * 公司id
     */
    @NotNull(message = "公司id不能为空")
    private String memberCode;
    /**
     * 平台ID
     */
    @NotNull(message = "平台ID不能为空")
    private String platformId;
    /**
     * 流水号
     */
    @NotNull(message = "流水号不能为空")
    private String serialNumber;
    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空")
    private BigDecimal tradeAmt;
}
