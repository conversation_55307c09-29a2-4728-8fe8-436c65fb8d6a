package com.wanlianyida.exter.api.finance.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class PayWayDTO {

    //支付中台流水号
    private String code;

    // 交易主体id
    private String member_id;
    
    // 唯一交易流水
    private String partner_order_code;
    
    // 支付方式
    private String pay_type;
    
    // 状态
    private Integer status;
    
    // 大额订单号
    private String bank_order_code;
    
    // 有效期
    private String expire_time;
}