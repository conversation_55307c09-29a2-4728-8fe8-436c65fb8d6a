package com.wanlianyida.exter.api.finance.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 账户查询参数
 * <AUTHOR>
 */
@Data
public class AccountQuery {

    /**
     * 会员编码
     */
    @NotBlank(message = "公司id不能为空")
    private String memberCode;

    /**
     * 渠道
     */
    //@NotBlank(message = "渠道不能为空")
    private String channel;

    /**
     * 平台id
     */
    @NotBlank(message = "平台ID不能为空")
    private String platformId;

}
