package com.wanlianyida.exter.api.finance.model.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * BankCardBindCardCommand
 *
 * <AUTHOR>
 * @since 2025/4/25
 */

@Data
public class BankCardBindCardCommandRequest {

    /**
     * 银行卡号
     * 必填项
     */
    private String cardNo;

    /**
     * 银行名称
     * 必填项
     */
    private String bankName;

    /**
     * 支行名称
     * 必填项
     */
    @NotBlank(message = "支行名称不能为空")
    private String branchName;

    /**
     * 银行支行联行号
     * 必填项
     */
    private String bankBranchNo;

    /**
     * 超级网银联行号
     * 必填项
     */
    private String superBankNo;

    /**
     * 渠道类型
     * 必填项
     */
    private String channel;

    /**
     * 会员编码
     * 必填项
     */
    private String memberCode;

    /**
     * 会员名称
     * 必填项
     */
    private String memberName;

    /**
     *平台ID
     */
    private String platformId;

}