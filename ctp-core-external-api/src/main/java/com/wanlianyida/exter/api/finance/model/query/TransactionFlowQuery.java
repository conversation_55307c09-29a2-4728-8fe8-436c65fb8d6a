package com.wanlianyida.exter.api.finance.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import javax.validation.constraints.NotBlank;

/**
 * 交易流水查询参数
 * <AUTHOR>
 */
@Data
public class TransactionFlowQuery {

    /**
     * 交易类型（对应原型账户变动原因-银行接口文档中是交易类型字段 如：充值、提现、退款等）
     */
    private String tradeType;

    /**
     * 交易开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tradeDateStart;

    /**
     * 交易结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tradeDateEnd;

    /**
     * 会员编码
     */
    @NotBlank(message = "公司id不能为空")
    private String memberCode;

    /**
     * 渠道
     */
    @NotBlank(message = "渠道不能为空")
    private String channel;

    /**
     * 平台id
     */
    @NotBlank(message = "平台ID不能为空")
    private String platformId;

    // 分页参数
    private Integer pageNum = 1;
    private Integer pageSize = 10;

    private Integer offset;

    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }
}
