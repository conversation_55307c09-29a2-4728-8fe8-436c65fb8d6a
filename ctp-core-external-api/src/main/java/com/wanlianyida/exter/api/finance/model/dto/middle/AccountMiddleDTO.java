package com.wanlianyida.exter.api.finance.model.dto.middle;

import lombok.Data;

import java.util.List;

@Data
public class AccountMiddleDTO {
    // 总冻结金额
    private Long freeze;
    
    // 总可用金额
    private Long balance;
    
    // 总金额
    private Long total;
    
    // 账户列表
    private List<AccountInfo> account_list;

    @Data
    public static class AccountInfo {
        // 渠道
        private String channel;

        // 账户名称
        private String account_name;

        // 三方账户名称
        private String third_account_name;

        // 三方账户
        private String third_account_no;

        // 账户类型
        private String account_type;

        // 开户时间
        private String open_time;

        // 状态
        private String status;

        // 冻结金额
        private Long freeze;

        // 可用余额
        private Long balance;

        // 金额
        private Long amount;
    }

}