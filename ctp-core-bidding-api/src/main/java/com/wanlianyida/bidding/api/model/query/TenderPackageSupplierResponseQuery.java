package com.wanlianyida.bidding.api.model.query;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 标包-供应商应答表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Getter
@Setter
public class TenderPackageSupplierResponseQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标包名称
     */
    private String tenderPackageName;

    /**
     * 供应商应答状态:枚举response_status
     */
    private Integer responseStatus;


    /**
     * 供应商id
     */
    private String supplierId;


}
