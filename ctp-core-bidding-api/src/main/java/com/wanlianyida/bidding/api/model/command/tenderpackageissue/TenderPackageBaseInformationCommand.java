package com.wanlianyida.bidding.api.model.command.tenderpackageissue;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 标包DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenderPackageBaseInformationCommand {
    @ApiModelProperty("标包ID")
    @NotNull(message = "标包ID 不能为空")
    private Long id;

    @NotNull(message = "标书费 不能为空")
    @ApiModelProperty("标书费用")
    @Min(value = 0, message = "标书费 不能小于0")
    private BigDecimal tenderExpense;

    @NotEmpty(message = "报价方式 不能为空")
    @ApiModelProperty("报价方式 10含税总价 20未含税总价 30未含税总价+含税总价 40未含税总价+税额+含税总价 50未含税单价+税额+含税单价 60未含税总价+税率+含税总价 70未含税单价+税率+含税单价")
    private String quotationMethod;

    @ApiModelProperty("是否线下递交投标文件 0否 1是")
    @NotNull(message = "是否线下递交投标文件 不能为空")
    private Integer offlineSubmit;

    @ApiModelProperty("评标方式 10线上 20线下")
    @NotNull(message = "评标方式 不能为空")
    private String tenderEvaluationMethod;

    @ApiModelProperty("审核供应商报名标识 0否 1是")
    @NotNull(message = "审核供应商报名标识 不能为空")
    private Integer reviewSupplierSignupFlag;

    //----INSERT

    @ApiModelProperty("标包名称")
    @Length(max = 100, message = "标包名称最多支持100个字符")
    private String tenderPackageName;

    @ApiModelProperty("采购方式 10公开竞谈 20邀请竞谈")
    private String purchaseMethod;

    @ApiModelProperty("预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty("'是否含税 0否 1是")
    private Integer taxFlag;

    @ApiModelProperty("投标保证金")
    private BigDecimal marginAmount;

    @ApiModelProperty("招标负责人id")
    private String tenderLeaderId;

    @ApiModelProperty("需求接收时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandReceiveTime;

    @ApiModelProperty("采购公司主体id")
    private String purchaseCompanyId;

    @ApiModelProperty("采购公司主体名称")
    private String purchaseCompanyName;

    @ApiModelProperty("需求部门id")
    private String purchaseDeptId;

    @ApiModelProperty("部门名称")
    private String purchaseDeptName;

    @ApiModelProperty("备注名称")
    @Length(max = 200, message = "备注最多支持200个字符")
    private String remark;

    @ApiModelProperty("招标负责人姓名")
    private String tenderLeaderName;

}
