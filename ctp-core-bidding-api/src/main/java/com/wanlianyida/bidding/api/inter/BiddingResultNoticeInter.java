package com.wanlianyida.bidding.api.inter;

import com.wanlianyida.bidding.api.model.dto.BiddingResultNoticeDTO;
import com.wanlianyida.bidding.api.model.dto.BiddingWinNoticeDetailDTO;
import com.wanlianyida.bidding.api.model.dto.CompressFileInfoDTO;
import com.wanlianyida.bidding.api.model.query.BiddingResultNoticeQuery;
import com.wanlianyida.bidding.api.model.query.BiddingWinNoticeQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/14/09:35
 */
@Api("标包-投标")
@FeignClient(name = "ctp-core-bidding", contextId = "biddingResultNoticeInterService", path = "/ctp-core-bidding")
public interface BiddingResultNoticeInter {

    /**
     * 查询列表
     * @param query
     * @return
     */
    @PostMapping("/bidding/resultNotice/queryPageList")
    @ApiModelProperty("分页查询")
    ResultMode<List<BiddingResultNoticeDTO>> list(@RequestBody @Validated PagingInfo<BiddingResultNoticeQuery> query);


    /**
     * 中标通知书详情
     * @param query
     * @return
     */
    @PostMapping("/bidding/resultNotice//winNoticeDetail")
    @ApiModelProperty("中标通知书详情")
    ResultMode<BiddingWinNoticeDetailDTO> winNoticeDetail(@RequestBody @Validated BiddingWinNoticeQuery query);

    /**
     * 中标结果信息分页查询
     * @param pageQuery
     * @return
     */
    @PostMapping("/bidding/resultNotice/page-query-result-notice")
    public ResultMode<List<BiddingResultNoticeDTO>> pageQueryResultNotice(@RequestBody @Validated PagingInfo<BiddingResultNoticeQuery> pageQuery);

    /**
     * 查询中标/未中标通知书url
     * @param query
     * @return
     */
    @PostMapping("/bidding/resultNotice/query-notice-url-list")
    ResultMode<List<CompressFileInfoDTO>> queryNoticeUrlList(BiddingResultNoticeQuery query);
}
