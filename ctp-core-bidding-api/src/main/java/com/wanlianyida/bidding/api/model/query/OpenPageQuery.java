package com.wanlianyida.bidding.api.model.query;

import lombok.Data;

/**
 * 开标
 * <AUTHOR>
 */
@Data
public class OpenPageQuery {

    /**
     * 标包名称
     */
    private String packageName;

    /**
     * 标包编号
     */
    private String packageNo;

    /**
     * 招标负责人
     */
    private String tenderLeaderName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目经理名称
     */
    private String projectManagerName;

    /**
     * 开标状态
     */
    private Integer openStatus;

}
