package com.wanlianyida.bidding.api.model.command;

import com.wanlianyida.bidding.api.model.dto.TenderPackageSupplierResponseDTO;
import com.wanlianyida.bidding.api.model.dto.TenderRegisterInfoDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 标包-供应商应答表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Getter
@Setter
public class TenderPackageSupplierPackageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详情
     */
    private TenderPackageSupplierResponseDTO supplierResponse;

    /**
     * 联系人信息
     */
    private TenderRegisterInfoDTO tenderRegisterInfo;


}
