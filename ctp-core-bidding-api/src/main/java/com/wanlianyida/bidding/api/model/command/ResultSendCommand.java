package com.wanlianyida.bidding.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/12/17/09:57
 */
@Data
public class ResultSendCommand {

    @NotBlank(message = "供应商id不允许为空")
    private List<String> supplierId;

    /**
     * 通知书内容信息
     */
    @Valid
    @NotNull(message = "通知书内容信息不允许为空")
    private NoticeSendContent noticeSendContent;

    /**
     * 附件列表
     */
    @Valid
    private List<ProjectAttachment> attachmentList;


    /**
     * 通知书内容信息
     */
    @Data
    public static class NoticeSendContent{

        /**
         * 标题
         */
        @NotBlank(message = "标题不能为空")
        private String articleTitle;

        /**
         * 内容摘要
         */
        private String contentSummary;


        /**
         * 内容详情
         */
        @NotBlank(message = "内容详情不能为空")
        private String contentText;


    }

    /**
     * 立项文件
     */
    @Data
    public static class ProjectAttachment {

        /**
         * 文件名
         */
        @NotBlank(message = "文件名不能为空")
        private String fileName;

        /**
         * 文件大小
         */
        @NotBlank(message = "文件大小不能为空")
        private String fileSize;

        /**
         * 文件url
         */
        @NotBlank(message = "文件url不能为空")
        private String fileUrl;

        /**
         * 上传时间
         */
        @NotNull(message = "上传时间不能为空")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createdDate;

    }
}
