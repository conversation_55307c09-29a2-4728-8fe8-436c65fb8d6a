package com.wanlianyida.bidding.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月10日 16:38
 */
@Data
public class ConsultInfoQuery {

    @ApiModelProperty("标包id")
    private Long tenderPackageId;

    @ApiModelProperty("供应商应答id")
    private Long supplierResponseId;

    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("供应商名称或账号")
    private String supplier;

    @ApiModelProperty("供应商应答状态[枚举response_status]")
    private Integer supplierResponseStatus;

    @ApiModelProperty("最新标志[1是，0否]")
    private Integer latestFlag;

    @ApiModelProperty("供应商账号")
    private String supplierAccount;

    @ApiModelProperty("咨询开始时间")
    private String consultTimeStart;

    @ApiModelProperty("咨询结束时间")
    private String consultTimeEnd;
}
