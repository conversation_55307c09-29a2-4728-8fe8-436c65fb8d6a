package com.wanlianyida.bidding.api.inter;

import com.wanlianyida.bidding.api.model.command.*;
import com.wanlianyida.bidding.api.model.dto.*;
import com.wanlianyida.bidding.api.model.query.*;
import com.wanlianyida.framework.ctpcommon.entity.IdCommand;
import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 评标-专家详评
 *
 * <AUTHOR>
 */
@Api("评标-专家详评")
@FeignClient(name = "ctp-core-bidding", contextId = "expertCriticiseService", path = "/ctp-core-bidding")
public interface ExpertCriticiseInter {

    /**
     * 评标管理-分页查询
     */
    @ApiOperation("评标管理-分页查询")
    @PostMapping(value = {"/expertCriticise/pageList"})
    public ResultMode<List<EvalTaskDetailPageDTO>> pageList(@RequestBody @Validated PagingInfo<EvalTaskDetailPageQuery> pagingInfo);

    /**
     * 评标管理-合规承诺书确认
     */
    @ApiOperation("评标管理-合规承诺书确认")
    @PostMapping(value = {"/expertCriticise/confirm"})
    public ResultMode confirm(@RequestBody @Validated IdCommand command);

    /**
     * 详评-项目信息
     */
    @ApiOperation("详评-项目信息")
    @PostMapping(value = {"/expertCriticise/projectInfo"})
    public ResultMode<EvalExpertDetailProjectDTO> projectInfo(@RequestBody @Validated EvalExpertDetailProjectQuery query);

    /**
     * 详评-详情
     */
    @ApiOperation("详评-详情")
    @PostMapping(value = {"/expertCriticise/detailInfo"})
    public ResultMode<EvalTaskDetailDTO> detailInfo(@RequestBody @Validated IdQuery query);

    /**
     * 详评-个人评分-评分列表
     */
    @ApiOperation("详评-个人评分-评分列表")
    @PostMapping(value = {"/expertCriticise/personalScoreList"})
    public ResultMode<List<EvalPersonalScoreListDTO>> personalScoreList(@RequestBody @Validated EvalPersonalScoreListQuery query);

    /**
     * 详评-小组评分汇总列表
     */
    @ApiOperation("详评-小组评分汇总列表")
    @PostMapping(value = {"/expertCriticise/groupScoreList"})
    public ResultMode<Map<String, Map<String, List<EvalGroupScoreListDTO>>>> groupScoreList(@RequestBody @Validated EvalGroupScoreListQuery query);

    /**
     * 详评-提交个人汇总
     */
    @ApiOperation("详评-提交个人汇总")
    @PostMapping(value = {"/expertCriticise/submitPersonalScore"})
    public ResultMode submitPersonalScore(@RequestBody @Validated EvalScoreSubmitCommand command);

    /**
     * 详评-提交小组汇总、打分回退、重新评审
     */
    @ApiOperation("详评-提交小组汇总、打分回退、重新评审")
    @PostMapping(value = {"/expertCriticise/submitStage"})
    public ResultMode submitStage(@RequestBody @Validated EvalStageSubmitCommand command);

    @ApiOperation("详评-评分结果汇总-评分结果列表")
    @PostMapping(value = {"/expertCriticise/queryEvalScoreResultPage"})
    ResultMode<List<EvalScoreResultListDTO>> queryEvalScoreResultPage(@RequestBody @Validated EvalScoreResultQuery query);

    @ApiOperation("详评-评分结果汇总-保存附加分")
    @PostMapping(value = {"/expertCriticise/saveExtraScore"})
    ResultMode saveExtraScore(@RequestBody @Validated EvalResultExtraScoreCommand command);

    @ApiOperation("详评-评分结果汇总-提交汇总得分")
    @PostMapping(value = {"/expertCriticise/submitSummaryScore"})
    ResultMode submitSummaryScore(@RequestBody @Validated SummaryScoreSubmitCommand command);

    @ApiOperation("详评-推荐预中选供应商-确认推荐")
    @PostMapping(value = {"/expertCriticise/recommendSupplier"})
    ResultMode recommendSupplier(@RequestBody @Validated SupplierRecommendCommand command);

    @ApiOperation("详评-出具评审报告-确认")
    @PostMapping(value = {"/expertCriticise/provideReviewReport"})
    ResultMode provideReviewReport(@RequestBody @Validated ProvideReviewReportCommand command);

    @ApiOperation("评委结束评审-中标候选人-修改排名")
    @PostMapping(value = {"/expertCriticise/updateRanking"})
    ResultMode recommendSupplier(@RequestBody @Validated SupplieRankingUpdateCommand command);
}
