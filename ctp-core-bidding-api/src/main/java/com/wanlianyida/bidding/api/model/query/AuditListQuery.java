package com.wanlianyida.bidding.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 用户任务查询
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
public class AuditListQuery {

    /**
     * 任务类型 10立项审批 20发标审批 30公示侯选人审批 40公示中标结果审批
     */
    private String taskType;

    /**
     * 任务类型 10立项审批 20发标审批 30公示侯选人审批 40公示中标结果审批
     */
    private List<String> taskTypeList;

    /**
     * 审核类型 10待审 20已审
     */
    private String auditType;

    /**
     * 审核状态 10待审 20审核通过 30审核不通过
     */
    private Integer auditStatus;

    /**
     * 流程状态 10待审批 20审批中 30审批结束 40审批驳回
     */
    private Integer flowStatus;

    /**
     * 提交开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyDateStart;

    /**
     * 提交结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyDateEnd;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户id
     */
    private List<String> userIdList;

}
