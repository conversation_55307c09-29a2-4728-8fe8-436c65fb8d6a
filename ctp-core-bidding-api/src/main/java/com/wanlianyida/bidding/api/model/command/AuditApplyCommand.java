package com.wanlianyida.bidding.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 审批提交command
 */
@Data
public class AuditApplyCommand {
    @ApiModelProperty("任务类型 10立项审批 20发标审批 30公示侯选人审批 40公示中标结果审批")
    @NotEmpty(message = "任务类型")
    String taskType;

    @ApiModelProperty("业务id(项目id、标包id等)")
    @NotNull(message = "业务id 不能为空")
    Long businessId;

    @ApiModelProperty("审批人信息")
    List<AuditUserCommand> auditUserList;

    @ApiModelProperty("公示审批-其他业务数据")
    WinningResultManagerCommand winningResultManagerCommand;
}
