package com.wanlianyida.bidding.api.model.command.tenderpackageissue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 标包-供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenderPackageSupplierCommand {
    @ApiModelProperty("标包id")
    @NotNull(message = "标包id 不能为空")
    private Long relTenderPackageId;

    @ApiModelProperty("供应商id")
    @NotEmpty(message = "供应商id 不能为空")
    private String supplierId;

    @ApiModelProperty("供应商名称")
    @NotEmpty(message = "供应商名称 不能为空")
    private String supplierName;

    @ApiModelProperty("供应商账号")
    @NotEmpty(message = "供应商账号 不能为空")
    private String supplierAccount;

    @ApiModelProperty("社会信用代码")
    @NotEmpty(message = "社会信用代码 不能为空")
    private String socialCreditCode;

    @ApiModelProperty("联系人")
    private String linkmanName;

    @ApiModelProperty("联系电话")
    private String linkmanMobile;
}
