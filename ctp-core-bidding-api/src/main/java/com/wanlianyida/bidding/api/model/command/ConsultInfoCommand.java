package com.wanlianyida.bidding.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月10日 14:09
 */
@Data
public class ConsultInfoCommand {

    @ApiModelProperty("供应商应答id")
    @NotNull(message = "供应商应答id不能为空")
    private Long supplierResponseId;

    @ApiModelProperty("咨询标题")
    @NotBlank(message = "咨询标题不能为空")
    private String consultTitle;

    @ApiModelProperty("咨询内容")
    @NotBlank(message = "咨询内容不能为空")
    private String consultContent;

    private List<AttachmentBaseCommand> attachmentList;
}
