package com.wanlianyida.bidding.api.model.command.tenderpackageissue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 标包-产品d
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenderPackageCreateProductCommand {
    @ApiModelProperty("项目id")
    private Long relProjectId;

    @ApiModelProperty("标包id")
    private Long relTenderPackageId;

    @ApiModelProperty("产品id")
    private Long relProjectProductId;

    @ApiModelProperty("产品编号")
    @Size(max = 50, message = "产品编号 最多50个字符")
    private String productNo;

    @ApiModelProperty("产品名称")
    @Size(max = 50, message = "产品名称 最多50个字符")
    @NotEmpty(message = "产品名称 不能为空")
    private String productName;

    @ApiModelProperty("备注")
    @Size(max = 100, message = "备注 最多100个字符")
    private String remark;

    @ApiModelProperty("需求数量")
    @NotNull(message = "需求数量 不能为空")
    @Min(value = 0, message = "需求数量 必须大于0")
    private BigDecimal productQuantity;

    @ApiModelProperty("产品单位")
    @NotNull(message = "产品单位 不能为空")
    private Integer productUnitId;
}
