package com.wanlianyida.bidding.api.model.command;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 评分结果汇总-提交汇总得分 Command
 * <AUTHOR>
 */
@Data
public class SummaryScoreSubmitCommand {

    /**
     * evalTaskDetailId
     */
    @NotNull(message = "evalTaskDetailId不允许为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long evalTaskDetailId;

}
