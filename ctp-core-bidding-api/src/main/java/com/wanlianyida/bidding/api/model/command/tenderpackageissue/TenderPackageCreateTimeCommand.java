package com.wanlianyida.bidding.api.model.command.tenderpackageissue;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <p>
 * 标包-招标时间
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenderPackageCreateTimeCommand {
    @ApiModelProperty("标包id")
    private Long relTenderPackageId;

    @ApiModelProperty("标书售卖开始时间")
    @NotNull(message = "标书售卖开始时间 不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date saleStartTime;

    @ApiModelProperty("标书售卖截止时间")
    @NotNull(message = "标书售卖截止时间 不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date saleEndTime;

    @ApiModelProperty("投标截止时间")
    @NotNull(message = "投标截止时间 不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidEndTime;

    @ApiModelProperty("开标时间")
    @NotNull(message = "开标时间 不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidOpeningTime;

    @ApiModelProperty("开标地址")
    @Size(max = 200, message = "开标地址 最多200个字符")
    @NotEmpty(message = "开标地址 不能为空")
    private String bidOpeningPlace;
}
