package com.wanlianyida.baseaiadv.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 产业顾问消息，es索引：industry-assistant-conversation-message
 * @Date 2025年04月14日 16:46
 */
@Data
public class AssistantConvMessagePageQuery {

    @ApiModelProperty("会话id")
    private Long convId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("排序类型，1：正序，0：倒序")
    private Integer sortType = 0;
}
