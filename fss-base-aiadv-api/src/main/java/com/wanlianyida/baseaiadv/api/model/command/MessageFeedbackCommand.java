package com.wanlianyida.baseaiadv.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月16日 19:59
 */
@ApiModel("消息反馈")
@Data
public class MessageFeedbackCommand {

    private String userId;

    @NotNull(message = "消息id不能为空")
    @ApiModelProperty("消息id")
    private String messageId;

    @ApiModelProperty("反馈类型，10内容不准确，20与问题不相关，30逻辑不通顺")
    private String feedbackType;

    @NotNull(message = "反馈内容不能为空")
    @ApiModelProperty("反馈内容")
    private String feedback;
}
