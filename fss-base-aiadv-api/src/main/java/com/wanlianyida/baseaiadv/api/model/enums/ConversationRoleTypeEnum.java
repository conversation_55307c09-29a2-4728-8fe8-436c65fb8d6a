package com.wanlianyida.baseaiadv.api.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年05月27日 09:55
 */
@Getter
@AllArgsConstructor
public enum ConversationRoleTypeEnum {

    DRIVER(101, "司机"),
    SHIPPER(102, "货主"),
    OPERATOR(103, "运营"),

    BUYER(201, "买家"),
    SELLER(202, "卖家"),
    ;

    private final Integer code;
    private final String name;
}
