package com.wanlianyida.baseaiadv.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月23日 11:39
 */
@Data
public class AssistantMessageReferenceQuery {

    @ApiModelProperty("消息id")
    @NotNull(message = "消息id不能为空")
    private Long messageId;

    @ApiModelProperty("引用来源，10：本地检索，20：线上检索，30：本地文件")
    @NotNull(message = "引用来源不能为空")
    private Integer referenceSource;
}
