package com.wanlianyida.baseaiadv.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月17日 09:40
 */
@Data
public class AssistantMessageQuery {

    @NotNull(message = "会话id不能为空")
    @ApiModelProperty("会话id")
    private Long convId;

    @ApiModelProperty("用户id")
    private String userId;
}
