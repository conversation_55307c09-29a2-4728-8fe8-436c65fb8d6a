package com.wanlianyida.baseaiadv.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 产业顾问消息，es索引：fss_service_conv_message
 * @Date 2025年04月14日 16:46
 */
@Data
public class ServiceConvMessageDetailQuery {

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户账号")
    private String userAccount;

    @ApiModelProperty("搜索内容")
    private String searchContent;

    @ApiModelProperty("会话时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("会话时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("反馈类型，10内容不准确，20与问题不相关，30逻辑不通顺")
    private String feedbackType;

    @ApiModelProperty("平台码[10-物流，20-商贸，60-AI大模型]")
    private Integer platformType;

    @ApiModelProperty("客户端类型[物流：111-司机APP,112-司机小程序,121-货主APP,122-3pl,123-货主小程序,131-4pl;商贸：211-用户端APP,212-用户端小程序,213-用户端web,221-门户web]")
    private Integer clientType;

    @ApiModelProperty("角色类型[物流：101-司机,111-货主,121-运营，商贸：201-买家,211-卖家]")
    private Integer roleType;

    @ApiModelProperty("排序类型，1：正序，0：倒序")
    private Integer sortType = 0;
}
