package com.wanlianyida.baseaiadv.api.inter;

import com.wanlianyida.baseaiadv.api.model.command.MessageEvaluateCommand;
import com.wanlianyida.baseaiadv.api.model.command.MessageFeedbackCommand;
import com.wanlianyida.baseaiadv.api.model.command.ServiceConvMessageInsertCommand;
import com.wanlianyida.baseaiadv.api.model.command.ServiceConvMessageUpdateCommand;
import com.wanlianyida.baseaiadv.api.model.dto.ServiceConvMessageDTO;
import com.wanlianyida.baseaiadv.api.model.dto.ServiceConvMessageDetailDTO;
import com.wanlianyida.baseaiadv.api.model.query.ServiceConvMessageDetailQuery;
import com.wanlianyida.baseaiadv.api.model.query.ServiceConvMessagePageQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月17日 10:19
 */
@Api(value = "消息")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.aiadv:}", name = "fss-base-aiadv", contextId = "serviceConvMessageService", path = "/fss-base-aiadv/serviceConvMessage")
public interface ServiceConvMessageInter {

    @ApiOperation("保存消息")
    @PostMapping("/save")
    ResponseMessage<ServiceConvMessageDTO> save(@RequestBody @Validated ServiceConvMessageInsertCommand command);

    @ApiOperation("刷新消息")
    @PostMapping("/refresh")
    ResponseMessage<ServiceConvMessageDTO> refresh(@RequestBody @Validated ServiceConvMessageUpdateCommand command);

    @ApiOperation("点赞")
    @PostMapping("/upvote")
    ResponseMessage<?> upvote(@RequestBody @Validated MessageEvaluateCommand command);

    @ApiOperation("踩")
    @PostMapping("/trample")
    ResponseMessage<?> trample(@RequestBody @Validated MessageEvaluateCommand command);

    @ApiOperation("反馈")
    @PostMapping("/feedback")
    ResponseMessage<?> feedback(@RequestBody @Validated MessageFeedbackCommand command);

    @ApiOperation("分页查询消息列表")
    @PostMapping("/pageMessageList")
    ResponseMessage<List<ServiceConvMessageDTO>> pageMessageList(@RequestBody @Validated PagingInfo<ServiceConvMessagePageQuery> pageQuery);

    @ApiOperation("分页查询消息详情")
    @PostMapping("/pageMessageDetail")
    ResponseMessage<List<ServiceConvMessageDetailDTO>> pageMessageDetail(@RequestBody @Validated PagingInfo<ServiceConvMessageDetailQuery> pageQuery);
}
