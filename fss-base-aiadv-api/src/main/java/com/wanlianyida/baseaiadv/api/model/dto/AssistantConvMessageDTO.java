package com.wanlianyida.baseaiadv.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 产业顾问对话消息，es索引：fss_industry_assistant_conv_message
 * @Date 2025年04月14日 16:46
 */
@Data
public class AssistantConvMessageDTO {

    @ApiModelProperty("消息id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long messageId;

    @ApiModelProperty("会话id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long convId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户账号")
    private String userAccount;

    @ApiModelProperty("问题类型，10：文本，20：语音，30：图片，40：文件")
    private Integer questionType;

    @ApiModelProperty("问题内容，文本")
    private String questionContent;

    @ApiModelProperty("问题url")
    private String questionUrl;

    @ApiModelProperty("答案类型，10：文本，20：语音，30：图片，40：文件")
    private Integer answerType;

    @ApiModelProperty("答案内容，文本")
    private String answerContent;

    @ApiModelProperty("答案url")
    private String answerUrl;

    @ApiModelProperty("会话时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date convTime;

    @ApiModelProperty("上一条记录id")
    private String preRecordId;

    @ApiModelProperty("私有知识域id")
    private String dataSetId;

    @ApiModelProperty("点赞状态 10点赞 20点踩")
    private Integer likeStatus;

    @ApiModelProperty("模型类型，10：产联大模型，20：DeepSeek")
    private Integer modelType;

    @ApiModelProperty("是否深度思考，1是0否")
    private Integer deepThinkFlag;

    @ApiModelProperty("是否联网搜索，1是0否")
    private Integer networkFlag;

    @ApiModelProperty("内容类型（10：从行业分析，20：行业资讯，30：行业调研）")
    private Integer contentType;

    @ApiModelProperty("位置，1左2右")
    private Integer position;

    @ApiModelProperty("本地搜索数量")
    private Integer localCount;

    @ApiModelProperty("联网搜索数量")
    private Integer networkCount;

    @ApiModelProperty("私有检索数量")
    private Integer privateCount;

    @ApiModelProperty("最后更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updaterDate;
}
