package com.wanlianyida.baseaiadv.api.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年05月27日 09:55
 */
@Getter
@AllArgsConstructor
public enum ServiceClientTypeEnum {


    DRIVER_APP(111, "司机APP"),
    DRIVER_MINI_PROGRAM(112, "司机小程序"),
    SHIPPER_APP(121, "货主APP"),
    SHIPPER_WEB(122, "3PL-货主web"),
    SHIPPER_MINI_PROGRAM(123, "货主小程序"),
    PLATFORM_WEB(131, "4pl-平台web"),

    ACCOUNT_CENTER_APP(211, "账户中心APP"),
    ACCOUNT_CENTER_MINI_PROGRAM(212, "账户中心小程序"),
    ACCOUNT_CENTER_WEB(213, "账户中心web"),
    PORTAL_WEB(221, "门户web"),

    AI_MODEL(600, "AI大模型"),
    ;

    private final Integer code;
    private final String name;
}
