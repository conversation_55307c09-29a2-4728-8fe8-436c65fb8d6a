package com.wanlianyida.baseaiadv.api.inter;

import com.wanlianyida.baseaiadv.api.model.command.EsConversationCommand;
import com.wanlianyida.baseaiadv.api.model.command.EsMessageFeedbackCommand;
import com.wanlianyida.baseaiadv.api.model.command.EsServiceConvMessageCommand;
import com.wanlianyida.baseaiadv.api.model.dto.EsConversationDTO;
import com.wanlianyida.baseaiadv.api.model.dto.EsServiceConvMessageDTO;
import com.wanlianyida.baseaiadv.api.model.query.EsConversationPageQuery;
import com.wanlianyida.baseaiadv.api.model.query.EsConversationQuery;
import com.wanlianyida.baseaiadv.api.model.query.EsServiceConvMessagePageQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月13日 15:33
 */
@Api(value = "消息")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.aiadv:}", name = "fss-base-aiadv", contextId = "esServiceConvMessageService", path = "/fss-base-aiadv/conversation")
public interface EsServiceConvMessageInter {

    @ApiOperation("创建会话")
    @RequestMapping("/create-convos")
    ResponseMessage<EsConversationDTO> createConversation(@RequestBody @Validated EsConversationCommand command);

    @ApiOperation("查询会话")
    @RequestMapping("/query-convos")
    ResponseMessage<EsConversationDTO> queryConversationCondition(@RequestBody @Validated EsConversationQuery query);

    @ApiOperation("分页查询会话")
    @RequestMapping("/page-query-convos")
    ResponseMessage<List<EsConversationDTO>> pageQueryConversation(@RequestBody @Validated PagingInfo<EsConversationPageQuery> pageQuery);

    @ApiOperation("保存会话消息")
    @RequestMapping("/save-message")
    ResponseMessage<EsServiceConvMessageDTO> saveMessage(@RequestBody @Validated EsServiceConvMessageCommand command);

    @ApiOperation("会话消息反馈")
    @RequestMapping("/message-feedback")
    ResponseMessage<?> feedbackMessage(@RequestBody @Validated EsMessageFeedbackCommand command);

    @ApiOperation("分页查询会话消息")
    @RequestMapping("/query-message")
    ResponseMessage<List<EsServiceConvMessageDTO>> pageMessageCondition(@RequestBody @Validated PagingInfo<EsServiceConvMessagePageQuery> pageQuery);
}
