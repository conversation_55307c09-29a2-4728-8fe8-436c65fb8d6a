package com.wanlianyida.baseaiadv.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description 数智客服对话消息，es索引：fss_service_conv_message
 * @Date 2025年04月14日 16:46
 */
@Data
public class EsServiceConvMessagePageQuery {

    @ApiModelProperty("会话id")
    @NotNull(message = "会话id不能为空")
    private Long convId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("开始时间，不传默认查近30天数据")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endtDate;

    @ApiModelProperty("客户端类型[物流：111-司机APP,112-司机小程序,121-货主APP,122-3pl,123-货主小程序,131-4pl;商贸：211-用户端APP,212-用户端小程序,213-用户端web,221-门户web]")
    private Integer clientType;

    @ApiModelProperty("排序类型，1：正序，0：倒序")
    private Integer sortType = 0;
}
