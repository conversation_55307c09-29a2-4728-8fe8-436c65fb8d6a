package com.wanlianyida.baseaiadv.api.model.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 产业顾问消息，es索引：industry-assistant-conversation-message
 * @Date 2025年04月14日 16:46
 */
@Data
public class AssistantConvMessageDetailQuery {

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户账号")
    private String userAccount;

    @ApiModelProperty("搜索内容")
    private String searchContent;

    @ApiModelProperty("会话时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty("会话时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty("模型类型，10：产联大模型，20：DeepSeek")
    private Integer modelType;

    @ApiModelProperty("是否深度思考，1是0否")
    private Integer deepThinkFlag;

    @ApiModelProperty("是否联网搜索，1是0否")
    private Integer networkFlag;

    @ApiModelProperty("内容类型（10：从行业分析，20：行业资讯，30：行业调研）")
    private Integer contentType;

    @ApiModelProperty("反馈类型，10内容不准确，20与问题不相关，30逻辑不通顺")
    private String feedbackType;

    @ApiModelProperty("排序类型，1：正序，0：倒序")
    private Integer sortType = 0;
}
