package com.wanlianyida.basemdm.api.model.query;

import lombok.Data;

/**
 * <p>
 * 企业受益人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
public class MdmCompanyBeneficiaryQuery {

    /**
     * id
     */
    private Long id;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司社会信用代码
     */
    private String socialCreditCode;

    /**
     * 受益人姓名
     */
    private String beneficiaryName;

    /**
     * 受益人证件类型
     * 10-居民身份证号 20-临时身份证号 30-外交护照 40-户口簿 50-中国人名解放军军官证 60-机动车驾驶证 70-暂住证 80-机动车行驶证
     */
    private String beneficiaryCertType;

    /**
     * 受益人证件号码
     */
    private String beneficiaryCertNumber;

    /**
     * 长期有效标志[0-否,1-是]
     */
    private Integer longTermValidFlag;

    /**
     * 法人标志[0-否,1-是]
     */
    private Integer legalPersonFlag;
}
