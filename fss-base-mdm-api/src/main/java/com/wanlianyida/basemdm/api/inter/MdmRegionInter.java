package com.wanlianyida.basemdm.api.inter;

import com.wanlianyida.basemdm.api.model.command.MdmRegionCommand;
import com.wanlianyida.basemdm.api.model.dto.*;
import com.wanlianyida.basemdm.api.model.query.MdmCmdRegionQuery;
import com.wanlianyida.basemdm.interfaces.model.dto.MdmRegionDetailDTO;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行政区划表 Inter
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Api(value = "行政区划表feign api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.mdm:}", name = "fss-base-mdm", contextId = "mdmCmdRegionInter", path = "/fss-base-mdm")
public interface MdmRegionInter {
    /**
     * 根据父节点获取城市数据
     *
     * @param parentcode 查询参数
     * @return {@link ResponseMessage}<{@link MdmCompanyAccountDTO}>
     */
    @RequestMapping(value = "/mdm-region/list/by-parent-code", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<MdmCmdRegionDTO>> listByParentCode(@RequestParam("parentCode") String parentCode);

    /**
     * 根据节点获取城市数据
     *
     * @param filter 查询参数
     * @return {@link ResponseMessage}<{@link MdmCompanyAccountDTO}>
     */
    @RequestMapping(value = "/mdm-region/list/by-codes", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<MdmCmdRegionDTO>> listBycodes(@RequestBody MdmCmdRegionQuery filter);

    /**
     * 获取所有行政区划数据
     */
    @RequestMapping(value = "/mdm-region/list/query-all", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<MdmCmdRegionDTO>> listQueryall();

    /**
     * 新增行政区划
     *
     * @param command
     * @return
     */
    @RequestMapping(value = "/mdm-region/add", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage<String> add(@RequestBody MdmRegionCommand command);

    /**
     * 新增行政区划
     *
     * @param command
     * @return
     */
    @RequestMapping(value = "/mdm-region/update", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage<String> update(@RequestBody MdmRegionCommand command);

    /**
     * 根据区域编码获取省市区县详情
     *
     * @param code 区域编码
     * @return {@link ResponseMessage}<{@link List }<{@link MdmRegionDetailDTO }>>
     */
    @RequestMapping(value = "/mdm-region/detail/by-code", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResponseMessage<List<MdmRegionDetailDTO>> getRegionDetailByCode(@RequestParam("code") String code);
}
