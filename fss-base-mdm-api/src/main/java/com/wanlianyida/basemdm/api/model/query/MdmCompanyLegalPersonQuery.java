package com.wanlianyida.basemdm.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MdmCompanyLegalPersonQuery {
    private String id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("用户手机号")
    private String userMobile;

    @ApiModelProperty("用户邮箱")
    private String userEmail;

    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("性别[1-男,2-女]")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("证件长期有效标志[11-是,21-否]")
    private Integer licenseLongValidFlag;

    @ApiModelProperty("申请来源[1-大宗平台,2-物流平台']")
    private String applySource;

}
