package com.wanlianyida.basemdm.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年03月26日 15:18
 */
@Data
@ApiModel("用户身份证信息")
public class MdmUserIdentityCardCommand {

    @NotNull(message = "用户id不能为空")
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("姓名")
    @NotBlank(message = "姓名不能为空")
    private String userName;

    @ApiModelProperty("身份证号")
    @NotBlank(message = "身份证号不能为空")
    private String idCardNo;

    @ApiModelProperty("身份证国徽面地址")
    private String frontUrl;

    @ApiModelProperty("身份证人像面地址")
    private String behindUrl;

    @ApiModelProperty("出生年月")
    private Date birthday;

    @ApiModelProperty("性别，1男2女")
    private Integer gender;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("县编码")
    private String countyCode;

    @ApiModelProperty("县名称")
    private String countyName;

    @ApiModelProperty("详细地址")
    private String addrDetail;

    @ApiModelProperty("发证机关")
    private String issuingAgency;

    @ApiModelProperty("开始时间")
    private Date startDate;

    @ApiModelProperty("截止日期")
    private Date untilDate;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;

    private Integer versionCode;
}
