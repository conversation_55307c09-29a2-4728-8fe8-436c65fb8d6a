package com.wanlianyida.basemdm.api.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 操作员信息表 查询query
 *
 * <AUTHOR>
 * @date 2025-05-23 10:03:16
 */
@Data
@Accessors(chain = true)
public class CmdOperatorContactQuery {

	/**
	 * id
	 */
	private String id;

	/**
	 * 用户姓名
	 */
	private String operatorName;

	/**
	 * 用户姓名
	 */
	private String loginName;

	/**
	 * 角色名称
	 */
	private String roleName;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 排序字段
	 */
	String orderBy;

	/**
	 * 用户ids
	 */
	private List<Long> operatorIdList;

}
