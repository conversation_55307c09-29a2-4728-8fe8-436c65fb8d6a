package com.wanlianyida.basemdm.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月08日 20:44
 */
@Data
public class PlatformUserInfoListDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("登录用户名")
    private String loginName;

    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("微信号")
    private String wechat;

    @ApiModelProperty("实名认证状态，1已认证，0未认证")
    private Integer realStatus;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("身份证号")
    private String idCardNo;

    @ApiModelProperty("有效期截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date untilDate;

    @ApiModelProperty("是否长期有效，1是0否")
    private Integer validFlag;

    @ApiModelProperty("绑定企业数量")
    private Integer bindCount;

    @ApiModelProperty("是否过期，1已过期，0未过期")
    private String expireFlag;

    @ApiModelProperty("是否启用:1是,0否")
    private String enableFlag;

    /**
     * 停用原因
     */
    private String disableReason;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
}
