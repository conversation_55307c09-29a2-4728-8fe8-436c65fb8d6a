package com.wanlianyida.basemdm.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户联系方式表 entity
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@Accessors(chain = true)
public class CmdOperatorUpdateCommand implements Serializable {

	/**
	 * ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 操作员id
	 */
	private Long operatorId;


	/**
	 * 操作员姓名
	 */
	private String operatorName;

	/**
	 * 登录名
	 */
	private String loginName;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 启用标识[1-启用,0-停用]
	 */
	private String enableFlag;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;


}
