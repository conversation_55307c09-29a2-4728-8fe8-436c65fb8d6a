package com.wanlianyida.basemdm.api.model.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 公司信息条件查询
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
public class UmOrgConditionQuery {
    /**
     * id
     */
    private Long id;

    /**
     * 信用代码
     */
    private String licenseNo;

    /**
     * 企业名称
     */
    private String licenseName;

    /**
     * 父级信用代码
     */
    private String parentLicenseNo;

    /**
     * 平台类型 10-用户端 20-平台端
     */
    @NotNull(message = "平台类型不能为空")
    private String platformType;
}
