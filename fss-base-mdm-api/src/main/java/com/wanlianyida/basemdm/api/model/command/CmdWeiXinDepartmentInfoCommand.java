package com.wanlianyida.basemdm.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业组织结构
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
@Data
public class CmdWeiXinDepartmentInfoCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * (企业微信)部门id
     */
    private String deptId;

    /**
     * (企业微信)部门名称
     */
    private String deptName;

    /**
     * (企业微信)父部门id。根部门为1
     */
    private String parentDeptId;

    /**
     * (企业微信)在父部门中的次序值
     */
    private Integer deptSeqNo;


    /**
     * 部门企业统一社会信用代码
     */
    private String deptSocialCreditCode;

    /**
     * 部门企业地址（省Code）
     */
    private String deptProvincePode;

    /**
     * 部门企业地址（省中文）
     */
    private String deptProvinceName;

    /**
     * 部门企业地址（市Code）
     */
    private String deptCityCode;

    /**
     * 部门企业地址（市中文）
     */
    private String deptCityName;

    /**
     * 部门企业地址（区Code）
     */
    private String deptAreaCode;

    /**
     * 部门企业地址（区中文）
     */
    private String deptAreaName;

    /**
     * 企业标识[1-是,0-否]
     */
    private String companyFlag;

    /**
     * 状态
     */
    private String validFlag;


    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;
}
