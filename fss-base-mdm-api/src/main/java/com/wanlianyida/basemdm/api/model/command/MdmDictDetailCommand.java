package com.wanlianyida.basemdm.api.model.command;

import lombok.Data;

/**
 * 字典值(MdmDictDetail)命令对象
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
public class MdmDictDetailCommand {
    // 主键id
    private Long id;

    // 平台编码
    private String platCode;

    // 字典编码
    private String dictCode;

    // 字典值
    private String dictValue;

    // 字典值名称
    private String valueName;

    // 排序号
    private Integer sortNo;

    // 字典值描述
    private String detailDesc;

    // 删除标志[0-否,1-是]

    private Integer delFlag;

    // 禁用标志[0-否,1-是]
    private Integer disableFlag;

    // 创建人id
    private String creatorId;

    // 最后更新人id
    private String lastUpdaterId;
} 
