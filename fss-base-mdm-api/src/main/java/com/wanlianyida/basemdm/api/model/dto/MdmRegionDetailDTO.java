package com.wanlianyida.basemdm.interfaces.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 省市区县详情 DTO
 */
@Data
public class MdmRegionDetailDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 区域编码
	 */
	private String code;

	/**
	 * 区域名称
	 */
	private String name;

	/**
	 * 层级
	 */
	private Integer level;

	/**
	 * 省市区县完整路径
	 */
	private List<MdmRegionDetailDTO> regionPath;
}