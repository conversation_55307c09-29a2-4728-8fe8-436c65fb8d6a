package com.wanlianyida.basemdm.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息表 entity
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
public class MdmUserInfoCommand implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;

	/**
	 * 登录用户名
	 */
	private String loginName;

	/**
	 * 姓名
	 */
	private String userName;

	/**
	 * 用户微信昵称
	 */
	private String nickname;

	/**
	 * 用户编码
	 */
	private String userCode;

	/**
	 * 身份证号
	 */
	private String idCardNo;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 是否启用:1是,0否
	 */
	private String enableFlag;

	/**
	 * 停用原因
	 */
	private String disableReason;

	/**
	 * 创建人id
	 */
	private String creatorId;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createdDate;

	/**
	 * 最后更新人id
	 */
	private String updaterId;

	/**
	 * 最后更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updatedDate;

	/**
	 * 版本号
	 */
	private Integer versionCode;

	/**
	 * 逻辑删除:1是,0否
	 */
	private String delFlag;


}
