package com.wanlianyida.basemdm.api.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 10:13
 */
@Data
@ApiModel("公司法定代表人表")
public class MdmCompanyLegalPersonInfoQuery {

    private Long id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业id")
    private List<String> companyIdList;

    @ApiModelProperty("企业社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("企业社会信用代码")
    private List<String> socialCreditCodeList;
}
