package com.wanlianyida.basemdm.api.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 企业组织结构
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
@Data
public class MdmCmdCompanyNodeDTO {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 上级统一信用代码
     */
    private String parentLicenseNo;

    /**
     * 资质名称即:营业执照公司企业名称
     */
    private String licenseName;

    /**
     * 企业简称
     */
    private String licenseShortName;

    /**
     * 统一信用代码
     */
    private String licenseNo;
    /**
     * 用于递归查询表示用户是否属于某个企业
     * 区分直接企业和间接企业
     */
    private boolean isCompany ;

    /**
     * 下级企业信息
     */
    private List<MdmCmdCompanyNodeDTO> children;

}
