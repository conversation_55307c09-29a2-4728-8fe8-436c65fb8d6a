package com.wanlianyida.basemdm.api.model.command;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 企业授权委托书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
@Data
public class MdmAccreditOperatorUpdateCommand {

    @NotNull(message = "授权委托书id不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("操作员id")
    private String operatorId;

    @ApiModelProperty("操作员编码")
    private String operatorCode;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("文件地址")
    private String fileUrl;

    @ApiModelProperty("授权状态[10-有效,20-失效]")
    private String authStatus;

    private String updaterId;

    private String updaterName;

    private Date updatedDate;
}
