package com.wanlianyida.basemdm.api.model.command;

import lombok.Data;

import java.io.Serializable;

/**
 * 危化品目录Command
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class MdmHazChemCatalogCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long id;                    // 主键
    private String hazchemName;         // 危化品名称
    private Integer disableFlag;        // 禁用标志[1-禁用,0-启用]
    private String remark;              // 备注
    private String creatorId;           // 创建人id
    private String lastUpdaterId;       // 最后更新人id
} 