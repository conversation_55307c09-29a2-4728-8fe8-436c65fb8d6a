package com.wanlianyida.basemdm.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否可用枚举
 *
 * <AUTHOR>
 * @date 2025-07-04 11:38:58
 */
@Getter
@AllArgsConstructor
public enum EnableFlagEnum {

    ENABLE("1", "启用"),
    DEACTIVATED("0", "停用");

    /*
     * 是否可用状态
     */
    private String code;

    /*
     * 是否可用状态描述
     */
    private String desc;

    public static String getDesc(String code) {
        for (EnableFlagEnum value : EnableFlagEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
