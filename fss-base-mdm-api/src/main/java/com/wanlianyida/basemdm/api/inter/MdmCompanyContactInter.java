package com.wanlianyida.basemdm.api.inter;

import com.wanlianyida.basemdm.api.model.command.MdmCompanyContactCommand;
import com.wanlianyida.basemdm.api.model.dto.MdmCompanyContactDTO;
import com.wanlianyida.basemdm.api.model.query.MdmCompanyContactPageQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@RefreshScope
@FeignClient(url = "${gtsp.api.url.mdm:}", value = "fss-base-mdm", contextId = "MdmCompanyContactInter", path = "/fss-base-mdm")
public interface MdmCompanyContactInter {
    /**
     * 新增联系人
     * @param command
     * @return {@link ResponseMessage }<{@link Void }>
     */
    @PostMapping("/mdm/company/contact/add")
    ResponseMessage<Void> add(@RequestBody MdmCompanyContactCommand command) ;

    /**
     * 更新联系人
     * @param command
     * @return {@link ResponseMessage }<{@link Void }>
     */
    @PostMapping("/mdm/company/contact/update")
    ResponseMessage<Void> update(@RequestBody MdmCompanyContactCommand command);

    /**
     * 批量删除联系人
     * @param idList
     * @return {@link ResponseMessage }<{@link Void }>
     */
    @PostMapping("/mdm/company/contact/batchDelete")
    ResponseMessage<Void> batchDelete(@RequestBody List<String> idList);

    /**
     * 设为默认联系人
     * @param command
     * @return {@link ResponseMessage }<{@link Void }>
     */
    @PostMapping("/mdm/company/contact/setDefault")
    ResponseMessage<Void> setDefault(@RequestBody MdmCompanyContactCommand command) ;

    /**
     * 查询联系人详情
     * @param id
     * @return {@link ResponseMessage }<{@link Object }>
     */
    @GetMapping("/mdm/company/contact/queryDetail/{id}")
    ResponseMessage<MdmCompanyContactDTO> queryDetail(@PathVariable String id);

    /**
     * 分页查询
     *
     * @param pagingInfo 分页信息
     * @return {@link ResponseMessage }
     */
    @PostMapping("/mdm/company/contact/queryPage")
    ResponseMessage<List<MdmCompanyContactDTO>> queryPage(@RequestBody PagingInfo<MdmCompanyContactPageQuery> pagingInfo);
}
