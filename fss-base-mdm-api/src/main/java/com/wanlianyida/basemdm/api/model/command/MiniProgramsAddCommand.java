package com.wanlianyida.basemdm.api.model.command;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 绑定小程序 command
 *
 * <AUTHOR>
 * @date 2025-06-16 10:27:50
 */
@Data
@Accessors(chain = true)
public class MiniProgramsAddCommand {

    /**
     * 用户userId
     */
    @NotNull(message = "userId不可为空")
    private Long userId;

    /**
     * 小程序appId
     */
    @NotBlank(message = "appId不可为空")
    private String appId;

    /**
     * 小程序openId
     */
    @NotBlank(message = "openId不可为空")
    private String openId;

    /**
     * 微信开放平台
     */
    private String unionId;

    /**
     * 创建人userId
     */
    private String creatorId;

    /**
     * 最后更新人userId
     */
    private String updaterId;
}
