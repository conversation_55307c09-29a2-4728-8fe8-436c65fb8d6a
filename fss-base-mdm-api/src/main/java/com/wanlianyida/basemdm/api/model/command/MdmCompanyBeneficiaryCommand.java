package com.wanlianyida.basemdm.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 企业受益人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-26
 */
@Data
public class MdmCompanyBeneficiaryCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */

    private String id;

    /**
     * 公司id
     */
    @NotNull
    private String companyId;

    /**
     * 公司名称
     */
    @NotNull
    private String companyName;

    /**
     * 公司社会信用代码
     */
    @NotNull
    private String socialCreditCode;

    /**
     * 受益人姓名
     */
    @NotNull
    private String beneficiaryName;

    /**
     * 受益人证件类型(详情见字典：beneficiary_cert_type)
     * 1-居民身份证号 2-护照 3-香港往来内地通行证 4-澳门来往内地通行证 5-台湾来往内地通行证
     */
    @NotNull
    private String beneficiaryCertType;

    /**
     * 受益人证件号码
     */
    @NotNull
    private String beneficiaryCertNumber;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 截止日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date untilDate;

    /**
     * 长期有效标志[0-否,1-是]
     */
    @NotNull
    private Integer longTermValidFlag;

    /**
     * 创建用户id
     */
    private String creatorId;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 法人标志[0-否,1-是]
     */
    private Integer legalPersonFlag;

    /**
     * 身份证国徽面地址
     */
    private String frontUrl;

    /**
     * 身份证人像面地址
     */
    private String behindUrl;
}
