package com.wanlianyida.basemdm.api.model.command;

import lombok.Data;

import java.io.Serializable;

/**
 * 企业管理员账号表 Command
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Data
public class MdmRegionCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;           // id
    private String code;          // 区域编码
    private String parentCode;    // 父节点区域编码
    private String name;            // 名字
    private Integer level;          // 层级（固定10层）
    private Integer sortNo;           // 排序
    private Integer delFlag;           // 是否删除  1 删除 0 正常
    private String creatorId;
    private String updaterId;
}
