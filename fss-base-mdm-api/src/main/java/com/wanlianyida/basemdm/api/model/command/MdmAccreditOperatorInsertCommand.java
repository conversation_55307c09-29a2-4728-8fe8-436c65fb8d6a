package com.wanlianyida.basemdm.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 企业授权委托书表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-30
 */
@Data
public class MdmAccreditOperatorInsertCommand {

    @NotBlank(message = "企业id不能为空")
    @ApiModelProperty("企业id")
    private String companyId;

    @NotBlank(message = "企业编码不能为空")
    @ApiModelProperty("企业编码")
    private String companyCode;

    @NotBlank(message = "企业名称不能为空")
    @ApiModelProperty("企业名称")
    private String companyName;

    @NotBlank(message = "操作员id不能为空")
    @ApiModelProperty("操作员id")
    private String operatorId;

    @ApiModelProperty("操作员编码")
    @NotBlank(message = "操作员编码不能为空")
    private String operatorCode;

    @ApiModelProperty("操作员姓名")
    @NotBlank(message = "操作员姓名不能为空")
    private String operatorName;

    @NotBlank(message = "授权日期不能为空")
    @ApiModelProperty("授权日期")
    private Date authDate;

    @NotBlank(message = "来源类型不能为空")
    @ApiModelProperty("来源类型[10-创建，20-申请，30-过户]")
    private String sourceType;

    @NotBlank(message = "文件地址不能为空")
    @ApiModelProperty("文件地址")
    private String fileUrl;

    @NotBlank(message = "授权状态不能为空")
    @ApiModelProperty("授权状态[10-有效,20-失效]")
    private String authStatus;

    @NotBlank(message = "平台编码不能为空")
    @ApiModelProperty("平台编码[10-大宗,20-物流]")
    private String platformCode;

    private String creatorId;

    private String creatorName;

    private Date createdDate;

    private String updaterId;

    private String updaterName;

    private Date updatedDate;
}
