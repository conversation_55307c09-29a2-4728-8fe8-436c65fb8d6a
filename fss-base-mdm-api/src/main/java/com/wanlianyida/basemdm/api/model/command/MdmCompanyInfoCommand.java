package com.wanlianyida.basemdm.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业组织结构
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
@Data
public class MdmCompanyInfoCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 平台类型:10用户端,20平台端
     */

    private String platformType;

    /**
     * 上级统一信用代码
     */
    private String parentLicenseNo;

    /**
     * 证照(资质)类型:1000企营,1100个体,2100道运,1900其他
     */
    private String certificateType;

    /**
     * 预留字段,证件类型,属于二级分类
     */
    @Deprecated
    private Integer licenseType;

    /**
     * 资质名称即:营业执照公司企业名称
     */
    private String licenseName;

    /**
     * 企业简称
     */
    private String licenseShortName;

    /**
     * 统一信用代码
     */
    private String licenseNo;

    /**
     * 企业法人
     */
    private String legalPerson;

    /**
     * 企业法人身份证号
     */
    private String legalPersonIdCard;

    /**
     * 发证机关单位
     */
    private String licenseDepartmentGov;

    /**
     * 初次发证日期(签发证日期)
     */
    @Deprecated
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseFirstTime;

    /**
     * 证照有效期起日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseStartDate;

    /**
     * 证照有效期止日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseEndDate;

    /**
     * 有效期总数
     */
    private Integer licenseValidDate;

    /**
     * 资质(营业执照)是否为长期有效:11是,21否
     */
    private String licenseValidIsLong;

    /**
     * 资质(营业执照)正面照片地址
     */
    private String frontFileUrl;

    /**
     * 资质(营业执照)反面照片地址
     */
    @Deprecated
    private String backFileUrl;

    /**
     * 资质(营业执照)照片地址
     */
    @Deprecated
    private String otherFileUrl;

    /**
     * 把以上三个附件地址字段以json的格式存储下来
     */
    @Deprecated
    private String fileAttId;

    /**
     * 经营范围
     */
    private String manageScope;

    /**
     * 经营性质
     */
    @Deprecated
    private String businessNature;

    /**
     * 企业类型(这个是营业执照里面的文本)
     */
    private String companyType;

    /**
     * 企业地址（省Code）
     */
    private String province;

    /**
     * 企业地址（省中文）
     */
    private String provinceName;

    /**
     * 企业地址（市Code）
     */
    private String city;

    /**
     * 企业地址（市中文）
     */
    private String cityName;

    /**
     * 企业地址（区/县Code）
     */
    private String area;

    /**
     * 企业地址（区/县中文）
     */
    private String areaName;

    /**
     * 企业地址（镇/街道Code）
     */
    private String street;

    /**
     * 企业地址（镇/街道中文）
     */
    private String streetName;

    /**
     * 企业地址（详细地址）
     */
    private String addressDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系人
     */
    @Deprecated
    private String contacts;

    /**
     * 电话
     */
    @Deprecated
    private String phone;

    /**
     * 主管税务机关
     */
    private String taxAuthority;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     * 申请来源:1大宗平台,2物流平台
     */
    private String applySource;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;

    @ApiModelProperty("法定代表人信息")
    private MdmCompanyLegalPersonInfoCommand legalPersonInfo;
}
