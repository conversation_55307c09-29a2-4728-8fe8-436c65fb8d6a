package com.wanlianyida.basemdm.api.model.command;

import lombok.Data;

import java.io.Serializable;

/**
 * 平台参数Command
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class MdmPlatformParameterCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 参数id
     */
    private Long id;
    
    /**
     * 平台编码
     */
    private String plfCode;
    
    /**
     * 参数编码
     */
    private String paraCode;
    
    /**
     * 参数值
     */
    private String paraValue;
    
    /**
     * 参数名称
     */
    private String paraName;
    
    /**
     * 参数说明
     */
    private String paraDesc;
    
    /**
     * 参数分组编码
     */
    private String groupCode;
    
    /**
     * 参数分组名称
     */
    private String groupName;
    private Integer delFlag;
    /**
     * 创建人id
     */
    private String creatorId;
    
    /**
     * 最后更新人id
     */
    private String lastUpdaterId;
} 