package com.wanlianyida.basemdm.api.inter;

import com.wanlianyida.basemdm.api.model.command.MdmDictDetailCommand;
import com.wanlianyida.basemdm.api.model.command.MdmDictionaryCommand;
import com.wanlianyida.basemdm.api.model.dto.MdmDictDetailDTO;
import com.wanlianyida.basemdm.api.model.dto.MdmDictionaryDTO;
import com.wanlianyida.basemdm.api.model.query.MdmDictDetailBatchQuery;
import com.wanlianyida.basemdm.api.model.query.MdmDictDetailQuery;
import com.wanlianyida.basemdm.api.model.query.MdmDictionaryQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * 数据字典表 Inter
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Api(value = "数据字典表feign api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.mdm:}", name = "fss-base-mdm", contextId = "MdmDictInter", path = "/fss-base-mdm")
public interface MdmDictInter {
    /**
     * ----------------------------------------------------主表-----------------------------------------
     */
    @RequestMapping(value = "/mdm/dict/" , produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage<List<MdmDictionaryDTO>> list(@RequestBody MdmDictionaryQuery query);

    @RequestMapping(value ="/mdm/dict/add", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage add(@RequestBody  MdmDictionaryCommand command) ;
    @RequestMapping(value ="/mdm/dict/update", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody  MdmDictionaryCommand command) ;

    @RequestMapping(value ="/mdm/dict/delete", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage delete(@RequestBody  MdmDictionaryCommand command) ;
    /**
     * ----------------------------------------------------detail表-----------------------------------------
     */
    @RequestMapping(value ="/mdm/dict/detail/page", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage<List<MdmDictDetailDTO>> detailList(@RequestBody PagingInfo<MdmDictDetailQuery> pagingInfo) ;

    @RequestMapping(value ="/mdm/dict/detail/add", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage detailAdd(@RequestBody  MdmDictDetailCommand command) ;

    @RequestMapping(value ="/mdm/dict/detail/update", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage detailUpdate(@RequestBody  MdmDictDetailCommand command);

    @RequestMapping(value ="/mdm/dict/detail/delete", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage detailDelete(@RequestBody  MdmDictDetailCommand command) ;

    @RequestMapping(value ="/mdm/dict/detail/code-list", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResponseMessage<Map<String, List<MdmDictDetailDTO>>> detailQueryValidList(@RequestBody MdmDictDetailBatchQuery query);

}
