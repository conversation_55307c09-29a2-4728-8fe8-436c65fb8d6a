package com.wanlianyida.basemdm.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 企业组织结构
 *
 * <AUTHOR>
 * @date 2025/01/02
 */
@Data
public class CmdWeiXinEmployeeInfoDTO {

    private Long id;

    /**
     * 企微userId
     */
    private String empId;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 员工手机号
     */
    private String empMobile;

    /**
     * 员工编号
     */
    private String empNo;

    /**
     * 员工主要部门id
     */
    private String empMainDepartment;

    /**
     * 员工职位
     */
    private String empPosition;

    /**
     * 员工状态[10-启用,20-禁用,30-离职]
     */
    private String empStatus;

    /**
     * 状态
     */
    private String validFlag;


    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新人id
     */
    private String updaterId;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 逻辑删除:1是,0否
     */
    private String delFlag;
}
