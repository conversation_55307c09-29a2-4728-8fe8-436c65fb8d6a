package com.wanlianyida.basemdm.api.inter;

import com.wanlianyida.basemdm.api.model.command.MdmCompanyBeneficiaryCommand;
import com.wanlianyida.basemdm.api.model.dto.MdmCompanyBeneficiaryDTO;
import com.wanlianyida.basemdm.api.model.query.MdmCompanyBeneficiaryQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import io.swagger.annotations.Api;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(value = "企业受益人 api")
@RefreshScope
@FeignClient(url = "${gtsp.api.url.mdm:}", value = "fss-base-mdm", contextId = "MdmCompanyBeneficiaryInter", path = "/fss-base-mdm")
//@FeignClient(url = "localhost:9805", value = "fss-base-mdm", contextId = "MdmCompanyBeneficiaryInter", path = "/fss-base-mdm")
public interface MdmCompanyBeneficiaryInter {
    /**
     * 新增企业受益人
     * @param command
     * @return
     */
    @PostMapping("/company/beneficiary/add")
    ResponseMessage<String> add(@RequestBody MdmCompanyBeneficiaryCommand command);

    /**
     * 修改企业受益人
     * @param command
     * @return
     */
    @PostMapping("/company/beneficiary/update")
    ResponseMessage<String> update(@RequestBody MdmCompanyBeneficiaryCommand command);

    /**
     * 删除企业受益人
     * @param command
     * @return
     */
    @PostMapping("/company/beneficiary/delete")
    ResponseMessage<Void> delete(@RequestBody MdmCompanyBeneficiaryCommand command);

    /**
     * 查询详情
     * @param id
     * @return
     */
    @PostMapping("/company/beneficiary/queryDetail/{id}")
    ResponseMessage<MdmCompanyBeneficiaryDTO> queryDetail(@PathVariable String id);

    /**
     * 条件查询
     * @param query
     * @return
     */
    @PostMapping("/company/beneficiary/queryByCondition")
    ResponseMessage<List<MdmCompanyBeneficiaryDTO>> queryByCondition(@RequestBody MdmCompanyBeneficiaryQuery query);
}
