package com.wanlianyida.basemdm.api.inter;

import com.wanlianyida.basemdm.api.model.command.CmdOperatorAddCommand;
import com.wanlianyida.basemdm.api.model.command.CmdOperatorUpdateCommand;
import com.wanlianyida.basemdm.api.model.dto.CmdOperatorContactDTO;
import com.wanlianyida.basemdm.api.model.query.CmdOperatorContactQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@RefreshScope
@FeignClient(url = "${gtsp.api.url.mdm:}", value = "fss-base-mdm", contextId = "MdmOperatorContactInter", path = "/fss-base-mdm")
public interface MdmOperatorContactInter {

    /**
     * 分页查询
     * @param pagingInfo 分页查询参数
     * @return {@link ResponseMessage}<{@link CmdOperatorContactDTO}>
     */
    @PostMapping("/mdm/cmdOperatorContact/queryPage")
    ResponseMessage<List<CmdOperatorContactDTO>> queryPage(@RequestBody PagingInfo<CmdOperatorContactQuery> pagingInfo);
    /**
     * 列表查询
     * @param query 查询参数
     * @return {@link ResponseMessage}<{@link CmdOperatorContactDTO}>
     */
    @PostMapping("/mdm/cmdOperatorContact/queryList")
    ResponseMessage<List<CmdOperatorContactDTO>> queryList(@RequestBody CmdOperatorContactQuery query);

    /**
     * 新增
     * @param command
     * @return Long
     */
    @PostMapping("/mdm/cmdOperatorContact/insert")
    ResponseMessage<?> insert(@RequestBody CmdOperatorAddCommand command);

    /**
     * 修改
     * @param command
     * @return Long
     */
    @PostMapping("/mdm/cmdOperatorContact/update")
    ResponseMessage<?> update(@RequestBody CmdOperatorUpdateCommand command);

    /**
     * 根据操作员id修改联系方式
     * @param command
     * @return Long
     */
    @PostMapping("/mdm/cmdOperatorContact/update/by-operatorId")
    ResponseMessage<?> updateByOperatorId(@RequestBody CmdOperatorUpdateCommand command);

    /**
     * 逻辑删除
     * @param id
     * @return Long
     */
    @PostMapping("/mdm/cmdOperatorContact/delete/{id}")
    ResponseMessage<?> delete(@PathVariable("id") Long id);

    /**
     * 查询操作员详情
     * @param id
     * @return {@link ResponseMessage }<{@link Object }>
     */
    @PostMapping("/mdm/cmdOperatorContact/getById/{id}")
    ResponseMessage<CmdOperatorContactDTO> queryDetail(@PathVariable("id") Long id);

    /**
     * 账号停用
     * @param operatorId
     * @return
     */
    @PostMapping("/mdm/cmdOperatorContact/accountSuspension/{operatorId}")
    ResponseMessage<Void> accountSuspension(@PathVariable("operatorId") String operatorId);

    /**
     * 账号启用
     * @param operatorId
     * @return
     */
    @PostMapping("/mdm/cmdOperatorContact/accountActivation/{operatorId}")
    ResponseMessage<Void> accountActivation(@PathVariable("operatorId") String operatorId);

    /**
     * 根据operatorId查询操作员联系方式
     * @param operatorId 操作员
     * @return 操作员联系方式
     */
    @PostMapping("/mdm/cmdOperatorContact/queryOperatorContactByOperatorId/{operatorId}")
    ResponseMessage<CmdOperatorContactDTO> queryOperatorContactByOperatorId(@PathVariable("operatorId") String operatorId);


    /**
     * 根据operatorId删除联系方式
     * @param operatorId 操作员Id
     * @return 无
     */
    @PostMapping("/mdm/cmdOperatorContact/delete/by-operatorId/{operatorId}")
    ResponseMessage<Void> deleteByOperatorId(@PathVariable("operatorId") String operatorId);

}
