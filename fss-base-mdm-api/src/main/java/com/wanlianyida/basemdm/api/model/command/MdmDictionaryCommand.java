package com.wanlianyida.basemdm.api.model.command;

import lombok.Data;

/**
 * 字典表(MdmDictionary)命令对象
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Data
public class MdmDictionaryCommand {
    // 主键id
    private Long id;

    // 平台编码
    private String platCode;

    // 字典编码
    private String dictCode;

    // 字典名称
    private String dictName;

    // 字典描述
    private String dictDesc;

    // 创建人id
    private String creatorId;

    // 最后更新人id
    private String lastUpdaterId;
} 
