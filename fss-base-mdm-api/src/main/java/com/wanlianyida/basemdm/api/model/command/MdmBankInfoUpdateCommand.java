package com.wanlianyida.basemdm.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年04月28日 19:20
 */
@Data
@ApiModel("银行信息表")
public class MdmBankInfoUpdateCommand {

    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("银行编码")
    @NotBlank(message = "银行编码不能为空")
    private String bankCode;

    @ApiModelProperty("银行名称")
    @NotBlank(message = "银行名称不能为空")
    private String bankName;

    @ApiModelProperty("启用标志[1-是,0-否]")
    private String enableFlag;
}
