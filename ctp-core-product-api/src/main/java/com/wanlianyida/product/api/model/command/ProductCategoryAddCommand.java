package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增品类参数
 * <AUTHOR>
 */
@Data
public class ProductCategoryAddCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 品类名称
     */
    @NotBlank(message = "品类名称不能为空")
    private String categoryName;

    /**
     * 品类图片
     */
    @NotBlank(message = "品类图片不能为空")
    private String categoryPic;

    /**
     * 品类描述
     */
    private String categoryDesc;

    /**
     * 排序序号
     */
    @NotNull(message = "排序序号不能为空")
    private Integer sortNumber;

}
