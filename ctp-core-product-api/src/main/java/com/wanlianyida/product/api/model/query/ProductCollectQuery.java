package com.wanlianyida.product.api.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月17日 19:01
 */
@Data
public class ProductCollectQuery {

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userBaseId;

    /**
     * 商品id
     */
    @NotBlank(message = "商品id不能为空")
    private String productId;
}
