package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 新增品类参数
 * <AUTHOR>
 */
@Data
public class ProductQualificationUpdateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;


    /**
     * 公司id
     */
    @NotNull(message = "公司id不能为空")
    private String companyId;

    /**
     * 资质名称
     */
    @NotNull(message = "资质名称不能为空")
    private String qualificationName;

    /**
     * 资质类型
     */
    @NotNull(message = "资质类型不能为空")
    private String qualificationType;

    /**
     * 最后更新人id
     */
    private String updaterId;


    /**
     * 附件列表
     */
    @Valid
    private List<PcProductAttachmentCommand> productAttachmentAddCommandList;

}
