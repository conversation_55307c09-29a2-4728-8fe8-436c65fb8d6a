package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月25日 16:08
 */
@Data
public class AuditRejectReasonSortCommand {

    @NotNull(message = "必要参数为空")
    @Size(min = 1, message = "必要参数为空")
    private List<AuditRejectReasonCommand> reasonList;
}
