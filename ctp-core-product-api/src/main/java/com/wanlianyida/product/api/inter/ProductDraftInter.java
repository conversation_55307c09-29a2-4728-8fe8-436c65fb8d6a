package com.wanlianyida.product.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.ProductDraftCommand;
import com.wanlianyida.product.api.model.dto.ProductDraftDetailDTO;
import com.wanlianyida.product.api.model.query.ProductDraftDetailQuery;
import com.wanlianyida.product.api.model.query.ProductDraftPageQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api("商品草稿箱")
@FeignClient(name = "ctp-core-product", contextId = "ProductDraftInter", path = "/ctp-core-product")
public interface ProductDraftInter {
    @ApiOperation("查询草稿箱列表")
    @PostMapping("/product/draft/draftPageList")
    ResultMode draftPageList(@RequestBody PagingInfo<ProductDraftPageQuery> pagingInfo);

    @ApiOperation("查询草稿箱详情")
    @PostMapping("/product/draft/draftDetail")
    ResultMode<ProductDraftDetailDTO> draftDetail(@RequestBody ProductDraftDetailQuery query);

    @ApiOperation("新增或保存草稿")
    @PostMapping("/product/draft/saveOrUpdateDraft")
    ResultMode<String> saveOrUpdateDraft(@RequestBody ProductDraftCommand command);

    @ApiOperation("批量删除草稿箱")
    @PostMapping("/product/draft/batchDeleteDraft")
    ResultMode<Void> batchDeleteDraft(@RequestBody ProductDraftCommand command);
}
