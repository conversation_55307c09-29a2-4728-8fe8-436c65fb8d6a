package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode
@ApiModel(value = "下架原因上移/下移接口",description = "")
public class OutSaleReasonChangeOrderCommand {

    @ApiModelProperty(value = "原因id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "操作类型：10上移 20下移")
    @NotEmpty(message = "operateType不能为空")
    private String operateType;
}
