package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 商品审核记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
public class AuditLogDTO {

    /**
     * 审核ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * spu编码
     */
    private String spuCode;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditDate;

    /**
     * 申请类型（10发布商品 20商品信息变更 30商品价格变更）
     */
    private String applyType;

    /**
     * 申请编号
     */
    private String applyCode;

    /**
     * 审核状态（10 待审核 20审核通过; 30审核驳回 40 审核撤销）
     */
    private Integer auditStatus;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 更新人名字
     */
    private String updaterName;

    /**
     * 品牌ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long relBrandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 1级品类ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long categoryId1;

    /**
     * 1级品类名称
     */
    private String categoryName1;

    /**
     * 2级品类ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long categoryId2;

    /**
     * 2级品类名称
     */
    private String categoryName2;

    /**
     * 3级品类ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long categoryId3;

    /**
     * 3级品类名称
     */
    private String categoryName3;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 是否审核后立即上架 1是 0否
     */
    private Boolean onSaleAfterAudit;

    /**
     * 关联仓库ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long relStockId;

    /**
     * 仓库名称
     */
    private String stockName;

    /**
     * 交货地省编号
     */
    private String deliveryAddrProvinceCode;

    /**
     * 交货地城市编号
     */
    private String deliveryAddrCityCode;

    /**
     * 交货地省名称
     */
    private String deliveryAddrProvinceName;

    /**
     * 交货地城市名称
     */
    private String deliveryAddrCityName;

    /**
     * 计价单位ID
     */
    private Integer relPricingUnitId;

    /**
     * 计量单位ID
     */
    private Integer relMeasurementUnitId;

    /**
     * 品类名多级拼接
     */
    private String categoryFullName;

    @ApiModelProperty("店铺ID")
    private Long shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;

    /**
     * 发布类型
     */
    private String publishType;
}
