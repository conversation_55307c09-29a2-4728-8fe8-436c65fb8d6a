package com.wanlianyida.product.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月23日 17:51
 */
@Data
public class AuditLogQuery {

    @ApiModelProperty("商品spu编码")
    private String spuCode;

    @ApiModelProperty("申请编号")
    private String applyCode;

    @ApiModelProperty("品类模糊查询")
    private String categoryNameLike;

    @ApiModelProperty("供应商")
    private String companyNameLike;

    @ApiModelProperty("审核状态")
    private Integer auditStatus;

    private String companyId;

    /**
     * 申请类型（10发布商品 20商品信息变更 30商品价格变更）
     */
    private String applyType;
}
