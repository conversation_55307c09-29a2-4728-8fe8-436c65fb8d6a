package com.wanlianyida.product.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.BatchImportCommand;
import com.wanlianyida.product.api.model.command.ProductImportTemplateCommand;
import com.wanlianyida.product.api.model.dto.CreateImportTemplateDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "ctp-core-product", contextId = "productImportService", path = "/ctp-core-product")
public interface ProductImportInter {
    /**
     * 商品批量导入
     */
    @PostMapping("/product/import/batchImport")
    ResultMode<Void> batchImport(@RequestBody @Valid BatchImportCommand batchImportCommand) throws Exception ;


    /**
     * 商品导入模板下载
     */
    @PostMapping("/product/import/createImportTemplate")
    ResultMode<CreateImportTemplateDTO> createImportTemplate(@RequestBody @Valid ProductImportTemplateCommand command) throws Exception ;

}
