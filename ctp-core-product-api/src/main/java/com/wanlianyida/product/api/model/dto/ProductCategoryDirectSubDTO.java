package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 父类直接子级
 * <AUTHOR>
 */
@Data
public class ProductCategoryDirectSubDTO {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 图片
     */
    private String categoryPic;

    /**
     * 启用状态(1启用 0停用)
     */
    private Integer enableStatus;

    /**
     * 排序序号
     */
    private Integer sortNumber;

    /**
     * 品类级别：1->1级；2->2级；3->3级
     */
    private Integer level;

    /**
     * 品类描述
     */
    private String categoryDesc;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 关联的规格名称列表
     */
    private String relAttributeName;

    /**
     * 关联的品牌名称列表
     */
    private String relBrandName;
}
