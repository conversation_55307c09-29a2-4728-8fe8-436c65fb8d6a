package com.wanlianyida.product.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.dto.ProductImportTaskDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName ProductImportTaskInter
 * @Description
 * <AUTHOR>
 * @Veriosn 1.0
 **/

@FeignClient(name = "ctp-core-product", contextId = "productImportTaskService", path = "/ctp-core-product")
public interface ProductImportTaskInter {

    @ApiOperation("查询导入任务列表")
    @PostMapping("/product/task/queryPageList")
    ResultMode<List<ProductImportTaskDTO>> queryPageList(@RequestBody PagingInfo info);

}
