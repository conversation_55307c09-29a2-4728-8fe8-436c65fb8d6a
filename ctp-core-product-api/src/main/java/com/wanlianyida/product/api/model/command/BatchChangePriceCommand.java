package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "批量改价参数")
public class BatchChangePriceCommand {

    List<ChangePriceCommand> changeList;

    @Data
    public static class ChangePriceCommand {
        @ApiModelProperty("skuID")
        private Long skuId;

        @ApiModelProperty("本sku的最新价格")
        private BigDecimal priceFee;
    }
}
