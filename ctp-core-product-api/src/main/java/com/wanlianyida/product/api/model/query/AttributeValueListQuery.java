package com.wanlianyida.product.api.model.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 卖家规格值列表query
 * <AUTHOR>
 */
@Data
public class AttributeValueListQuery {

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 3级品类id
     */
    @NotNull(message = "3级品类id不能为空")
    private Long categoryId;

    /**
     * 规格id
     */
    @NotNull(message = "规格id不能为空")
    private Long attributeId;

}
