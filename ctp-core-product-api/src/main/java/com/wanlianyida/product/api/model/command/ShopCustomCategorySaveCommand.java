package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 自定义品类保存参数
 * <AUTHOR>
 */
@Data
public class ShopCustomCategorySaveCommand {

    /**
     * 参数（树形结构）
     */
    @Valid
    @NotNull(message = "自定义分类不允许为空")
    @Size(min = 1,message = "自定义分类不允许为空")
    private List<CustomCategory> customCategoryList;

    /**
     * 自定义分类信息
     */
    @Data
    public static class CustomCategory {
        /**
         * id
         */
        @NotNull(message = "id不能为空")
        private Long id;

        /**
         * 企业id
         */
        @NotBlank(message = "企业id不能为空")
        private String companyId;

        /**
         * 店铺id
         */
        @NotNull(message = "店铺id不能为空")
        private Long shopId;

        /**
         * 分类名称
         */
        @NotBlank(message = "分类名称不能为空")
        private String categoryName;

        /**
         * 排序序号
         */
        @NotNull(message = "排序序号不能为空")
        private Integer sortNumber;

        /**
         * 子级
         */
        private List<CustomCategory> children;
    }

}
