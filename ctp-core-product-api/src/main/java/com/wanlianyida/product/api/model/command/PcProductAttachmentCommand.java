package com.wanlianyida.product.api.model.command;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 商品附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Getter
@Setter
public class PcProductAttachmentCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 附件类型(字典编码ATTACHMENT_TYPE_E)
     */
    @NotNull(message = "附件类型(字典编码ATTACHMENT_TYPE_E)不能为空")
    private String attachmentType;

    /**
     * 文件名
     */
    @NotNull(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件大小
     */
    @NotNull(message = "文件大小不能为空")
    private String fileSize;

    /**
     * 文件url
     */
    @NotNull(message = "文件url不能为空")
    private String fileUrl;


}
