package com.wanlianyida.product.api.model.command;

import lombok.Data;

/**
 * 品类导入command
 * <AUTHOR>
 */
@Data
public class CategoryAttributeImportCommand {

    /**
     * 一级品类名称
     */
    private String firstLevelName;

    /**
     * 二级品类名称
     */
    private String secondLevelName;

    /**
     * 三级品类名称
     */
    private String thirdLevelName;

    /**
     * 规格名称
     */
    private String attributeName;

    /**
     * 输入方式(10选项 20文本)
     */
    private String inputType;

    /**
     * 是否是销售规格(0不是 1是)
     */
    private Integer typeFlag;

    /**
     * 是否必填(0非必填 1必填)
     */
    private Integer inputFlag;

    /**
     * 规格值
     */
    private String values;

    /**
     * excel中的序号
     */
    private String excelRowNum;
}
