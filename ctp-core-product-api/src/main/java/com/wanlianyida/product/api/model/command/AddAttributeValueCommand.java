package com.wanlianyida.product.api.model.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 卖家新增规格值command
 * <AUTHOR>
 */
@Data
public class AddAttributeValueCommand {

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 品类id
     */
    @NotNull(message = "品类id不能为空")
    private Long relCategoryId;

    /**
     * 规格id
     */
    @NotNull(message = "规格id不能为空")
    private Long relAttributeId;

    /**
     * 规格值
     */
    @NotBlank(message = "规格值不能为空")
    private String attributeValue;

}
