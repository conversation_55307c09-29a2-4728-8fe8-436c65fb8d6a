package com.wanlianyida.product.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月26日 10:09
 */
@Data
public class ProductSkuListQuery {

    private String skuCode;

    private Long categoryId3;

    private Long categoryId;

    @ApiModelProperty("品类模糊查询")
    private String categoryNameLike;

    @ApiModelProperty("规格模糊查询")
    private String specificationNameLike;

    @ApiModelProperty("品牌模糊查询")
    private String brandNameLike;

    @ApiModelProperty("商品名称模糊查询")
    private String skuNameLike;

    @ApiModelProperty("供应商名称模糊查询")
    private String companyNameLike;

    @ApiModelProperty("上架状态（10未上架 20已上架 30违规下架）")
    private Integer onSaleStatus;

    @ApiModelProperty("上架状态列表")
    private List<Integer> onSaleStatusList;

    @ApiModelProperty("是否删除 1删除 0未删除")
    private Integer deleted;

    private String companyId;

    @ApiModelProperty("关联的店铺自定义分类数量值")
    private Integer customCategoryCount;

    /**
     * 关联的自定义分类id
     */
    @ApiModelProperty("关联的自定义分类id")
    private Long customCategoryId;

    @ApiModelProperty("店铺名称模糊查询")
    private String shopNameLike;

    /**
     * 关联的自定义分类id集合
     */
    @ApiModelProperty("关联的自定义分类id集合")
    private List<Long> customCategoryIdList;

    @ApiModelProperty("资质ID")
    private String qualificationId;

    @ApiModelProperty("发布类型 10 公开 20 定向")
    private String publishType;

    @ApiModelProperty("下架类型[10-自主下架,20-违规下架,30-售罄下架]")
    private String outSaleType;

    @ApiModelProperty("下架类型[10-自主下架,20-违规下架,30-售罄下架]")
    private List<String> outSaleTypeList;

}
