package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

/**
 * 更新品类状态参数
 * <AUTHOR>
 */
@Data
public class ProductCategoryUpdateEnableStatusCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 状态(1启用 0停用)
     */
    @NotNull(message = "启用状态不能为空")
    private Integer enableStatus;

}
