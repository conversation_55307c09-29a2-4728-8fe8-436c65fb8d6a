package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 商品品类配置返回结果
 * <AUTHOR>
 */
@Data
public class ProductCategoryConfigPageDTO {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 三级品类的id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relCategoryId;

    /**
     * 品类名称
     */
    private String ancestorNames;

    /**
     * 规格名称
     */
    private String attributeNames;

    /**
     * 品牌名称
     */
    private String brandNames;
}
