package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode
@ApiModel(value = "批量上下架参数",description = "批量上下架参数")
public class BatchChangeSaleStatusCommand {

    @ApiModelProperty(value = "sku id 列表")
    @NotNull(message = "skuIdList不能为空")
    private List<Long> skuIdList;

    @ApiModelProperty(value = "上下架标志 10 上架 20 下架 30 违规下架")
    @NotNull(message = "saleStatus不能为空")
    private Integer onSaleStatus;

    @ApiModelProperty(value = "违规下架原因")
    private String reason;
}
