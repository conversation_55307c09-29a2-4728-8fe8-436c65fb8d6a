package com.wanlianyida.product.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量发布返回值1")
public class ProductBatchPublishDTO {

    @ApiModelProperty("返回值信息")
    List<ItemDTO> resultList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ItemDTO {
        @ApiModelProperty(value = "序号")
        Integer id;

        @ApiModelProperty(value = "是否成功")
        Integer success;

        @ApiModelProperty(value = "审核Id")
        String auditId;

        @ApiModelProperty(value = "spuCode")
        String spuCode;

        @ApiModelProperty(value = "skuCode列表")
        List<String> skuCodeList;

        @ApiModelProperty(value = "失败原因")
        String failReason;
    }
}
