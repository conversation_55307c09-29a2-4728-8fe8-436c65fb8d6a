package com.wanlianyida.product.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("发布或修改返回值")
public class ProductPublishOrUpdateDTO {
    @ApiModelProperty(value = "审核ID")
    String auditId;

    @ApiModelProperty(value = "spuID")
    String spuId;

    @ApiModelProperty(value = "spuCode")
    String spuCode;

    @ApiModelProperty(value = "skuId列表")
    List<String> skuIdList;
}
