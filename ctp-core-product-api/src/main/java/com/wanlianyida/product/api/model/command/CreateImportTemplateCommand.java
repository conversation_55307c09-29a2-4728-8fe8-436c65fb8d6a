package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class CreateImportTemplateCommand {

    /**
     * 仓库集合
     */
    @NotNull(message = "仓库集合不能为空")
    @Size(min = 1, message = "仓库集合不能为空")
    private List<Stock> stockList;

    /**
     * 计量单位集合
     */
    @NotNull(message = "计量单位集合不能为空")
    private List<MeasurementUnit> measurementUnitList;

    /**
     * 计价单位集合
     */
    @NotNull(message = "计价单位集合不能为空")
    @Size(min = 1, message = "计价单位集合不能为空")
    private List<PricingUnit> pricingUnitList;

    /**
     * 计重方式集合
     */
    @NotNull(message = "计重方式不能为空")
    @Size(min = 1, message = "计重方式不能为空")
    private List<WeightMeasurement> weightMeasurementList;

    /**
     * 是否现货集合
     */
    @NotNull(message = "是否现货不能为空")
    @Size(min = 1, message = "是否现货不能为空")
    private List<SpotGoods> spotGoodsList;

    /**
     * 品类信息
     */
    private CategoryInfo categoryInfo;

    /**
     * 3级品类关联的有效品牌信息
     */
    private List<CategoryBrand> categoryBrandList;

    /**
     * 3级品类关联的所有有效规格集合
     */
    private List<CategoryAttribute> categoryAttributeList;

    /**
     * 店铺信息
     */
    private UmShop umShop;

    /**
     * 仓库信息
     */
    @Data
    public static class Stock{
        /**
         * 关联仓库ID
         */
        private Long relStockId;

        /**
         * 仓库名称
         */
        private String stockName;

        /**
         * 交货地省编号
         */
        private String deliveryAddrProvinceCode;

        /**
         * 交货地城市编号
         */
        private String deliveryAddrCityCode;

        /**
         * 交货地省名称
         */
        private String deliveryAddrProvinceName;

        /**
         * 交货地城市名称
         */
        private String deliveryAddrCityName;

    }

    /**
     * 计量单位
     */
    @Data
    public static class MeasurementUnit{
        /**
         * 计量单位ID
         */
        private Long relMeasurementUnitId;

        /**
         *计量单位编码
         */
        private Integer pricingType;

        /**
         * 计量单位名称
         */
        private String relMeasurementUnitName;

        /**
         * 小数点前位数
         */
        private Integer beforePoint;

        /**
         * '小数点后位数
         */
        private Integer afterPoint;
    }

    /**
     * 计价单位
     */
    @Data
    public static class PricingUnit{
        /**
         * 计价单位ID
         */
        private Long relPricingUnitId;

        /**
         * 计价单位编码
         */
        private Integer pricingType;

        /**
         * 计价单位名称
         */
        private String relPricingUnName;

        /**
         * 小数点前位数
         */
        private Integer beforePoint;

        /**
         * '小数点后位数
         */
        private Integer afterPoint;
    }

    /**
     * 计重方式
     */
    @Data
    public static class WeightMeasurement{
        /**
         * 计重方式id（'10'理重 '20'过磅）
         */
        private String weightMeasurementTypeId;

        /**
         * 计重方式名称（理重、过磅）
         */
        private String weightMeasurementTypeName;
    }

    /**
     * 是否现货
     */
    @Data
    public static class SpotGoods{

        /**
         * 是否现货:1是 0否;
         */
        private Integer isSpotGoodsId;

        /**
         * 是否现货：是 、否;
         */
        private String isSpotGoodsName;
    }

    /**
     * 品类信息
     */
    @Data
    public static class CategoryInfo{

        /**
         * 一级品类ID
         */
        private Long categoryId1;

        /**
         * 二级品类ID
         */
        private Long categoryId2;

        /**
         * 三级品类ID
         */
        private Long categoryId3;

        /**
         * 一级品类名称
         */
        private String categoryId1Name;

        /**
         * 二级品类名称
         */
        private String categoryId2Name;

        /**
         * 三级品类名称
         */
        private String categoryId3Name;

        /**
         * 品类祖籍id
         */
        private String ancestorIds;

        /**
         * 品类祖籍名称
         */
        private String ancestorNames;
    }

    /**
     * 品类品牌关联
     */
    @Data
    public static class CategoryBrand {
        /**
         * 关联的品牌ID
         */
        private Long relBrandId;

        /**
         * 品牌名称
         */
        private String brandName;
    }

    /**
     * 品类规格关联
     */
    @Data
    public static class CategoryAttribute {
        /**
         * 关联的规格id
         */
        private Long relAttributeId;

        /**
         * 规格名称
         */
        private String attributeName;

        /**
         * 规格输入方式：10选项（从列表中选取）；20文本（手工录入）
         */
        private String inputType;

        /**
         * 是否是销售规格(0不是 1是)
         */
        private Integer typeFlag;

        /**
         * 是否必填(0非必填 1必填)
         */
        private Integer inputFlag;

        /**
         * 启用状态(1启用 0停用)
         */
        private Integer enableStatus;

        /**
         * 排序序号
         */
        private Integer sortNumber;

        /**
         * 品类规格-规格值
         */
        private List<String> attributeValueList;
    }

    /**
     * 店铺信息
     */
    @Data
    public static class UmShop{

        /**
         * 店铺id
         */
        private Long id;

        /**
         * 公司id
         */
        private String companyId;

        /**
         * 公司名称
         */
        private String shopName;
    }

}
