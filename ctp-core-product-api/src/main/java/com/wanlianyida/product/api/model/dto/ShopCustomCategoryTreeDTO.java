package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 店铺自定义品类树结构
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ShopCustomCategoryTreeDTO {

    /**
     * id
     */
    private String id;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 父id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 祖级id列表,用‘,’分隔
     */
    private String ancestorIds;

    /**
     * 祖级名称列表,以‘_’分隔
     */
    private String ancestorNames;

    /**
     * 枚举排序序号
     */
    private Integer sortNumber;

    /**
     * 品类级别：1->1级；2->2级
     */
    private Integer categoryLevel;

    /**
     * 关联商品数
     */
    private Integer productCount;

    /**
     * 是否展示
     */
    private Boolean isExpanded = false;

    private List<ShopCustomCategoryTreeDTO> children;

    public ShopCustomCategoryTreeDTO(){
        this.children = new ArrayList<>();
    }

    public void addChild(ShopCustomCategoryTreeDTO children){
        this.children.add(children);
    }

}
