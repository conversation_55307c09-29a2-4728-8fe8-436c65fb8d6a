package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductSkuDetailUpdateCommand {
    @ApiModelProperty(value = "sku基础信息")
    private ProductSkuUpdateCommand sku;

    @ApiModelProperty(value = "sku属性信息")
    private List<ProductAttrUpdateCommand> attrList;
}
