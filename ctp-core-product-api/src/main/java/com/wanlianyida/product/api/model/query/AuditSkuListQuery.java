package com.wanlianyida.product.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月25日 13:36
 */
@Data
public class AuditSkuListQuery {

    @ApiModelProperty("商品spu编码")
    private String spuCode;

    @ApiModelProperty("商品sku编码")
    private String skuCode;

    @ApiModelProperty("3级品类名称")
    private String categoryName3;

    @ApiModelProperty("规格")
    private String skuSpecificationName;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("审核状态（10 待审核 20审核通过; 30审核驳回 40 审核撤销）")
    private Integer auditStatus;

    private String companyId;
}
