package com.wanlianyida.product.api.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 商品品类分页查询参数
 * <AUTHOR>
 */
@Data
public class ProductCategoryPageQuery {

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 启用状态(1启用 0停用)
     */
    private Integer enableStatus;

    /**
     * 查询类型（"10" 查询1级品类，"20" 查询所有品类，"30"查询3级品类）
     */
    @NotBlank(message = "查询类型不能为空（10查询1级品类 20查询所有品类 30查询3级品类）")
    private String queryType;

    /**
     * 父类id
     */
    private Long parentId;

    /**
     * 关联属性标识(1 关联 0 不关联)
     */
    private Integer relateAttributeFlag;
}
