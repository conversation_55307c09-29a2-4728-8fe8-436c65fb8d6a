package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月23日 14:01
 */
@Data
@ApiModel("商品spu详情")
public class ProductSpuDetailDTO {


    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("SPU ID")
    private Long id;

    @ApiModelProperty("spu编码")
    private String spuCode;

    @ApiModelProperty("企业ID（店铺ID）")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("品牌ID")
    private Long relBrandId;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("1级品类ID")
    private Long categoryId1;

    @ApiModelProperty("1级品类名称")
    private String categoryName1;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("2级品类ID")
    private Long categoryId2;

    @ApiModelProperty("2级品类名称")
    private String categoryName2;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("3级品类ID")
    private Long categoryId3;

    @ApiModelProperty("3级品类名称")
    private String categoryName3;

    @ApiModelProperty("SPU名称")
    private String spuName;

    @ApiModelProperty("是否审核后立即上架 1是 0否")
    private Boolean onSaleAfterAudit;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("关联仓库ID")
    private Long relStockId;

    @ApiModelProperty("仓库名称")
    private String stockName;

    @ApiModelProperty("交货地省编号")
    private String deliveryAddrProvinceCode;

    @ApiModelProperty("交货地城市编号")
    private String deliveryAddrCityCode;

    @ApiModelProperty("交货地省名称")
    private String deliveryAddrProvinceName;

    @ApiModelProperty("交货地城市名称")
    private String deliveryAddrCityName;

    @ApiModelProperty("计价单位ID")
    private Integer relPricingUnitId;

    @ApiModelProperty("计量单位ID")
    private Integer relMeasurementUnitId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty("")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    @ApiModelProperty("创建人名字")
    private String creatorName;

    @ApiModelProperty("更新人名字")
    private String updaterName;

    @ApiModelProperty("商品图片")
    private String picUrl;

    @ApiModelProperty("spu属性")
    private List<ProductSkuAttrDTO> productSpuAttrList;

    @ApiModelProperty("sku列表")
    private List<ProductSkuInfoDTO> productSkuDetailList;

    @ApiModelProperty("发布类型")
    private String publishType;

    @ApiModelProperty("定向发布公司名单")
    private List<ProductPublishCompanyDTO> publishCompanyList;

    @ApiModelProperty("资质列表")
    private List<ProductQualificationListDTO> qualificationList;

    @ApiModelProperty("图片库")
    private List<SkuPicDTO> picList;
}
