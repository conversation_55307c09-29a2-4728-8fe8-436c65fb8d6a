package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 品类已经关联的品牌DTO
 * <AUTHOR>
 */
@Data
public class CategoryRelatedBrandDTO {

    /**
     * 关联的品类id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relCategoryId;

    /**
     * 关联的品牌id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long relBrandId;

    /**
     * 品牌名称
     */
    private String brandName;

}
