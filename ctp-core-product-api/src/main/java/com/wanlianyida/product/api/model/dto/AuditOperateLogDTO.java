package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月23日 15:30
 */
@Data
public class AuditOperateLogDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 操作类型 10 通过 20 不通过 30已撤回
     */
    private String operateType;

    /**
     * 原因
     */
    private String rejectReason;

    /**
     * 操作账号
     */
    private String operateAccount;

    /**
     * 操作人姓名
     */
    private String operateName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

}
