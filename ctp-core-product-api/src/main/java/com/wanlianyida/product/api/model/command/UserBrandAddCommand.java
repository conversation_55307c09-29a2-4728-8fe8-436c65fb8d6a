package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 卖家新增品牌command
 * <AUTHOR>
 */
@Data
public class UserBrandAddCommand {

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 品牌名称
     */
    @NotBlank(message = "品牌名称不能为空")
    private String brandName;

    /**
     * 3级品类id集合
     */
    @NotNull(message = "3级品类id集合不能为空")
    @Size(min = 1,message = "3级品类id集合不能为空")
    private List<Long> categoryList;
}
