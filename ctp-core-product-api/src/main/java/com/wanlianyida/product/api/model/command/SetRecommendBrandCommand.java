package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class SetRecommendBrandCommand {

    /**
     * 1级品类id
     */
    @NotNull(message = "1级品类id不能为空")
    private Long categoryId;

    /**
     * 推荐品牌列表
     */
    @Valid
    private List<RecommendBrand> recommendBrandList;


    /**
     * 推荐品牌信息
     */
    @Data
    public static class RecommendBrand {
        /**
         * 推荐品牌id
         */
        @NotNull(message = "推荐品牌id不能为空")
        private Long brandId;

        /**
         * 推荐品牌名称
         */
        @NotBlank(message = "推荐品牌名称不能为空")
        private String brandName;
    }
}
