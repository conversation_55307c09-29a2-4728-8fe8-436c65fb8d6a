package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("发布商品参数")
public class ProductPublishCommand {

    @ApiModelProperty("店铺ID")
    private Long shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("品牌ID")
    @NotNull(message = "品牌ID不能为空")
    private Long relBrandId;

    @ApiModelProperty("品类一级ID")
    @NotNull(message = "categoryId1 不能为空")
    private Long categoryId1;

    @ApiModelProperty("品类2级ID")
    @NotNull(message = "categoryId2 不能为空")
    private Long categoryId2;

    @ApiModelProperty("品类3级ID")
    @NotNull(message = "categoryId3 不能为空")
    private Long categoryId3;

    @ApiModelProperty("是否审核后立即上架 1是 0否")
    @NotNull(message = "onSaleAfterAudit 不能为空")
    private Integer onSaleAfterAudit;

    @ApiModelProperty("关联仓库ID")
    @NotNull(message = "relStockId 不能为空")
    private Long relStockId;

    @ApiModelProperty("仓库名称")
    private String stockName;

    @ApiModelProperty("交货地省编号")
    @NotEmpty(message = "deliveryAddrProvinceCode 不能为空")
    private String deliveryAddrProvinceCode;

    @ApiModelProperty("交货地城市编号")
    @NotEmpty(message = "deliveryAddrCityCode 不能为空")
    private String deliveryAddrCityCode;

    @ApiModelProperty("交货地省名称")
    @NotEmpty(message = "deliveryAddrProvinceName 不能为空")
    private String deliveryAddrProvinceName;

    @ApiModelProperty("交货地城市名称")
    @NotEmpty(message = "deliveryAddrCityName 不能为空")
    private String deliveryAddrCityName;

    @ApiModelProperty("计价单位ID")
    @NotNull(message = "relPricingUnitId 不能为空")
    private Integer relPricingUnitId;

    @ApiModelProperty("计量单位ID")
    @NotNull(message = "relMeasurementUnitId 不能为空")
    private Integer relMeasurementUnitId;

    @ApiModelProperty("spu属性信息")
    private List<ProductAttrPublishCommand> attrList;

    @ApiModelProperty("sku信息")
    private List<ProductSkuDetailPublishCommand> skuList;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("发布类型")
    private String publishType;

    @ApiModelProperty("定向发布公司列表")
    private List<PublishCompanyCommand> publishCompanyList;

    @ApiModelProperty("图片列表")
    private List<ProductPicCommand> picList;

    @ApiModelProperty("商品资质id列表")
    private List<String> qualificationIdList;

    @ApiModelProperty("商品富文本信息")
    private String richText;

    @ApiModelProperty("转卖原spuCode（只有转卖商品时记录）")
    private String resellFromSpuCode;

    @ApiModelProperty("草稿箱spuId")
    private String detailId;
}
