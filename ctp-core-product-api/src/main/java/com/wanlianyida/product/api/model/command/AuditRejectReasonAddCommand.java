package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 驳回原因表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
public class AuditRejectReasonAddCommand {

    /**
     * 原因内容
     */
    @NotBlank(message = "原因内容不能为空")
    private String value;

    /**
     * 排序
     */
    @NotNull(message = "序号不能为空")
    private Integer sortNo;

    @ApiModelProperty("原因类型，10商品审核，20商品上下架，30求购审核")
    private Integer reasonType;
}
