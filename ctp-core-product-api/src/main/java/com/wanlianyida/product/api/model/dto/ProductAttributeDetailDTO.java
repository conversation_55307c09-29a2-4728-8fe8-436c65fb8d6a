package com.wanlianyida.product.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 商品规格详情结果
 * <AUTHOR>
 */
@Data
public class ProductAttributeDetailDTO {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 规格名称
     */
    private String attributeName;

    /**
     * 规格值，用逗号间隔
     */
    private String attributeValues;

    /**
     * 启用状态(1启用 0停用)
     */
    private Integer enableStatus;

    /**
     * 创建人名字
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 名称说明
     */
    private String nameDesc;

    /**
     * 规格值列表
     */
    private List<ProductAttributeValueDetailDTO> valueDetailDTOList;
}
