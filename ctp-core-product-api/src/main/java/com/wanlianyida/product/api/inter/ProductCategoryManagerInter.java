package com.wanlianyida.product.api.inter;

import com.wanlianyida.framework.ctpcommon.entity.IdQuery;
import com.wanlianyida.framework.ctpcommon.entity.PagingInfo;
import com.wanlianyida.framework.ctpcommon.entity.ResultMode;
import com.wanlianyida.product.api.model.command.ProductCategoryAddCommand;
import com.wanlianyida.product.api.model.command.ProductCategoryDeleteCommand;
import com.wanlianyida.product.api.model.command.ProductCategoryEditCommand;
import com.wanlianyida.product.api.model.command.ProductCategoryUpdateEnableStatusCommand;
import com.wanlianyida.product.api.model.dto.*;
import com.wanlianyida.product.api.model.query.ProductCategoryDirectSubQuery;
import com.wanlianyida.product.api.model.query.ProductCategoryPageQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api("商品品类管理")
@FeignClient(name = "ctp-core-product", contextId = "productCategoryManagerService", path = "/ctp-core-product")
public interface ProductCategoryManagerInter {

    @ApiOperation("新增品类")
    @PostMapping(value = {"/category/manager/add"})
    ResultMode<AddCategoryDTO> add(@RequestBody @Valid ProductCategoryAddCommand command);

    @ApiOperation("编辑品类")
    @PostMapping(value = {"/category/manager/edit"})
    ResultMode<?> edit(@RequestBody @Valid ProductCategoryEditCommand command);

    @ApiOperation("删除品类")
    @PostMapping(value = {"/category/manager/delete"})
    ResultMode<?> delete(@RequestBody @Valid ProductCategoryDeleteCommand command);

    @ApiOperation("更新品类状态")
    @PostMapping(value = {"/category/manager/updateEnableStatus"})
    ResultMode<?> updateEnableStatus(@RequestBody @Valid ProductCategoryUpdateEnableStatusCommand command);

    @ApiOperation("商品品类分页查询")
    @PostMapping(value = {"/category/manager/pageQuery"})
    ResultMode<List<ProductCategoryPageDTO>> pageQueryProductCategory(@RequestBody PagingInfo<ProductCategoryPageQuery> query);

    @ApiOperation("获取分类下所有未删除的直接子级")
    @PostMapping(value = {"/category/manager/queryDirectSubLevel"})
    ResultMode<List<ProductCategoryDirectSubDTO>> queryDirectSubLevel(@RequestBody ProductCategoryDirectSubQuery query);

    @ApiOperation("根据主键列表查询")
    @PostMapping(value = {"/category/manager/queryByIds"})
    ResultMode<List<ProductCategoryDTO>> queryByIds(@RequestBody List<Long> idList);

    @ApiOperation("根据主键查询品类图片")
    @PostMapping(value = {"/category/manager/queryCategoryPicById"})
    ResultMode<ProductCategoryPicDTO> queryCategoryPicById(@RequestBody IdQuery query);

    @ApiOperation("根据主键查询品类信息")
    @PostMapping(value = {"/category/manager/queryCategoryById"})
    ResultMode<ProductCategoryDTO> queryCategoryById(@RequestBody IdQuery query);

}
