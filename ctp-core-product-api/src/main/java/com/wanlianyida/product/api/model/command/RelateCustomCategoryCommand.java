package com.wanlianyida.product.api.model.command;

import lombok.Data;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 批量关联自定义分类command
 * <AUTHOR>
 */
@Data
public class RelateCustomCategoryCommand {

    /**
     * 关联类型 1 新增 2 覆盖 3 删除
     */
    @NotNull(message = "关联类型不允许为空")
    private Integer relationType;

    /**
     * 商品skuCodeList
     */
    @Valid
    @NotNull(message = "商品信息不允许为空")
    @Size(min = 1,message = "商品信息不允许为空")
    private List<String> skuCodeList;

    /**
     * 关联数据信息
     */
    @Valid
    @NotNull(message = "关联数据信息不允许为空")
    @Size(min = 1,message = "关联数据信息不允许为空")
    private List<RelateCustomCategory> relateCustomCategoryList;

    /**
     * 关联数据信息
     */
    @Data
    public static class RelateCustomCategory {

        /**
         * 企业id
         */
        @NotBlank(message = "企业id不能为空")
        private String companyId;

        /**
         * 店铺id
         */
        @NotNull(message = "店铺id不能为空")
        private Long shopId;

        /**
         * 关联分类的1级分类id
         */
        @NotNull(message = "关联分类的1级分类id不能为空")
        private String customCategoryId1;

        /**
         * 关联分类的2级分类id
         */
        private String customCategoryId2;

        /**
         * 关联分类id
         */
        @NotNull(message = "关联分类id不能为空")
        private String customCategoryId;

        /**
         * 关联分类名称
         */
        @NotBlank(message = "关联分类名称不能为空")
        private String customCategoryName;

        /**
         * 关联分类祖级名称列表
         */
        @NotBlank(message = "关联分类祖级名称列表不能为空")
        private String ancestorNames;

    }

}
