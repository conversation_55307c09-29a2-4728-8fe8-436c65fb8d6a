package com.wanlianyida.product.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DelayTaskCommand {
    @ApiModelProperty("任务id")
    private Long id;
    @ApiModelProperty("任务类型")
    private String taskType;
    @ApiModelProperty("业务id")
    private String bizId;
    @ApiModelProperty("执行时间")
    private Date execTime;
    @ApiModelProperty("执行状态")
    private Integer execStatus;
    @ApiModelProperty("任务描述")
    private String remark;
}
