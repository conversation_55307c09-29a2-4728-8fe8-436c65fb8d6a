package com.wanlianyida.product.api.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品审核记录表
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Data
public class ProductViewLogDTO {

    /**
     * 浏览时间
     */
    private String viewTimeDay;

    /**
     * 当天浏览总数
     */
    private Long total;

    /**
     * 是否当日
     */
    private boolean today;

    /**
     * 商品列表集合
     */
    private List<ProductViewList> productViewLists;

    /**
     * 商品列表
     */
    @Data
    public static class ProductViewList {

        /**
         * 主键id
         */
        private Long id;

        /**
         * sku名称
         */
        private String skuName;

        /**
         * 商品id
         */
        private String productId;


        /**
         * sku编码
         */
        private String skuCode;

        /**
         * 上架状态
         */
        private Integer onSaleStatus;

        /**
         * 删除状态
         */
        private Integer deleted;

        /**
         * 价格
         */
        private BigDecimal priceFee;

        /**
         * 分类图片
         */
        private String categoryPic;

        /**
         * 店铺logo
         */
        private String shopLogo;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 店铺id
         */
        private Long shopId;

        /**
         * 关联的计费单位id
         */
        private Integer relPricingUnitId;

        /**
         * 定向发布公司id
         */
        private List<String> publishCompanyId;

        /**
         * 发布类型
         */
        private String publishType;

    }

}
