package com.wanlianyida.transaction.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RfqBidPageListDTO {


    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("我的出价")
    private BigDecimal bidPrice;

    @ApiModelProperty("出价时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bidTime;

    @ApiModelProperty("出价账号")
    private String bidderId;

    @ApiModelProperty("出价人")
    private String bidderName;

    @ApiModelProperty("计价单位id")
    private Integer pricingUnitId;

    @ApiModelProperty("报价方式[10-按单价报价,20-按总价报价]")
    private Integer quoteMethod;

}
