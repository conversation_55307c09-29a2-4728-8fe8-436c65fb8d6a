package com.wanlianyida.transaction.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月06日 16:29
 */
@Data
@ApiModel("求购信息表")
public class PurchaseInfoDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("求购单号")
    private String purchaseNo;

    @ApiModelProperty("求购单标题")
    private String purchaseTitle;

    @ApiModelProperty("求购单状态，10待审核，20审核驳回，30询价中，40比价中，50已下单、100已关闭")
    private Integer purchaseStatus;

    @ApiModelProperty("发布时间（审核通过时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseDate;

    @ApiModelProperty("驳回原因")
    private String rejectReason;

    @ApiModelProperty("报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deadlineDate;

    @ApiModelProperty("交货地省编码")
    private String provinceCode;

    @ApiModelProperty("交货地省")
    private String provinceName;

    @ApiModelProperty("交货地市编码")
    private String cityCode;

    @ApiModelProperty("交货地市")
    private String cityName;

    @ApiModelProperty("期望交货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty("报价是否含税，1是，0否")
    private Integer quoteTaxFlag;

    @ApiModelProperty("报价是否含运费，1是，0否")
    private Integer quoteFreightFlag;

    @ApiModelProperty("仅部分商品报价，1允许，0不允许")
    private Integer portionProductFlag;

    @ApiModelProperty("仅部分数量报价，1允许，0不允许")
    private Integer portionQuantityFlag;

    @ApiModelProperty("商品数量")
    private Integer productCount;

    @ApiModelProperty("当前报价数量")
    private Integer quotationCount;

    @ApiModelProperty("其他要求")
    private String otherRequirement;

    @ApiModelProperty("联系人姓名")
    private String linkmanName;

    @ApiModelProperty("联系人电话")
    private String linkmanMobile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDate;

    @ApiModelProperty("交货方式[10-自提,20-物流配送]")
    private Integer deliveryMethod;

    @ApiModelProperty("结算开票方式[10-一票结算,20-两票结算]")
    private Integer settIssueMethod;
}
