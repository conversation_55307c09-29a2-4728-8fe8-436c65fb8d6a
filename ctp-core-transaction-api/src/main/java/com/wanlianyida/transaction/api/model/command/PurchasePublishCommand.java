package com.wanlianyida.transaction.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月06日 16:29
 */
@Data
@ApiModel("求购信息表")
public class PurchasePublishCommand {

    @ApiModelProperty("报价截止时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deadlineDate;

    @ApiModelProperty("交货地省编码")
    @NotBlank(message = "交货地省编码不能为空")
    private String provinceCode;

    @ApiModelProperty("交货地省")
    @NotBlank(message = "交货地省不能为空")
    private String provinceName;

    @ApiModelProperty("交货地市编码")
    @NotBlank(message = "交货地市编码不能为空")
    private String cityCode;

    @ApiModelProperty("交货地市")
    @NotBlank(message = "交货地市不能为空")
    private String cityName;

    @ApiModelProperty("期望交货日期")
    @NotNull(message = "期望交货日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty("报价是否含税，1是，0否")
    private Integer quoteTaxFlag;

    @ApiModelProperty("报价是否含运费，1是，0否")
    private Integer quoteFreightFlag;

    @ApiModelProperty("仅部分商品报价，1允许，0不允许")
    @NotNull(message = "商品数量标识不能为空")
    private Integer portionProductFlag;

    @ApiModelProperty("仅部分数量报价，1允许，0不允许")
    @NotNull(message = "报价数量标识不能为空")
    private Integer portionQuantityFlag;

    @ApiModelProperty("其他要求")
    private String otherRequirement;

    @ApiModelProperty("联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    private String linkmanName;

    @ApiModelProperty("联系人电话")
    @NotBlank(message = "联系人电话不能为空")
    private String linkmanMobile;

    @ApiModelProperty("商品信息")
    @Valid
    @NotEmpty(message = "商品信息不能为空")
    @Size(max = 100, message = "最多支持添加100个商品")
    private List<PurchaseProductCommand> productList;

    /**
     * 求购范围[10-公开求购,20定向求购]
     */
//    @NotBlank(message = "求购范围不能为空")
    private String purchaseScope;

    /**
     * 定向求购客户公司列表
     */
    @Valid
    private List<PurchaseCompanyCommand> companyCommandList;

    /**
     * 报价截止天数
     */
    private Integer quotationDeadlineDays;

    @ApiModelProperty("交货方式[10-自提,20-物流配送]")
    private Integer deliveryMethod;

    @ApiModelProperty("结算开票方式[10-一票结算,20-两票结算]")
    private Integer settIssueMethod;
}
