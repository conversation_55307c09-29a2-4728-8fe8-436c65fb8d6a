package com.wanlianyida.transaction.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月09日 10:02
 */
@Data
public class PurchaseHomeDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("求购单号")
    private String purchaseNo;

    @ApiModelProperty("求购单标题")
    private String purchaseTitle;

    @ApiModelProperty("发布时间（审核通过时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseDate;

    @ApiModelProperty("报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadlineDate;

    @ApiModelProperty("求购单状态，10待审核，20审核驳回，30询价中，40比价中，50已下单、100已关闭")
    private Integer purchaseStatus;
}
