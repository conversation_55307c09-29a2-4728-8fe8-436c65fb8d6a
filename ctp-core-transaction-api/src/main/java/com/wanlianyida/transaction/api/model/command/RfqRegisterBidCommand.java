package com.wanlianyida.transaction.api.model.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class RfqRegisterBidCommand {

    @ApiModelProperty("询比价单单号")
    @NotBlank(message = "询比价单单号不能为空")
    private String rfqNo;

    @ApiModelProperty("报价金额")
    @NotNull(message = "报价金额不能为空")
    private BigDecimal price;
}
