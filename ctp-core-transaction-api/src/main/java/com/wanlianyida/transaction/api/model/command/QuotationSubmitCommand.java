package com.wanlianyida.transaction.api.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月07日 13:07
 */
@Data
public class QuotationSubmitCommand {

    @ApiModelProperty("店铺id")
    private Long shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("求购单id")
    @NotNull(message = "求购单id不能为空")
    private Long relPurchaseId;

    @ApiModelProperty("买家公司id")
    private String buyerCompanyId;

    @ApiModelProperty("报价有效期")
    @NotNull(message = "报价有效期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validDate;

    @ApiModelProperty("联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    private String linkmanName;

    @ApiModelProperty("联系人电话")
    @NotBlank(message = "联系人电话不能为空")
    private String linkmanMobile;

    @ApiModelProperty("其他报价说明")
    private String quotationDesc;
    @Valid
    @NotEmpty(message = "报价商品不能为空")
    private List<QuotationProductSubmitCommand> productList;
}
