package com.wanlianyida.transaction.api.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月09日 10:01
 */
@Data
public class PurchaseListQuery {

    @ApiModelProperty("求购单状态，10待审核，20审核驳回，30询价中，40比价中，50已下单、100已关闭")
    private Integer purchaseStatus;

    /**
     * 求购范围[10-公开求购,20定向求购]
     */
    private String purchaseScope;

    /**
     * 求购单号
     */
    private String purchaseNoLike;
    /**
     * 求购企业
     */
    private String companyNameLike;


    /**
     * 类型[10-运营端]
     */
    private String sourceType;
}
