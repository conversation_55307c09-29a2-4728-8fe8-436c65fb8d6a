package com.wanlianyida.transaction.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年12月06日 16:46
 */
@Data
@ApiModel("求购商品表")
public class PurchaseProductDetailDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty("报价单id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long relQuotationId;

    @ApiModelProperty("1级品类ID")
    private Long categoryId1;

    @ApiModelProperty("1级品类名称")
    private String categoryName1;

    @ApiModelProperty("2级品类ID")
    private Long categoryId2;

    @ApiModelProperty("2级品类名称")
    private String categoryName2;

    @ApiModelProperty("3级品类ID")
    private Long categoryId3;

    @ApiModelProperty("3级品类名称")
    private String categoryName3;

    @ApiModelProperty("求购规格")
    private String purSpecificationName;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("采购数量")
    private Integer purchaseCount;

    @ApiModelProperty("计量单位ID")
    private Integer relQuoMeasurementUnitId;

    @ApiModelProperty("其他说明")
    private String remark;

    @ApiModelProperty("报价商品id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long quotationProductId;

    @ApiModelProperty("sku编码")
    private String skuCode;

    @ApiModelProperty("SKU名称（品牌+三级品类+sku规格名称）")
    private String skuName;

    @ApiModelProperty("报价规格")
    private String quoSpecificationName;

    @ApiModelProperty("供应数量")
    private BigDecimal supplyCount;

    @ApiModelProperty("单价")
    private BigDecimal priceFee;

    @ApiModelProperty("单位转换 1吨=x吨度(计价单位转换为计量单位)")
    private BigDecimal unitTransfer;

    @ApiModelProperty("小计")
    private BigDecimal subtotal;

    @ApiModelProperty("计量单位ID")
    private Integer relMeasurementUnitId;

    @ApiModelProperty("计价单位ID")
    private Integer relPricingUnitId;

    @ApiModelProperty("关联意向订单号")
    private String orderNo;

    @ApiModelProperty("品类图片地址")
    private String picUrl;

    @ApiModelProperty("店铺id")
    private Long shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;
}
