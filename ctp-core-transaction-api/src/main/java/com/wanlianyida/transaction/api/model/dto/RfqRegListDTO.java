package com.wanlianyida.transaction.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class RfqRegListDTO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("询比价单号")
    private String rfqNo;

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("报名状态[10-报名中,20-报名成功,30-报名失败]")
    private Integer regStatus;

    @ApiModelProperty("报名人id")
    private String registrantId;

    @ApiModelProperty("报名人名称")
    private String registrantName;

    @ApiModelProperty("报名时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date regTime;

    @ApiModelProperty("报名成功时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date regSuccessTime;

    @ApiModelProperty("询比价保证金表id")
    private Long depositId;

    @ApiModelProperty("保证金状态")
    private Integer depositStatus;

    @ApiModelProperty("是否出价[1-已出价,0-未出价]")
    private Boolean bidFlag;

    @ApiModelProperty("最终出价金额")
    private BigDecimal finalBidPrice;

    @ApiModelProperty("创建人id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;


}
