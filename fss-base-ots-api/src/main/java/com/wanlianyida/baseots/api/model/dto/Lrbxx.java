package com.wanlianyida.baseots.api.model.dto;

import lombok.Data;

@Data
public class Lrbxx {
    // 纳税人登记序号 VARCHAR2(50)
    private String djxh;

    // 纳税人识别号 VARCHAR2(40)
    private String nsrsbh;

    // 申报日期 VARCHAR2(20)
    private String sbrq;

    // 所属日期起 VARCHAR2(20)
    private String ssqq;

    // 所属日期止 VARCHAR2(20)
    private String ssqz;

    // 财务报表类型代码 VARCHAR2(20)
    private String cwbblxDm;

    // 资料报送类型名称 VARCHAR2(2000)
    private String zlbsxlmc;

    // 项目名称 VARCHAR2(2000)
    private String xm;

    // 序号 VARCHAR2(2000)
    private String mc;

    // 本月数 NUMBER(18,2)
    private Number bys;

    // 本年累计金额 NUMBER(18,2)
    private Number bnlije;

    // 上期金额 NUMBER(18,2)
    private Number sqje;
}
