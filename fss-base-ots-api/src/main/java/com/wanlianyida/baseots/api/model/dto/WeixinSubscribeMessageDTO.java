package com.wanlianyida.baseots.api.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

import javax.validation.constraints.NotBlank;

/**
 * 微信订阅消息dto
 *
 * <AUTHOR>
 * @date 2025-06-16 15:01
 */
@Data
@Accessors(chain = true)
public class WeixinSubscribeMessageDTO {

    /**
     * 消息的id,排查问题使用
     */
    private String msgId;

    /**
     * 小程序的appid
     */
    @NotBlank(message = "小程序的appid不能为空")
    private String appid;

    /**
     * 模板id
     */
    @NotBlank(message = "模板id不能为空")
    private String templateId;

    /**
     * 跳转页面
     */
    private String page;

    /**
     * 接收者openid
     */
    @NotBlank(message = "接收者openid不能为空")
    private String touser;

    /**
     * 模板内容，格式形如{ "phrase3": { "value": "审核通过" }, "name1": { "value": "订阅" }, "date2": { "value": "2019-12-25 09:42" } }
     */
    private Map<String, Filed> data;

    /**
     * 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
     */
    private String miniprogramState = "formal";

    /**
     * 进入小程序查看”的语言类型，支持zh_CN(简体中文)、en_US(英文)、zh_HK(繁体中文)、zh_TW(繁体中文)，默认为zh_CN
     */
    private String lang = "zh_CN";

    /**
     * 模板字段内容
     */
    @Data
    public static class Filed {

        /**
         * 模板字段内容
         */
        private String value;
    }

}
