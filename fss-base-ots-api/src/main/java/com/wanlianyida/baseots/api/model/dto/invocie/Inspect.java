package com.wanlianyida.baseots.api.model.dto.invocie;

import lombok.Data;

@Data
public class Inspect {
    // 检查当前标志
    private Integer inspectCurrFlag;

    // 过去12个月检查违规数量
    private Integer inspectInvIllegalNum12m;

    // 过去6个月检查违规数量
    private Integer inspectInvIllegalNum6m;

    // 最后一次检查日期差值
    private Integer inspectLastCurdaydiff;

    // 最后一次检查的违法事实
    private String inspectLastIllegalFacts;

    // 最后一次检查月份差值
    private Integer inspectLastMondiff;

    // 过去12个月检查月份计数
    private Integer inspectMonthCnt12m;

    // 过去36个月检查月份计数
    private Integer inspectMonthCnt36m;

    // 过去6个月检查月份计数
    private Integer inspectMonthCnt6m;

    // 过去12个月检查数量
    private Integer inspectNum12m;

    // 过去36个月检查数量
    private Integer inspectNum36m;

    // 过去6个月检查数量
    private Integer inspectNum6m;

    // 过去12个月税务设备数量
    private Integer inspectTaxDeviceNum12m;

    // 过去6个月税务设备数量
    private Integer inspectTaxDeviceNum6m;

    // 当前税务逃税标志
    private Integer inspectTaxEvasionCurrFlag;

    // 过去12个月税务逃税数量
    private Integer inspectTaxEvasionNum12m;

    // 过去6个月税务逃税数量
    private Integer inspectTaxEvasionNum6m;

    // 过去12个月税务欺诈数量
    private Integer inspectTaxFraudNum12m;

    // 过去6个月税务欺诈数量
    private Integer inspectTaxFraudNum6m;
}
