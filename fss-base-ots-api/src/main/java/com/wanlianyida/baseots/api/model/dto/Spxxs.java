package com.wanlianyida.baseots.api.model.dto;

import lombok.Data;

@Data
public class Spxxs {
    // 登记序号
    private String djxh; // ""

    // 货物编码数量
    private int hwbmCount; // 1

    // 金额合计
    private double jeSum; // 11500

    // 金额合计排名
    private int jeSumRank; // 1

    // 开票月份(yyyyMM格式)
    private String kpyf; // "202307"

    // 纳税人识别号
    private String nsrsbh; // "914303JDZXNSCS0002"

    // 税额合计
    private double seSum; // 0

    // 数量合计
    private int slSum; // 5

    // 税率
    private String slv; // "0.00"

    // 商品编码
    private String spbm; // "1010112000000000000"

    // 商品名称
    private String spmc; // "其他蔬菜"

    // 总成交笔数占比(%)
    private int zcjeCntRate; // 1

    // 总成交金额占比(%)
    private int zcjeSumRate; // 1
}
